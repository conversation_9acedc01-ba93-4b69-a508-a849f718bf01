const path = require("path");
const resolve = (dir) => path.join(__dirname, dir);
module.exports = {
  publicPath: "./",
  pluginOptions: {
    i18n: {
      locale: undefined,
      fallbackLocale: undefined,
      localeDir: undefined,
      enableInSFC: undefined,
      enableBridge: undefined
    }
  },
  configureWebpack: {
    devtool: 'source-map'
  },
  chainWebpack: (config) => {
    config.module
      .rule("svg")
      .exclude.add(resolve("src/assets/icons/svg"))
      .end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons/svg"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();
  },
}
// const { defineConfig } = require("@vue/cli-service");
// // const webpack = require("webpack");
// const path = require("path");
// const resolve = (dir) => path.join(__dirname, dir);
// module.exports = defineConfig({
//   transpileDependencies: true,
//   publicPath: "./",
//   chainWebpack: (config) => {
//     config.module
//       .rule("svg")
//       .exclude.add(resolve("src/assets/icons/svg"))
//       .end();
//     config.module
//       .rule("icons")
//       .test(/\.svg$/)
//       .include.add(resolve("src/assets/icons/svg"))
//       .end()
//       .use("svg-sprite-loader")
//       .loader("svg-sprite-loader")
//       .options({
//         symbolId: "icon-[name]",
//       })
//       .end();
//   },
// });
