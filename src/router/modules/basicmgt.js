import addProduct from "@/views/basic/commodity/addProduct.vue";
import productDetail from "@/views/basic/commodity/productDetail.vue";
import addWarehouse from "@/views/basic/warehouse/addWarehouse.vue";
import warehouseDetail from "@/views/basic/warehouse/warehouseDetail.vue";
import addCustomer from "@/views/basic/customer/addCustomer.vue";
import customerDetail from "@/views/basic/customer/customerDetail.vue";
import addSupplier from "@/views/basic/supplier/addSupplier.vue";
import supplierDetail from "@/views/basic/supplier/supplierDetail.vue";
const basicRouter = [
  {
    path: '/addWarehouse/:id',
    name: 'addWarehouse',
    component: addWarehouse
  },
  {
    path: '/warehouseDetail/:id',
    name: 'warehouseDetail',
    component: warehouseDetail
  },
  {
    path: '/addCustomer/:id',
    name: 'addCustomer',
    component: addCustomer
  },
  {
    path: '/customerDetail/:id',
    name: 'customerDetail',
    component: customerDetail
  },
  {
    path: '/addSupplier/:id',
    name: 'addSupplier',
    component: addSupplier,
  },
  {
    path: '/supplierDetail/:id',
    name: 'supplierDetail',
    component: supplierDetail,
  },
  {
    path: '/basic/commodity/list',
    component: () => import('@/views/basic/commodity/list'),
    name: 'basic_commodity_list'
  },
  {
    path: '/addProduct/:id',
    name: 'addProduct',
    component: addProduct,
  },
  {
    path: '/productDetail/:id',
    name: 'productDetail',
    component: productDetail,
  },
  {
    path: '/basic/brand/list',
    component: () => import('@/views/basic/brand/list'),
    name: 'basic_brand_list'
  },
  {
    path: '/basic/warehouse/list',
    component: () => import('@/views/basic/warehouse/list'),
    name: 'basic_warehouse_list'
  },
  {
    path: '/basic/supplier/list',
    component: () => import('@/views/basic/supplier/list'),
    name: 'basic_supplier_list'
  },
  {
    path: '/basic/salesCompany/list',
    component: () => import('@/views/basic/salesCompany/list'),
    name: 'basic_salesCompany_list'
  },
  {
    path: '/basic/unit/list',
    component: () => import('@/views/basic/unit/list'),
    name: 'basic_unit_list'
  },
  {
    path: '/basic/logistics/list',
    component: () => import('@/views/basic/logistics/list'),
    name: 'basic_logistics_list'
  },
  {
    path: '/basic/customer/list',
    component: () => import('@/views/basic/customer/list'),
    name: 'basic_customer_list'
  },
  {
    path: '/basic/shippInfo/list',
    component: () => import('@/views/basic/shippInfo/list'),
    name: 'basic_shippInfo_list'
  }
]
export default basicRouter;
