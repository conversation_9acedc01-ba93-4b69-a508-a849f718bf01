import addPurchaseApply from "@/views/purchase/purchaseApply/AddApplyOrder.vue";
import applyOrderDetail from "@/views/purchase/purchaseApply/ApplyOrderDetail.vue";
import purchaseApplyOrderDetail from "@/views/purchase/purchaseApply/ApplyOrderDetail.vue";
import purchaseOrderDetail from "@/views/purchase/purchaseOrder/PurchaseOrderDetail.vue";
const purchaseRoute = [
  {
    path: '/purchase/purchaseApply/list',
    name: 'purchaseApplyList',
    component: () => import('@/views/purchase/purchaseApply/list'),
  },
  {
    path: '/addPurchaseApply/:id',
    name: 'addPurchaseApply',
    component: addPurchaseApply
  },
  {
    path: '/purchaseApplyOrderDetail/:id',
    name: 'purchaseApplyOrderDetail',
    component: purchaseApplyOrderDetail,
  },
  {
    path: '/purchase/purchaseOrder/list',
    name: 'purchaseOrderList',
    component: () => import('@/views/purchase/purchaseOrder/list'),
  },
  {
    path: '/purchaseOrderDetail/:id',
    name: 'purchaseOrderDetail',
    component: purchaseOrderDetail,
  },
]

export default purchaseRoute;
