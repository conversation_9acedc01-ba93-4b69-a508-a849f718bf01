import salesOrderDetail from "@/views/sales/salesOrder/salesOrderDetail.vue";
import salesAdd from "@/views/sales/salesOrder/salesAdd.vue";
const salesRouter = [
  {
    path: '/sales/order/list',
    component: () => import('@/views/sales/salesOrder/list'),
    name: 'sales_order_list'
  },
  {
    path: '/salesAdd/:id',
    name: 'salesAdd',
    component: salesAdd,
  },
  {
    path: '/salesOrderDetail/:id',
    name: 'salesOrderDetail',
    component: salesOrderDetail,
  }
]
export default salesRouter;
