import AddTaskOrder from "@/views/mps/taskOrder/add.vue";
import TaskOrderDetail from "@/views/mps/taskOrder/detail";
import MaterialRegister from "@/views/mps/taskOrder/materialRegister";
import MaterialTrack from "@/views/mps/taskOrder/materialTrack";
const mpsRouter = [
  {
    path: '/mps/bom/list',
    component: () => import('@/views/mps/bom/list'),
    name: 'mps_bom_list'
  },
  /**
   * 生产任务单管理路由
   */
  {
    path: '/mps/task-order/list',
    component: () => import('@/views/mps/taskOrder/list'),
    name: 'mps_task_order_list'
  },
  {
    path: '/AddTaskOrder/:id',
    component: AddTaskOrder,
    name: 'AddTaskOrder'
  },
  {
    path: '/TaskOrderDetail/:id',
    component: TaskOrderDetail,
    name: 'TaskOrderDetail'
  },
  {
    path: '/MaterialRegister/:id',
    component: MaterialRegister,
    name: 'MaterialRegister'
  },
  {
    path: '/MaterialTrack/:id',
    component: MaterialTrack,
    name: 'MaterialTrack'
  },
  /**
   * 生产领料单管理路由
   */
  {
    path: '/mps/issue/list',
    component: () => import('@/views/mps/issue/list'),
    name: 'mps_issue_list'
  },
  {
    path: '/mps/issue/add/:id',
    component: () => import('@/views/mps/issue/add'),
    name: 'AddIssue'
  },
  {
    path: '/mps/issue/detail/:id',
    component: () => import('@/views/mps/issue/detail'),
    name: 'IssueDetail'
  },
  /**
   * 生产退料单管理路由
   */
  {
    path: '/mps/return/list',
    component: () => import('@/views/mps/return/list'),
    name: 'mps_return_list'
  },
  {
    path: '/mps/return/add/:id',
    component: () => import('@/views/mps/return/add'),
    name: 'AddReturn'
  },
  {
    path: '/mps/return/detail/:id',
    component: () => import('@/views/mps/return/detail'),
    name: 'ReturnDetail'
  }
]

export default mpsRouter;
