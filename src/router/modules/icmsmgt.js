import addBulletin from "@/views/icmsmgt/bulletin/addBulletin.vue";
import bulletinDetails from "@/views/icmsmgt/bulletin/bulletinDetails.vue";

const icmsmgtRouter = [
  {
    path: '/icmsmgt/data/list',
    component: () => import('@/views/icmsmgt/data/list'),
    name: 'icmsmgt_data_list'
  },
  {
    path: '/icmsmgt/history/list',
    component: () => import('@/views/icmsmgt/history/list'),
    name: 'icmsmgt_history_list'
  },
  {
    path: '/bulletinDetails/:id',
    name: 'bulletinDetails',
    component: bulletinDetails
  },
  {
    path: '/icmsmgt/bulletin/list',
    component: () => import('@/views/icmsmgt/bulletin/list'),
    name: 'icmsmgt_bulletin_list'
  },
  {
    path: '/addBulletin/:id',
    name: 'addBulletin',
    component: addBulletin
  },
]
export default icmsmgtRouter;
