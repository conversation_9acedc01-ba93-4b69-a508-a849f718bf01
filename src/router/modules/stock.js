import beOutStockDetail from "@/views/stock/outStock/beOutStockDetail.vue";
import outStockDetail from "@/views/stock/outStock/outStockDetail.vue";
import inStockDetail from "@/views/stock/inStock/inStockDetail.vue";
import beInStockDetail from "@/views/stock/inStock/beInStockDetail.vue";
import addInStock from "@/views/stock/inStock/addInStock.vue";

import addOutStock from "@/views/stock/outStock/addOutStock.vue";
import transferStockAdd from "@/views/stock/transferStock/transferStockAdd.vue";
import transferDetail from "@/views/stock/transferStock/transferDetail.vue";
const stockRouter = [
  {
    path: '/stock/inventory/list',
    component: () => import('@/views/stock/inventory/list'),
    name: 'stock_inventory_list'
  },
  // 出库相关
  {
    path: '/stock/outStock/list',
    name: 'stock_outStock_list',
    component: () => import('@/views/stock/outStock/list'),
  },
  {
    path: '/addOutStock/:type/:id',
    name: 'addOutStock',
    component: addOutStock,
  },
  {
    path: '/outStockDetail/:id',
    name: 'outStockDetail',
    component: outStockDetail,
  },
  {
    path: '/beOutStockDetail/:id',
    name: 'beOutStockDetail',
    component: beOutStockDetail,
  } ,
  // 入库相关
  {
    path: '/stock/inStock/list',
    name: 'stock_inStock_list',
    component: () => import('@/views/stock/inStock/list'),
  },
  {
    path: '/addInStock/:type/:id',
    name: 'addInStock',
    component: addInStock,
  },
  {
    path: '/inStockDetail/:id',
    name: 'inStockDetail',
    component: inStockDetail,
  },
  {
    path: '/beInStockDetail/:id',
    name: 'beInStockDetail',
    component: beInStockDetail,
  },
  // {
  //   path: '/purchaseOrderDetail/:id',
  //   name: 'purchaseOrderDetail',
  //   component: purchaseOrderDetail,
  // }

  // -------------------------调拨单 ----------------------------
  {
    path: '/stock/transferStock/list',
    name: 'stock_transferStock_list',
    component: () => import('@/views/stock/transferStock/list'),
  },
  {
    path: '/transferStockAdd/:id',
    name: 'transferStockAdd',
    component: transferStockAdd,
  },
  {
    path: '/transferDetail/:id',
    name: 'transferDetail',
    component: transferDetail,
  }
  // {
  //   path: '/salesOrderDetail/:id',
  //   name: 'salesOrderDetail',
  //   component: salesOrderDetail,
  // }
]
export default stockRouter;
