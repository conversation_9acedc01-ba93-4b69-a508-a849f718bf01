import {post, get, down, del, download} from "../plugins/request";
import {sysServerUrl} from '../assets/js/common'

export const importPackage = (params) => post(sysServerUrl + 'tis/maintenance/batchImport', params);
export const clearPackageData = (params) => post(sysServerUrl + 'tis/maintenance/clearData', params);
export const listPackage = (params) => get(sysServerUrl + 'tis/maintenance/listPackage', params);
export const deletePackage = (params) => post(sysServerUrl + 'tis/maintenance/deletePackage', params);
export const downloadPackage = (params) => download(sysServerUrl + 'tis/maintenance/downloadPackage', params);
export const modelWithPackage = (params) => get(sysServerUrl + 'tis/maintenance/modelWithPackage', params);
