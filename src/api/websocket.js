import store from '@/store/index';
import {sysServerUrl, cmsServerUrl} from '@/assets/js/common'

export function connectMessageServer(messageCallBack, error, onReady, onClose) {
  if ("WebSocket" in window) {
    if (sessionStorage.token == undefined || sessionStorage.token == null || sessionStorage.token == "") {
      return;
    }

    var webSocket = new WebSocket(`${cmsServerUrl.startsWith("https") ? "wss" : "ws"}:${cmsServerUrl.substring(cmsServerUrl.indexOf("//"))}websocket/${sessionStorage.token}`);
    webSocket.onopen = function () {
      console.log("连接成功！");
      onReady(webSocket)
    };
    webSocket.onmessage = function (evt) {
      var received_msg = evt.data;
      // console.log("接受消息：" + received_msg);
      messageCallBack(evt)

    };
    webSocket.onclose = function (e) {
      console.log("连接关闭！");
      onClose(e)
    };
    webSocket.onerror = function (e) {
      console.log("连接异常！");
      error(e)
    };
    return webSocket;
  }

}
