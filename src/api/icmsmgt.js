import {post, get, down, del, download} from "../plugins/request";
import {cmsServerUrl, sysServerUrl} from '../assets/js/common'


export const circuitImport = (params) => post(sysServerUrl + 'tis/icms/batchImport', params); // 电路管理-导入数据
export const clearPackageData = (params) => post(sysServerUrl + 'tis/icms/clearData', params); // 电路管理-清除数据
export const listPackage = (params) => get(sysServerUrl + 'tis/icms/listPackage', params); // 电路管理-数据包列表
export const deletePackage = (params) => post(sysServerUrl + 'tis/icms/deletePackage', params); // 电路管理-删除数据包
export const downloadPackage = (params) => download(sysServerUrl + 'tis/icms/downloadPackage', params); // 电路管理-下载数据包
// export const downloadPackage = (params) => get(sysServerUrl + 'tis/icms/api/getDetail', params); // 电路管理-内容数据
export const modelWithPackage = (params) => get(sysServerUrl + 'tis/icms/modelWithPackage', params);// 电路管理-车型列表（包含车型是否存在数据)

// 技术通告
export const bulletinData = (params) => get(cmsServerUrl + 'sys/manual/bulletin/list', params); // 技术通告-内容数据
export const bulletinAdd = (params) => post(cmsServerUrl + 'sys/manual/bulletin/add', params); // 技术通告-新增
export const bulletinEdit = (params) => post(cmsServerUrl + 'sys/manual/bulletin/edit', params); // 技术通告-编辑
export const bulletinContent = (params) => post(cmsServerUrl + 'sys/manual/bulletin/content/' + params); // 技术通告-内容
export const bulletinStatusEdit = (params) => post(cmsServerUrl + 'sys/manual/bulletin/statusEdit', params); // 技术通告-状态
export const bulletinDelete = (params) => post(cmsServerUrl + 'sys/manual/bulletin/del/' + params); // 技术通告-删除
export const bulletinInfo = (params) => post(cmsServerUrl + 'sys/manual/bulletin/details/' + params); // 技术通告-明细
export const bulletinLanguageType = (params) => get(sysServerUrl + 'cms/manual/language', params); // 语种分类
export const userCountryData = (params) => get(sysServerUrl + 'sys/region/api/getCountry'); // 获取国家
export const bulletinTypeData = (params) => get(sysServerUrl + 'sys/manual/bulletin/type', params); // 获取类型

// 用户管理-获取全部的国家信息包含大洲
export const getCountryAll = (params) => post(sysServerUrl + 'sys/region/api/country/all');

