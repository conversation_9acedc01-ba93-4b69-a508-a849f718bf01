import {sysServerUrl} from "@/assets/js/common";
import {get, post} from "@/plugins/request";

//---------------采购管理申请单-------------
//新增采购申请单
export const addPurchaseApply = (params) => post(sysServerUrl + 'purchase/apply/order/add', params);
//分页查询采购申请单
export const purchaseApplyList = (params) => post(sysServerUrl + 'purchase/apply/order/list', params);

export const deleteApplyOrderById = (id) => get(sysServerUrl + 'purchase/apply/order/delete/' + id)

//查询采购申请单详情
export const purchaseApplyInfo = (id) => get(sysServerUrl + 'purchase/apply/order/info/' + id)

//审核，作废
export const purchaseApplyAudit = (params) => post(sysServerUrl + 'purchase/apply/order/audit', params)

//生成采购订单
export const generatePurchaseOrder =(id) => get(sysServerUrl + 'purchase/apply/order/generatePurchaseOrder/' + id)
//编辑
export const purchaseApplyEdit = (params) => post(sysServerUrl + 'purchase/apply/order/edit', params)

//添加物料，根据销售订单号获取物料信息
export const getProductListBySaleNo = (params) => post(sysServerUrl + 'sales/order/getProductList', params);

//根据商品id查询bom单，获取物料列表
export const getMaterialListByProductId = (params) => post(sysServerUrl + 'bom/list', params);

//根据商品idlist，查询新增申请单详情所需的物料信息
export const queryProductsOrderDetail = (params) => post(sysServerUrl + 'purchase/apply/order/queryProductsOrderDetailVO', params);

//获取采购人员列表
export const getPurchasePersonnel = (params) => post(sysServerUrl + 'purchase/apply/order/getPurchasePersonnel', params);



//-------------------采购订单-------------------
export const purchaseOrderList = (params) => post(sysServerUrl + 'purchase/purchaseorder/list', params);

export const purchaseOrderInfo = (id) => get(sysServerUrl + 'purchase/purchaseorder/info/' +  id);

//审核,作废
export const purchaseOrderAudit = (params) => post(sysServerUrl + 'purchase/purchaseorder/audit', params)

//删除
export const deleteOrderById = (id) => get(sysServerUrl + 'purchase/purchaseorder/' + id)

//查询入库类型枚举
export const getPurchaseStoreEnum = (params) => get(sysServerUrl + 'purchase/purchaseorder/getPurchaseStoreEnum', params)

//添加采购订单物流信息
export const addPurchaseOrderLogistics = (params) => post(sysServerUrl + 'purchase/purchaseorder/addOrderLogisticsInfo', params)

export const generateBeInStock =(id) => get(sysServerUrl + 'purchase/purchaseorder/generateBeInStock/' + id)
