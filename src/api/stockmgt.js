import {sysServerUrl} from "@/assets/js/common";
import {del, get, post, put} from "@/plugins/request";

//库存管理

//分页查询库存信息
export const inventoryList = (params) => get(sysServerUrl + 'stock/stockinventory/list', params);

export const getInventoryInfo = (param) => get(sysServerUrl + 'stock/stockinventory/info/'+ param); // 详情信息
export const getInventoryAdd = (param) => post(sysServerUrl + 'stock/stockinventory/add', param); // 新增 Product 数据
export const lockInventory = (param) => post(sysServerUrl + 'stock/stockinventory/lockStock', param); // 锁定库存
export const unLockInventory = (param) => post(sysServerUrl + 'stock/stockinventory/unlockStock', param); // 锁定库存
export const getInventoryEdit = (param) => put(sysServerUrl + 'stock/stockinventory/edit', param); // 修改 Product 数据
export const getInventoryDel = (param) => del(sysServerUrl + 'stock/stockinventory/' + param); // 根据ID删除
export const getInventoryAudit = (param) => post(sysServerUrl + 'stock/stockinventory/audit' + param); // 审核


// 出库管理
export const beOutStockList = (params) => get(sysServerUrl + 'stock/outStock/list', params);

export const outboundInfo = (param) => get(sysServerUrl + 'stock/outStock/info/'+ param); // 详情信息

export const outboundDetailInfo = (param) => get(sysServerUrl + 'stock/outStock/detailInfo', param); // 出库明细

export const addOutbound = (param) => post(sysServerUrl + 'stock/outStock/add', param); // 生成出库单

export const editOutbound = (param) => put(sysServerUrl + 'stock/outStock/edit', param); // 编辑出库单


export const getSourceOrderInfo = (param) => get(sysServerUrl + 'stock/outStock/sourceNoInfo/', param); // 详情信息

export const auditOutbound = (params) => get(sysServerUrl + 'stock/outStock/audit', params); // 批量审核

export const auditRejectOutbound = (params) => get(sysServerUrl + 'stock/outStock/auditReject', params); // 批量驳回

export const delOutbound = (params) => get(sysServerUrl + 'stock/outStock/del', params); // 批量删除


// 入库管理

export const beInStockList = (params) => get(sysServerUrl + 'stock/inStock/list', params);

export const inboundInfo = (param) => get(sysServerUrl + 'stock/inStock/info/'+ param); // 详情信息

export const inboundDetailInfo = (param) => get(sysServerUrl + 'stock/inStock/detailInfo', param); // 入库明细

export const addInbound = (param) => post(sysServerUrl + 'stock/inStock/add', param); // 生成出库单

export const editInbound = (param) => put(sysServerUrl + 'stock/inStock/edit', param); // 编辑出库单

export const getInSourceOrderInfo = (param) => get(sysServerUrl + 'stock/inStock/sourceNoInfo/', param); // 详情信息

export const auditInbound = (params) => get(sysServerUrl + 'stock/inStock/audit', params); // 批量审核

export const auditRejectInbound = (params) => get(sysServerUrl + 'stock/inStock/auditReject', params); // 批量驳回

export const delInbound = (params) => get(sysServerUrl + 'stock/inStock/del', params); // 批量删除


// 调拨管理

export const transferList = (params) => get(sysServerUrl + 'stock/transfer/list', params);

export const transferBoundInfo = (param) => get(sysServerUrl + 'stock/transfer/info/'+ param); // 详情信息

export const addTransferBound = (param) => post(sysServerUrl + 'stock/transfer/add', param); // 生成出库单

export const editTransferBound = (param) => put(sysServerUrl + 'stock/transfer/edit', param); // 编辑出库单

export const getTransferSourceOrderInfo = (param) => get(sysServerUrl + 'stock/transfer/sourceNoInfo/', param); // 详情信息

export const auditTransferBound = (params) => get(sysServerUrl + 'stock/transfer/audit', params); // 批量审核

export const auditRejectTransferBound = (params) => get(sysServerUrl + 'stock/transfer/auditReject', params); // 批量驳回

export const delTransferBound = (params) => get(sysServerUrl + 'stock/transfer/del', params); // 批量删除
