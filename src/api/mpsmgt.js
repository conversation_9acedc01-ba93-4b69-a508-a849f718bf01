// 生产管理
import {down, download, formPost, get, post, postWithSearchParams} from "@/plugins/request";
import {sysServerUrl} from '@/assets/js/common'

// 根据商品信息查询bom
export const getBomList = (param) => post(sysServerUrl + 'mps/bom/list', param);
export const addBomList = (param) => post(sysServerUrl + 'mps/bom/add', param);
/**
 * 删除单个BOM物料
 * @param {number} id - BOM物料的唯一ID
 * @returns {Promise}
 */
export const deleteBom = (id) => postWithSearchParams(sysServerUrl + 'mps/bom/delete', {id});
/**
 * 批量删除BOM物料
 * @param {Array<number>} ids - 需要删除的BOM物料ID数组
 * @returns {Promise}
 */
export const deleteBomList = (ids) => postWithSearchParams(sysServerUrl + 'mps/bom/batchDelete', {ids});
/**
 * 批量导出BOM物料
 * @param {Object} param - 查询参数productId
 * @returns {Promise} 文件流
 */
export const exportBomList = (param) => down(`${sysServerUrl}mps/bom/batchExport?productId=${param}`);
/**
 * 批量更新BOM物料用量
 * @param {Array<Object>} items - 需要更新的BOM物料数组，每个对象包含id和quantity字段
 * @returns {Promise}
 */
export const batchUpdateBomQuantity = (items) => post(sysServerUrl + 'mps/bom/batchUpdate', {items});
/**
 * 更新单个BOM物料信息
 * @param {Object} item - 需要更新的BOM物料对象，包含id、quantity(用量)、attritionRate(损耗率)、remark(备注)字段
 * @returns {Promise}
 */
export const updateBom = (item) => post(sysServerUrl + 'mps/bom/update', item);

/**
 * 下载BOM导入模板
 * @returns {Promise} 模板文件流
 */
export const downloadBomTemplate = () => down(`${sysServerUrl}mps/bom/template`);

/**
 * 批量导入BOM物料
 * @param {FormData} formData - 包含文件和productId的表单数据
 * @returns {Promise}
 */
export const importBomList = (formData) => formPost(sysServerUrl + 'mps/bom/import', formData);

/**
 * 复制BOM到其他商品
 * @param {Object} param - 参数对象，包含：
 *   productId: 源商品ID（当前商品ID）
 *   targetProductIds: 目标商品ID数组（要复制到的商品ID列表）
 * @returns {Promise} 请求Promise对象
 */
export const copyBomToProducts = (param) => postWithSearchParams(sysServerUrl + 'mps/bom/copy', param);

/**
 * 生产任务单管理API
 */

/**
 * 获取生产任务单列表
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getTaskOrderList = (param) => post(sysServerUrl + 'mps/task-order/list', param);

/**
 * 删除生产任务单
 * @param {number} id - 任务单ID
 * @returns {Promise} 请求Promise对象
 */
export const deleteTaskOrder = (id) => postWithSearchParams(sysServerUrl + 'mps/task-order/delete', {id});

/**
 * 导出生产任务单
 * @param {Object} param - 导出参数
 * @returns {Promise} 文件流
 */
export const exportTaskOrder = (param) => download(sysServerUrl + 'mps/task-order/export', param);

/**
 * 新增生产任务单
 * @param {Object} param - 新增参数
 * @returns {Promise} 请求Promise对象
 */
export const addTaskOrder = (param) => post(sysServerUrl + 'mps/task-order/add', param);

/**
 * 编辑生产任务单
 * @param {Object} param - 编辑参数
 * @returns {Promise} 请求Promise对象
 */
export const editTaskOrder = (param) => post(sysServerUrl + 'mps/task-order/update', param);

/**
 * 根据生产任务ID查询详细信息
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getOrderDetail = (param) => postWithSearchParams(sysServerUrl + 'mps/task-order/detail', param);
/**
 * 批量删除生产任务单
 * @param {Array<number>} ids - 任务单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const batchDeleteTaskOrder = (ids) => postWithSearchParams(sysServerUrl + 'mps/task-order/batchDelete', {ids});

/**
 * 反审核生产任务单
 * @param {number} id - 任务单ID
 * @returns {Promise} 请求Promise对象
 */
/**
 * 反审核生产任务单
 * @param {Array<number>} ids - 任务单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const revertTaskOrder = (ids) => postWithSearchParams(sysServerUrl + 'mps/task-order/revert', {ids});

/**
 * 作废生产任务单
 * @param {Array<number>} ids - 任务单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const abrogateTaskOrder = (ids) => postWithSearchParams(sysServerUrl + 'mps/task-order/abrogate', {ids});

/**
 * 驳回生产任务单
 * @param {number} id - 任务单ID
 * @param {string} rejectReason - 驳回原因
 * @param {Array} accessoryList - 附件列表
 * @returns {Promise} 请求Promise对象
 */
export const rejectTaskOrder = (id, rejectReason, accessoryList) => postWithSearchParams(sysServerUrl + 'mps/task-order/reject', {
  id,
  rejectReason,
  accessoryList
});

/**
 * 生产任务单审核
 * @param {Array<number>} ids - 任务单ID列表
 * @param {number} status - 审核状态
 * @returns {Promise} 请求Promise对象
 */
export const auditTaskOrder = (ids, status) => postWithSearchParams(sysServerUrl + 'mps/task-order/audit', {
  ids,
  status
});

/**
 * 完工生产任务单
 * @param {number} id - 任务单ID
 * @returns {Promise} 请求Promise对象
 */
export const finishTaskOrder = (id) => postWithSearchParams(sysServerUrl + 'mps/task-order/finish', {id});


/**
 * 物料追踪管理API
 */

/**
 * 获取物料追踪列表（不分页）
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getMaterialTrackList = (param) => post(sysServerUrl + 'mps/material-track/list', param);

/**
 * 获取物料追踪列表（分页）
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getMaterialTrackPage = (param) => post(sysServerUrl + 'mps/material-track/page', param);
/**
 * 更新物料追踪
 * @param {Object} param - 更新参数
 * @returns {Promise} 请求Promise对象
 */
export const updateMaterialTrack = (param) => post(sysServerUrl + 'mps/material-track/update', param);

/**
 * 导出物料追踪
 * @param {Object} param - 导出参数
 * @returns {Promise} 文件流
 */
export const exportMaterialTrack = (param) => download(sysServerUrl + 'mps/material-track/export', param);


/**
 * 生产领料单管理API
 */

/**
 * 获取生产领料单列表
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getIssueList = (param) => post(sysServerUrl + 'mps/issue/list', param);

/**
 * 新增生产领料单
 * @param {Object} param - 新增参数
 * @returns {Promise} 请求Promise对象
 */
export const addIssue = (param) => post(sysServerUrl + 'mps/issue/add', param);

/**
 * 编辑生产领料单
 * @param {Object} param - 编辑参数
 * @returns {Promise} 请求Promise对象
 */
export const editIssue = (param) => post(sysServerUrl + 'mps/issue/update', param);

/**
 * 根据生产领料单ID查询详细信息
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getIssueDetail = (param) => postWithSearchParams(sysServerUrl + 'mps/issue/detail', param);

/**
 * 删除生产领料单
 * @param {number} id - 领料单ID
 * @returns {Promise} 请求Promise对象
 */
export const deleteIssue = (id) => postWithSearchParams(sysServerUrl + 'mps/issue/delete', {id});

/**
 * 批量删除生产领料单
 * @param {Array<number>} ids - 领料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const batchDeleteIssue = (ids) => postWithSearchParams(sysServerUrl + 'mps/issue/batchDelete', {ids});

/**
 * 审核生产领料单
 * @param {Array<number>} ids - 领料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const auditIssue = (ids) => postWithSearchParams(sysServerUrl + 'mps/issue/audit', {ids});

/**
 * 生产退料单管理API
 */

/**
 * 获取生产退料单列表
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getReturnList = (param) => post(sysServerUrl + 'mps/return/list', param);

/**
 * 新增生产退料单
 * @param {Object} param - 新增参数
 * @returns {Promise} 请求Promise对象
 */
export const addReturn = (param) => post(sysServerUrl + 'mps/return/add', param);

/**
 * 编辑生产退料单
 * @param {Object} param - 编辑参数
 * @returns {Promise} 请求Promise对象
 */
export const editReturn = (param) => post(sysServerUrl + 'mps/return/update', param);

/**
 * 根据生产退料单ID查询详细信息
 * @param {Object} param - 查询参数
 * @returns {Promise} 请求Promise对象
 */
export const getReturnDetail = (param) => postWithSearchParams(sysServerUrl + 'mps/return/detail', param);

/**
 * 删除生产退料单
 * @param {number} id - 退料单ID
 * @returns {Promise} 请求Promise对象
 */
export const deleteReturn = (id) => postWithSearchParams(sysServerUrl + 'mps/return/delete', {id});

/**
 * 批量删除生产退料单
 * @param {Array<number>} ids - 退料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const batchDeleteReturn = (ids) => postWithSearchParams(sysServerUrl + 'mps/return/batchDelete', {ids});

/**
 * 审核生产退料单
 * @param {Array<number>} ids - 退料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const auditReturn = (ids) => postWithSearchParams(sysServerUrl + 'mps/return/audit', {ids});

/**
 * 反审核生产退料单
 * @param {Array<number>} ids - 退料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const revertReturn = (ids) => postWithSearchParams(sysServerUrl + 'mps/return/revert', {ids});

/**
 * 作废生产退料单
 * @param {Array<number>} ids - 退料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const abrogateReturn = (ids) => postWithSearchParams(sysServerUrl + 'mps/return/abrogate', {ids});

/**
 * 驳回生产退料单
 * @param {number} id - 退料单ID
 * @param {string} rejectReason - 驳回原因
 * @param {Array} accessoryList - 附件列表
 * @returns {Promise} 请求Promise对象
 */
export const rejectReturn = (id, rejectReason, accessoryList) => postWithSearchParams(sysServerUrl + 'mps/return/reject', {
  id,
  rejectReason,
  accessoryList
});

/**
 * 导出生产退料单
 * @param {Object} param - 导出参数
 * @returns {Promise} 文件流
 */
export const exportReturn = (param) => download(sysServerUrl + 'mps/return/export', param);

/**
 * 反审核生产领料单
 * @param {Array<number>} ids - 领料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const revertIssue = (ids) => postWithSearchParams(sysServerUrl + 'mps/issue/revert', {ids});

/**
 * 驳回生产领料单
 * @param {number} id - 领料单ID
 * @param {string} rejectReason - 驳回原因
 * @param {Array} accessoryList - 附件列表
 * @returns {Promise} 请求Promise对象
 */
export const rejectIssue = (id, rejectReason, accessoryList) => postWithSearchParams(sysServerUrl + 'mps/issue/reject', {
  id,
  rejectReason,
  accessoryList
});

/**
 * 作废生产领料单
 * @param {Array<number>} ids - 领料单ID列表
 * @returns {Promise} 请求Promise对象
 */
export const abrogateIssue = (ids) => postWithSearchParams(sysServerUrl + 'mps/issue/abrogate', {ids});

/**
 * 导出生产领料单
 * @param {Object} param - 导出参数
 * @returns {Promise} 文件流
 */
export const exportIssue = (param) => download(sysServerUrl + 'mps/issue/export', param);

/**
 * 成品入库生成待入库单
 * @param id
 * @returns {AxiosPromise}
 */
export const generateBeInStock =(id) => get(sysServerUrl + 'mps/task-order/generateBeInStock/' + id)

