/**
 * 发布管理
 */


import {down, download, get, post} from "../plugins/request";
import {cmsServerUrl, sysServerUrl} from '../assets/js/common'

export const listPart = (param) => post(cmsServerUrl + "release/part/list", param);
export const listModelConfig = (param) => post(cmsServerUrl + "release/part/listModelConfig", param);
export const listPushRecord = (param) => post(cmsServerUrl + "release/part/listPushRecord", param);
export const rePush = (param) => post(cmsServerUrl + "release/part/rePush", param);
export const getReplaceChain = (params) => post(cmsServerUrl + 'release/replacement/chainByBaseCode', params); // 查询替换链
export const deleteReplacement = (params) => post(cmsServerUrl + 'release/replacement/delete', params); // 查询替换链
// =================== 技术手册

export const languageData = (param) => get(cmsServerUrl + "release/api/language");
export const productTree = (param) => get(cmsServerUrl + "release/api/product/tree");
export const releaseInfo = (param) => get(cmsServerUrl + "release/api/info/" + param);


// 技术手册-数据内容
export const fetchCarTree = (param) => get(cmsServerUrl + "car/tree", param);
export const configTree = (param) => get(cmsServerUrl + "car/use/tree", param);
export const manualData = (param) => post(cmsServerUrl + "release/manual/findPage", param);
// 技术手册-上传
export const manualUpload = (param) => post(cmsServerUrl + "release/manual/upload", param);
export const uploadPart = (param) => post(cmsServerUrl + "release/manual/uploadPart", param);
export const uploadWorkDetail = (param) => post(cmsServerUrl + "release/manual/uploadWorkDetail", param);
export const exportWorkHour = (param) => download(cmsServerUrl + "release/manual/workHour/export", param);
export const exportWorkHourDetail = (param) => download(cmsServerUrl + "release/manual/workHour/exportDetail", param);
export const uploadPartImage = (param) => post(cmsServerUrl + "release/manual/uploadPartImage", param);
export const uploadReplacement = (param) => post(cmsServerUrl + "release/manual/uploadReplacement", param);
export const downImage = (params) => download(cmsServerUrl + 'release/image/download', params);  // 下载
export const clearImage = (params) => post(cmsServerUrl + 'release/image/clear', params);  // 下载
export const addManual = (param) => post(cmsServerUrl + "release/manual/add", param);

// =================== 配置管理
export const listConfig = (param) => get(cmsServerUrl + "manual/type/page", param);
export const addConfig = (param) => post(cmsServerUrl + "manual/type/add", param);
export const updateConfig = (param) => post(cmsServerUrl + "manual/type/update", param);
export const deleteConfig = (param) => post(cmsServerUrl + "manual/type/delete/" + param);
export const listAllConfig = (param) => get(cmsServerUrl + "manual/type/all");
export const listAllWithImageConfig = (param) => get(cmsServerUrl + "manual/type/all/with-image");

// =================== 手册图片管理
export const listManualImage = (param) => get(cmsServerUrl + "manual/category/image/page", param);
export const saveManualImage = (param) => post(cmsServerUrl + "manual/category/image/save", param);

// =================== SBOM
export const saveData = (params, tableName) => post(sysServerUrl + `sbom/${tableName}/save`, params);// SBOM管理-信息列表
export const updateData = (params, tableName) => get(sysServerUrl + `sbom/${tableName}/update`, params);// SBOM管理-信息列表
export const deleteData = (params, tableName) => get(sysServerUrl + `sbom/${tableName}/delete`, params);// SBOM管理-信息列表
export const updateBatchById = (params, tableName) => getWithArray(sysServerUrl + `sbom/${tableName}/updateBatchById`, params);// SBOM管理-信息列表
export const listData = (params, tableName) => get(sysServerUrl + `sbom/${tableName}/list`, params);// SBOM管理-信息列表
export const importData = (params, tableName) => post(sysServerUrl + `sbom/${tableName}/import`, params);// SBOM管理-信息导入
export const exportData = (params, tableName) => download(sysServerUrl + `sbom/${tableName}/export`, params);// SBOM管理-信息导出
export const clearData = (params, tableName) => get(sysServerUrl + `sbom/${tableName}/clear`, params);// SBOM管理-信息列表
// export const basicReplaceChain = (params) => post(cmsServerUrl + 'cms/epc/replacement/chain', params); // 查询替换链
// export const basicImage = (params) => post(cmsServerUrl + 'cms/epc/basic/image/'+ params); // 查询实物图
// export const basicImportImage = (params) => post(cmsServerUrl + 'cms/epc/basic/importImages', params); // 导入 zip
// export const basicreplaceImage = (params) => post(cmsServerUrl + 'cms/epc/basic/replaceImage', params);  // 清空
// export const partsImageTemplate = () => down(sysServerUrl + 'static/excel/实物图模板.zip'); // 实物图模板下载
// export const partsInfoTemplate = () => down(sysServerUrl + 'static/excel/配件明细模板.xlsx');
export const downloadTemplate = (params) => down(sysServerUrl + `static/excel/${params}`);
// 技术手册-还原
export const manualReduction = (param) => post(cmsServerUrl + "release/manual/reduction/" + param);



// =================== 工具管理
// 专项工具-数据内容
export const toolData = (param) => post(cmsServerUrl + "release/tool/findPage", param);
// 专项工具-能源类型
export const toolType = (param) => post(cmsServerUrl + "release/tool/type", param);
// 专项工具-新增
export const toolAdd = (param) => post(cmsServerUrl + "release/tool/add", param);
// 专项工具-编辑
export const toolEdit = (param) => post(cmsServerUrl + "release/tool/edit", param);
// 专项工具-上传
export const toolUpload = (param) => post(cmsServerUrl + "release/tool/upload", param);
// 专项工具-还原
export const toolReduction = (param) => post(cmsServerUrl + "release/tool/reduction/" + param);
// 专项工具-删除
export const toolDelete = (param) => post(cmsServerUrl + "release/tool/del/" + param);

// 专项工具 - 厂家 - 列表
export const toolFactoryList = (param) => get(cmsServerUrl + "tool/type/factory/list", param);
// 专项工具 - 厂家 - 新增
export const toolFactoryAdd = (param) => post(cmsServerUrl + "tool/type/factory/add", param);
// 专项工具 - 厂家 - 编辑
export const toolFactoryEdit = (param) => post(cmsServerUrl + "tool/type/factory/edit", param);
// 专项工具 - 厂家 - 删除
export const toolFactoryDel = (id) => post(cmsServerUrl + "tool/type/factory/del/" + id);


// 专项工具 - 总成 - 列表
export const toolAssemblyList = (param) => get(cmsServerUrl + "tool/type/assembly/list", param);
// 专项工具 - 总成 - 新增
export const toolAssemblyAdd = (param) => post(cmsServerUrl + "tool/type/assembly/add", param);
// 专项工具 - 总成 - 编辑
export const toolAssemblyEdit = (param) => post(cmsServerUrl + "tool/type/assembly/edit", param);
// 专项工具 - 总成 - 删除
export const toolAssemblyDel = (id) => post(cmsServerUrl + "tool/type/assembly/del/" + id);


// 专项工具 - 能源 - 列表
export const toolEnergyList = (param) => get(cmsServerUrl + "tool/type/energy/list", param);
// 专项工具 - 能源 - 新增
export const toolEnergyAdd = (param) => post(cmsServerUrl + "tool/type/energy/add", param);
// 专项工具 - 能源 - 编辑
export const toolEnergyEdit = (param) => post(cmsServerUrl + "tool/type/energy/edit", param);
// 专项工具 - 能源 - 删除
export const toolEnergyDel = (id) => post(cmsServerUrl + "tool/type/energy/del/" + id);


// =================== VR管理
// VR管理-数据内容
export const vrData = (param) => post(cmsServerUrl + "release/vr/findPage", param);
// VR管理-上传
export const vrUpload = (param) => post(cmsServerUrl + "release/vr/upload", param);
// VR管理-还原
export const vrReduction = (param) => post(cmsServerUrl + "release/vr/reduction/" + param);
