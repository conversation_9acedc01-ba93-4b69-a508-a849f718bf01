import {post, get, down, del, put} from "../plugins/request";
import { sysServerUrl, cmsServerUrl } from '../assets/js/common'

import axios from 'axios'

import store from '@/store/index';
//必须使用此种方式,否则拦截器会在header上加token
export const requestTokenRefresh = (params) =>  axios.create()({// 刷新token
  url: sysServerUrl + 'system/refresh',
  method: 'post',
  data: params
})

// 验证码
export const captCha = (params) => get(sysServerUrl + 'sys/captcha/get', params);
// 获取最新安装包
export const appInfo = () => get(sysServerUrl + "api/app/getPackageInfo");
// App下载-历史版本
export const appHistoryVersion = (id, params) => get(sysServerUrl + "api/app/getHistorical/" + id,  params);
// 登录
export const onLogin = (params) => post(sysServerUrl + 'system/login', params);
// 查询关联子账号
export const querySubAccounts = (params) => post(sysServerUrl + 'system/querySubAccounts', params);
// 第一次登录修改密码
export const initialPsw = (params) => post(sysServerUrl + 'sys/user/updatePsw', params);
// 退出登录
export const logout = (params) => post(sysServerUrl + 'system/logout', params);

// 当前用户信息
export const getUserInfo = (params) => get(sysServerUrl + 'system/getCurrentInfo', params);
// 当前用户信息
export const currentUserInfo = (params) => get(sysServerUrl + 'system/getCurrentInfo', params);
// 目录列表
export const catalog = (params) => get(sysServerUrl + 'system/current/menutreee', params);
// 用户修改密码
export const userDefinedPwd = (params) => post(sysServerUrl + 'sys/user/updatePsw', params);
// 用户修改主题
export const updatedTheme = (params) => post(sysServerUrl + "sys/user/updatedTheme", params);
// 用户修改基本信息
// export const userDefinedInfo = (params) => post(sysServerUrl + "sys/user/updateInfo", params);
export const userDefinedInfo = (id, params) => put(sysServerUrl + 'sys/user/' + id, params);





// 我的主页-查询发布的系统公告
export const systemBulletinList = (params) => get(sysServerUrl + 'sys/notice/getNoticeByRelease', params);
// 我的主页-查询系统公告明细
export const systemBulletinInfo = (params) => get(sysServerUrl + 'sys/notice/info/' + params);
// 我的主页-修改公告的观看次数
export const systemBulletinRead = (params) => post(sysServerUrl + 'sys/notice/editRead/' + params);
// 我的主页-获取主页信息
export const getHomeData = (params) => get('', params);
// 我的主页-访问记录
export const getRecordsData = (params) => get('', params);
// 我的主页-相关项目
export const projectCorrelation = () => post(cmsServerUrl + 'cms/project/size/myProject');
// 我的主页-最新动态
export const dynamic = (params) => get(sysServerUrl + 'front/message/dynamic/list', params) ;


// 系统管理-文件上传
export const importAttach = (params) => post(sysServerUrl + 'sys/upload/attach', params);
// 系统管理-文件管理-批量上传
export const procSplitFile = (params) => post(sysServerUrl + "sys/upload/procFile", params);
// 系统管理-文件管理-批量上传分片
export const checkUploadProgress = (params) => post(sysServerUrl + "sys/upload/checkProgress", params);
// 系统管理-文件管理-获取文件上传的信息
export const getFileInfo = (param) => post(sysServerUrl + "sys/upload/getFileInfo", param);
// 系统管理-文件管理-删除文件上传的信息
export const delFileInfo= (param) => post(sysServerUrl + "sys/upload/delFileInfo", param);
// 系统管理-文件下载
export const downFile= (param) => down(param);
// 数据处理进度
export const uploadProgressApi = (params) => get(sysServerUrl +  'progress/getRedisResult/' + params); // 图片上传进度


// ============ 我的主页 =========== //
// 近一周访问量
export const accessOneWeek = () => get(sysServerUrl + "statistics/access/access");
// 快捷入口-查询添加和未添加的菜单
export const quickEntryMenuData = (params) => get(sysServerUrl + 'sys/quickEntry/api/quickEntryList');
// 快捷入口-修改快捷入口
export const quickEntryEdit = (params) => post(sysServerUrl + 'sys/quickEntry/api/edit', params);
// 汇总消息
export const messageData = (params) => get(sysServerUrl + 'front/message/background', params); // 汇总消息



// ========== 系统管理 ========== //

// 数据字典-分页查询 内容数据
export const dictData = (page, params) => post(sysServerUrl + 'sys/dict/search/' + page, params);
// 数据字典-类型数据
export const dictTypeList = (params) => get(sysServerUrl + 'sys/dict/list/type', params);
// 数据字典-新增
export const dictAdd = (params) => post(sysServerUrl + 'sys/dict', params);
// 数据字典-编辑
export const dictEdit = (id, params) => put(sysServerUrl + 'sys/dict/' + id, params);
// 数据字典-删除
export const dictDel = (params) => del(sysServerUrl + 'sys/dict/' + params);
// 根据类型获取
export const dictTypeQueryData = (params) => get(sysServerUrl + 'sys/dict/query?dictType='+ params);


// 用户管理-分页查询 内容数据
export const userDataList = (params) => get(sysServerUrl + 'sys/user/findList', params);
// 用户管理-全部的角色信息（用户能看到的）
export const userRoleData = (params) => get(sysServerUrl + 'sys/role/list/all', params);
// 用户管理-增加
export const userAdd = (params) => post(sysServerUrl + 'sys/user/add', params);
// 用户管理-编辑
export const userEdit = (params) => post(sysServerUrl + 'sys/user/edit', params);
// 用户管理-删除
export const userDel = (id) => del(sysServerUrl + 'sys/user/physical/' + id);
// 用户管理-密码重置
export const userPasswordReset = (params) => get(sysServerUrl + 'sys/user/initPsw/' + params);
// 用户管理-根据ID查询用户信息
export const assignRole = (params) => get(sysServerUrl + 'sys/user/info/' + params);
// 用户管理-分配角色(分配)
export const updateAssignRole = (id, params) => post(sysServerUrl + 'sys/user/assignRole/' + id, params);
// 用户管理-获取国家
export const userCountryData = () => get(sysServerUrl + 'sys/region/api/getCountry');
// 用户管理-获取全部的国家信息包含大洲
export const getCountryAll = (params) => post(sysServerUrl + 'sys/region/api/country/all');
// 用户管理-获取用户的国家
export const getUserCountry = (params) => get(sysServerUrl + 'sys/user/getUserCountry/' + params);
// 用户管理-修改授权国家
export const updatedCountry = (params) => post(sysServerUrl + 'sys/user/updatedCountry', params);

// 用户管理-模板下载
export const downTemplate = () => down(sysServerUrl + 'static/excel/用户导入模板.xlsx');
// 用户管理-批量添加用户-文件上传
export const batchAdd = (params) => post(sysServerUrl + 'sys/user/batchImport', params);

// 用户管理-导出用户
export const userDown = (params) => down(sysServerUrl + 'sys/user/batch?' + params);

export const userAtlasCountry = (params) => post(sysServerUrl + 'sys/user/atlasCountry');


export const userEmpowerCountry = (params) => get(sysServerUrl + 'sys/region/api/empowerCountry');



//经销商管理
// 经销商管理-内容数据
export const dealerData = (params) => get(sysServerUrl + 'sys/dealer/list', params);

/**
 * 服务店子用户
 * @param params
 * @return {AxiosPromise}
 */
export const dealerChildrenData = (params) => get(sysServerUrl + 'sys/dealer/subUserList', params);
// 经销商管理-添加
export const dealerAdd = (params) => post(sysServerUrl + 'sys/dealer/add', params);
// 经销商管理-修改
export const dealerEdit = (params) => post(sysServerUrl + 'sys/dealer/edit', params);
export const dealerUserInfo = (params) => post(sysServerUrl + 'sys/dealer/info', params);
// 经销商管理-删除
export const dealerDel = (params) => post(sysServerUrl + 'sys/dealer/del/' + params);
// 经销商管理-批量添加子账户
export const dealerBatchSubAccount = (params) => post(sysServerUrl + 'sys/dealer/subAccount', params);
// 经销商管理-根据ID查询用户信息
export const getUserInfoId = (params) => get(sysServerUrl + 'sys/user/info', params);
// 经销商管理-根据国家查询国家下的信息
export const getCountryTree = (params) => post(sysServerUrl + 'sys/region/api/getCountryTree', params);


// 经销商管理-树结构单个节点子节点数据获取
export const dealerChildData = (params) => get(sysServerUrl + 'sys/dealer/child/list', params);
// 经销商-模板下载
export const downDealerTemplate = (params) => down(sysServerUrl + 'static/excel/服务店导入模板.xlsx');
// 经销商-导出
export const dealerBatchDown = (params) => down(sysServerUrl + 'sys/dealer/batchExport?' + params);
// 经销商-导入
export const dealerBatchImport = (params) => post(sysServerUrl + 'sys/dealer/batchImport', params);

export const getUserTrainByUserId = (params) => post(sysServerUrl + 'sys/user/getUserTrainByUserId', params);

export const getAll = (params) => post(sysServerUrl + 'sys/dealer/all', params);
export const authorize = (params) => post(sysServerUrl + 'sys/dealer/authorize', params);



//角色管理-内容数据
export const roleData = (params) => get(sysServerUrl + 'sys/role/list', params);
//角色管理-增加
export const roleAdd = (params) => post(sysServerUrl + 'sys/role', params);
//角色管理-编辑
export const roleEdit = (id,params) => put(sysServerUrl + 'sys/role/' + id, params);
//角色管理-删除
export const roleDel = (id,params) => del(sysServerUrl + 'sys/role/' + id);



// 菜单管理-内容数据
export const menuData = (params) => get(sysServerUrl + 'sys/menu/getMenuTree', params);
// 菜单管理-分配按钮列表
export const menuAssignList = (params) => get(sysServerUrl + 'sys/permission/list/all', params);
// 菜单管理-增加
export const menuAdd = (params) => post(sysServerUrl + 'sys/menu/add', params);
// 菜单管理-编辑
export const menuEdit = (params) => post(sysServerUrl + 'sys/menu/edit', params);
// 菜单管理-查看
export const menuCheck = (params) => get(sysServerUrl + 'sys/menu/info' + params);
// 菜单管理-删除
export const menuDel = (params) => get(sysServerUrl + 'sys/menu/del' + params);
// 菜单管理-分配按钮信息
export const menuAssignInfo = (params) => get(sysServerUrl + 'sys/permission/list/bymenu' + params);
// 菜单管理-分配按钮提交
export const menuAssignUpdate = (params) => post(sysServerUrl + 'sys/menu/assign', params);

// 菜单管理-权限码列表
export const permissionList = (params) => get(sysServerUrl + 'sys/permission/list/all', params);
// 菜单管理-添加权限码
export const operatePermission = (params) => post(sysServerUrl + 'sys/permission/add', params);
// 菜单管理-删除权限码
export const delPermission = (params) => post(sysServerUrl + 'sys/permission/del', params);




//权限管理-内容数据
export const oauthData = (params) => get(sysServerUrl + 'sys/permission/list', params);
//权限管理-角色列表
export const oauthRoleList = (params) => get(sysServerUrl + 'sys/role/list/all', params);
//权限管理-用户列表
export const oauthUserList = (params) => get(sysServerUrl + 'sys/user/findList/all', params);
//权限管理-提交
export const oauthSubmit = (params) => post(sysServerUrl + 'sys/permission/assign', params);
//权限管理-刷新用户
export const userRefresh = (params) => post(sysServerUrl + 'sys/permission/list/user', params);
//权限管理-刷新角色
export const roleRefresh = (params) => post(sysServerUrl + 'sys/permission/list/role', params);





// 部门管理
export const departmentData = (params) => get(sysServerUrl + 'sys/dept/getTree', params); //部门管理-查询树
export const departmentType = (params) => get(sysServerUrl + 'sys/dept/getDept', params); // 部门管理-查询所以部门
export const departmentTeam = (params) => get(sysServerUrl + 'sys/dept/getGruop/' + params); // 部门管理-查询部门下的小组
export const departmentAdd = (params) => post(sysServerUrl + 'sys/dept/add', params); // 部门管理-添加
export const departmentEdit = (params) => post(sysServerUrl + 'sys/dept/update', params); // 部门管理-修改
export const departmentDel = (params) => del(sysServerUrl + 'sys/dept/' + params); // 部门管理-删除



// 系统公告
//系统公告-公告类型
export const noticeTypeList = (params) => get(sysServerUrl + 'sys/notice/type', params);
//系统公告-内容数据
export const noticeData = (params) => get(sysServerUrl + 'sys/notice/list', params);
// 系统公告-状态
export const noticeStateEdit = (params) => post(sysServerUrl + 'sys/notice/statusEdit', params);
// 系统公告-新增
export const noticeAdd = (params) => post(sysServerUrl + 'sys/notice/add', params);
// 系统公告-编辑
export const noticeEdit = (params) => post(sysServerUrl + 'sys/notice/edit', params);
// 系统公告-删除
export const noticeDel = (params) => post(sysServerUrl + 'sys/notice/del/' + params);
// 系统公告-明细
export const noticeInfo = (params) => post(sysServerUrl + 'sys/notice/getNoticeById/' + params);

// 获取对象
export const noticTarget = (params) => get(sysServerUrl + "sys/notice/getTarget", params);
// 获取对象
export const noticEmpower = (params) => post(sysServerUrl + "sys/notice/empower/" + params);



// ========== 车型管理 ========== //
// 车系管理-内容数据
export const trainData = (params) => get(sysServerUrl + 'sys/car/train/list', params);
// 车系管理-节点类型
export const trainTypeDate = (params) => get(sysServerUrl + 'sys/car/train/type', params);
// 车系管理-获取当前信息
export const getTrainInfo = (params) => post(sysServerUrl + 'sys/car/train/find' + params);
// 车系管理-增加节点
export const trainAdd = (params) => post(sysServerUrl + 'sys/car/train/addNode', params);
// 车系管理-修改
export const trainUpdate = (params) => post(sysServerUrl + 'sys/car/train/update', params);
// 车系管理-删除
export const trainDel = (params) => post(sysServerUrl + 'sys/car/train/del/' + params);
// 车系管理-能源类型：新能源、燃油车
// export const trainCarTypeList = (params) => get(sysServerUrl + 'sys/car/energy/type', params);

export const importTrain = (params) => post(sysServerUrl + 'sys/car/train/importTrain', params); // 批量上传

export const trainTemplate = () => down(sysServerUrl + 'static/excel/车型导入模板.xlsx'); // 车型管理-车型模板下载

// 车型条件查询
export const trainFindList = (param) => get(sysServerUrl + "car/train", param);
// 配置条件查询
export const modelFindList = (param) => get(sysServerUrl + "car/train", param);
// 车型管理
// 车型管理-内容数据
export const modelData = (params) => get(sysServerUrl + 'sys/car/model/findTree', params);
// 车型管理-车型数据
export const modelTrainList = (params) => get(sysServerUrl + 'sys/car/train/list', params);
// 车型管理-增加
export const modelAdd = (params) => post(sysServerUrl + 'sys/car/model/add', params);
// 车型管理-编辑
export const modelEdit = (params) => post(sysServerUrl + 'sys/car/model/edit', params);
// 车型管理-删除
export const modelDel = (params) => post(sysServerUrl + 'sys/car/model/del/' + params);
// 获取完整的结构树
export const trainTree = () => get(sysServerUrl + "sys/car/train/completeTree");


export const modelImport = (params) => post(sysServerUrl + 'sys/car/model/batchImport', params); // 车型管理-内容数据

export const modelYearDel = (params) => post(sysServerUrl + 'sys/car/model/delYear', params);
export const modelConfigData = (params) => get(sysServerUrl + 'sys/car/model/cfglist' + params); // 车型管理-车型配置
export const configBatchDel = (params) => get(sysServerUrl + 'sys/car/model/deleteAllCfg' + params); //车型管理-删除全部配置代码
export const modelConfigDel = (params) => get(sysServerUrl + 'sys/car/model/deleteConfig' + params); //车型管理-删除当前行配置代码
export const configSave = (params) => post(sysServerUrl + 'sys/car/model/saveConfig', params); //车型管理-车型配置保存
export const modelTemplate = (params) => down(sysServerUrl + 'static/excel/车型导入模板.xlsx' + params); // 车型管理-车型模板下载
export const configTemplate = (params) => down(sysServerUrl + 'static/excel/车型配置表.xlsx' + params); // 车型管理-配置模板下载
export const editUseFlag = (params) => post(sysServerUrl + 'sys/car/model/editUseFlag', params);
export const modelEditYear = (params) => post(sysServerUrl + 'sys/car/model/editYear', params);  // 修改年款





//日志相关


export const requestLogList = (params) => post(sysServerUrl + 'sys/log/list', params);


export const requestLogModularList = (params) => get(sysServerUrl + 'sys/log/getLogModularList', params);

export const requestLogStackInfo = (params) => get(sysServerUrl + 'sys/log/getLogStack', params);



// ========= 区域管理 ====== //
// 获取结构树
export const regionData = (params) => get(sysServerUrl + 'sys/region/api/findTree', params);
// 获取类型
export const regionType = (params) => get(sysServerUrl + 'sys/region/api/typeList', params);
// 添加
export const regionAdd = (params) => get(sysServerUrl + 'sys/region/api/add', params);
// 修改
export const regionEdit = (params) => get(sysServerUrl + 'sys/region/api/edit', params);
// 删除
export const regionDel = (params) => del(sysServerUrl + 'sys/region/physical/' + params);
// 导入
export const regionImportInfo = (params) => post(sysServerUrl + 'sys/region/api/batchImport', params);
// 明细模板下载
export const regionTemplate = (params) => down(sysServerUrl + 'static/excel/' + params);



export const appAccountData = (params) => post(sysServerUrl + 'sys/user/getAppAccountList', params);  // 用户管理-应用账号列表

//应用管理
export const appBasicData = (params) => get(sysServerUrl + 'sys/app/basic/list', params); //应用管理-列表
export const appBasicAdd = (params) => post(sysServerUrl + 'sys/app/basic/add', params); //应用管理-增加
export const appBasicEdit = (params) => post(sysServerUrl + 'sys/app/basic/edit', params);//应用管理-编辑
export const appBasicDel = (params) => get(sysServerUrl + 'sys/app/basic/del/' + params);//应用管理-删除

//应用版本管理
export const appVersionData = (params) => get(sysServerUrl + 'sys/app/version/list', params); //应用版本管理-列表
export const appVersionQueryAll = (params) => get(sysServerUrl + 'sys/app/version/list', params); //应用版本管理-全部列表
export const appVersionInstaller = (params) => get(sysServerUrl + 'sys/app/version/getInstallerList', params); //应用版本管理-全部列表
export const appVersionAdd = (params) => post(sysServerUrl + 'sys/app/version/add', params); //应用版本管理-增加
export const appVersionEdit = (params) => post(sysServerUrl + 'sys/app/version/edit', params);//应用版本管理-编辑
export const appVersionDel = (params) => get(sysServerUrl + 'sys/app/version/del/' + params);//应用版本管理-删除
export const appVersionUpload = (params) => get(sysServerUrl + 'sys/app/version/finishUpload?fileId=' + params);//应用版本管理-完成上传
export const appVersionDelFiile = (params) => post(sysServerUrl + 'sys/app/version/delUploadFile', params);//应用版本管理-删除上传文件
export const appVersionRelease = (params) => post(sysServerUrl + 'sys/app/version/release', params);

export const appVersionDescData = (params) => post(sysServerUrl + 'sys/app/version/detail/list/all', params); //应用版本描述管理-列表
export const appVersionDescAdd = (params) => post(sysServerUrl + 'sys/app/version/detail/add', params); //应用版本描述管理-增加
export const appVersionDescEdit = (params) => post(sysServerUrl + 'sys/app/version/detail/edit', params);//应用版本描述管理-编辑
export const appVersionDescDel = (params) => get(sysServerUrl + 'sys/app/version/detail/del/' + params);//应用版本描述管理-删除
export const appVersionDescSort = (params) => post(sysServerUrl + 'sys/app/version/detail/sort' , params);//应用版本描述管理-排序


// ========== 同步
export const syncUserSendApi = (params) => post(sysServerUrl + 'sync/user/send' , params);//应用版本描述管理-排序



//查询缓存详情
export const getCache = (params) => get(sysServerUrl + 'sys/cache/detail/' + params);

//查询缓存名称列表
export const listCacheName = ()=> get(sysServerUrl + 'sys/cache/getNames');

// 查询缓存键名列表
export const listCacheKey = (cacheName) => get(sysServerUrl + 'sys/cache/getKeys/' + cacheName);

// 查询缓存内容
export const getCacheValue = (cacheName, cacheKey) => get(sysServerUrl + 'sys/cache/getValue/' + cacheName + '/' + cacheKey);

// 清理指定名称缓存
export const clearCacheName = (cacheName) => get(sysServerUrl + 'sys/cache/clearCacheName/' + cacheName);

// 清理指定键名缓存
export const clearCacheKey = (cacheKey) => get(sysServerUrl + 'sys/cache/clearCacheKey/' + cacheKey);

// 清理全部缓存
export const clearCacheAll = () => get(sysServerUrl + 'sys/cache/clearCacheAll');

// 获取缓存监控数据
export const getCacheMonitor = () => get(sysServerUrl + 'sys/cache/getCacheMonitor');


// ============================================= 新 ==============================================
// ============================================= 新 ==============================================

// 查询用户列表
export const findUserPage = (url, params) => post(sysServerUrl + 'sys/user/search/' + url, params);
// 平台管理-用户列表
export const findUserPageTenant = (url, params) => post(sysServerUrl + 'sys/user/searchByTenant/' + url, params);

// 查询角色
export const findRoleAll = (params) => get(sysServerUrl + 'sys/role/all', params);
// 添加用户
export const addUser = (params) => post(sysServerUrl + 'sys/user', params);
// 授权用户
export const accreditUser = (params) => post(sysServerUrl + 'sys/user/accredit', params);
// 添加用户
export const editUser = (id, params) => put(sysServerUrl + 'sys/user/' + id, params);

// 查询部门
export const findDeptAll = () => get(sysServerUrl + 'sys/dept/tree');
// 查询部门类型
export const findDeptType = () => get(sysServerUrl + 'sys/dept/type');
// 添加部门节点
export const addDeptApi = (params) => post(sysServerUrl + 'sys/dept', params);
// 编辑部门节点
export const editDeptApi = (id, params) => put(sysServerUrl + 'sys/dept/' + id, params);

// 查询区域
export const findRegionAll = () => get(sysServerUrl + 'sys/region/tree');
// 查询区域类型
export const findRegionType = () => get(sysServerUrl + 'sys/region/type');
// 添加部门节点
export const addRegionApi = (params) => post(sysServerUrl + 'sys/region', params);
// 编辑部门节点
export const editRegionApi = (id, params) => put(sysServerUrl + 'sys/region/' + id, params);



// 车型
export const findCarAll = () => get(sysServerUrl + 'car/tree');
// 添加车型节点
export const addCarApi = (params) => post(sysServerUrl + 'car', params);
// 编辑车型节点
export const editCarApi = (id, params) => put(sysServerUrl + 'car/' + id, params);
// 删除节点
export const delCarApi = (id) => del(sysServerUrl + 'car/' + id);
// 车型类型
export const findCarType = () => get(sysServerUrl + 'car/type');
// excel批量导入
export const carBatchImport = (params) => post(sysServerUrl + 'car/batch/import', params);
export const trainCarTypeList = (params) => get(sysServerUrl + 'car/energy/type', params);

/**
 * 在线用户相关
 * @param page
 * @param size
 * @return {AxiosPromise}
 */
export const requestOnLineUserList = (page,size) => get(sysServerUrl + `onlineUser/list/${page}/${size}`);//获取在线用户列表
export const requestKicKOutOnLineUser = (params) => get(sysServerUrl + 'onlineUser/kickOut',params);//踢掉在线用户


// ======== 租户管理 ======
// 分页查询
export const tenantFindPage = (page, params) => post(sysServerUrl + 'sys/tenant/search/' + page, params);
// 添加
export const tenantAddApi = (params) => post(sysServerUrl + 'sys/tenant', params);
// 修改
export const tenantEditApi = (id, params) => put(sysServerUrl + 'sys/tenant/'+id, params);
export const requestTenantDetail = (id) => get(sysServerUrl + 'sys/tenant/detail/'+id);
// 删除
export const tenantDelApi = (id) => del(sysServerUrl + 'sys/tenant/' + id);
// 获取租户下的所有用户
export const tenantAllUserApi = (id) => get(sysServerUrl + 'sys/user/find/' + id);
export const tenantAdministratorsApi = (id) => get(sysServerUrl + 'sys/user/administrators/' + id);
// 获取租户下的部门
export const tenantDeptApi = (id) => get(sysServerUrl + 'sys/dept/tree/' + id);



/**
 * 以下接口为懒加载
 * @param params
 * @return {AxiosPromise}
 */
export const requestSysApiGroupList = (params) => get(sysServerUrl + 'sysApi/listGroup' , params);//获取api的组列表
export const requestSysGroupApiList = (params) => get(sysServerUrl + 'sysApi/listGroupApi' , params);//获取组下的api列表
/**
 * 获取api/组 详情
 * @param params
 * @return {AxiosPromise}
 */
export const requestApiDetail = (params) => get(sysServerUrl + 'sysApi/detail' , params);//获取单个api/api组 详情


export const requestPermissionList = (params) => get(sysServerUrl + 'sysApi/listCanGrantPermission' , params);//获取所有权限列表
export const requestUpdateSysPermissionList = (params) => post(sysServerUrl + 'sysApi/grantPermission' , params);//更新系统api权限列表

export const requestUpdateSysApiInfo = (params) => post(sysServerUrl + 'sysApi/edit' , params);//更新系统api信息



