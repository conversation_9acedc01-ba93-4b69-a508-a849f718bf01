import { post,  get,down } from "../plugins/request";
import { sysServerUrl, cmsServerUrl } from '../assets/js/common'


//智能问答
export const chatSubscribe = (params) => get(sysServerUrl + 'qas/chat/subscribe/' + params); // 问答-订阅
export const chatUnSubscribe= (params) => get(sysServerUrl + 'qas/chat/unsubscribe/' + params); // 问答-取消订阅
export const chatSend = (params) => post(sysServerUrl + 'qas/chat/send', params); // 问答-发送

export const chatSubscribeUrl = sysServerUrl + 'qas/chat/subscribe' // 问答-订阅


