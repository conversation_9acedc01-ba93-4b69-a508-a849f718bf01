import {cmsServerUrl, sysServerUrl} from "@/assets/js/common";
import {del, get, post, put} from "@/plugins/request";

//销售管理

//分页查询销售订单
export const salesList = (params) => get(sysServerUrl + 'sales/order/list', params);

export const shipDetailList = (params) => get(sysServerUrl + 'sales/order/shipDetailList', params);

export const getSalesOrderInfo = (param) => get(sysServerUrl + 'sales/order/info/'+ param); // 详情信息
export const getSalesOrderAdd = (param) => post(sysServerUrl + 'sales/order/add', param); // 新增 Product 数据
export const getSalesOrderEdit = (param) => put(sysServerUrl + 'sales/order/edit', param); // 修改 Product 数据
export const getSalesOrderDel = (param) => del(sysServerUrl + 'sales/order/' + param); // 根据ID删除
export const getSalesOrderAudit = (param) => post(sysServerUrl + 'sales/order/audit' + param); // 审核
export const salesOrderDel = (params) => post(sysServerUrl + 'sales/order/del', params); // 批量删除
export const salesOrderDiscard = (params) => post(sysServerUrl + 'sales/order/discard', params); // 批量作废
export const salesOrderPass = (params) => post(sysServerUrl + 'sales/order/auditPass', params); // 批量审核通过
export const salesOrderReject = (params) => post(sysServerUrl + 'sales/order/auditReject', params); // 批量驳回
//根据销售订单号查询销售订单id
export const getSalesOrderId = (params) => get(sysServerUrl + 'sales/order/getIdBySalesNo', params);
