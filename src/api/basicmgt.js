import {post, get, down, del, download, put, postWithSearchParams} from "../plugins/request";
import { sysServerUrl } from '../assets/js/common'

// 商品信息
export const getProductList = (param) => get(sysServerUrl + 'bas/product/list', param); // 分页查询列表
export const listProductByIds = (param) => postWithSearchParams(sysServerUrl + 'bas/product/listByIds', param); // 分页查询列表
export const getProductInfo = (param) => get(sysServerUrl + 'bas/product/info/'+ param); // 详情信息
export const getProductAdd = (param) => post(sysServerUrl + 'bas/product/add', param); // 新增 Product 数据
export const getProductEdit = (param) => put(sysServerUrl + 'bas/product/edit', param); // 修改 Product 数据
export const getProductDel = (param) => del(sysServerUrl + 'bas/product/' + param); // 根据ID删除
export const getSupplierData = (id, param) => get(sysServerUrl + 'bas/product/getSupplier/' + id, param); // 查询供应商联系人
export const getHistPriceList = (id, param) => get(sysServerUrl + 'bas/product/getHistPrice/'+id, param); // 查询供应商历史价格

// 商品分类
export const getProductcategoryTree = (param) => post(sysServerUrl + 'bas/productcategory/tree', param); // 商品类别树结构
export const getProductcategoryList = (param) => post(sysServerUrl + 'bas/productcategory/list', param); // 商品类别树结构
export const getProductcategoryInfo = (param) => get(sysServerUrl + 'bas/productcategory/info/' + param); // 商品类别详情信息
export const getProductcategoryAdd = (param) => post(sysServerUrl + 'bas/productcategory/add', param); // 新增 商品分类 数据
export const getProductcategoryEdit = (param) => put(sysServerUrl + 'bas/productcategory/edit', param); // 修改 商品分类 数据
export const getProductcategoryDel = (param) => del(sysServerUrl + 'bas/productcategory/' + param); // 商品类别详情信息

// =========== 商品品牌管理 ===========
export const getCategoryTree = (params) => post(sysServerUrl + 'bas/category/tree', params); // 通用分类树

export const getBrandList = (params) => get(sysServerUrl + 'bas/brand/list', params); // 商品品牌列表

export const updateCategory = (params) => put(sysServerUrl + 'bas/category/edit', params); // 通用分类修改

export const editBrand = (params) => put(sysServerUrl + 'bas/brand/edit', params); // 商品品牌修改

export const addCategory = (params) => post(sysServerUrl + 'bas/category/add', params); // 通用分类新增

export const addBrand = (params) => post(sysServerUrl + 'bas/brand/add', params); // 商品品牌新增

export const deleteCategory = (params) => post(sysServerUrl + 'bas/category/del' , params); // 通用分类删除

export const deleteBrand = (id) => del(sysServerUrl + 'bas/brand/' + id); // 商品品牌删除

// =========== 仓库管理 ===========
export const getWarehouseList = (params) => get(sysServerUrl + 'bas/warehouse/list', params); // 仓库管理列表

export const addWarehouse = (params) => post(sysServerUrl + 'bas/warehouse/add', params); // 仓库管理新增

export const editWarehouse = (params) => put(sysServerUrl + 'bas/warehouse/edit', params); // 仓库管理修改

export const deleteWarehouse = (id) => del(sysServerUrl + 'bas/warehouse/' + id); // 仓库管理删除

export const warehouseInfo = (id) => get(sysServerUrl + 'bas/warehouse/info/' + id); // 仓库详情


// =========== 供应商管理 ===========
export const getSupplierList = (params) => get(sysServerUrl + 'bas/supplier/list', params); // 供应商列表

export const getSupplierInfo = (params) =>get(sysServerUrl + 'bas/supplier/info/' + params); // 供应商 详情信息

export const addSupplier = (params) => post(sysServerUrl + 'bas/supplier/add', params); // 新增 供应商 数据

export const editSupplier = (params) => put(sysServerUrl + 'bas/supplier/edit', params); // 修改 Supplier 数据

export const deleteSupplier = (id) => del(sysServerUrl + 'bas/supplier/' + id); // 根据ID删除 供应商

export const getSupplierByProductId = (productId) => get(sysServerUrl + 'bas/supplier/getSupplier/' + productId)


//销售公司管理
export const getSalesCompanyList = (params) => get(sysServerUrl + 'bas/salesCompany/list', params);

export const addSalesCompany = (params) => post(sysServerUrl + 'bas/salesCompany/add', params);

export const editSalesCompany = (params) => put(sysServerUrl + 'bas/salesCompany/edit', params);

export const deleteSalesCompany = (id) => del(sysServerUrl + 'bas/salesCompany/' + id);

//单位管理
export const getUnitList = (params) => get(sysServerUrl + 'bas/unit/list', params);

export const addUnit = (params) => post(sysServerUrl + 'bas/unit/add', params);

export const editUnit = (params) => put(sysServerUrl + 'bas/unit/edit', params);

export const deleteUnit = (id) => del(sysServerUrl + 'bas/unit/' + id);

//物流管理
export const getLogisticsList = (params) => get(sysServerUrl + 'bas/logistics/list', params);

//获取所有的物流公司列表
export const getLogisticsAllList = (params) => get(sysServerUrl + 'bas/logistics/getLogisticsCompanyList', params);

export const addLogistics = (params) => post(sysServerUrl + 'bas/logistics/add', params);

export const editLogistics = (params) => put(sysServerUrl + 'bas/logistics/edit', params);

export const deleteLogistics = (id) => del(sysServerUrl + 'bas/logistics/' + id);

export const cloneLogistics = (params) => post(sysServerUrl + 'bas/logistics/clone', params);


// =========== 客户管理 ===========
export const getCustomerList = (params) => get(sysServerUrl + 'bas/customer/list', params); // 客户管理列表

export const addCustomer = (params) => post(sysServerUrl + 'bas/customer/add', params); // 客户管理新增

export const editCustomer = (params) => put(sysServerUrl + 'bas/customer/edit', params); // 客户管理修改

export const deleteCustomer = (id) => del(sysServerUrl + 'bas/customer/' + id); // 客户管理删除

export const customerInfo = (id) => get(sysServerUrl + 'bas/customer/info/' + id); // 客户详情

//根据客户或者供应商id查询相关联系人信息
export const getContactsBySupplierId = (supplierId) => get(sysServerUrl + 'bas/customer/getContacts/' + supplierId)

export const cusProductInfo = (params) => post(sysServerUrl + 'bas/customer/productInfo', params); // 客户产品查询

//=========== 发货信息 ===========
export const getShippInfo = (params) => post(sysServerUrl + 'bas/shippinfo/list', params);
export const addShippInfo = (params) => post(sysServerUrl + 'bas/shippinfo/add', params);
export const editShippInfo = (params) => post(sysServerUrl + 'bas/shippinfo/edit', params);
export const deleteShippInfo = (id) => get(sysServerUrl + 'bas/shippinfo/' + id);

