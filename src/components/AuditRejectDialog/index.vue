<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="800px !important"
    @close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item :label="documentLabel">
        <el-input v-model="form.documentNo" disabled></el-input>
      </el-form-item>

      <el-form-item label="驳回原因" prop="rejectReason" required>
        <el-input
          type="textarea"
          v-model="form.rejectReason"
          placeholder="请输入驳回原因"
          :rows="4"
        />
      </el-form-item>

      <el-form-item label="附件">
        <el-upload
          class="upload-demo"
          drag
          action=""
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :file-list="fileList"
          :auto-upload="false"
          :limit="5"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :http-request="handleUpload"
          accept=".jpg,.png,.xlsx,.docx,.zip"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            拖拽附件至此或 Ctrl+V 粘贴上传<br/>
            <el-link type="primary">点击上传</el-link>
          </div>
          <div class="el-upload__tip" slot="tip">
            支持上传 jpg, png, xlsx, docx, zip 格式的文件，且文件数不超过 5 个，单个文件大小不超过 100M
          </div>
        </el-upload>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit" :loading="loading">提交</el-button>
      <el-button plain @click="handleCancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { importAttach } from '@/api/sysmgt';

export default {
  name: "AuditRejectDialog",
  props: {
    /**
     * 对话框标题
     */
    title: {
      type: String,
      default: '审核驳回'
    },
    /**
     * 是否显示对话框
     */
    visible: {
      type: Boolean,
      default: false
    },
    /**
     * 单据号标签
     */
    documentLabel: {
      type: String,
      default: '单据号'
    },
    /**
     * 单据号
     */
    documentNo: {
      type: String,
      default: ''
    },
    /**
     * 单据ID
     */
    documentId: {
      type: [String, Number],
      default: ''
    },
    /**
     * 文件标识
     */
    fileFlag: {
      type: String,
      default: 'document'
    },
    /**
     * 驳回API方法
     */
    rejectApi: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      rules: {
        rejectReason: [
          {required: true, message: '请输入驳回原因', trigger: 'blur'}
        ]
      },
      form: {
        rejectReason: ''
      },
      fileList: [],
      uploadFileList: [], // 新上传的文件列表
      fileImage: [], // 上传进度控制
      fileNum: 0, // 上传计数
      isFlag: true // 上传状态标识
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm();
      }
    },
    documentNo(newVal) {
      this.form.documentNo = newVal;
    }
  },
  methods: {
    /**
     * 初始化表单
     */
    initForm() {
      this.form.documentNo = this.documentNo;
      this.form.rejectReason = '';
      this.fileList = [];
      this.uploadFileList = [];
      this.fileImage = [];
      this.fileNum = 0;
      this.isFlag = true;
      this.loading = false;
      
      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },

    /**
     * 文件上传前的校验
     */
    beforeUpload(file) {
      const fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase();
      const isLimit = file.size / 1024 / 1024 < 100; // 100MB限制
      const suffix = [
        "jpg", "png", "xlsx", "docx", "zip"
      ];

      if (!suffix.includes(fileName)) {
        this.$DonMessage.warning(`文件格式不支持，请上传 ${suffix.join(', ')} 格式的文件`);
        return false;
      }

      if (!isLimit) {
        this.$DonMessage.warning('文件大小不能超过 100MB');
        return false;
      }

      return true;
    },

    /**
     * 自定义上传方法
     */
    handleUpload(param) {
      this.fileImage.push(param);
      this.isFlag = true;

      const formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', this.fileFlag);

      importAttach(formData).then(res => {
        if (res.data.code === 100) {
          const obj = {
            fileName: res.data.data.fileName,
            name: res.data.data.fileName,
            path: res.data.data.fileUrl,
            uid: param.file.uid,
            url: res.data.data.fileUrl // 用于预览
          };
          this.uploadFileList.push(obj);
          this.fileNum += 1;
          this.$DonMessage.success(`${param.file.name} 上传成功`);
        } else {
          this.fileNum += 1;
          this.$DonMessage.error(`${param.file.name} 上传失败: ${res.data.msg}`);
        }

        // 检查是否所有文件都已处理完成
        if (this.fileImage.length === this.fileNum) {
          this.fileImage = [];
          this.fileNum = 0;
        }

        this.isFlag = true;
      }).catch(error => {
        this.fileNum += 1;
        this.$DonMessage.error(`${param.file.name} 上传失败`);
        console.error('Upload error:', error);

        if (this.fileImage.length === this.fileNum) {
          this.fileImage = [];
          this.fileNum = 0;
        }
      });
    },

    /**
     * 文件移除
     */
    handleRemove(file, fileList) {
      // 从上传文件列表中移除
      for (let i = 0; i < this.uploadFileList.length; i++) {
        const obj = this.uploadFileList[i];
        if (obj.uid === file.uid) {
          this.uploadFileList.splice(i, 1);
          break;
        }
      }

      // 更新组件的文件列表
      this.fileList = fileList;

      if (fileList.length <= 0) {
        this.fileList = [];
        this.uploadFileList = [];
      }
    },

    /**
     * 文件数量超出限制
     */
    handleExceed() {
      this.$DonMessage.warning('最多只能上传 5 个文件');
    },

    /**
     * 文件预览
     */
    handlePreview(file) {
      if (file.url) {
        window.open(file.url, '_blank');
      } else if (file.path) {
        // 如果有文件路径，构建完整的预览URL
        const previewUrl = `${this.$filePath || ''}${file.path}`;
        window.open(previewUrl, '_blank');
      } else {
        this.$DonMessage.warning('无法预览该文件');
      }
    },

    /**
     * 提交驳回
     */
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true;
          
          // 调用父组件传入的驳回API
          this.rejectApi(this.documentId, this.form.rejectReason, this.uploadFileList)
            .then(res => {
              if (res.data.code === 100) {
                this.$DonMessage.success("驳回成功");
                this.$emit('success');
                this.handleClose();
              } else {
                this.$DonMessage.error(res.data.msg);
              }
            })
            .catch(error => {
              this.$DonMessage.error('驳回失败：' + error.message);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    /**
     * 取消操作
     */
    handleCancel() {
      this.handleClose();
    },

    /**
     * 关闭对话框
     */
    handleClose() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
}
</script>

<style scoped>
.upload-demo {
  width: 100%;
}
</style> 