<template>
  <div :class="{'hidden':hidden}" class="pagination-container">
    <div class="pagDetail">
      <el-pagination
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="layout"
        :page-sizes="pageSizes"
        :pager-count="pagerCount"
        :total="total"
        v-bind="$attrs"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: 'Pagination',
  props: {
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 40, 50, 100]
      }
    },
    // 移动端页面按钮的数量端默认值5
    pagerCount: {
      type: Number,
      default: document.body.clientWidth < 992 ? 5 : 7,
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      }
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      if (this.currentPage * val > this.total) {
        this.currentPage = 1;
      }
      this.$emit('pagination', { page: this.currentPage, limit: val });
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize });
    },
    pageWidth() {
      setTimeout(() => {
        var length = $(".pagination-container").parent().length
        for(var i = 0; i < length ; i++) {
          let allWidth =$(".pagination-container").parent().eq(i).width();
          $(".pagination-container").eq(i).css("width", allWidth);
        }
        window.addEventListener("resize", function () {
          var length = $(".pagination-container").parent().length
          for(var i = 0; i < length ; i++) {
            let allWidth = $(".pagination-container").parent().eq(i).width();
            $(".pagination-container").eq(i).css("width", allWidth);
          }
        })
      }, 80)
    },
  },
  mounted() {
    var _this = this;
    _this.pageWidth();
  },
}
</script>
<style>
.pagination-container {
  padding: 8px 5px;
  /* display: flex; */
  position: fixed;
  right: 24px;
  bottom: 24px;
  /* border: 1px solid var(--table-border); */
  /* border-top: none; */
  color: #616169;
  box-sizing: border-box;
}
.el-dialog .pagination-container {
  position: relative;
  bottom: 0;
  right: 0;
}
.pagination-container .pagDetail {
  width: 100%;
  height: 100%;
}
.pagDetail .el-pagination button.el-icon {
  font-size: 13px;
}
.el-pagination {
  padding: 0px !important;
  display: flex;
  align-items: center;
  justify-content: end;
  font-weight: normal !important;
}
.el-pagination button {
  padding: 0 !important;
}
.pagination-container button:disabled,
.pagination-container button:disabled:hover,
.pagination-container button:disabled:focus {
  color: var(--disabled-color) !important;
  background: var(--disabled-bgColor) !important;
  border: 1px solid var(--disabled-border) !important;
  cursor: no-drop;
}
.pagination-container .el-pager li:hover,
.pagination-container button:hover {
  color: var(--theme-color) !important;
  border: 1px solid var(--theme-color) !important;
}
.el-pagination .el-pager li,
.el-pagination button,
.el-pagination span:not([class*='suffix']),
.el-pagination .el-pagination__sizes .el-input__inner,
.el-pagination__editor.el-input .el-input__inner {
  font-size: 14px;
  min-width: 28px !important;
  height: 28px !important;
  line-height: 28px !important;
  border-radius: var(--border-radius);
  color: #616169 !important;
  background-color: #fff !important;
  box-sizing: border-box;
  border: 1px solid var(--border-color) !important;
}
.pagDetail .el-pager li {
  line-height: 27px !important;
}
.el-pagination span:not([class*='suffix']) {
  border: none !important;
}
.el-pagination .el-pagination__sizes .el-input__icon {
  line-height: 28px !important;
}
.el-pagination__total,
.el-pagination__sizes .el-input__inner,
.el-pagination__jump .el-input__inner {
  border: 1px solid var(--border-color) !important;
  color: #616169 !important;
}
.el-dialog .el-pagination .el-select,
.el-pagination .el-select .el-input,
.el-main .el-pagination .el-select .el-input {
  width: 80px !important;
  margin: 0;
}
.pagination-container .el-select .el-input__suffix .el-input__icon.el-input__validateIcon.el-icon-circle-close {
  display: none;
} 
.el-pagination .el-pagination__sizes .el-input__inner,
.el-main .el-pagination .el-pagination__sizes .el-input__inner {
  width: 80px !important;
  font-size: 12px !important;
  text-align: left;
  padding: 0 16px 0 5px !important;
}
.el-pagination__jump {
  margin-left: 10px !important;
  font-size: 12px;
}
.el-dialog .el-pagination .el-pagination__jump .el-input,
.el-dialog .el-pagination .el-pagination__jump .el-input .el-input__inner,
.el-pagination .el-pagination__jump .el-input__inner {
  width: 50px !important;
}
.el-pagination__editor.el-input {
  margin: 0 8px !important;
}
.el-pagination__jump .el-input__inner {
  padding: 0 4px !important;
}
.pagDetail .el-pager li.btn-quicknext,
.pagDetail .el-pager li.btn-quickprev,
.pagDetail .el-pager .more::before {
  line-height: 27px;
}
.pagination-container .el-button,
.pagination-container button,
.pagination-container .el-pager li {
  margin: 0 5px;
}
.pagination-container .el-pager li.active,
.pagination-container .el-pager li.active:hover {
  color: #fff !important;
  border: 1px solid var(--theme-color) !important;
  background-color: var(--theme-color) !important;
}
@media screen and (max-width: 1600px) and (min-width: 1440px) {
  .pagination-container {
    padding: 8px 5px;
  }
  .pagDetail .el-pager li,
  .el-pagination button,
  .el-pagination
    span:not([class*='suffix'])
    .el-pagination
    .el-pagination__sizes
    .el-input__inner,
  .el-pagination__editor.el-input .el-input__inner {
    min-width: 26px !important;
    height: 26px !important;
    line-height: 26px !important;
  }
  .pagDetail .el-pager li {
    line-height: 25px !important;
  }
  .el-pagination .el-pagination__sizes .el-input__icon {
    line-height: 26px !important;
  }
  .pagDetail .el-pager li.btn-quicknext,
  .pagDetail .el-pager li.btn-quickprev,
  .pagDetail .el-pager .more::before {
    line-height: 25px;
  }
}
@media screen and (max-width: 1440px) and (min-width: 1366px) {
  .pagination-container {
    padding: 6px 5px;
  }
  .pagDetail .el-pager li,
  .el-pagination button,
  .el-pagination
    span:not([class*='suffix'])
    .el-pagination
    .el-pagination__sizes
    .el-input__inner,
  .el-pagination__editor.el-input .el-input__inner {
    font-size: 13px;
    min-width: 25px !important;
    height: 25px !important;
    line-height: 25px !important;
  }
  .pagDetail .el-pager li {
    line-height: 24px !important;
  }
  .el-pagination .el-pagination__sizes .el-input__icon {
    line-height: 25px !important;
  }
  .pagDetail .el-pager li.btn-quicknext,
  .pagDetail .el-pager li.btn-quickprev,
  .pagDetail .el-pager .more::before {
    line-height: 24px;
  }
}
@media screen and (max-width: 1366px) and (min-width: 1280px) {
  .pagination-container {
    padding: 5px;
  }
  .pagDetail .el-pager li,
  .el-pagination button,
  .el-pagination span:not([class*='suffix']),
  .el-pagination .el-pagination__sizes .el-input__inner,
  .el-pagination__editor.el-input .el-input__inner {
    font-size: 12px;
    min-width: 22px !important;
    height: 22px !important;
    line-height: 22px !important;
  }
  .pagDetail .el-pager li {
    line-height: 21px !important;
  }
  .el-pagination .el-pagination__sizes .el-input__icon {
    line-height: 22px !important;
  }
  .pagDetail .el-pager li.btn-quicknext,
  .pagDetail .el-pager li.btn-quickprev,
  .pagDetail .el-pager .more::before {
    line-height: 22px;
  }
}
@media screen and (max-width: 1280px) and (min-width: 1024px) {
  .pagination-container {
    padding: 5px;
  }
  .pagDetail .el-pager li,
  .el-pagination button,
  .el-pagination span:not([class*='suffix']),
  .el-pagination .el-pagination__sizes .el-input__inner,
  .el-pagination__editor.el-input .el-input__inner {
    font-size: 12px;
    min-width: 22px !important;
    height: 22px !important;
    line-height: 22px !important;
  }
  .pagDetail .el-pager li {
    line-height: 21px !important;
  }
  .el-pagination .el-pagination__sizes .el-input__icon {
    line-height: 22px !important;
  }
  .pagDetail .el-pager li.btn-quicknext,
  .pagDetail .el-pager li.btn-quickprev,
  .pagDetail .el-pager .more::before {
    line-height: 21px;
  }
}
@media screen and (max-width: 1024px) {
  .pagination-container {
    padding: 5px;
  }
  .pagDetail .el-pager li,
  .el-pagination button,
  .el-pagination span:not([class*='suffix']),
  .el-pagination .el-pagination__sizes .el-input__inner,
  .el-pagination__editor.el-input .el-input__inner {
    font-size: 12px;
    min-width: 20px !important;
    height: 20px !important;
    line-height: 20px !important;
  }
  .pagDetail .el-pager li {
    line-height: 19px !important;
  }
  .el-pagination .el-pagination__sizes .el-input__icon {
    line-height: 20px !important;
  }
  .pagDetail .el-pager li.btn-quicknext,
  .pagDetail .el-pager li.btn-quickprev,
  .pagDetail .el-pager .more::before {
    line-height: 20px;
  }
}
</style>
