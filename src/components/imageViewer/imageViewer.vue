<template>
  <transition name="viewer-fade">
    <div
      ref="el-image-viewer__wrapper"
      tabindex="-1"
      class="el-image-viewer__wrapper"
      :style="{ 'z-index': viewerZIndex }"
    >
      <div class="el-image-viewer__mask" @click.self="handleMaskClick"></div>
      <!-- CLOSE -->
      <span class="el-image-viewer__btn el-image-viewer__close" @click="hide">
        <i class="el-icon-close"></i>
      </span>
      <!-- ARROW -->
      <template v-if="!isSingle">
        <span
          class="el-image-viewer__btn el-image-viewer__prev"
          :class="{ 'is-disabled': !infinite && isFirst }"
          @click="prev"
        >
          <i class="el-icon-arrow-left" />
        </span>
        <span
          class="el-image-viewer__btn el-image-viewer__next"
          :class="{ 'is-disabled': !infinite && isLast }"
          @click="next"
        >
          <i class="el-icon-arrow-right" />
        </span>
      </template>
      <!-- ACTIONS -->
      <div class="el-image-viewer__btn el-image-viewer__actions">
        <div class="el-image-viewer__actions__inner">
          <i class="el-icon-zoom-out" @click="handleActions('zoomOut')"></i>
          <i class="el-icon-zoom-in" @click="handleActions('zoomIn')"></i>
          <i class="el-image-viewer__actions__divider"></i>
          <i :class="mode.icon" @click="toggleMode"></i>
          <i class="el-image-viewer__actions__divider"></i>
          <i
            class="el-icon-refresh-left"
            @click="handleActions('anticlocelise')"
          ></i>
          <i
            class="el-icon-refresh-right"
            @click="handleActions('clocelise')"
          ></i>
        </div>
      </div>
      <!-- CANVAS -->
      <div class="el-image-viewer__canvas">
        <template v-for="(url, i) in urlList">
          <img
            v-if="i === index"
            ref="img"
            :key="url"
            class="el-image-viewer__img"
            :src="currentImg"
            :style="imgStyle"
            @load="handleImgLoad"
            @error="handleImgError"
            @mousedown="handleMouseDown"
          />
        </template>
      </div>
    </div>
  </transition>
</template>

<script>
import { on, off } from 'element-ui/src/utils/dom';
import { rafThrottle, isFirefox } from 'element-ui/src/utils/util';
import { PopupManager } from 'element-ui/src/utils/popup';
const Mode = {
  // CONTAIN: {
  //   name: "contain",
  //   icon: "el-icon-full-screen",
  // },
  ORIGINAL: {
    name: 'original',
    icon: 'el-icon-c-scale-to-original',
  },
};

const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';

export default {
  name: 'ElImageViewer',

  props: {
    urlList: {
      type: Array,
      default: () => [],
    },
    zIndex: {
      type: Number,
      default: 2000,
    },
    onSwitch: {
      type: Function,
      default: () => {},
    },
    onClose: {
      type: Function,
      default: () => {},
    },
    initialIndex: {
      type: Number,
      default: 0,
    },
    appendToBody: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      index: this.initialIndex,
      isShow: false,
      infinite: true,
      loading: false,
      mode: Mode.ORIGINAL,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
    };
  },
  computed: {
    isSingle() {
      return this.urlList.length <= 1;
    },
    isFirst() {
      return this.index === 0;
    },
    isLast() {
      return this.index === this.urlList.length - 1;
    },
    currentImg() {
      var imgName = this.urlList[this.index];
      return imgName;
    },
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? 'transform .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`,
        'max-height': '100%',
        'max-width': '100%',
      };
      // if (this.mode === Mode.CONTAIN) {
      //   style.maxWidth = style.maxHeight = "100%";
      // }
      return style;
    },
    viewerZIndex() {
      const nextZIndex = PopupManager.nextZIndex();
      return this.zIndex > nextZIndex ? this.zIndex : nextZIndex;
    },
  },
  watch: {
    index: {
      handler: function (val) {
        this.reset();
        this.onSwitch(val);
      },
    },
    currentImg() {
      this.$nextTick(() => {
        const $img = this.$refs.img[0];
        if (!$img.complete) {
          this.loading = true;
        }
      });
    },
  },
  mounted() {
    this.deviceSupportInstall();
    if (this.appendToBody) {
      document.body.appendChild(this.$el);
    }
    // add tabindex then wrapper can be focusable via Javascript
    // focus wrapper so arrow key can't cause inner scroll behavior underneath
    this.$refs['el-image-viewer__wrapper'].focus();
  },
  destroyed() {
    // if appendToBody is true, remove DOM node after destroy
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },
  methods: {
    hide() {
      this.deviceSupportUninstall();
      this.onClose();
    },
    deviceSupportInstall() {
      this._keyDownHandler = (e) => {
        e.stopPropagation();
        const keyCode = e.keyCode;
        switch (keyCode) {
          // ESC
          case 27:
            this.hide();
            break;
          // SPACE
          case 32:
            this.toggleMode();
            break;
          // LEFT_ARROW
          case 37:
            this.prev();
            break;
          // UP_ARROW
          case 38:
            this.handleActions('zoomIn');
            break;
          // RIGHT_ARROW
          case 39:
            this.next();
            break;
          // DOWN_ARROW
          case 40:
            this.handleActions('zoomOut');
            break;
        }
      };
      this._mouseWheelHandler = rafThrottle((e) => {
        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
        if (delta > 0) {
          this.handleActions('zoomIn', {
            zoomRate: 0.3,
            enableTransition: false,
          });
        } else {
          this.handleActions('zoomOut', {
            zoomRate: 0.3,
            enableTransition: false,
          });
        }
      });
      on(document, 'keydown', this._keyDownHandler);
      on(document, mousewheelEventName, this._mouseWheelHandler);
    },
    deviceSupportUninstall() {
      off(document, 'keydown', this._keyDownHandler);
      off(document, mousewheelEventName, this._mouseWheelHandler);
      this._keyDownHandler = null;
      this._mouseWheelHandler = null;
    },
    handleImgLoad(e) {
      this.loading = false;
      const { transform } = this;
      var eleImg = document.querySelector('.el-image-viewer__img');
      var imgWidth = eleImg.offsetWidth;
      var imgHeight = eleImg.offsetHeight;
      var canvasWidth = $('.el-image-viewer__canvas').width();
      var canvasHeight = $('.el-image-viewer__canvas').height();
      if (canvasWidth > imgWidth * 2 && canvasHeight > imgHeight * 2) {
        if (canvasWidth > imgWidth * 3 && canvasHeight > imgHeight * 3) {
          if (canvasWidth > imgWidth * 4 && canvasHeight > imgHeight * 4) {
            transform.scale = 4;
          } else {
            transform.scale = 3;
          }
        } else {
          transform.scale = 2;
        }
      }
      setTimeout(() => {
        this.dropImage();
        this.zoomImage();
      }, 200);
    },
    handleImgError(e) {
      this.loading = false;
      e.target.alt = '加载失败';
    },
    handleMouseDown(e) {
      if (this.loading || e.button !== 0) return;
      const { offsetX, offsetY } = this.transform;
      const { transform } = this;
      const startX = e.pageX;
      const startY = e.pageY;
      let imgWidth = $('.el-image-viewer__img').width() * transform.scale;
      let canvasWidth = $('.el-image-viewer__canvas').width();
      let imgHeight = $('.el-image-viewer__img').height() * transform.scale;
      let canvasHeight = $('.el-image-viewer__canvas').height();
      this._dragHandler = rafThrottle((ev) => {
        var leftVal = canvasWidth + imgWidth - imgWidth * 0.3;
        var TopVal = canvasHeight + imgHeight - imgHeight * 0.3;
        var moveX = offsetX + ev.pageX - startX;
        var moveY = offsetY + ev.pageY - startY;
        if (Math.abs(moveX) < leftVal) {
          this.transform.offsetX = moveX;
        }
        if (Math.abs(moveY) < TopVal) {
          this.transform.offsetY = moveY;
        }
      });
      on(document, 'mousemove', this._dragHandler);
      on(document, 'mouseup', (ev) => {
        off(document, 'mousemove', this._dragHandler);
      });
      e.preventDefault();
    },
    dropImage() {
      const { offsetX, offsetY } = this.transform;
      const { transform } = this;
      let oBox = document.querySelector('.el-image-viewer__img');
      oBox.addEventListener(
        'touchstart',
        function (ev) {
          let imgWidth = $('.el-image-viewer__img').width() * transform.scale;
          let canvasWidth = $('.el-image-viewer__canvas').width();
          let imgHeight = $('.el-image-viewer__img').height() * transform.scale;
          let canvasHeight = $('.el-image-viewer__canvas').height();
          let startX = ev.targetTouches[0].clientX;
          let startY = ev.targetTouches[0].clientY;
          let disX = startX - Number(oBox.style.marginLeft.split('px')[0]);
          let disY = startY - Number(oBox.style.marginTop.split('px')[0]);
          function fnMove(ev) {
            var leftVal = canvasWidth + imgWidth - imgWidth * 0.3;
            var moveX = ev.targetTouches[0].clientX - disX;
            var TopVal = canvasHeight + imgHeight - imgHeight * 0.3;
            var moveY = ev.targetTouches[0].clientY - disY;
            if (Math.abs(moveX) < leftVal) {
              oBox.style.marginLeft = moveX + 'px';
            }
            if (Math.abs(moveY) < TopVal) {
              oBox.style.marginTop = moveY + 'px';
            }
          }
          function fnEnd() {
            oBox.removeEventListener('touchmove', fnMove, false);
            oBox.removeEventListener('touchend', fnEnd, false);
          }
          oBox.addEventListener('touchmove', fnMove, false);
          oBox.addEventListener('touchend', fnEnd, false);
        },
        false
      );
    },
    zoomImage() {
      const { transform } = this;
      var eleImg = document.querySelector('.el-image-viewer__img');
      var imgWidth = eleImg.offsetWidth;
      var imgHeight = eleImg.offsetHeight;
      var canvasWidth = $('.el-image-viewer__canvas').width();
      var canvasHeight = $('.el-image-viewer__canvas').height();
      if (canvasWidth > imgWidth * 2 && canvasHeight > imgHeight * 2) {
        if (canvasWidth > imgWidth * 3 && canvasHeight > imgHeight * 3) {
          if (canvasWidth > imgWidth * 4 && canvasHeight > imgHeight * 4) {
            eleImg.style.transform = 'scale(4)';
          } else {
            eleImg.style.transform = 'scale(3)';
          }
        } else {
          eleImg.style.transform = 'scale(2)';
        }
      }
      // var imgName = eleImg.src;
      // var imgType = imgName.substring(imgName.lastIndexOf('.') + 1);
      // if (imgType == 'svg') {
      //   eleImg.style.transform = 'scale(4)';
      // }
      var store = {
        scale: 1,
      };
      // 缩放处理
      eleImg.addEventListener('touchstart', function (event) {
        var touches = event.touches;
        var events = touches[0];
        var events2 = touches[1];
        if (!events) {
          return;
        }
        event.preventDefault();
        // 第一个触摸点的坐标
        store.pageX = events.pageX;
        store.pageY = events.pageY;
        store.moveable = true;
        if (events2) {
          store.pageX2 = events2.pageX;
          store.pageY2 = events2.pageY;
        }
        store.scale = transform.scale;
        store.originScale = store.scale || 1;
      });
      document.addEventListener('touchmove', function (event) {
        if (!store.moveable) {
          return;
        }
        var touches = event.touches;
        var events = touches[0];
        var events2 = touches[1];
        if (events2) {
          // 双指移动
          if (!store.pageX2) {
            store.pageX2 = events2.pageX;
          }
          if (!store.pageY2) {
            store.pageY2 = events2.pageY;
          }
          // 获取坐标之间的举例
          var getDistance = function (start, stop) {
            return Math.hypot(stop.x - start.x, stop.y - start.y);
          };
          var zoom =
            getDistance(
              {
                x: events.pageX,
                y: events.pageY,
              },
              {
                x: events2.pageX,
                y: events2.pageY,
              }
            ) /
            getDistance(
              {
                x: store.pageX,
                y: store.pageY,
              },
              {
                x: store.pageX2,
                y: store.pageY2,
              }
            );
          var newScale = store.originScale * zoom;
          // 最大缩放比例限制
          if (newScale > 8) {
            newScale = 8;
          }
          if (newScale < 0.8) {
            newScale = 0.8;
          }
          // 记住使用的缩放值
          store.scale = newScale;
          // 图像应用缩放效果
          eleImg.style.transform = 'scale(' + newScale + ')';
          transform.scale = newScale;
        }
      });
      document.addEventListener('touchend', function () {
        store.moveable = false;
        delete store.pageX2;
        delete store.pageY2;
      });
      document.addEventListener('touchcancel', function () {
        store.moveable = false;
        delete store.pageX2;
        delete store.pageY2;
      });
    },
    handleMaskClick() {
      if (this.maskClosable) {
        this.hide();
      }
    },
    reset() {
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      };
    },
    toggleMode() {
      if (this.loading) return;
      const modeNames = Object.keys(Mode);
      const modeValues = Object.values(Mode);
      const index = modeValues.indexOf(this.mode);
      const nextIndex = (index + 1) % modeNames.length;
      this.mode = Mode[modeNames[nextIndex]];
      this.reset();
    },
    prev() {
      if (this.isFirst && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
    },
    next() {
      if (this.isLast && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.3,
        rotateDeg: 90,
        enableTransition: true,
        ...options,
      };
      const { transform } = this;
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.8) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case 'zoomIn':
          if (transform.scale < 8) {
            transform.scale = parseFloat(
              (transform.scale + zoomRate).toFixed(3)
            );
          }
          break;
        case 'clocelise':
          transform.deg += rotateDeg;
          break;
        case 'anticlocelise':
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    },
  },
};
</script>
