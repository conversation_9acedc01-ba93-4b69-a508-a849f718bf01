<template>
  <div class="upload-demo" :class="{ 'hide-remove-button': isDetail, 'hide-success-icon': false }">
    <el-upload
      class=""
      ref="upload"
      :class="uploadClass"
      :action="action"
      :file-list="fileList"
      :on-preview="handlePreview"
      :on-remove="isDetail ? null : handleRemove"
      :on-change="handleChange"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :http-request="handleUpload"
      :on-exceed="handleExceed"
      :limit="limit"
      :auto-upload="autoUpload"
      :accept="accept"
      :list-type="listType"
      :drag="drag"
      :disabled="isDetail"
    >
      <slot>
        <template v-if="drag && !isDetail">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            拖拽文件至此或 Ctrl+V 粘贴上传<br/>
            <el-link type="primary">点击上传</el-link>
          </div>
        </template>

        <template v-else-if="listType === 'picture-card'">
        <div class="uploadTip">
          <div class="el-icon-plus"></div>
          <div>点击或拖拽上传</div>
        </div>
        </template>

        <template v-else>
          <el-link :disabled="isDetail" type="primary" class="linkStyle">点击上传</el-link>
        </template>
      </slot>

      <div class="el-upload__tip" slot="tip" v-if="showTip && !isDetail">
        {{ tipText }}
      </div>
    </el-upload>
    <div>
      <ImagePreview :visible="imagePreviewVisible" :image-url="imageUrl" :imageName="imageName" @close="imagePreviewVisible = false"></ImagePreview>
    </div>
  </div>
</template>

<script>
import ImagePreview from "@/components/ImagePreview/ImagePreview.vue";
import {importAttach} from "@/api/sysmgt";

export default {
  name: 'FileUpload',
  components: {ImagePreview},
  props: {
    //是否为详情页
    isDetail:{
      type: Boolean,
      default: false
    },
    // 文件列表
    value: {
      type: Array,
      default: () => []
    },
    // 上传地址（通常为空，使用自定义上传）
    action: {
      type: String,
      default: '#'
    },
    // 上传文件类型限制
    accept: {
      type: String,
      default: '.jpg,.png,.xlsx,.docx,.zip,.pdf'
    },
    // 文件数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 列表类型
    listType: {
      type: String,
      default: 'text'
    },
    // 是否启用拖拽上传
    drag: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 上传组件的CSS类
    uploadClass: {
      type: String,
      default: ''
    },
    // 文件大小限制（MB）
    maxSize: {
      type: Number,
      default: 100
    },
    // 允许的文件格式
    allowedFormats: {
      type: Array,
      default: () => ['jpg', 'png', 'xlsx', 'docx', 'zip', 'pdf']
    },
    // 上传标识
    uploadFlag: {
      type: String,
      default: 'purchase'
    },
    // 是否显示提示文本
    showTip: {
      type: Boolean,
      default: true
    },
    // 自定义提示文本
    customTipText: {
      type: String,
      default: ''
    },
    // 自定义上传方法
    customUploadMethod: {
      type: Function,
      default: null
    },
    // 文件路径前缀
    filePath: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imagePreviewVisible: false,
      imageUrl: '',
      imageName: '',
      fileList: [],
      uploadFileList: [], // 上传成功的文件列表
      fileNum: 0, // 文件数量
      isFlag: true // 上传状态标识
    }
  },
  computed: {
    // 提示文本
    tipText() {
      if (this.customTipText) {
        return this.customTipText;
      }
      return `支持上传 ${this.allowedFormats.join(', ')} 格式的文件，且文件数不超过 ${this.limit} 个，单个文件大小不超过 ${this.maxSize}M`;
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.loadFileData(newVal);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 加载文件数据
     * @param {Array} fileData - 文件数据数组
     */
    loadFileData(fileData) {
      if (fileData && fileData.length > 0) {
        this.fileList = fileData.map((item, index) => {
          return {
            uid: item.id || item.uid || Date.now() + index,
            name: item.fileName || item.name || '未知文件',
            url: item.path || item.url,
            path: item.path || item.url,
            status: 'success',
            fileSize: item.fileSize,
            response: {
              data: {
                fileName: item.fileName || item.name,
                fileUrl: item.path || item.url,
                fileSize: item.fileSize || item.size || 0
              }
            }
          }
        });

        this.uploadFileList = [...this.fileList];
        this.fileNum = this.fileList.length;
        this.isFlag = this.fileList.length === 0;
      } else {
        this.fileList = [];
        this.uploadFileList = [];
        this.fileNum = 0;
        this.isFlag = true;
      }
    },

    /**
     * 文件预览处理
     * @param {Object} file - 文件对象
     */
    async handlePreview(file) {
      // console.log('预览文件:', file);

      // 检查文件类型是否为图片
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
      const fileExtension = file.name ? file.name.split('.').pop().toLowerCase() : '';

      if (imageTypes.includes(fileExtension)) {
        // 图片文件，触发图片预览事件
        let previewUrl = '';
        if (file.url) {
          previewUrl = file.url.startsWith('http') ? file.url : (this.filePath || this.$filePath || '') + file.url;
        } else if (file.path) {
          previewUrl = file.path.startsWith('http') ? file.path : (this.filePath || this.$filePath || '') + file.path;
        }

        //打开图片预览窗
        this.imageUrl = previewUrl;
        this.imageName = file.name || '图片预览';
        this.imagePreviewVisible = true;

        // this.$emit('image-preview', {
        //   url: previewUrl,
        //   name: file.name || '图片预览'
        // });
      } else {
        // 非图片文件，直接下载
        let fileUrl = (this.filePath || this.$filePath || '') + (file.url || file.path);
        if (fileUrl) {
          try {
            const res = await fetch(fileUrl, {mode: 'cors'});
            const blob = await res.blob();
            const blobUrl = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = file.name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(blobUrl);
          } catch (error) {
            console.error('下载失败:', error);
            this.$DonMessage.error('文件下载失败');
          }
        }
      }

      this.$emit('preview', file);
    },

    /**
     * 文件移除方法
     * @param {Object} file - 被移除的文件对象
     * @param {Array} fileList - 当前文件列表
     */
    handleRemove(file, fileList) {
      // console.log('移除文件:', file);
      this.fileList = fileList;
      this.uploadFileList = fileList;
      this.fileNum = fileList.length;

      if (fileList.length === 0) {
        this.isFlag = true;
      }

      this.syncFileData();
      this.$emit('remove', file, fileList);
    },

    /**
     * 文件状态改变时的回调
     * @param {Object} file - 文件对象
     * @param {Array} fileList - 当前文件列表
     */
    handleChange(file, fileList) {
      // console.log('文件状态改变:', file, fileList);
      this.fileList = fileList;
      this.uploadFileList = fileList;
      this.fileNum = fileList.length;

      // this.syncFileData();
      this.$emit('change', file, fileList);
    },

    /**
     * 文件上传前的校验
     * @param {File} file - 待上传的文件
     * @returns {boolean} - 校验结果
     */
    beforeUpload(file) {
      // console.log('文件上传前校验:', file.name);

      const fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase();
      const isLimit = file.size / 1024 / 1024 < this.maxSize;

      if (!this.allowedFormats.includes(fileName)) {
        this.$DonMessage.warning(`文件格式不支持，请上传 ${this.allowedFormats.join(', ')} 格式的文件`);
        return false;
      }

      if (!isLimit) {
        this.$DonMessage.warning(`文件大小不能超过 ${this.maxSize}MB`);
        return false;
      }

      // console.log('文件校验通过，准备上传');
      this.$emit('before-upload', file);
      return true;
    },

    /**
     * 文件上传成功回调
     * @param {Object} res - 响应数据
     * @param {Object} rawFile - 原始文件对象
     */
    handleSuccess(res, rawFile) {
      this.$emit('success', res, rawFile);
    },

    /**
     * 自定义上传方法
     * @param {Object} param - 上传参数对象
     */
    handleUpload(param) {
      // 如果提供了自定义上传方法，使用自定义方法
      if (this.customUploadMethod && typeof this.customUploadMethod === 'function') {
        this.customUploadMethod(param, this.uploadCallback.bind(this));
        return;
      }

      // 默认上传逻辑
      // console.log('开始上传文件:', param.file.name);

      const formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', this.uploadFlag);

      //上传附件接口
      importAttach(formData).then(res => {
        //解析附件数据
        this.uploadCallback(param, res, null)
      }).catch(error => {
        this.uploadCallback(param, null, error)
      });
  },

    /**
     * 上传回调处理
     * @param {Object} param - 上传参数
     * @param {Object} res - 响应结果
     * @param {Error} error - 错误信息
     */
    uploadCallback(param, res, error) {
      if (error) {
        console.error('上传失败:', error);
        const fileIndex = this.fileList.findIndex(file => file.uid === param.file.uid);
        if (fileIndex !== -1) {
          this.$set(this.fileList[fileIndex], 'status', 'fail');
        }
        param.onError(error);
        this.$DonMessage.error('文件上传失败');
        return;
      }

      if (res && res.data && res.data.code === 100) {
        // 更新文件列表显示状态
        const fileIndex = this.fileList.findIndex(file => file.uid === param.file.uid);
        if (fileIndex !== -1) {
          this.$set(this.fileList[fileIndex], 'status', 'success');
          this.$set(this.fileList[fileIndex], 'path', res.data.data.fileUrl);
          this.$set(this.fileList[fileIndex], 'url', res.data.data.fileUrl);
          this.$set(this.fileList[fileIndex], 'fileSize', res.data.data.fileSize); // 添加这行
          this.$set(this.fileList[fileIndex], 'response', res.data);
        } else {
          // 如果找不到对应的文件，则添加新文件
          const obj = {
            fileName: res.data.data.fileName,
            name: res.data.data.fileName,
            path: res.data.data.fileUrl,
            fileSize: res.data.data.fileSize,
            uid: param.file.uid,
            status: 'success'
          };
          this.fileList.push(obj);
        }

        this.syncFileData();
        param.onSuccess(res.data);
        this.$DonMessage.success('文件上传成功');
      } else {
        const fileIndex = this.fileList.findIndex(file => file.uid === param.file.uid);
        if (fileIndex !== -1) {
          this.$set(this.fileList[fileIndex], 'status', 'fail');
        }
        param.onError(new Error(res.data.msg || '上传失败'));
        this.$DonMessage.error(res.data.msg || '文件上传失败');
      }
    },

    /**
     * 文件数量超出限制
     */
    handleExceed() {
      this.$DonMessage.warning(`最多只能上传 ${this.limit} 个文件`);
      this.$emit('exceed');
    },

    /**
     * 同步文件数据到父组件
     */
    syncFileData() {
      const successFiles = this.fileList
        .filter(file => file.status === 'success')
        .map(file => {
          return {
            fileName: file.name,
            path: file.path || file.url,
            fileSize: file.fileSize,
            status: file.status,
            // fileUrl: file.path || file.url,
            // name: file.name,
          };
        });
      this.$emit('input', successFiles);
      this.$emit('file-change', successFiles);
    },

    /**
     * 清空文件列表
     */
    clearFiles() {
      this.fileList = [];
      this.uploadFileList = [];
      this.fileNum = 0;
      this.isFlag = true;
      this.$refs.upload.clearFiles();
      this.syncFileData();
    },

    /**
     * 获取文件列表
     * @returns {Array} 文件列表
     */
    getFileList() {
      return this.fileList.filter(file => file.status === 'success');
    }
  }
}
</script>

<style scoped>
.file-upload-component {
  width: 100%;
}

.linkStyle {
  color: #409EFF;
  text-decoration: none;
}

.linkStyle:hover {
  color: #66b1ff;
}

/* 拖拽上传样式 */
.upload-demo {
  width: 100%;
}

.el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}

/* 限制文件列表宽度 */
.upload-demo >>> .el-upload-list {
  max-width: 600px;
}

.upload-demo >>> .el-upload-list__item {
  max-width: 100%;
  word-break: break-all;
}

.upload-demo >>> .el-upload-list__item-name {
  max-width: calc(100% - 80px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 隐藏删除按钮 */
.hide-remove-button >>> .el-upload-list__item-delete {
  display: none !important;
}

.hide-remove-button >>> .el-icon-close {
  display: none !important;
}

.hide-remove-button >>> .el-upload-list__item .el-icon-close-tip {
  display: none !important;
}

/* 隐藏成功状态图标（对号） */
.hide-success-icon >>> .el-upload-list__item-status-label {
  display: none !important;
}

.hide-success-icon >>> .el-upload-list__item .el-icon-upload-success {
  display: none !important;
}

.hide-success-icon >>> .el-upload-list__item .el-icon-circle-check {
  display: none !important;
}

.hide-success-icon >>> .el-upload-list__item .el-icon-check {
  display: none !important;
}
</style>
