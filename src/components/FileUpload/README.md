# FileUpload 文件上传组件

一个基于 Element UI 的可复用文件上传组件，支持多种文件格式上传、拖拽上传、文件预览、图片预览等功能。

## 功能特性

- ✅ 支持多种文件格式上传（图片、文档、压缩包等）
- ✅ 文件大小和数量限制
- ✅ 拖拽上传支持
- ✅ 文件预览功能
- ✅ 图片预览功能（配合 ImagePreview 组件）
- ✅ 文件下载功能
- ✅ 上传进度显示
- ✅ 错误处理和提示
- ✅ 自定义上传方法
- ✅ 响应式设计

## 基础用法

### 1. 导入组件

```javascript
import FileUpload from '@/components/FileUpload'

export default {
  components: {
    FileUpload
  },
  data() {
    return {
      fileList: []
    }
  }
}
```

### 2. 在模板中使用

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <FileUpload 
      v-model="fileList"
      @upload="handleUpload"
      @image-preview="handleImagePreview"
    />
    
    <!-- 拖拽上传 -->
    <FileUpload 
      v-model="fileList"
      :drag="true"
      upload-class="upload-demo"
      @upload="handleUpload"
    />
    
    <!-- 自定义配置 -->
    <FileUpload 
      v-model="fileList"
      :limit="10"
      :max-size="200"
      :allowed-formats="['jpg', 'png', 'pdf']"
      accept=".jpg,.png,.pdf"
      upload-flag="custom"
      custom-tip-text="支持上传图片和PDF文件"
      @upload="handleUpload"
    />
    
    <!-- 隐藏成功状态图标（对号） -->
    <FileUpload 
      v-model="fileList"
      :show-success-icon="false"
      @upload="handleUpload"
    />
  </div>
</template>
```

### 3. 处理上传事件

```javascript
import { importAttach } from '@/api/sysmgt'

export default {
  methods: {
    // 处理文件上传
    handleUpload({ param, formData, callback }) {
      // 调用上传接口
      importAttach(formData).then(res => {
        callback(param, res, null)
      }).catch(error => {
        callback(param, null, error)
      })
    },
    
    // 处理图片预览
    handleImagePreview({ url, name }) {
      // 显示图片预览弹窗
      this.previewImageUrl = url
      this.previewImageName = name
      this.previewVisible = true
    }
  }
}
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| value / v-model | 文件列表数据 | Array | — | [] |
| action | 上传地址 | String | — | '#' |
| accept | 接受的文件类型 | String | — | '.jpg,.png,.xlsx,.docx,.zip,.pdf' |
| limit | 文件数量限制 | Number | — | 5 |
| autoUpload | 是否自动上传 | Boolean | — | true |
| listType | 列表类型 | String | text/picture/picture-card | 'text' |
| drag | 是否启用拖拽上传 | Boolean | — | false |
| disabled | 是否禁用 | Boolean | — | false |
| uploadClass | 上传组件的CSS类 | String | — | '' |
| maxSize | 文件大小限制(MB) | Number | — | 100 |
| allowedFormats | 允许的文件格式 | Array | — | ['jpg', 'png', 'xlsx', 'docx', 'zip', 'pdf'] |
| uploadFlag | 上传标识 | String | — | 'purchase' |
| showTip | 是否显示提示文本 | Boolean | — | true |
| customTipText | 自定义提示文本 | String | — | '' |
| customUploadMethod | 自定义上传方法 | Function | — | null |
| filePath | 文件路径前缀 | String | — | '' |
| showSuccessIcon | 是否显示成功状态图标（对号） | Boolean | — | true |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| input | v-model 双向绑定事件 | (fileList) |
| file-change | 文件列表变化事件 | (fileList) |
| upload | 文件上传事件 | ({ param, formData, callback }) |
| image-preview | 图片预览事件 | ({ url, name }) |
| preview | 文件预览事件 | (file) |
| remove | 文件移除事件 | (file, fileList) |
| change | 文件状态变化事件 | (file, fileList) |
| before-upload | 文件上传前事件 | (file) |
| success | 文件上传成功事件 | (response, file) |
| exceed | 文件数量超出限制事件 | — |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| clearFiles | 清空文件列表 | — |
| getFileList | 获取成功上传的文件列表 | — |

### Slots

| 插槽名 | 说明 |
|--------|------|
| default | 自定义上传按钮内容 |

## 高级用法

### 1. 自定义上传方法

```vue
<FileUpload 
  v-model="fileList"
  :custom-upload-method="customUpload"
/>
```

```javascript
methods: {
  customUpload(param, callback) {
    // 自定义上传逻辑
    const formData = new FormData()
    formData.append('file', param.file)
    
    // 调用自定义上传接口
    this.$http.post('/custom-upload', formData).then(res => {
      callback(param, res, null)
    }).catch(error => {
      callback(param, null, error)
    })
  }
}
```

### 2. 配合图片预览组件使用

```vue
<template>
  <div>
    <FileUpload 
      v-model="fileList"
      @upload="handleUpload"
      @image-preview="handleImagePreview"
    />
    
    <!-- 图片预览组件 -->
    <ImagePreview 
      :visible="previewVisible"
      :image-url="previewImageUrl"
      :image-name="previewImageName"
      @close="handlePreviewClose"
    />
  </div>
</template>
```

### 3. 自定义样式

```vue
<FileUpload 
  v-model="fileList"
  upload-class="custom-upload"
  @upload="handleUpload"
>
  <el-button type="primary" icon="el-icon-upload">自定义上传按钮</el-button>
</FileUpload>
```

```css
.custom-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.custom-upload:hover {
  border-color: #409EFF;
}
```

## 数据格式

### 输入数据格式

```javascript
[
  {
    id: 1,
    fileName: 'document.pdf',
    name: 'document.pdf',
    path: '/uploads/document.pdf',
    fileSize: 1024000
  }
]
```

### 输出数据格式

```javascript
[
  {
    id: 'unique-id',
    fileName: 'document.pdf',
    fileUrl: '/uploads/document.pdf',
    fileSize: 1024000,
    name: 'document.pdf',
    path: '/uploads/document.pdf'
  }
]
```

## 注意事项

1. **上传处理**: 组件本身不包含具体的上传逻辑，需要通过 `@upload` 事件在父组件中处理实际的文件上传。

2. **图片预览**: 图片预览功能需要配合 `ImagePreview` 组件使用，通过 `@image-preview` 事件触发。

3. **文件路径**: 文件路径前缀可以通过 `filePath` prop 设置，或使用全局的 `$filePath`。

4. **错误处理**: 组件内置了基本的错误处理和用户提示，可以通过事件监听进行自定义处理。

5. **样式定制**: 支持通过 `uploadClass` prop 和插槽进行样式定制。

## 兼容性

- Vue 2.x
- Element UI 2.x
- 现代浏览器（IE 10+）

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础文件上传功能
- 支持拖拽上传
- 支持文件预览和图片预览
- 支持自定义配置和样式