import FileUpload from './FileUpload.vue'

export default FileUpload

/**
 * FileUpload 文件上传组件
 * 
 * 使用方法：
 * 
 * 1. 基础用法：
 * <FileUpload 
 *   v-model="fileList"
 *   @upload="handleUpload"
 *   @image-preview="handleImagePreview"
 * />
 * 
 * 2. 拖拽上传：
 * <FileUpload 
 *   v-model="fileList"
 *   :drag="true"
 *   upload-class="upload-demo"
 *   @upload="handleUpload"
 * />
 * 
 * 3. 自定义配置：
 * <FileUpload 
 *   v-model="fileList"
 *   :limit="10"
 *   :max-size="200"
 *   :allowed-formats="['jpg', 'png', 'pdf']"
 *   accept=".jpg,.png,.pdf"
 *   upload-flag="custom"
 *   @upload="handleUpload"
 * />
 * 
 * Props:
 * @param {Array} value - 文件列表数据 (v-model)
 * @param {String} action - 上传地址，默认 '#'
 * @param {String} accept - 接受的文件类型，默认 '.jpg,.png,.xlsx,.docx,.zip,.pdf'
 * @param {Number} limit - 文件数量限制，默认 5
 * @param {Boolean} autoUpload - 是否自动上传，默认 true
 * @param {String} listType - 列表类型，默认 'text'
 * @param {Boolean} drag - 是否启用拖拽上传，默认 false
 * @param {Boolean} disabled - 是否禁用，默认 false
 * @param {String} uploadClass - 上传组件的CSS类
 * @param {Number} maxSize - 文件大小限制(MB)，默认 100
 * @param {Array} allowedFormats - 允许的文件格式，默认 ['jpg', 'png', 'xlsx', 'docx', 'zip', 'pdf']
 * @param {String} uploadFlag - 上传标识，默认 'purchase'
 * @param {Boolean} showTip - 是否显示提示文本，默认 true
 * @param {String} customTipText - 自定义提示文本
 * @param {Function} customUploadMethod - 自定义上传方法
 * @param {String} filePath - 文件路径前缀
 * @param {Boolean} showSuccessIcon - 是否显示成功状态图标（对号），默认 true
 * 
 * Events:
 * @event input - v-model 双向绑定事件
 * @event file-change - 文件列表变化事件
 * @event upload - 文件上传事件，参数: { param, formData, callback }
 * @event image-preview - 图片预览事件，参数: { url, name }
 * @event preview - 文件预览事件
 * @event remove - 文件移除事件
 * @event change - 文件状态变化事件
 * @event before-upload - 文件上传前事件
 * @event success - 文件上传成功事件
 * @event exceed - 文件数量超出限制事件
 * 
 * Methods:
 * @method clearFiles - 清空文件列表
 * @method getFileList - 获取成功上传的文件列表
 * 
 * 注意事项：
 * 1. 组件需要配合父组件的上传方法使用，通过 @upload 事件处理实际的文件上传
 * 2. 图片预览功能需要配合 ImagePreview 组件使用，通过 @image-preview 事件触发
 * 3. 文件路径前缀可以通过 filePath prop 或全局 $filePath 设置
 * 4. 支持自定义上传方法，通过 customUploadMethod prop 传入
 */