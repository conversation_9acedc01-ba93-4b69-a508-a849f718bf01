<template>
  <div class="headerContent" v-cloak>
    <div class="navHeader">
      <div class="logo">
        <img src="../../assets/image/headerIcon/logo.png" alt="">
      </div>
      <div class="headerRight">
        <div class="toggleIcon">
          <svg-icon icon-class="openIcon" class="openIcon" @click="toggleClick()"></svg-icon>
          <svg-icon icon-class="flodIcon" class="flodIcon" @click="toggleClick()"></svg-icon>
          <span>ERP进销存系统</span>
        </div>
        <div class="navIcon">
          <div class="frontButton" v-if="false" @click="frontPage()">
            前台首页
          </div>
          <!-- 帮助手册 -->
          <div>
            <svg-icon icon-class="helpManual"></svg-icon>
          </div>
          <!-- 系统公告 -->
          <div @click="noticeClick()">
            <el-tooltip
              class="item"
              effect="light"
              content="系统公告"
              placement="bottom"
            >
              <el-badge
                :hidden="unreadNum != 0 ? false : true"
                :value="unreadNum"
                :max="99"
                class="item"
                style="width: 100%; height: 100%"
              >
                <svg-icon icon-class="systemNotice"></svg-icon>
              </el-badge>
            </el-tooltip>
          </div>
          <!-- 主题 -->
          <div v-if="false" @click="themeClick()">
            <el-tooltip
              class="item"
              effect="light"
              content="主题切换"
              placement="bottom"
            >
              <svg-icon icon-class="themeSwitch"></svg-icon>
            </el-tooltip>
          </div>

          <div class="dropdrow" v-cloak>
            <el-dropdown style="cursor: pointer;">
              <span class="el-dropdown-link">
                <img v-if="!userInfo.headimgurl || userInfo.headimgurl.length <= 0" src="../../assets/image/defaultAvatar.png" alt="">
                <img v-else :src="imagePath + userInfo.headimgurl" alt="">
                {{userInfo.realName}}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="basicInfo()">{{$t('nav.data')}}</el-dropdown-item>
                <el-dropdown-item @click.native="modifyCipher()">{{$t('nav.change')}}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div @click="signOut()" style="cursor: pointer;">
            {{$t('nav.signOut')}}
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form v-if="dialogStatus === 'info'" :label-width="formLabelWidth" ref="infoData" :model="infoData" :rules="formRules">
        <el-form-item label="头像" class="headPortrait">
          <el-tooltip class="item infoAvata" effect="dark" content="仅支持jpg,png格式的图片，且大小不能超过2MB" placement="right">
            <div class="changeHead">
              <el-upload
                class="avatar-uploader"
                action="#"
                :http-request="uploadAttach"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload">
                <img v-if="imageUrl" :src="imageUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div class="maskTips">
                <div>
                  <p>更换</p>
                  <p>头像</p>
                </div>
              </div>
            </div>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="账号名" prop="username">
          <el-input type="text" placeholder="请输入账号名" v-model.trim="infoData.username" disabled></el-input>
        </el-form-item>
        <el-form-item label="名称" prop="realName">
          <el-input type="text" placeholder="请输入名称" v-model.trim="infoData.realName" disabled></el-input>
        </el-form-item>
        <!-- <el-form-item label="联系人" prop="contacts">
          <el-input type="tel" placeholder="请输入联系人" v-model.trim="infoData.contacts"></el-input>
        </el-form-item> -->
        <el-form-item label="性别" prop="sex">
          <el-radio-group v-model="infoData.sex">
            <el-radio :label="1">男</el-radio>
            <el-radio :label="2">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="联系电话" prop="mobile">
          <el-input type="tel" placeholder="请输入联系电话" maxlength="11" v-model.trim="infoData.mobile"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input type="email" placeholder="请输入邮箱" v-model.trim="infoData.email"></el-input>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input type="text" placeholder="请输入地址" v-model.trim="infoData.address"></el-input>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="infoSubmit('infoData')">
            提交
          </el-button>
        </div>
      </el-form>
      <!-- 修改密码 -->
      <el-form class="updatePswArea" :label-width="formLabelWidth" v-if="dialogStatus === 'password'" :rules="formPwd" ref="modify" :model="modify" :validate-on-rule-change="false">
        <el-form-item label="旧密码" prop="oldPassword" class="oldPassword">
          <el-input type="password" v-model.trim="modify.oldPassword" placeholder="请输入旧密码"></el-input>
          <img
            class="seeArea"
            @click="noSeeClick('oldPassword')"
            src="../../assets/image/sightIocn.png"
          />
          <img
            class="noSeeArea"
            @click="seeClick('oldPassword')"
            src="../../assets/image/noSeeIcon.png"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword" class="newPassword">
          <el-input type="password" v-model.trim="modify.newPassword" placeholder="请输入新密码"></el-input>
          <img
            class="seeArea"
            @click="noSeeClick('newPassword')"
            src="../../assets/image/sightIocn.png"
          />
          <img
            class="noSeeArea"
            @click="seeClick('newPassword')"
            src="../../assets/image/noSeeIcon.png"
          />
        </el-form-item>
        <el-form-item label="确定密码" prop="confirmPassword" class="repeatPwd">
          <el-input type="password" v-model.trim="modify.confirmPassword" placeholder="请输入确定密码"></el-input>
          <img
            class="seeArea"
            @click="noSeeClick('repeatPwd')"
            src="../../assets/image/sightIocn.png"
          />
          <img
            class="noSeeArea"
            @click="seeClick('repeatPwd')"
            src="../../assets/image/noSeeIcon.png"
          />
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="cipherSubmit('modify')">
            提交
          </el-button>
          <el-button plain @click="resetPwd('modify')">
            重置
          </el-button>
        </div>
      </el-form>
      <!-- 切换主题 -->
      <div v-if="dialogStatus == 'themeSwitch'">
        <div class="themeSwitchArea">
          <div class="themeType">
            <div
              v-for="(item, index) of themeList"
              :key="index"
              :style="
                item.color == themeColorVal
                  ? 'border: 2px solid var(--theme-color)'
                  : ''
              "
            >
              <img
                @click="themeColor(item.color)"
                :src="require('../../assets/image/headerIcon/' + item.img)"
                alt=""
              />
              <div>{{ item.name }}</div>
            </div>
          </div>
          <div class="submitArea">
            <el-button type="primary" class="butStyle" @click="themeSubmit()">
              确定
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog v-dialogDrag title="选择国家" :visible.sync="dialogCountryFormVisible">
      <el-form ref="alloter" :label-width="formLabelWidth" :validate-on-rule-change="false">
        <el-form-item label="选择国家">
          <el-select v-model="countryCode" placeholder="请选择" filterable clearable>
            <el-option
              v-for="item in countryList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="submitCountry()">
            跳转
          </el-button>
          <el-button plain @click="resetCode()">
            取消
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script src="./header.js"></script>
<style src="./header.css"></style>
