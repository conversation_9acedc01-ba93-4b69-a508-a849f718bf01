/* 顶部导航 */
.el-header {
  height: 50px !important;
  padding: 0 25px 0 10px !important;
  background: var(--header-color);
  width: 100%;
}

.el-header .headerContent,
.el-header .navHeader {
  width: 100%;
  height: 100%;
  color: #fff;
  font-size: 14px;
}

.el-header .navHeader,
.headerContent .headerRight,
.el-header .navHeader .navIcon {
  display: flex;
  align-items: center;
}

/* logo */
.el-header .navHeader .logo {
  width: 200px;
  padding-right: 10px;
  display: flex;
  align-items: center;
}

.el-header .navHeader .logo img {
  max-width: 86%;
}

/* 导航按钮 */
.headerContent .headerRight {
  flex: 1;
  justify-content: space-between;
}

/* 左 */
.headerContent .headerRight .toggleIcon {
  font-size: 20px;
  display: flex;
  align-items: center;
}

.headerContent .svg-icon {
  width: 22px;
  height: 22px;
  margin-right: 12px;
  cursor: pointer;
  color: #fff !important;

}

.toggleIcon .flodIcon {
  display: none;
}

/* 导航按钮 */
.el-header .navHeader .navIcon>div {
  margin-left: 30px;
  cursor: pointer;
}

/* 文字提示框 */
.el-tooltip__popper.is-light {
  border: 1px solid var(--theme-color) !important;
  padding: 6px 8px !important;
}

.el-tooltip__popper {
  max-width: 800px;
}

.el-tooltip__popper.is-dark .popper__arrow {
  border-color: transparent !important;
}

.el-tooltip__popper .popper__arrow {
  top: 100%;
  border-top-color: var(--theme-color) !important;
  border-bottom-color: var(--theme-color) !important;
}

.el-header .navHeader .el-dropdown span {
  color: #fff;
}

/* 下拉框 */
/* .el-header .frontButton {
  padding: 5px 10px;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius);
} */

/* 下拉框 */
.el-header .navHeader .el-dropdown {
  color: #fff !important;
}
.dropdrow {
  margin-right: 10px;
}
.dropdrow img {
  border-radius: 50%;
  width: 28px;
  height: 28px;
  margin-right: 6px;
  vertical-align: middle;
  margin-top: 0px;
}

.el-header .el-dropdown span {
  display: inline-block;
  height: 100%;
}

.dropdrow .el-icon--right {
  margin-left: 2px;
}
/* 切换主题 */
.headerContent .themeType {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 0 15px;
}

.headerContent .themeType>div {
  width: 25%;
  border: 2px solid transparent;
  text-align: center;
  min-width: 215px;
  margin: 8px 10px;
}

.headerContent .themeType>div>div {
  text-align: center;
  padding-bottom: 8px;
  font-size: 16px;
}

.headerContent .themeType img {
  padding: 10px;
  width: calc(100% - 30px);
  cursor: pointer;
}
/* 提交按钮 */
.submitArea,
.el-dialog .submitArea {
  text-align: right;
  margin: 15px 0 !important;
}

.el-dialog .submitArea {
  padding: 12px 20px !important;
  margin: 15px 0 -20px -20px !important;
  width: calc(100% + 40px);
  margin-top: -20px;
  border-top: 1px solid var(--border-color);
}

.submitArea .el-form-item__content {
  margin: 0 !important;
}

.headerContent .el-dialog .el-dialog__body {
  margin: 20px 0 0;
}

.headerContent .el-dialog .updatePswArea .el-input .el-input__inner {
  padding: 0 30px 0 10px !important;
}

.headerContent .el-dialog .updatePswArea img {
  cursor: pointer;
  width: 20px;
  position: absolute;
  top: 50%;
  right: 28px;
  transform: translate(0, -50%);
}

.headerContent .el-dialog .updatePswArea .seeArea {
  display: none;
}

/* 头像 */
.headPortrait .el-form-item__label {
  margin-top: 4%;
}

.headPortrait .el-form-item__content {
  line-height: normal;
}

.changeHead {
  cursor: pointer;
  overflow: hidden;
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

.avatar-uploader .el-upload {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: none;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 75px !important;
  text-align: center;
}

.avatar {
  width: 80px;
  height: 80px;
  display: block;
}

.maskTips {
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  text-align: center;
  display: none;
  pointer-events: none;
}

.maskTips>div {
  margin-top: 50%;
  transform: translateY(-50%);
}

.maskTips p {
  line-height: 1.5;
  letter-spacing: 2px;
  color: #fff;
  font-size: 14px;
}

@media screen and (max-width: 1600px) and (min-width: 1440px) {

  /* 顶部导航 */
  .el-header .navHeader .navIcon>div {
    margin-left: 32px;
  }

  /* 下拉框 */
  .dropdrow img {
    width: 26px;
    height: 26px;
  }

  .headerContent .themeType>div>div {
    font-size: 15px;
  }

  .headerContent .el-dialog .updatePswArea img {
    width: 19px;
  }

  /* 头像 */
  .changeHead,
  .avatar-uploader .el-upload {
    width: 75px;
    height: 75px;
  }

  .avatar-uploader-icon {
    width: 75px;
    height: 75px;
    line-height: 70px !important;
  }

  .avatar,
  .maskTips {
    width: 75px;
    height: 75px;
  }

  .maskTips p {
    line-height: 1.4;
    letter-spacing: 2px;
    font-size: 14px;
  }
}

@media screen and (max-width: 1440px) and (min-width: 1366px) {

  /* 顶部导航 */
  .headerContent .el-dialog .updatePswArea img {
    width: 19px;
  }

  /* 头像 */
  .changeHead,
  .avatar-uploader .el-upload {
    width: 70px;
    height: 70px;
  }

  .avatar-uploader-icon {
    width: 70px;
    height: 70px;
    line-height: 65px !important;
  }

  .avatar,
  .maskTips {
    width: 70px;
    height: 70px;
  }

  .maskTips p {
    line-height: 1.4;
    letter-spacing: 1.5px;
    font-size: 14px;
  }
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {

  /* 顶部导航 */
  .el-header {
    height: 45px !important;
  }
  /* 导航按钮 */
  /* 左 */
  .el-header .navHeader .navIcon>div {
    margin-left: 25px;
  }
  .headerContent .headerRight .toggleIcon {
    font-size: 18px;
  }
  .headerContent .toggleIcon .svg-icon,
  .headerContent .navIcon .svg-icon {
    width: 20px;
    height: 20px;
  }
  /* 下拉框 */
  .dropdrow img {
    width: 24px;
    height: 24px;
    margin-top: -3px;
  }

  .headerContent .themeType img {
    padding: 8px;
    width: calc(100% - 16px);
  }

  .headerContent .themeType>div>div {
    font-size: 14px;
  }

  .headerContent .el-dialog .updatePswArea img {
    width: 16px;
  }

  /* 头像 */
  .changeHead,
  .avatar-uploader .el-upload {
    width: 65px;
    height: 65px;
  }

  .avatar-uploader-icon {
    width: 65px;
    height: 65px;
    line-height: 60px !important;
  }

  .avatar,
  .maskTips {
    width: 65px;
    height: 65px;
  }

  .maskTips p {
    line-height: 1.4;
    letter-spacing: 1.5px;
    font-size: 13px;
  }
}

@media screen and (max-width: 1280px) and (min-width: 1024px) {

  /* 顶部导航 */
  .el-header {
    height: 40px !important;
  }

  /* logo */
  /* 导航按钮 */
  /* 左 */
  .headerContent .headerRight .toggleIcon {
    font-size: 16px;
  }
  .headerContent .toggleIcon .svg-icon,
  .headerContent .navIcon .svg-icon {
    width: 20px;
    height: 20px;
    margin-top: -1px;
  }

  .el-header .navHeader .navIcon>div {
    margin-left: 25px;
  }

  /* 下拉框 */
  .dropdrow img {
    width: 22px;
    height: 22px;
    margin-top: -3px;
  }

  .headerContent .themeType img {
    padding: 8px;
    width: calc(100% - 16px);
  }

  .headerContent .el-dialog .updatePswArea img {
    width: 15px;
  }

  /* 头像 */
  .changeHead,
  .avatar-uploader .el-upload {
    width: 60px;
    height: 60px;
  }

  .avatar-uploader-icon {
    width: 60px;
    height: 60px;
    line-height: 55px !important;
  }

  .avatar,
  .maskTips {
    width: 60px;
    height: 60px;
  }

  .maskTips p {
    line-height: 1.4;
    letter-spacing: 1.5px;
    font-size: 12px;
  }
}

@media screen and (max-width: 1024px) {
  /* 顶部导航 */
  .el-header {
    height: 40px !important;
  }

  /* 导航按钮 */
  /* 左 */
  .headerContent .headerRight .toggleIcon {
    font-size: 16px;
  }
  .headerContent .toggleIcon .svg-icon,
  .headerContent .navIcon .svg-icon {
    width: 20px;
    height: 20px;
  }

  .el-header .navHeader .navIcon>div {
    margin-left: 25px;
  }

  /* 下拉框 */
  .dropdrow img {
    width: 22px;
    height: 22px;
    margin-top: -2px;
  }

  .headerContent .themeType img {
    padding: 8px;
    width: calc(100% - 16px);
  }

  .headerContent .themeType>div>div {
    font-size: 13px;
  }

  .headerContent .el-dialog .updatePswArea img {
    width: 15px;
  }

  /* 头像 */
  .changeHead,
  .avatar-uploader .el-upload {
    width: 60px;
    height: 60px;
  }

  .avatar-uploader-icon {
    width: 60px;
    height: 60px;
    line-height: 55px !important;
  }

  .avatar,
  .maskTips {
    width: 60px;
    height: 60px;
  }

  .maskTips p {
    line-height: 1.4;
    letter-spacing: 1.5px;
    font-size: 12px;
  }
}