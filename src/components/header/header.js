import {
  addTabs,
  frontHome,
  sysServerUrl,
  loginStatus
} from '@/assets/js/common.js'
import {
  getUserInfo,
  importAttach,
  logout,
  messageData,
  updatedTheme,
  userDefinedInfo,
  userDefinedPwd
} from '@/api/sysmgt.js'
import { closeRealMessage } from '@/assets/js/real_message.js'

export default {
  name: "home",
  data() {
    return {
      imagePath: sysServerUrl,
      imgSrc: sysServerUrl + "sys/upload/display?filePath=",
      userInfo: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        info: '基本资料',
        password: '修改密码',
        themeSwitch: '主题切换',
      },
      unreadNum: "",
      // 基本资料
      infoData: {
        username: '',
        realName: '',
        userType: '',
        sex: '',
        email: '',
        telephone: '',
        address: '',
        mobile: '',  // 手机号
        contacts: '', // 联系人
        theme: '',
      },
      // 修改密码
      modify: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      formLabelWidth: '85px',
      themeColorVal: "",
      dialogCountryFormVisible: false,
      countryList: [],
      countryCode: '',
      // 头像
      imageUrl: "",
      imageHeadUrl: "",
    }
  },
  computed: {
    themeList() {
      var list = [
        {
          img: "themeBlack.png",
          color: "rgb(24, 122, 255)",
          name: "经典黑",
        },
        {
          img: "themeBlue.png",
          color: "rgb(35, 134, 238)",
          name: "简约蓝",
        },
        {
          img: "themeCyan.png",
          color: "rgb(26, 172, 228)",
          name: "典雅青",
        },
      ];
      return list;
    },
    formRules() {
      return {
        contacts: [
          {
            required: true,
            message: "联系人不能为空",
            trigger: ["blur", "change"],
          }
        ],
        // email: [
        //   {
        //     required: true,
        //     message: "邮箱不能为空",
        //     trigger: ["blur", "change"],
        //   },
        //   {
        //     type: "email",
        //     message: "请输入正确的邮箱格式",
        //   },
        // ],
        mobile: [
          {
            required: true,
            message: "联系电话不能为空",
            trigger: ["blur", "change"],
          },
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的联系电话格式",
          },
        ],
      };
    },
    formPwd() {
      return {
        oldPassword: [
          { required: true, message: '旧密码不能为空', trigger: ['blur', 'change'] }
        ],
        newPassword: [
          { required: true, message: '新密码不能为空', trigger: ['blur', 'change'] },
          // {
          //   pattern: /^(?=.*\d+)(?=.*[A-Za-z]+)[\w]{6,16}$/,
          //   required: true,
          //   message: "新密码为6~16位字母和数字组合",
          //   trigger: ["blur", "change"],
          // },
        ],
        confirmPassword: [
          { required: true, message: '确定密码不能为空', trigger: ['blur', 'change'] }
        ]
      }
    },
  },
  methods: {
    // 左侧目录显示隐藏
    toggleClick() {
      window.collapseState();
      var displayVal = $(".toggleIcon .flodIcon").css("display");
      if (displayVal == "none") {
        $(".toggleIcon .flodIcon").show();
        $(".toggleIcon .openIcon").hide();
      } else {
        $(".toggleIcon .flodIcon").hide();
        $(".toggleIcon .openIcon").show();
      }
      window.menuHeight();
    },
    // 登录
    // logoClick() {
    //   this.$router.push('/index');
    // },
    // 退出登录
    signOut() {
      logout().then(res => {
        this.$store.commit('del_token');
        loginStatus();
      }).catch(e => {
        this.$store.commit('del_token');
      })
      var linkLength = document.getElementsByTagName("link");
      for (var i = 0; i < linkLength.length; i++) {
        if (linkLength[i].getAttribute("id") == "theme") {
          linkLength[i].parentNode.removeChild(linkLength[i]);
        }
      }
      closeRealMessage();
    },
    // 未读信息
    unreadDetail() {
      var _this = this
      // messageData().then(res => {
      //   _this.unreadNum = res.data.data.notice;
      // })
    },
    noticeClick() {
      this.$router.push({ name: "systemBulletin" });
      addTabs(this.$route.path, "系统公告");
    },
    // 密码不可见
    noSeeClick(text) {
      $("." + text + " .seeArea").hide();
      $("." + text + " .noSeeArea").show();
      $("." + text + " .el-input .el-input__inner").attr("type", "password");
    },
    // 密码可见
    seeClick(text) {
      $("." + text + " .seeArea").show();
      $("." + text + " .noSeeArea").hide();
      $("." + text + " .el-input .el-input__inner").attr("type", "text");
    },
    // 切换主题
    themeClick() {
      this.dialogStatus = "themeSwitch";
      this.userData();
      this.dialogFormVisible = true;
    },
    themeColor(val) {
      this.themeColorVal = val;
    },
    themeSubmit() {
      var params = new FormData();
      var themeType = "";
      if (this.themeColorVal == "rgb(35, 134, 238)") {
        themeType = "blue";
      } else if (this.themeColorVal == "rgb(26, 172, 228)") {
        themeType = "cyan";
      } else if (this.themeColorVal == "rgb(24, 122, 255)") {
        themeType = "black";
      } else if (this.themeColorVal == "rgb(28, 144, 59)") {
        themeType = "green";
      }
      params.append("theme", themeType);
      updatedTheme(params).then((res) => {
        if (res.data.code == 100) {
          this.dialogFormVisible = false;
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          if (themeType == "blue") {
            document.getElementById("theme").href =
              "./static/theme/themeBlue.css";
          } else if (themeType == "cyan") {
            document.getElementById("theme").href =
              "./static/theme/themeCyan.css";
          } else if (themeType == "black") {
            document.getElementById("theme").href =
              "./static/theme/themeBlack.css";
          } else {
            document.getElementById("theme").href =
              "./static/theme/defaultTheme.css";
            $("body").addClass("greenTheme");
          }
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      });
    },
    // 用户信息
    userData() {
      getUserInfo().then(res => {
        if (res.data.code == 100) {
          this.userInfo = res.data.data;
          this.infoData = Object.assign({}, this.userInfo);
          this.imageUrl = this.infoData.headimgurl ? sysServerUrl + this.infoData.headimgurl : require("../../assets/image/defaultAvatar.png");

          if (
            this.infoData.theme == "green" ||
            this.infoData.theme == undefined ||
            this.infoData.theme == null ||
            this.infoData.theme == ""
          ) {
            this.themeColorVal = "rgb(28, 144, 59)";
          } else if (this.infoData.theme == "blue") {
            this.themeColorVal = "rgb(35, 134, 238)";
          } else if (this.infoData.theme == "cyan") {
            this.themeColorVal = "rgb(26, 172, 228)";
          } else if (this.infoData.theme == "black") {
            this.themeColorVal = "rgb(24, 122, 255)";
          }
        }
      })
    },
    // 基本资料
    basicInfoReset() {
      this.infoData.username = "";
      this.infoData.realName = "";
      this.infoData.userType = "";
      this.infoData.sex = "";
      this.infoData.email = "";
      this.infoData.telephone = "";
      this.infoData.address = "";
      this.infoData.mobile = "";
      this.infoData.contacts = "";
      this.$nextTick(() => {
        this.$refs.infoData.clearValidate();
      });
    },
    basicInfo() {
      this.dialogStatus = 'info'
      this.basicInfoReset()
      this.userData()
      this.dialogFormVisible = true;
      setTimeout(() => {
        $(".changeHead").mouseenter(() => {
          setTimeout(() => {
            $(".maskTips").show();
          }, 50);
        }).mouseleave(() => {
          $(".maskTips").hide();
          $(".el-tooltip__popper").hide();
        })
      });
      setTimeout(() => {
        this.$nextTick(() => {
          this.$refs.infoData.clearValidate();
        });
      }, 100);
    },
    // 确认修改基本信息
    infoSubmit() {
      this.$refs['infoData'].validate((valid) => {
        if (valid) {
          this.infoData.contacts = this.infoData.realName
          let param = {
            'userType': 1, // 用户类型需要查询，暂时写死
            'realName': this.infoData.realName,
            'username': this.infoData.username,
            'mobile': this.infoData.mobile,
            'email': this.infoData.email,
            'contacts': this.infoData.contacts,
            'address': this.infoData.address,
            'sex': this.infoData.sex,
          }
          if (this.imageHeadUrl && this.imageHeadUrl.length > 0) {
            param.avatar = this.imageHeadUrl
          }
          userDefinedInfo(this.infoData.id, param).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.userData();
              this.dialogFormVisible = false;
              this.imageHeadUrl = "";
              window.uploadInfo();
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 修改密码
    modifyCipher() {
      this.dialogFormVisible = true;
      this.dialogStatus = 'password';
      this.resetPwd();
    },
    // 自定义修改密码
    cipherSubmit() {
      this.$refs['modify'].validate((valid) => {
        if (valid) {
          var param = new URLSearchParams();
          param.append("oldPwd", this.modify.oldPassword)
          param.append("newPwd", this.modify.newPassword)
          param.append("confirmPwd", this.modify.confirmPassword)
          userDefinedPwd(param).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dialogFormVisible = false;
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    resetPwd() {
      $(".seeArea").hide();
      $(".noSeeArea").show();
      $(".updatePswArea .el-input .el-input__inner").attr("type", "password");
      this.modify.oldPassword = "";
      this.modify.newPassword = "";
      this.modify.confirmPassword = "";
      this.$nextTick(() => {
        this.$refs.modify.clearValidate();
      });
    },
    // 跳转前台
    frontPage() {
      // userAtlasCountry().then(res => {
      //   this.countryList = res.data.data
      //   if (this.countryList.length == 1) {
      //     this.countryCode = this.countryList[0].code
      //     let url = frontHome + "?country=" + this.countryCode
      //     window.open(url);
      //   }else{
      //     this.countryCode = this.countryList[0].code
      //     this.dialogCountryFormVisible = true;
      //   }
      // })
      // window.open(frontHome);
    },
    // 取消
    resetCode() {
      this.dialogCountryFormVisible = false;
      this.countryList = []
      this.countryCode = ''
    },
    // 跳转前台提交
    submitCountry() {
      this.dialogCountryFormVisible = false;
      let url = frontHome + "?country=" + this.countryCode
      window.open(url);
    },

    // 文件上传
    uploadAttach(param) {
      var formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', "temp/syshead/");
      importAttach(formData).then(res => {
        this.imageHeadUrl = res.data.data.fileUrl;
        this.imageUrl = sysServerUrl + "sys/upload/display?filePath=" + res.data.data.fileUrl;
      })
    },
    handleAvatarSuccess(res, file) {
      // this.imageHeadUrl = res.data.data.fileUrl;
      // this.imageUrl = sysServerUrl + "sys/upload/display?filePath=" + res.data.data.fileUrl;
      // this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 5;

      if (!isJPG) {
        this.$DonMessage.warning('上传头像图片只能是 JPG、PNG 格式!');
      }
      if (!isLt2M) {
        this.$DonMessage.warning('上传头像图片大小不能超过 5MB!');
      }
      return isJPG && isLt2M;
    }

  },
  mounted() {
    this.userData()
    // 未读信息
    this.unreadDetail();
    window.noticeUnread = () => {
      setTimeout(() => {
        this.unreadDetail();
      }, 100)
    }
  },
}
