<template>
  <div class="asideContent">
    <el-aside width="collapse">
      <el-menu :collapse="isCollapse" :state="isState" class="el-menu-vertical-demo is-collapse"
        :default-active="selectTabPath" router>
        <el-menu-item index="/dashboard" @click="selectMenu({ 'url': '/dashboard', 'name': '我的主页' })">
          <img src="../../assets/image/home.png" alt="">
          <span slot="title" v-if="!isState">{{ $t('catalogue.oneTitle') }}</span>
          <span>{{ $t('catalogue.oneTitle') }}</span>
        </el-menu-item>
        <el-submenu :index="num + '2'" v-for="(item, num) of newList" :key='num'>
          <template slot="title">
            <img v-if="item.icon !== ''" :src="imgSrc + item.icon" alt="" />
            <img v-if="item.icon == ''" src="../../assets/image/commonIcon.png" alt="">
            <span>{{ item.name }}</span>
          </template>
          <el-menu-item v-for="(itm, idx) of item.children" :key="idx" class="groupName" :index="itm.url"
            @click="selectMenu(itm)">
            <img v-if="itm.icon !== ''" :src="imgSrc + itm.icon" alt="">
            <img v-if="itm.icon == ''" src="../../assets/image/commonIcon.png" alt="">
            <span>{{ itm.name }}</span>
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </el-aside>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { sysServerUrl, addTabs } from '@/assets/js/common.js'
import { catalog } from '@/api/sysmgt.js'
import $ from 'jquery'
export default {
  name: 'index',
  computed: {
    ...mapState({ // 从 state 中的到的计算属性
      activeName: state => state.activeName, // 已选中菜单
      selectTabPath: state => state.selectTabPath, // 已选中菜单
    })
  },
  data() {
    return {
      imgSrc: sysServerUrl + "sys/upload/display?filePath=",
      isCollapse: true,
      isState: true,
      newList: [],
      color: 'var(--theme-color)',
      // menuVisible: false,
    }
  },
  methods: {
    // 点击菜单
    selectMenu(item) {
      if (item.url == "/dashboard") {
        this.$router.push('/dashboard')
      } else {
        this.$router.push(item.url);
        setTimeout(() => {
          addTabs(this.$route.path, item.name);
        });
      }
    },
    foo() {
      // 取消鼠标监听事件 菜单栏
      this.menuVisible = false
      document.removeEventListener('click', this.foo) // 关掉监听，
    },
    // 目录信息
    catalogueList() {
      var _this = this
      catalog().then(res => {
        _this.newList = res.data.data
      })
    },
    // 获取内容区域
    heightSize() {
      setTimeout(() => {
        if ($(".asideContent").length > 0) {
          var allHeight = $(".el-container.is-vertical").height();
          var header = $(".headerContent").height();
          var val = allHeight - header;
          $(".asideContent,.el-aside").css("height", val);
        }
      }, 100);
    },
    areaSize() {
      var _this = this;
      _this.heightSize();
      window.addEventListener("resize", function () {
        _this.heightSize();
      })
    },
  },
  mounted() {
    var _this = this
    _this.catalogueList();
    _this.areaSize();
    window.collapseState = () => {
      _this.isState = !_this.isState;
      if (_this.isState == false) {
        $(".el-menu-vertical-demo").removeClass("is-collapse") 
      } else {
        $(".el-menu-vertical-demo").addClass("is-collapse");
      }
    }
    window.menuHeight = () => {
      _this.areaSize();
    }
  },
}
</script>
<style>
/* 左侧目录 */
.el-aside {
  position: relative;
  color: var(--text-color);
  text-align: center;
  overflow: hidden !important;
  background-color: var(--aside-bgColor);
}
.el-menu-vertical-demo.el-menu {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

.el-menu-vertical-demo {
  transition: all 0.28s ease-in-out; /* 默认过渡效果 */
}

.el-menu-vertical-demo.is-collapse.el-menu--collapse {
  width: 150px !important;
}

.el-menu--collapse {
  width: 50px !important
}

.el-menu-vertical-demo.is-collapse.el-menu--collapse>.el-menu-item span,
.el-menu-vertical-demo.is-collapse.el-menu--collapse>.el-submenu>.el-submenu__title span {
  width: auto;
  height: auto;
  visibility: inherit;
}

/* 目录 */
.el-menu {
  border: none !important;
  text-align: left;
  background-color: var(--aside-bgColor) !important;
}

/* 一级目录 */
.el-menu-item,
.el-submenu__title {
  height: 50px !important;
  line-height: 50px !important;
  color: var(--aside-color) !important;
  font-size: 14px !important;
  padding: 0px 15px 0px 10px !important;
  display: flex;
  align-items: center;
  opacity: 0.8;
}
/* 菜单图标 */
.blackTheme .el-menu-vertical-demo img,
.blackTheme .el-menu--vertical img {
  filter: invert(100%);
}
/* 菜单标题 */
.el-menu-item>span,
.el-submenu__title>span {
  flex: 1;
  line-height: 1.6;
  text-overflow: ellipsis;
}
/* 展开目录样式 */
.el-menu-vertical-demo.is-collapse.el-menu--collapse .el-menu-item>div,
.el-menu-vertical-demo.is-collapse.el-menu--collapse .el-submenu__title {
  padding: 0px 15px 0px 10px !important;
}

.el-menu-item>img,
.el-menu-item>div>img,
.el-menu-vertical-demo.is-collapse.el-menu--collapse .el-submenu .el-submenu__title img,
.el-menu-vertical-demo.is-collapse.el-menu--collapse .el-submenu .el-menu-item img {
  margin-right: 8px !important;
  width: 22px !important;
  height: 22px !important;
}

/* 折叠目录样式 */
.el-menu-vertical-demo.el-menu--collapse .el-menu-item>div,
.el-menu-vertical-demo.el-menu--collapse .el-submenu__title {
  padding: 0px 13px !important;
}

/* 菜单滑动样式 */
.el-menu-item:focus,
.el-menu-item:hover,
.el-submenu__title:hover {
  background-color: var(--aside-hover) !important;
  opacity: 1;
}

/* 右侧图标 */
.el-submenu__icon-arrow {
  right: 10px !important;
  font-size: 13px !important;
  margin-top: -6px !important;
  font-weight: bold !important;
  color: var(--aside-color) !important;
}

.el-menu-vertical-demo.is-collapse .el-breadcrumb__item:last-child .el-breadcrumb__separator,
.el-menu-vertical-demo.is-collapse.el-menu--collapse>.el-menu-item .el-submenu__icon-arrow,
.el-menu-vertical-demo.is-collapse.el-menu--collapse>.el-submenu>.el-submenu__title .el-submenu__icon-arrow {
  display: block;
  transform: rotate(0deg);
}

/* 二级目录 */
.el-menu--vertical .el-menu--popup {
  min-width: 200px !important;
}
.el-menu--vertical .el-menu-item {
  height: 42px !important;
  line-height: 42px !important;
  font-size: 14px !important;
}
.el-submenu.is-active .el-submenu__title {
  color: var(--aside-color) !important;
  opacity: 1;
}

.el-menu--vertical .el-menu-item.is-active:hover,
.el-menu--vertical .el-menu-item.is-active {
  background-color: var(--theme-color) !important;
  color: #fff !important;
  opacity: 1;
}
</style>
