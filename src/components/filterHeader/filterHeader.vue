<template>
  <div @click.stop class="tableFilter">
    <el-popover
      ref="myPopover"
      placement="bottom"
    >
      <span slot="reference" @click="fieldClick($event)" :class="labelColor">
        {{fieldName}}
        <svg-icon icon-class="catalogFilter"></svg-icon>
      </span>
      <div class="filterArea">
        <!-- 单个文本框 -->
        <div v-if="filterType == 'text'">
          <el-input
            v-model.trim="conditions.text"
            clearable
            placeholder="请输入筛选内容"
            @keyup.native.enter="filterClick()"
          />
        </div>
        <!-- 数值范围 -->
        <div v-else-if="filterType == 'number'">
          <el-input
            v-model.trim="conditions.number1"
            type="number"
            step="0.01"
            placeholder="请输入开始数值"
          />
          <el-input
            v-model.trim="conditions.number2"
            step="0.01"
            style="margin-top: 10px"
            placeholder="请输入结束数值"
          />
        </div>
        <!-- 日期-->
        <div v-else-if="filterType == 'date'">
          <el-date-picker
            v-model="conditions.date1"
            type="date"
            clearable
            placeholder="开始时间"
            value-format="yyyy-MM-dd"
          />
          <el-date-picker
            v-model="conditions.date2"
            style="margin-top: 10px"
            type="date"
            clearable
            placeholder="结束时间"
            value-format="yyyy-MM-dd"
          />
        </div>
        <!-- 下拉框-->
        <div v-else-if="filterType == 'select'">
          <el-select
            v-model="conditions.select"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="(item, index) in customArrList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <!-- 复选框-->
        <div v-else-if="filterType == 'checkbox'">
          <el-checkbox-group v-model="conditions.checkbox">
            <el-checkbox
              v-for="(item, index) in customArrList"
              :key="index"
              :label="item.value"
            >{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <!--单选按钮-->
        <div v-else-if="filterType == 'radio'">
          <el-radio-group v-model="conditions.radio">
            <el-radio
              v-for="(item, index) in customArrList"
              :key="index"
              :label="item.value"
            >{{ item.label }}</el-radio>
          </el-radio-group>
        </div>
      </div> 
      <!-- confirm 确定框-->
      <div class="filterButton">
        <el-button type="primary" @click="filterClick">筛选</el-button>
        <el-button plain @click="resetClick">重置</el-button>
      </div>
    </el-popover>
  </div>
  </template>
  <script>
  export default {
    name: 'FilterHeader',
    props: {
      column: {
        type: Object,
        defalut: null
      },
      fieldName: {
        type: String,
        defalut: ''
      },
      fieldProp: {
        type: String,
        default: '',
      },
      filterType: {
        type: String,
        defalut: 'txt'
      },
      customArrList: {
        type: Array,
        defalut: []
      }
    },
    data() {
      return {
        conditions: {
          text: '',
          number1: '',
          number2: '',
          date1: '',
          date2: '',
          select: '',
          checkbox: [],
          radio: ''
        }
      }
    },
    computed: {
      // 有条件的话高亮显示标题
      labelColor() {
        switch (this.filterType) {
          case 'text':
            if (this.conditions.text) { return 'heighLight' }
            return ''
          case 'number':
            if (this.conditions.number1 || this.conditions.number2) { return 'heighLight' }
            return ''
          case 'date':
            if (this.conditions.date1 || this.conditions.date2) { return 'heighLight' }
            return ''
          case 'select':
            if (this.conditions.select) { return 'heighLight' }
            return ''
          case 'checkbox':
            if (this.conditions.checkbox.length > 0) { return 'heighLight' }
            return ''
          case 'radio':
            if (this.conditions.radio !== '') { return 'heighLight' }
            return ''
        }
        return ''
      }
    },
    methods: {
      fieldClick(event) {
        event.stopPropagation();
        $(".el-popover").hide();
        $(event.target).show();
      },
      // 筛选
      filterClick() {
        this.$refs.myPopover.doClose()
        this.$emit('tableFilter', {
          filterType: this.filterType,
          fieldName: this.fieldName,
          fieldProp: this.fieldProp,
          conditions: this.conditions
        })
      },
      // 重置
      resetClick() {
        switch (this.filterType) {
          case 'text':
            this.conditions.text = ''
            break
          case 'number':
            this.conditions.number1 = ''
            this.conditions.number2 = ''
            break
          case 'date':
            this.conditions.date1 = ''
            this.conditions.date2 = ''
            break
          case 'select':
            this.conditions.select = ''
            break
          case 'checkbox':
            this.conditions.checkbox = []
            break
          case 'radio':
            this.conditions.radio = ''
            break
        }
        this.$refs.myPopover.doClose()
        this.$emit('resetFilter', {
          filterType: this.filterType,
          fieldName: this.fieldName,
          fieldProp: this.fieldProp,
          conditions: this.conditions
        })
      }
    },
    mounted() {
      window.clearAllFilter = () => {
        var list = this.$parent.$parent.$parent.$parent.$parent.conditionsFields;
        if (list.length > 0) { 
          list.forEach((item, index) => {
            list[index].conditions[item.filterType] = ""
          })
          list = [];
        }
        $(".el-popover").remove();
      }
    },
  }
  </script>
  <style>
    .tableFilter {
      display: inline-block
    }
    .tableFilter .svg-icon {
      width: 13px !important;
      height: 13px !important;
      color: #b6bcc6 !important;
    }
    .el-popover {
      min-width: 200px
    }
    .el-popover .filterArea .el-input {
      margin-top: 6px;
    }
    .el-popover .filterArea .el-input .el-input__inner {
      width: 220px !important;
    }
    .el-popover .filterArea .el-radio,
    .el-popover .filterArea .el-checkbox {
      display: block !important;
      margin: 5px 0;
    }
    .el-popover .filterButton {
      text-align: right;
      margin-top: 10px;
    }
    .el-popover .filterButton .el-button {
      font-size: 12px;
      padding: 0 10px;
      height: 30px;
    }
    .label {
      user-select: none;
    }
    .heighLight {
      color: var(--theme-color) !important;
    }
    .el-table thead .heighLight .svg-icon {
      color: var(--theme-color) !important;
    }
  </style>
  