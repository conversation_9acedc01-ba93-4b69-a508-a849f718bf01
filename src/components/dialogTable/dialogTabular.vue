<template>
  <div class="dialogContainer supplierContainer">
    <div class="dialogSearch">
      <el-form :inline="true" :label-width="$labelFour" class="demo-form-inline">
        <el-form-item label="商品编码" prop="searchCode">
          <el-input v-model.trim="searchCode" placeholder="请输入商品编码"></el-input>
        </el-form-item>
        <el-form-item label="商品名称" prop="searchName">
          <el-input v-model.trim="searchName" placeholder="请输入商品名称"></el-input>
        </el-form-item>
        <el-checkbox v-model="enableState">
          <span>仅显示库存不足</span>
          <!-- <span v-else>仅显示启用</span> -->
          <!-- <span v-else>仅显示生效</span> -->
        </el-checkbox>
        <el-button type="primary" @click="searchClick()">{{ $t('button.search') }}</el-button>
        <el-button plain @click="resetClick()">{{ $t('button.reset') }}</el-button>
      </el-form>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="8" class="leftData">
          <div>
            <el-table border class="tableHeaderArea" >
              <el-table-column prop="title">
                <template slot="header">
                  <p>商品列表</p>
                </template>
              </el-table-column>
            </el-table>
            <el-table
              :data="paramList"
              :height="paramHeight"
              style="width: 100%;"
              ref="table"
              highlight-current-row
              border
              stripe
              row-key="id"
              @header-dragend="changeColWidth"
              @row-click="handelRowClick"
            >
              <el-table-column type="index" width="50" label="序号"></el-table-column>
              <template v-for="column in tableColumns">
                <el-table-column
                  v-if="!column.slotName"
                  :key="column.prop"
                  :prop="column.prop"
                  :label="column.label"
                  :min-width="column.minWidth"
                  :width="column.width"
                ></el-table-column>
              </template>
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
          </div>
        </el-col>
        <el-col :span="16">
          <el-row>
            <el-col :span="18" class="nationalInfo">
              <div>
                <el-table border class="tableHeaderArea" >
                  <el-table-column prop="title">
                    <template slot="header">
                      <p>物料列表</p>
                    </template>
                  </el-table-column>
                </el-table>
                <el-table
                  :data="detailList"
                  :height="detailHeight"
                  style="width: 100%;"
                  ref="rightTable"
                  highlight-current-row
                  border
                  stripe
                  row-key="id"
                  @header-dragend="changeColWidth"
                   @selection-change="handleSelection"
                >
                  <el-table-column type="selection" :reserve-selection="true" width="40" fixed="left" align="center"></el-table-column>
                  <el-table-column type="index" width="50" label="序号"></el-table-column>
                  <template v-for="column in detailColumns">
                    <el-table-column
                      v-if="!column.slotName"
                      :key="column.prop"
                      :prop="column.prop"
                      :label="column.label"
                      :min-width="column.minWidth"
                      :width="column.width"
                    >
                      <template v-if="column.prop === 'orderUsage'" #default="scope">
                        {{sumQuantity(scope.row)}}
                      </template>
                    </el-table-column>
                    <!-- <el-table-column
                      v-else
                      :key="column.prop || column.label"
                      :label="column.label"
                      :width="column.width"
                    > -->
                    <!-- <template slot-scope="scope"> -->
                      <!-- <slot :name="column.slotName" :row="scope.row" :$index="scope.$index"></slot> -->
                    <!-- </template> -->
                    <!-- </el-table-column> -->
                  </template>
                </el-table>
                <pagination v-show="detailTotal > 0" :total="detailTotal" :page.sync="detailPage" :limit.sync="detailPage" @pagination="handelRowClick" />
              </div>
            </el-col>
            <el-col :span="6" class="nationalSelect">
              <el-table
                style="width:100%"
                :height="maxHeight"
                border
                highlight-current-row
                :data="determineModelList"
                ref="applytable"
                :header-cell-style="{}"
                @cell-mouse-enter="cellMouseEnter"
                @cell-mouse-leave="cellLeaveEnter"
              >
            <el-table-column prop="title">
              <template slot="header">
                <div class="authorityTitle">
                  <div>
                    <span>已选择(<b> {{ selectNum }} </b>)条</span>
                  </div>
                  <div>
                    <span class="deleteRed-icon clearAction" @click="emptyCountry">清空</span>
                  </div>
                </div>
              </template>
              <template slot-scope="scope">
                <div :class="'nationalList ' + '_' + scope.row.id">
                  <span>{{ scope.row.code }} {{  scope.row.name }}</span>
                  <i class="el-icon-close" @click="deleteEmpower(scope.row)"></i>
                </div>
              </template>
            </el-table-column>
          </el-table>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="submitArea">
      <div style="float: left;" v-if="false">
        <el-button plain @click="supplierAdd">新增</el-button>
      </div>
      <el-button type="primary" @click="supplierSubmit()">{{ $t('button.confirm') }}</el-button>
      <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import {getBomListData, getProductData, getProductSaleNoData} from "@/views/basic/basicCommon";
import {inventoryColumns, materialColumns, orderColumns} from "@/assets/js/tableHeader"

export default {
  name: "addSupplier",
  components: { Pagination },
  props: {
    type: {
      type: String,
      required: true
    },
    paramId: {
      type: String,
    },
    dialogStatue: {
      type: Boolean,
      required: true
    },
    formList: {
      type: Array,
      required: true
    },
    columns: {
      type:Array,
      required: true,
      validator: (cols) => cols.every(col => col.label && (col.prop || col.slotName))
    }
  },
  computed: {
    tableColumns() {
      return this.columns.filter(col => !col.hidden)
    }
  },
  data() {
    return {
      detailColumns: [], // 表格Columns
      searchCode:"",
      searchName:"",
      enableState: false,
      status: "",
      paramList: [], // 商品列表
      detailList:[], // 物料列表
      pagesize: 10,
      currentPage: 1,
      total: 0,
      detailSize: 10,
      detailPage: 1,
      detailTotal: 0,
      paramHeight: 0,
      detailHeight:0,
      maxHeight:0,
      // 选中的对象
      determineModelList: [],
      // 选中的数量
      selectNum: 0,
      selectAllList: [],
      handleQuantity: "",
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    getTableColumns() {
      if (this.type === "bomProduct") {
        this.detailColumns = materialColumns;
      }
      if (this.type === "saleProduct") {
        this.detailColumns = materialColumns.slice(0,2).concat(orderColumns.slice(0, 2)).concat(inventoryColumns)
      }
    },
    // 搜索
    searchClick() {
      this.currentPage = 1;
      this.dataList();
    },
    // 重置
    resetClick() {
      this.searchCode = "";
      this.searchName = "";
      this.enableState = false;
      this.currentPage = 1;
      this.dataList();
    },
    // 列表数据
    dataList() {
      const list = async () => {
        this.status = this.enableState ? 1 : "";
        if (this.type === "bomProduct") {
          this.paramList = await getProductData(this, "", this.paramId, this.enableState);
        }
        if (this.type === "saleProduct") {
          const result = await getProductSaleNoData(this, this.paramId);
          result.forEach(row => {
            row.code = row.productCode
            row.name = row.productName
          })
          this.paramList = result;
        }
        this.sizeArea()
      }
      list()
    },
    //计算用量
    sumQuantity(row) {
      if (!row.quantity){
        return 0;
      }
      let number = row.quantity * this.handleQuantity;
      row.orderUsage = number;
      return number;
    },
    // 左侧列表选择
    handelRowClick(node) {
      if (node.quantity){
        this.handleQuantity = node.quantity
      }
      const list = async () => {
        if (this.type === "bomProduct") {
          this.detailList = await getBomListData(this, node.id);
          if (this.detailList.length === 0){
            const newRow = {...node}
            newRow.code = node.code
            newRow.name = node.name
            newRow.materialId = node.id
            newRow.productId = node.id
            this.detailList.push(newRow);
            this.detailTotal = this.detailList.length
          }
        }
        if (this.type === "saleProduct") {
          this.detailList = await getBomListData(this, node.productId);
          if (this.detailList.length === 0){
            const newRow = {...node}
            newRow.materialId = node.productId
            newRow.code = node.productCode
            newRow.name = node.productName
            newRow.quantity = 1
            this.detailList.push(newRow);
            this.detailTotal = this.detailList.length
          }
        }

        this.sizeArea()
      }
      list()
    },
    // 表格多选
    handleSelection(selection) {
      this.determineModelList = selection;
      this.selectNum = this.determineModelList.length
    },
    // 删除
    cellMouseEnter(row) {
      $(".nationalList._" + row.id + " .el-icon-close").show()
    },
    cellLeaveEnter() {
      $(".nationalList .el-icon-close").hide()
    },
    deleteEmpower(row) {
      let _this = this
      _this.determineModelList.forEach(itm => {
        if (itm.id === row.id) {
          _this.$refs.table.toggleRowSelection(row, false)
        }
      })

    },
    // 清空
    emptyCountry() {
      this.determineModelList = [];
      this.handleSelection([])
      this.$refs.rightTable.clearSelection();
      this.$refs.applytable.clearSelection();
    },
    // 新增
    supplierAdd() {
      // if (this.type == "supplier") {
      //   addSupplierInfo(null);
      // }
      // if (this.type == "product") {
      //   addProductInfo(null);
      // }
    },
    // 提交
    supplierSubmit() {
      const list = this.formList;
      let sign = "";
      if (this.type === "bomProduct" ||　this.type === "saleProduct") {
        sign = "productId";
        this.determineModelList.forEach(row => {
          if (this.type === "saleProduct"){
            row.productId = row.materialId;
            row.quantity = (row.orderUsage === '' ? 1 : row.orderUsage)
          }else if (this.type === "bomProduct"){
            row.productId = row.materialId;
          }
          this.$set(row, 'quantity', !row.quantity ? 1 :　row.quantity);
          this.$set(row, 'unitPrice', null);
          this.$set(row, 'contactsId', null);
          this.$set(row, 'contactPhone', '');
          this.$set(row, 'referLink', '');
        })
      }
      const newList = [...new Map([...list, ...this.determineModelList].map(item => [item[sign], item])).values()]
        .map(item => {
          const original = list.find(i => i[sign] === item[sign]);
          return original ? { ...original, ...item } : item; // 合并字段
        });
      this.$emit("update:formList", newList);
      this.$emit("update:dialogStatue", false);
    },
    cancelClick() {
      this.$emit("update:dialogStatue", false);
    },
    heightArea() {
      setTimeout(() => {
        if ($(".supplierContainer .infoDetail").length != 0) {
          var allHeight = $(".supplierContainer .infoDetail").height();
          var topHeight = $(".el-table.tableHeaderArea").outerHeight(true);
          var paramPageVal = 0
          var displayParam = $(".leftData .pagination-container").css("display");
          if ($(".leftData .pagination-container").length != 0 && displayParam != "none") {
            paramPageVal = $(".leftData .pagination-container").outerHeight(true);
          }
          var detailPageVal = 0;
          var displayPage = $(".nationalInfo .pagination-container").css("display");
          if ($(".nationalInfo .pagination-container").length != 0 && displayPage != "none") {
            detailPageVal = $(".nationalInfo .pagination-container").outerHeight(true);
          }
          this.paramHeight = allHeight - topHeight - paramPageVal;
          this.detailHeight = allHeight - topHeight - detailPageVal;
          this.maxHeight = allHeight;
        }
      });
    },
    sizeArea() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function() {
        _this.heightArea();
      })
    },
  },
  mounted() {
    this.determineModelList = [];
    // this.getCategoryData();
    this.dataList()
    this.getTableColumns();
  },
}
</script>
<style>
.dialogContainer .dialogSearch .el-input {
  width: 350px !important;
  margin-right: 10px;
}
.dialogContainer .dialogSearch .el-form .el-input {
  width: 200px !important;
}
.dialogContainer .dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}
/* el-table */
.dialogContainer .dialogSearch .el-input .el-input__inner {
  width: 100% !important;
}
.dialogContainer .dialogSearch .el-checkbox {
  margin-right: 10px !important;
}
.el-table.tableHeaderArea .el-table__header-wrapper thead th {
  background: transparent !important;
}
.el-table.tableHeaderArea .el-table__body-wrapper{
  display: none;
}
.el-table.tableHeaderArea::before {
  height: 0;
}
.el-table.tableHeaderArea th.el-table__cell.is-leaf {
  border-bottom: none;
}
/* .el-dialog */
.dialogContainer .infoDetail {
  height: 575px;
}
.dialogContainer .nationalList i {
  cursor: pointer;
  display: none;
  font-size: 13px;
  font-weight: bold;
}
.dialogContainer .nationalList i:hover{
  color: #d91c1c;
}
/*.dialogContainer .infoDetail .statusIcon {
  display: none;
  border: 1px solid var(--border-color);
  text-align: center;
  background-color: var(--manual-bgColor) !important;
}
.dialogContainer .infoDetail .statusIcon i {
  margin-top: 8px;
  font-size: 16px;
  margin-bottom: 5px;
}
.dialogContainer .infoDetail .statusIcon div span {
  writing-mode: vertical-lr;
}
.dialogContainer .infoDetail .leftData>div .scrollClass {
  padding: 0;
}
.dialogContainer .infoDetail .topButton.otherContent {
  display: flex;
  justify-content: space-between;
}
.dialogContainer .infoDetail .scrollClass {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
} */
.dialogContainer .pagination-container {
  border-bottom: 1px solid var(--table-border);
  border-left: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border);
}
</style>
