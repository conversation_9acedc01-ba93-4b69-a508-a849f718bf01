<template>
  <el-dialog
    v-dialogDrag
    append-to-body
    width="1300px !important"
    :visible.sync="dialogFormVisible"
    :title="titleMap[type]"
    @close="closeDialog"
    v-if="dialogFormVisible"
  >
    <div class="dialogContainer supplierContainer">
      <div class="dialogSearch">
        <el-form :inline="true" :label-width="$labelTwo" class="demo-form-inline">
          <div v-if="type == 'material'">
            <el-form-item label="编码" prop="searchCode">
              <el-input v-model.trim="searchCode" placeholder="请输入编码"></el-input>
            </el-form-item>
            <el-form-item label="名称" prop="searchName">
              <el-input v-model.trim="searchName" placeholder="请输入名称"></el-input>
            </el-form-item>
          </div>

          <div v-if="type == 'product'">
            <el-form-item label="商品编码" prop="searchCode">
              <el-input v-model.trim="searchCode" placeholder="请输入编码"></el-input>
            </el-form-item>
            <el-form-item label="商品名称" prop="searchName">
              <el-input v-model.trim="searchName" placeholder="请输入名称"></el-input>
            </el-form-item>
          </div>

          <el-checkbox v-model="enableState" v-if="type == 'material'">
            <span v-if="type == 'material'">仅显示库存不足</span>
<!--            <span v-else>仅显示启用</span>-->
            <!-- <span v-else>仅显示生效</span> -->
          </el-checkbox>
          <el-button type="primary" @click="searchClick()">{{ $t('button.search') }}</el-button>
          <el-button plain @click="resetClick()">{{ $t('button.reset') }}</el-button>
        </el-form>
      </div>
      <div class="infoDetail">
        <el-row>
          <el-col :span="4" class="leftData">
            <div class="statusIcon" @click="leftOperate">
              <i class="el-icon-d-arrow-right"></i>
              <div>
                <span>{{ textMap[type] }}</span>
              </div>
            </div>
            <div class="categoryLeft">
              <div class="topButton otherContent">
                <div>{{ textMap[type] }}</div>
                <div><i class="el-icon-d-arrow-left" @click="leftOperate"></i></div>
              </div>
              <div class="scrollClass elTreeStyle">
                <!-- <el-scrollbar> -->
                  <el-tree :data="categoryTreeList" node-key="id" :render-content="renderContent"
                    :default-expand-all="true" @node-click="handleCategorySelect" :props="{
                      label: 'name',
                      children: 'children'
                    }" ref="categoryTree"></el-tree>
                <!-- </el-scrollbar> -->
              </div>
            </div>
          </el-col>
          <el-col :span="20">
            <el-row>
              <el-col :span="18" class="nationalInfo">
                <div>
                  <el-table
                    :data="detailList"
                    :height="tableHeight"
                    style="width: 100%;"
                    ref="table"
                    highlight-current-row
                    border
                    stripe
                    row-key="id"
                    @header-dragend="changeColWidth"
                    @selection-change="handleSelection"
                  >
                    <el-table-column type="selection" :reserve-selection="true" width="40" fixed="left" align="center"></el-table-column>
                    <el-table-column type="index" width="50" label="序号"></el-table-column>
                    <template v-for="column in tableColumns">
                      <el-table-column
                        v-if="!column.slotName"
                        :key="column.prop"
                        :prop="column.prop"
                        :label="column.label"
                        :min-width="column.minWidth"
                        :width="column.width"
                      >
                        <template v-if="column.prop === 'status'" #default="scope">
                          <span class="successColor" v-if="scope.row.status === 1">启用</span>
                          <span class="errorColor" v-if="scope.row.status === 0">禁用</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-else
                        :key="column.prop || column.label"
                        :label="column.label"
                        :width="column.width"
                      >
                        <template slot-scope="scope">
                          <slot :name="column.slotName" :row="scope.row" :$index="scope.$index"></slot>
                        </template>
                      </el-table-column>
                    </template>
                  </el-table>
                  <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
                </div>
              </el-col>
              <el-col :span="6" class="nationalSelect">
                <el-table
                  style="width:100%"
                  :height="maxHeight"
                  border
                  highlight-current-row
                  :data="determineModelList"
                  ref="applytable"
                  :header-cell-style="{}"
                  @cell-mouse-enter="cellMouseEnter"
                  @cell-mouse-leave="cellLeaveEnter"
                >
              <el-table-column prop="title">
                <template slot="header">
                  <div class="authorityTitle">
                    <div>
                      <span>已选择(<b> {{ selectNum }} </b>)条</span>
                    </div>
                    <div>
                      <span class="deleteRed-icon clearAction" @click="emptyCountry">清空</span>
                    </div>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div :class="'nationalList ' + '_' + scope.row.id">
                    <span>{{ scope.row.code }} {{  scope.row.name }}</span>
                    <i class="el-icon-close" @click="deleteEmpower(scope.row)"></i>
                  </div>
                </template>
              </el-table-column>
            </el-table>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <div class="submitArea">
        <div style="float: left;" v-if="false">
          <el-button plain @click="supplierAdd">新增</el-button>
        </div>
        <el-button type="primary" @click="supplierSubmit()">{{ $t('button.confirm') }}</el-button>
        <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import Pagination from '@/components/Pagination'
import { productCategoryTree, getCategoryTreeData, addSupplierInfo, getSupplierData, getProductData, addProductInfo } from "@/views/basic/basicCommon";
import { renderTree, tableHeight } from "@/assets/js/common";
export default {
  name: "addSupplier",
  components: { Pagination },
  props: {
    type: {
      type: String,
      required: true
    },
    paramId: {
      type: String,
    },
    formList: {
      type: Array,
      required: true
    },
    isReload: {
      type: Boolean,
      required: true
    },
    selectType: {
      type: String,
    },
    columns: {
      type:Array,
      required: true,
      validator: (cols) => cols.every(col => col.label && (col.prop || col.slotName))
    }
  },
  computed: {
    tableColumns() {
      return this.columns.filter(col => !col.hidden)
    }
  },
  data() {
    return {
      dialogFormVisible: false, // 弹框
      searchCode:"",
      searchName:"",
      categoryId: "",
      enableState: true,
      status: "",
      categoryTreeList: [],
      detailList:[],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      tableHeight:0,
      maxHeight:0,
      // 选中的对象
      determineModelList: [],
      // 选中的数量
      selectNum: 0,
      selectAllList: [],
      titleMap: {
        supplier: "添加供应商",
        product: "添加商品",
        material: "按物料添加",
      },
      textMap: {
        supplier: "供应商类别",
        product: "商品类别",
        material: "商品类别",
      },
      deleteId: "",
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    renderContent(h, { node, data }) {
      var dataName = "";
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon";
      }
      renderTree(".scrollClass");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 关闭弹框
    closeDialog() {
      this.dialogFormVisible = false,
      this.$emit("update:isReload", false);
    },
    // 展开收起
    leftOperate() {
      var display = $(".infoDetail > .el-row > .leftData > .categoryLeft").css("display");
      if (display == "none") {
        $(".infoDetail > .el-row > .leftData").removeAttr("style");
        $(".infoDetail > .el-row > .leftData > .statusIcon").hide();
        $(".infoDetail > .el-row > .leftData > .categoryLeft").show();
        $(".infoDetail > .el-row > .el-col:last-child").removeAttr("style");
      } else {
        $(".infoDetail > .el-row > .leftData").css("width", "50px");
        $(".infoDetail > .el-row > .leftData > .statusIcon").show();
        $(".infoDetail > .el-row > .leftData > .categoryLeft").hide();
        $(".infoDetail > .el-row > .el-col:last-child").css("width", "calc(100% - 50px)");
      }
    },
    // 类别数据
    getCategoryData() {
      const tree = async() => {
        if (this.type == "product" || this.type == "material") {
          var result = await productCategoryTree();
          this.categoryTreeList = result;
        } else {
          var result = await getCategoryTreeData(this.type);
          this.categoryTreeList = result;
        }
        this.$nextTick(() => {
          if (this.categoryTreeList.length > 0) {
            this.$refs.categoryTree.setCurrentKey(this.categoryTreeList[0]); // 选中第一个节点
          }
        });
      }
      tree();
    },
    // 树结构点击事件
    handleCategorySelect(row) {
      this.categoryId = row.id;
      this.currentPage = 1;
      this.dataList();
    },
    // 搜索
    searchClick() {
      this.currentPage = 1;
      this.dataList();
    },
    // 重置
    resetClick() {
      this.searchCode = "";
      this.searchName = "";
      this.enableState = false;
      this.currentPage = 1;
      this.dataList();
    },
    // 列表数据
    dataList() {
      const list = async () => {
        let result;
        this.status = this.enableState ? 1 : '';
        if (this.type === "supplier") {
          result = await getSupplierData(this);
          this.detailList = result;
        }
        if (this.type === "product" || this.type === "material") {
          if (this.type === "product") {
            this.enableState = true
          }
          result = await getProductData(this, this.categoryId, this.paramId, this.enableState, this.selectType);
          this.detailList = result;
        }
        if (this.deleteId) {
          this.deleteSelect(this.detailList);
        }
        this.sizeArea()
      }
      list()
    },
    // 表格多选
    handleSelection(selection) {
      this.determineModelList = selection;
      this.selectNum = this.determineModelList.length;
    },
    // 删除
    cellMouseEnter(row) {
      $(".nationalList._" + row.id + " .el-icon-close").show()
    },
    cellLeaveEnter() {
      $(".nationalList .el-icon-close").hide()
    },
    deleteSelect(newData) {
      var _this = this;
      setTimeout(() => {
        newData.forEach((item) => {
          if (item.id === _this.deleteId) {
            _this.$refs.table.toggleRowSelection(item, false)
            _this.deleteId = "";
          }
        })
      })
    },
    deleteEmpower(row) {
      let _this = this;
      _this.deleteId = row.id
      var newData = []
      if (_this.categoryId == row.categoryId || _this.categoryId == "") {
        newData = _this.detailList;
        _this.deleteSelect(newData);
      } else {
        _this.$refs.categoryTree.setCurrentKey(row.categoryId);
        _this.categoryId = row.categoryId;
        _this.currentPage = 1;
        _this.dataList();
      }
    },
    // 清空
    emptyCountry() {
      this.determineModelList = [];
      this.handleSelection([])
      this.$refs.table.clearSelection();
      this.$refs.applytable.clearSelection();
    },
    // 新增
    supplierAdd() {
      if (this.type == "supplier") {
        addSupplierInfo(null);
      }
      if (this.type == "product") {
        addProductInfo(null);
      }
    },
    // 提交
    supplierSubmit() {
      var list = this.formList;
      let sign = "";
      if (this.type === 'supplier') {
        sign = "supplierId";
        this.determineModelList.forEach(row => {
          row.supplierId = row.id;
          row.supplierCode = row.code;
          row.supplierName = row.name;
        })
      }
      if (this.type === "product") {
        sign = "productId";
        this.determineModelList.forEach(row => {
          row.productId = row.id;
          row.productCode = row.code;
          row.productName = row.name;
        })
      }
      if (this.type === "material") {
        sign = "productId";
        this.determineModelList.forEach(row => {
          row.productId = row.id;
          this.$set(row, 'quantity', !row.quantity ? 1 :　row.quantity);
          this.$set(row, 'unitPrice', null);
          this.$set(row, 'contactsId', null);
          this.$set(row, 'contactPhone', '');
          this.$set(row, 'referLink', '');
        })
      }
      const newList = [...new Map([...list, ...this.determineModelList].map(item => [item[sign], item])).values()]
        .map(item => {
          const original = list.find(i => i[sign] === item[sign]);
          return original ? { ...original, ...item } : item; // 合并字段
        });
      this.$emit("update:formList", newList);
      this.closeDialog();
    },
    cancelClick() {
      this.closeDialog();
    },
    heightArea() {
      var allHeight = $(".supplierContainer .infoDetail").height();
      var topHeight = $(".infoDetail .topButton").outerHeight(true);
      var leftVal = allHeight - topHeight;
      $(".supplierContainer .infoDetail .scrollClass").css("height", leftVal);
      var pageHeight = 0;
      var displayVal = $(".nationalInfo .pagination-container").css("display");
      if ($(".nationalInfo .pagination-container").length != 0 && displayVal != "none") {
        pageHeight = $(".nationalInfo .pagination-container").outerHeight(true);
      }
      this.tableHeight = allHeight - pageHeight;
      this.maxHeight = allHeight;
    },
    sizeArea() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function() {
        _this.heightArea();
      })
    },
  },
  mounted() {
    this.determineModelList = [];
    this.dialogFormVisible = true;
    this.getCategoryData();
    this.dataList()
  },
}
</script>
<style>
.dialogContainer .dialogSearch .el-input {
  width: 350px !important;
  margin-right: 10px;
}
.dialogContainer .dialogSearch .el-form .el-input {
  width: 200px !important;
}
.dialogContainer .dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.dialogContainer .dialogSearch .el-input .el-input__inner {
  width: 100% !important;
}
.dialogContainer .dialogSearch .el-checkbox {
  margin-right: 10px !important;
}
/* .el-dialog */
.dialogContainer .infoDetail {
  height: 550px;
}
.dialogContainer .infoDetail .statusIcon {
  display: none;
  border: 1px solid var(--border-color);
  text-align: center;
  background-color: var(--manual-bgColor) !important;
}
.dialogContainer .infoDetail .statusIcon i {
  margin-top: 8px;
  font-size: 16px;
  margin-bottom: 5px;
}
.dialogContainer .infoDetail .statusIcon div span {
  writing-mode: vertical-lr;
}
.dialogContainer .infoDetail .leftData>div .scrollClass {
  padding: 0;
}
.dialogContainer .infoDetail .topButton.otherContent {
  display: flex;
  justify-content: space-between;
}
.dialogContainer .infoDetail .scrollClass {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
.dialogContainer .pagination-container {
  border-bottom: 1px solid var(--table-border);
  border-left: 1px solid var(--table-border);
}
.dialogContainer .nationalList i {
  cursor: pointer;
  display: none;
  font-size: 13px;
  font-weight: bold;
}
.dialogContainer .nationalList i:hover{
  color: #d91c1c;
}
</style>
