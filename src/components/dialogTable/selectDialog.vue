<template>
  <el-dialog
    v-dialogDrag
    append-to-body
    width="1300px !important"
    :visible.sync="dialogFormVisible"
    :title="titleMap[type]"
    @close="closeDialog"
    v-if="dialogFormVisible"
  >
    <div v-cloak class="selectDialogContainer">
      <div class="dialogSearch">
        <el-form :inline="true" :label-width="$labelTwo" class="demo-form-inline">
          <el-form-item label="编码" prop="searchCode">
            <el-input v-model.trim="searchCode" placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="searchName">
            <el-input v-model.trim="searchName" placeholder="请输入名称"></el-input>
          </el-form-item>
          <el-button type="primary" @click="searchClick()">{{ $t('button.search') }}</el-button>
          <el-button plain @click="resetClick()">{{ $t('button.reset') }}</el-button>
        </el-form>
      </div>
      <div class="infoDetail">
        <el-row>
          <el-col :span="4" class="leftData">
            <div class="statusIcon" @click="leftOperate">
              <i class="el-icon-d-arrow-right"></i>
              <div>
                <span>{{ textMap[type] }}</span>
              </div>
            </div>
            <div class="categoryLeft">
              <div class="topButton otherContent">
                <div>{{ textMap[type] }}</div>
                <div><i class="el-icon-d-arrow-left" @click="leftOperate"></i></div>
              </div>
              <div class="scrollClass elTreeStyle">
                <!-- <el-scrollbar> -->
                <el-tree :data="categoryTreeList" node-key="id" :render-content="renderContent"
                  :default-expand-all="true" @node-click="handleCategorySelect" :props="{
                    label: 'name',
                    children: 'children'
                  }" ref="categoryTree"></el-tree>
              <!-- </el-scrollbar> -->
              </div>
            </div>
          </el-col>
          <el-col :span="20" class="rightTable">
            <el-table
              :data="detailList"
              :height="tableHeight"
              style="width: 100%;"
              ref="table"
              highlight-current-row
              border
              stripe
              row-key="id"
              @header-dragend="changeColWidth"
              @row-click="handleRowClick"
            >
              <el-table-column width="40" fixed="left" align="center">
                <template slot-scope="{ row }">
                  <el-radio 
                    v-model="selectedRowId"
                    :label="row.id"
                  >
                    &nbsp;
                  </el-radio>
                </template>
              </el-table-column>
              <el-table-column type="index" width="50" label="序号"></el-table-column>
              <template v-for="column in tableColumns">
                <el-table-column
                  v-if="!column.slotName"
                  :key="column.prop"
                  :prop="column.prop"
                  :label="column.label"
                  :min-width="column.minWidth"
                  :width="column.width"
                >
                  <template v-if="column.prop === 'status'" #default="scope">
                    <span class="successColor" v-if="scope.row.status === 1">启用</span>
                    <span class="errorColor" v-if="scope.row.status === 0">禁用</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  :key="column.prop || column.label"
                  :label="column.label"
                  :width="column.width"
                >
                  <template slot-scope="scope">
                    <slot :name="column.slotName" :row="scope.row" :$index="scope.$index"></slot>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
          </el-col>
        </el-row>
      </div>
      <div class="submitArea">
        <el-button type="primary" @click="supplierSubmit()">{{ $t('button.confirm') }}</el-button>
        <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import Pagination from '@/components/Pagination'
import {
  getBrandData,
  getCategoryTreeData,
  getProductData,
  productCategoryTree,
  getCustomerData,
  getSupplierData,
} from "@/views/basic/basicCommon";
import { renderTree, tableHeight } from "@/assets/js/common";
export default {
  name: "selectDialog",
  components: { Pagination },
  props: {
    type: {
      type: String,
      required: true
    },
    paramId: {
      type: String,
    },
    isReload: {
      type: Boolean,
      required: true
    },
    inputValue: {
      type: String,
      required: true
    },
    columns: {
      type:Array,
      required: true,
      validator: (cols) => cols.every(col => col.label && (col.prop || col.slotName))
    }
  },
  computed: {
    tableColumns() {
      return this.columns.filter(col => !col.hidden)
    }
  },
  data() {
    return {
      dialogFormVisible: false, // 弹框
      // 搜索
      searchCode:"",
      searchName:"",
      categoryId: "",
      categoryTreeList: [], // 类别数据
      detailList:[], // 列表数据
      pagesize: 10,
      currentPage: 1,
      total: 0,
      tableHeight:0,
      maxHeight:0,
      titleMap: {
        brandInfo: "品牌信息",
        productInfo: "商品信息",
        customerInfo: "客户信息",
        supplierInfo: "供应商信息",
        partnerSupplierInfo: "往来单位信息",
        partnerCustomerInfo: "往来单位信息",
      },
      textMap: {
        brandInfo: "品牌类别",
        productInfo: "商品类别",
        customerInfo: "客户类别",
        supplierInfo: "供应商类别",
        partnerSupplierInfo: "往来单位类别",
        partnerCustomerInfo: "往来单位类别",
      },
      treeCurrentNode: null, // 选中数据
      selectedRowId: null,
      status: 1, // 启用
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    renderContent(h, { node, data }) {
      var dataName = "";
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon";
      }
      renderTree(".scrollClass");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 展开收起
    leftOperate() {
      var display = $(".infoDetail > .el-row > .leftData > .categoryLeft").css("display");
      if (display == "none") {
        $(".infoDetail > .el-row > .leftData").removeAttr("style");
        $(".infoDetail > .el-row > .leftData > .statusIcon").hide();
        $(".infoDetail > .el-row > .leftData > .categoryLeft").show();
        $(".infoDetail > .el-row > .el-col:last-child").removeAttr("style");
      } else {
        $(".infoDetail > .el-row > .leftData").css("width", "50px");
        $(".infoDetail > .el-row > .leftData > .statusIcon").show();
        $(".infoDetail > .el-row > .leftData > .categoryLeft").hide();
        $(".infoDetail > .el-row > .el-col:last-child").css("width", "calc(100% - 50px)");
      }
    },
    // 关闭弹框
    closeDialog() {
      this.dialogFormVisible = false,
      this.$emit("update:isReload", false);
    },
    // 类别数据
    getCategoryData() {
      const tree = async() => {
        if (this.type == "productInfo") {
          var result = await productCategoryTree();
          this.categoryTreeList = result;
        } else {
          var typeVal = "";
          if (this.type == "partnerSupplierInfo") {
            typeVal = "supplier";
          } else if (this.type == "partnerCustomerInfo") {
            typeVal = "customer";
          } else {
            typeVal = this.type;
          }
          var text = typeVal.replace("Info", "");
          var result = await getCategoryTreeData(text);
          this.categoryTreeList = result;
        }
        this.$nextTick(() => {
          if (this.categoryTreeList.length > 0) {
            this.$refs.categoryTree.setCurrentKey(this.categoryTreeList[0]); // 选中第一个节点
          }
        });
      }
      tree();
    },
    // 树结构点击事件
    handleCategorySelect(row) {
      this.categoryId = row.id;
      this.currentPage = 1;
      this.dataList();
    },
    // 搜索
    searchClick() {
      this.currentPage = 1;
      this.dataList();
    },
    // 重置
    resetClick() {
      this.searchCode = "";
      this.searchName = "";
      this.currentPage = 1;
      this.dataList();
    },
    // 列表数据
    dataList() {
      const list = async () => {
        // 商品信息
        if (this.type == "productInfo") {
          var result = await getProductData(this, this.categoryId, "", "");
          this.detailList = result;
        }
        // 品牌信息
        if (this.type == "brandInfo") {
          var result = await getBrandData(this);
          this.detailList = result;
        }
        // 客户信息
        if (this.type == "customerInfo" || this.type == "partnerCustomerInfo") {
          var result = await getCustomerData(this);
          this.detailList = result;
        }
        // 供应商信息
        if (this.type == "supplierInfo" || this.type == "partnerSupplierInfo") {
          var result = await getSupplierData(this);
          this.detailList = result;
        }
        this.sizeArea()
      }
      list()
    },
    // 列表数据选中
    handleRowClick(node) {
      this.treeCurrentNode = node;
      this.selectedRowId = node.id;
    },
    // 提交
    supplierSubmit() {
      this.$emit("update:inputValue", this.treeCurrentNode.name);
      this.$emit('select', this.treeCurrentNode);
      this.closeDialog()
    },
    cancelClick() {
      this.closeDialog()
    },
    heightArea() {
      var allHeight = $(".selectDialogContainer .infoDetail").height();
      var topHeight = $(".infoDetail .topButton").outerHeight(true);
      var leftVal = allHeight - topHeight;
      $(".selectDialogContainer .infoDetail .scrollClass").css("height", leftVal);
      var pageHeight = 0;
      var displayVal = $(".rightTable .pagination-container").css("display");
      if ($(".rightTable .pagination-container").length != 0 && displayVal != "none") {
        pageHeight = $(".rightTable .pagination-container").outerHeight(true);
      }
      this.tableHeight = allHeight - pageHeight;
      this.maxHeight = allHeight;
    },
    sizeArea() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function() {
        _this.heightArea();
      })
    },
  },
  mounted() {
    this.dialogFormVisible = true;
    console.log(this.type);
    this.getCategoryData();
    this.dataList()
  },
}
</script>
<style>
.selectDialogContainer .dialogSearch .el-input {
  width: 350px !important;
  margin-right: 10px;
}
.selectDialogContainer .dialogSearch .el-form .el-input {
  width: 200px !important;
}
.selectDialogContainer .dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}
.selectDialogContainer .dialogSearch .el-form-item {
  margin: 0 !important;
  margin-bottom: 20px !important;
}
.selectDialogContainer .dialogSearch .el-form--inline .el-form-item {
  margin-right: 10px !important;
}
.selectDialogContainer .dialogSearch .el-input .el-input__inner {
  width: 100% !important;
}
.selectDialogContainer .dialogSearch .el-checkbox {
  margin-right: 10px !important;
}
/* .el-dialog */
.selectDialogContainer .infoDetail {
  height: 550px;
}
.selectDialogContainer .infoDetail .statusIcon {
  display: none;
  border: 1px solid var(--border-color);
  text-align: center;
  background-color: var(--manual-bgColor) !important;
}
.selectDialogContainer .infoDetail .statusIcon i {
  margin-top: 8px;
  font-size: 16px;
  margin-bottom: 5px;
}
.selectDialogContainer .infoDetail .statusIcon div span {
  writing-mode: vertical-lr;
}
.selectDialogContainer .infoDetail .leftData>div .scrollClass {
  padding: 0;
}
.selectDialogContainer .infoDetail .topButton.otherContent {
  display: flex;
  justify-content: space-between;
}
.selectDialogContainer .infoDetail .scrollClass {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
.selectDialogContainer .pagination-container {
  border-bottom: 1px solid var(--table-border);
  border-left: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border);
}
/* .selectDialogContainer .nationalList i {
  cursor: pointer;
  display: none;
  font-size: 13px;
  font-weight: bold;
}
.selectDialogContainer .nationalList i:hover{
  color: #d91c1c;
} */
</style>
