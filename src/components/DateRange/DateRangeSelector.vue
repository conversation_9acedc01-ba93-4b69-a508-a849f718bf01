<template>
  <div class="date-range-selector">
    <div class="date-tabs">
      <span
        v-for="(item, index) in dateOptions"
        :key="index"
        :class="{ 'active': activeTab === item.value }"
        @click="handleTabClick(item.value)"
      >
        {{ item.label }}
      </span>
      <el-popover
        placement="bottom"
        width="300"
        trigger="click"
        v-model="showPopover"
      >
        <span slot="reference" class="more-btn" :class="!showPopover ? '': 'active'" @click="handleMoreClick">
          更多
          <i>
            <el-icon name="arrow-down" v-if="!showPopover"></el-icon>
            <el-icon name="arrow-up" v-else></el-icon>
          </i>
        </span>
        <div class="date-range-container">
<!--          <div class="date-range-header">自定义时间范围</div>-->
          <div class="date-range-content">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
            <!--              value-format="yyyy-MM-dd HH:mm:ss"-->

          </div>
          <div class="date-range-footer">
            <el-button plain @click="showPopover = false">取消</el-button>
            <el-button type="primary" @click="confirmDateRange">确定</el-button>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<script>
/**
 * 时间范围选择组件
 * @description 提供快捷的时间范围选择功能，包括今天、近天、近7天、近1月、近3月等选项，以及自定义时间范围
 */
export default {
  name: 'DateRangeSelector',
  props: {
    // 初始选中的时间范围类型
    defaultTab: {
      type: String,
      default: 'today'
    },
    // 是否显示自定义时间范围选项
    showCustomRange: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      firstCreated: true,
      dateRange:[],
      // 当前选中的标签
      // activeTab: this.defaultTab,
      activeTab: {},
      // 是否显示弹出框
      showPopover: false,
      // 开始日期
      startDate: '',
      // 结束日期
      endDate: '',
      // 日期选项
      dateOptions: [
        { label: '今天', value: 'today' },
        { label: '昨天', value: 'yesterday' },
        { label: '近7天', value: 'last7days' },
        { label: '近1月', value: 'last1month' },
        { label: '近3月', value: 'last3months' }
      ],
      // 开始日期选择器配置
      startPickerOptions: {
        disabledDate: (time) => {
          if (this.endDate) {
            return time.getTime() > new Date(this.endDate).getTime();
          }
          return false;
        }
      },
      // 结束日期选择器配置
      endPickerOptions: {
        disabledDate: (time) => {
          if (this.startDate) {
            return time.getTime() < new Date(this.startDate).getTime();
          }
          return false;
        }
      }
    };
  },
  created() {
    // 初始化日期范围
    this.initDateRange();
  },
  methods: {

    /**
     * 重置日期选择器状态
     */
    reset() {
      this.activeTab = {}
      this.dateRange = []
      this.startDate = ''
      this.endDate = ''
      this.showPopover = false

      // 发送重置事件
      this.$emit('change', {
        startDate: '',
        endDate: '',
        type: ''
      })
    },
    /**
     * 初始化日期范围
     */
    initDateRange() {
      this.handleTabClick(this.activeTab);
    },

    /**
     * 处理标签点击事件
     * @param {String} tabValue - 标签值
     */
    handleTabClick(tabValue) {
      if (this.firstCreated){
        this.firstCreated = false;
        return;
      }
      let start;
      let end;
      if (this.activeTab === tabValue){
        this.activeTab = {};
        start = '';
        end = '';
        tabValue = '';
      }else {
        this.activeTab = tabValue;
      }
      const today = new Date();

      switch (tabValue) {
        case 'today':
          // 今天
          start = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          end = today;
          break;
        case 'yesterday':
          // 昨天
          const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
          start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
          end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
          break;
        case 'last7days':
          // 近7天
          const last7days = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000);
          start = new Date(last7days.getFullYear(), last7days.getMonth(), last7days.getDate());
          end = new Date(today.getFullYear(), today.getMonth(), today.getDate(),23, 59, 59);
          break;
        case 'last1month':
          // 近1月
          const last1month = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
          start = new Date(last1month.getFullYear(), last1month.getMonth(), last1month.getDate());
          end = new Date(today.getFullYear(), today.getMonth(), today.getDate(),23, 59, 59);
          break;
        case 'last3months':
          // 近3月
          const last3months = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate());
          start = new Date(last3months.getFullYear(), last3months.getMonth(), last3months.getDate());
          end = new Date(today.getFullYear(), today.getMonth(), today.getDate(),23, 59, 59);
          break;
        default:
          break;
      }

      // 格式化日期
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // 发送日期范围变更事件
      this.$emit('change', {
        // startDate: conversion(start, 'yyyy-MM-dd HH:mm:ss'),
        // endDate: conversion(end, "yyyy-MM-dd HH:mm:ss"),
        startDate: start,
        endDate: end,
        type: tabValue
      });
    },

    /**
     * 处理更多按钮点击事件
     */
    handleMoreClick() {
      $(".date-tabs > span").removeClass("active")
      // 如果当前没有选择日期，则默认设置为今天
      if (!this.startDate || !this.endDate) {
        const today = new Date();
        const formatDate = (date) => {
          const year = date.getFullYear();
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          return `${year}-${month}-${day}`;
        };

        this.startDate = formatDate(today);
        this.endDate = formatDate(today);
      }
    },

    /**
     * 确认自定义日期范围
     */
    confirmDateRange() {

      console.log(this.dateRange)

      this.startDate = this.dateRange[0];
      this.endDate = this.dateRange[1];

      if (!this.startDate || !this.endDate) {
        this.$DonMessage.warning('请选择开始和结束日期');
        return;
      }

      // 发送日期范围变更事件
      this.$emit('change', {
        startDate: this.startDate,
        endDate: this.endDate,
        type: 'custom'
      });

      // 关闭弹出框
      this.showPopover = false;

      // 设置当前选中的标签为空（表示自定义）
      this.activeTab = 'custom';
    }
  }
};
</script>

<style scoped>

::v-deep(.el-date-editor--daterange .el-range-separator) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.date-range-selector {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-sizing: border-box;
}
.date-tabs {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 4px;
}
.date-tabs > span {
  padding: 0px 8px;
  border-right: 1px solid var(--border-color);
  cursor: pointer;
  font-size: 12px;
}
.date-tabs span:last-child {
  border: none;
  padding: 0;
}
.date-tabs span .more-btn {
  display: inline-block;
  height: 100%;
  padding: 0px 8px;
}
.more-btn:hover,
.date-tabs span:hover:not(.active) {
  color: var(--text-color) !important;
  background-color: var(--hover-color) !important;
}
.date-tabs span.active {
  background-color: var(--select-color) !important;
  color: var(--theme-color) !important;
}
.more-btn i {
  font-size: 13px;
  color: var(--text-color);
  margin-left: -1px;
}

/* 内容 */
.date-range-container {
  padding: 10px 0 5px;
}
.el-popover .el-input__inner {
  width: 100%;
}
.date-separator {
  text-align: center;
  color: #606266;
  padding: 5px 0;
}
/* 按钮 */
.el-popover .date-range-footer {
  text-align: right;
  margin-top: 15px;
}
.el-popover .date-range-footer .el-button {
  font-size: 12px;
  padding: 0 12px;
  height: 30px;
}
</style>
