<template>
  <!-- 图片预览弹窗 -->
  <el-dialog
    :visible.sync="dialogVisible"
    :title="imageName"
    width="40% !important"
    center
    :before-close="handleClose"
    append-to-body
  >
    <div class="preview-image-container">
      <img
        :src="imageUrl"
        :alt="imageName"
        class="preview-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="downloadImage">下载</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ImagePreview',
  props: {
    /**
     * 控制弹窗显示状态
     */
    visible: {
      type: Boolean,
      default: false
    },
    /**
     * 图片URL地址
     */
    imageUrl: {
      type: String,
      default: ''
    },
    /**
     * 图片名称
     */
    imageName: {
      type: String,
      default: '图片预览'
    }
  },
  computed: {
    /**
     * 内部控制弹窗显示状态
     */
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    }
  },
  methods: {
    /**
     * 关闭图片预览弹窗
     */
    handleClose() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },

    /**
     * 图片加载成功回调
     */
    handleImageLoad() {
      console.log('图片加载成功');
      this.$emit('load');
    },

    /**
     * 图片加载失败回调
     */
    handleImageError() {
      console.log('图片加载失败');
      this.$emit('error');
      // 可以显示错误提示
      if (this.$DonMessage) {
        this.$DonMessage.error('图片加载失败');
      } else if (this.$message) {
        this.$message.error('图片加载失败');
      }
    },

    /**
     * 下载图片
     */
    async downloadImage() {
      if (!this.imageUrl) {
        if (this.$DonMessage) {
          this.$DonMessage.warning('没有可下载的图片');
        } else if (this.$message) {
          this.$message.warning('没有可下载的图片');
        }
        return;
      }

      try {
        const res = await fetch(this.imageUrl, { mode: 'cors' });
        const blob = await res.blob();
        const blobUrl = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = this.imageName || 'image';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 释放对象 URL
        URL.revokeObjectURL(blobUrl);

        this.$emit('download', {
          url: this.imageUrl,
          name: this.imageName
        });
      } catch (error) {
        console.error('下载失败:', error);
        if (this.$DonMessage) {
          this.$DonMessage.error('图片下载失败');
        } else if (this.$message) {
          this.$message.error('图片下载失败');
        }
        this.$emit('download-error', error);
      }
    }
  }
};
</script>

<style scoped>
/* 图片预览弹窗样式 */
.preview-image-container {
  text-align: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  cursor: zoom-in;
}

.preview-image:hover {
  transform: scale(1);
}

/* 弹窗头部样式 */
.el-dialog__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.el-dialog__title {
  font-weight: 600;
  color: #333;
}

/* 弹窗底部按钮样式 */
.dialog-footer {
  text-align: center;
  padding: 15px 0;
}

.dialog-footer .el-button {
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-image-container {
    padding: 10px;
  }

  .preview-image {
    max-height: 60vh;
  }
}
</style>
