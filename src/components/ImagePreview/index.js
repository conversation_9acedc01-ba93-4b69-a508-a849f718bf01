import ImagePreview from './ImagePreview.vue';

/**
 * 图片预览组件
 * 
 * 使用方法：
 * 1. 在需要使用的组件中导入：
 *    import ImagePreview from '@/components/ImagePreview';
 * 
 * 2. 在components中注册：
 *    components: { ImagePreview }
 * 
 * 3. 在模板中使用：
 *    <ImagePreview
 *      :visible.sync="previewVisible"
 *      :image-url="previewImageUrl"
 *      :image-name="previewImageName"
 *      @close="handlePreviewClose"
 *      @load="handleImageLoad"
 *      @error="handleImageError"
 *      @download="handleDownload"
 *      @download-error="handleDownloadError"
 *    />
 * 
 * Props:
 * - visible: Boolean - 控制弹窗显示状态
 * - imageUrl: String - 图片URL地址
 * - imageName: String - 图片名称，默认为'图片预览'
 * 
 * Events:
 * - update:visible - 更新visible状态
 * - close - 弹窗关闭事件
 * - load - 图片加载成功事件
 * - error - 图片加载失败事件
 * - download - 图片下载成功事件
 * - download-error - 图片下载失败事件
 */
export default ImagePreview;