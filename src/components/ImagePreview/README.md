# ImagePreview 图片预览组件

一个可复用的图片预览弹窗组件，支持图片预览、下载等功能。

## 功能特性

- 🖼️ 图片预览弹窗显示
- 📥 图片下载功能
- 🎨 响应式设计，适配移动端
- 🔄 图片加载状态处理
- ⚡ 轻量级，无额外依赖
- 🎯 完整的事件回调支持

## 使用方法

### 1. 导入组件

```javascript
import ImagePreview from '@/components/ImagePreview';

export default {
  components: {
    ImagePreview
  },
  // ...
}
```

### 2. 在模板中使用

```vue
<template>
  <div>
    <!-- 触发预览的按钮或图片 -->
    <img 
      src="thumbnail.jpg" 
      @click="showPreview" 
      style="cursor: pointer;"
    />
    
    <!-- 图片预览组件 -->
    <ImagePreview
      :visible.sync="previewVisible"
      :image-url="previewImageUrl"
      :image-name="previewImageName"
      @close="handlePreviewClose"
      @load="handleImageLoad"
      @error="handleImageError"
      @download="handleDownload"
      @download-error="handleDownloadError"
    />
  </div>
</template>

<script>
import ImagePreview from '@/components/ImagePreview';

export default {
  components: {
    ImagePreview
  },
  data() {
    return {
      previewVisible: false,
      previewImageUrl: '',
      previewImageName: ''
    }
  },
  methods: {
    /**
     * 显示图片预览
     */
    showPreview() {
      this.previewImageUrl = 'https://example.com/image.jpg';
      this.previewImageName = '示例图片.jpg';
      this.previewVisible = true;
    },
    
    /**
     * 预览弹窗关闭回调
     */
    handlePreviewClose() {
      console.log('预览弹窗已关闭');
    },
    
    /**
     * 图片加载成功回调
     */
    handleImageLoad() {
      console.log('图片加载成功');
    },
    
    /**
     * 图片加载失败回调
     */
    handleImageError() {
      console.log('图片加载失败');
    },
    
    /**
     * 图片下载成功回调
     */
    handleDownload(data) {
      console.log('图片下载成功:', data);
    },
    
    /**
     * 图片下载失败回调
     */
    handleDownloadError(error) {
      console.log('图片下载失败:', error);
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| visible | 控制弹窗显示状态 | Boolean | false | 是 |
| imageUrl | 图片URL地址 | String | '' | 是 |
| imageName | 图片名称 | String | '图片预览' | 否 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:visible | 更新visible状态 | (value: Boolean) |
| close | 弹窗关闭事件 | - |
| load | 图片加载成功事件 | - |
| error | 图片加载失败事件 | - |
| download | 图片下载成功事件 | { url: String, name: String } |
| download-error | 图片下载失败事件 | (error: Error) |

## 样式定制

组件使用了scoped样式，如需自定义样式，可以通过以下CSS类名进行覆盖：

```css
/* 预览容器 */
.preview-image-container {
  /* 自定义样式 */
}

/* 预览图片 */
.preview-image {
  /* 自定义样式 */
}

/* 弹窗头部 */
.el-dialog__header {
  /* 自定义样式 */
}

/* 弹窗标题 */
.el-dialog__title {
  /* 自定义样式 */
}

/* 底部按钮区域 */
.dialog-footer {
  /* 自定义样式 */
}
```

## 注意事项

1. 组件依赖Element UI的`el-dialog`和`el-button`组件
2. 图片下载功能需要服务器支持CORS
3. 组件会自动处理图片加载失败的情况
4. 支持响应式设计，在移动端会自动调整显示效果
5. 组件兼容项目中的`$DonMessage`和`$message`消息提示方法

## 兼容性

- Vue 2.x
- Element UI 2.x
- 现代浏览器（支持fetch API）

## 更新日志

### v1.0.0
- 初始版本发布
- 支持图片预览和下载功能
- 响应式设计
- 完整的事件回调支持