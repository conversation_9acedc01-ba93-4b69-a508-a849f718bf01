<template>
  <div>
    <el-upload
      class="upload-demo inline-block"
      ref="elUpload"
      action="#"
      :show-file-list="false"
      multiple
      :limit="1"
      :file-list="fileList"
      :file-added="onFileAdded"
      :before-upload="onBeforeUpload"
      :http-request="uploadFile"
    >
      <el-button size="min" icon="bulkImport-icon" type="primary">批量上传</el-button>
    </el-upload>
    <el-progress type="circle" :percentage="0"></el-progress>
  </div>
</template>
<script>
import {sysServerUrl} from '@/assets/js/common.js'
import SparkMD5 from 'spark-md5'

/** 计算文件md5值
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
function getmd5(file, chunkSize) {
  return new Promise((resolve, reject) => {
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
    let chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    let spark = new SparkMD5.ArrayBuffer();
    let fileReader = new FileReader();
    fileReader.onload = function (e) {
      spark.append(e.target.result);
      currentChunk++;
      if (currentChunk < chunks) {
        loadNext();
      } else {
        let md5 = spark.end();
        resolve(md5);
        //  console.log(md5);
      }
    };
    fileReader.onerror = function (e) {
      reject(e);
    };

    function loadNext() {
      let start = currentChunk * chunkSize;
      let end = start + chunkSize;
      if (end > file.size) {
        end = file.size;
      }
      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }

    loadNext();
  });
}

export default {
  name: 'SplitUpoad',
  data() {
    return {
      //切片文件
      fileShard: {},
      //当前文件
      curFile: {},
      //文件分割的开始位置
      start: 0,
      //文件分割的结束位置
      end: 0,
      //文件大小
      fileSize: 0,
      fileKey: '',
      fileShardIndex: 0,
      fileShardTotal: 0,
      fileShardSize: 0,
      switchC: false,
      uploadParam: {
        fileName: '',
        fileShardTotal: 0,
        fileKey: '',
        fileSuffix: '',
        fileShardSize: 0,
        fileSize: 0,
        fileFlag: ''
      }
    };
  },
  created() {

  },
  mounted() {
  },
  methods: {
    onFileAdded(file) {
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const zipExt = fileExt === 'zip'
      const isLimit = file.size / 1024 / 1024 < 1024
      if (!zipExt) {
        this.$DonMessage.warning("上传文件只能是 zip格式!")
        return false;
      }
      if (!isLimit) {
        this.$DonMessage.warning("上传文件大小不能超过 1GB!")
        return false;
      }
      fileShardSize = 1 * 1024 * 1024; //每片文件大小

      //点击后隐藏上传按钮 ，防止重复点击
      //$("#fileUpload").css('visibility','hidden');
      //显示进度百分比
      //element.progress('uploadProgress', '0%');

      var _this = this
      getmd5(file, fileShardSize).then(e => {
        _this.switchC = false;
        _this.fileShardIndex = 1;//分片索引
        _this.curFile = file;
        _this.fileKey = e;
        _this.fileSize = file.size;
        _this.fileShardTotal = Math.ceil(file.size / fileShardSize);//分片总数
        var fileFullName = file.name;
        _this.fileSuffix = fileFullName.substr(fileFullName.lastIndexOf('.') + 1);
        _this.fileName = fileFullName.substr(0, fileFullName.lastIndexOf('.'));

        //上传参数
        var params = new FormData()
        params.append('fileName', _this.fileName)
        params.append('fileShardTotal', _this.fileShardTotal)
        params.append('fileKey', _this.fileKey)
        params.append('fileSuffix', _this.fileSuffix)
        params.append('fileShardSize', _this.fileShardSize)
        params.append('fileSize', _this.fileSize)
        params.append('fileFlag', "manual")

        // _this.uploadParam.fileName = _this.fileName;
        // _this.uploadParam.fileShardTotal=_this.fileShardTotal;
        // _this.uploadParam.fileKey=_this.fileKey;
        // _this.uploadParam.fileSuffix=_this.fileSuffix;
        // _this.uploadParam.fileShardSize=_this.fileShardSize;
        // _this.uploadParam.fileSize=_this.fileSize;
        // _this.uploadParam.fileFlag="manual";

        this.progress('uploadProgress', fileShardIndex + '%');

        _this.updateProgress(file, params)


      })

    },
    // 附件上传
    onBeforeUpload(file) {

    },
    // 批量上传
    uploadFile(formData) {
      var _this = this
      _this.$axios({
        method: 'post',
        url: sysServerUrl + "sys/upload/procFile",
        data: formData
      }).then(res => {
        if (res.code == 200) {
          //上传分片完成
          if (res.shardIndex < _this.fileShardTotal) {
            _this.fileShardIndex = _this.fileShardIndex + 1;
            _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
            _this.end = Math.min(_this.curFile.size, _this.start + _this.fileShardSize);
            _this.fileSize = _this.curFile.size;

            var params = new FormData()
            params.append('fileName', _this.fileName)
            params.append('fileShardTotal', _this.fileShardTotal)
            params.append('fileKey', _this.fileKey)
            params.append('fileSuffix', _this.fileSuffix)
            params.append('fileShardSize', _this.fileShardSize)
            params.append('fileSize', _this.fileSize)
            params.append('fileFlag', "manual")

            params.append('fileShardIndex', _this.fileShardIndex)
            //data.fileShardIndex=_this.fileShardIndex;
            var fileShardtem = _this.curFile.slice(_this.start, _this.end);//从文件中获取当前分片数据
            let fileReader = new FileReader();
            //异步读取本地文件分片数据并转化为base64字符串
            fileReader.readAsDataURL(fileShardtem);
            //本地异步读取成功后，进行上传
            fileReader.onload = function (e) {
              let base64str = e.target.result;
              params.append('base64', base64str)
              _this.uploadFile(params)
              // data.base64=base64str;
              // updateobj.upload(data.base64,fileName,fileKey,
              //     fileShardTotal,data.fileShardIndex,fileSuffix,fileSize,fileShardSize);
            }

            element.progress('uploadProgress', Math.ceil(fileShardIndex * 100 / fileShardTotal) + '%');

          }
        } else if (res.code == 100) {
          var fileId = res.id
          //上传完成
          layer.close(loadingIndex);
          layer.msg('上传成功!', {
            icon: 1,
            time: 1000
          }, function () {

            $("#fileUpload").css('visibility', 'visible');
            $('.loading').hide();
            batchImport(manualId, fileId);
          });

        }
      }).catch(function (error) {
        if (error.response) {
          console.log(error.response.data)
          console.log(error.response.status)
          console.log(error.response.headers)
        } else {
          console.log(error.message)
        }
      })

    },
    updateProgress(file, params) {
      var _this = this
      var params = new URLSearchParams()
      params.append('shardKey', _this.fileKey)
      _this.$axios({
        method: 'POST',
        url: sysServerUrl + "sys/upload/checkProgress",
        data: params
      }).then(res => {
        var fileId = res.id
        if (res.code == 200) {
          //新文件
          _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
          _this.end = Math.min(file.size, _this.start + _this.fileShardSize);
          _this.fileShard = file.slice(_this.start, _this.end);//从文件中获取当前分片数据

        } else if (res.code == 220) {
          _this.fileShardIndex = res.ShardIndex;
          //有上传未完成的
          _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
          _this.end = Math.min(file.size, _this.start + _this.fileShardSize);
          _this.fileShard = file.slice(_this.start, _this.end);//从文件中获取当前分片数据
        } else if (res.code == 240) {
          //急速上传
          element.progress('uploadProgress', '100%');
          _this.switchC = true;

          layer.close(loadingIndex);
          layer.msg('极速上传成功!', {
            icon: 1,
            time: 1000
          }, function () {
            $("#fileUpload").css('visibility', 'visible');
            $('.loading').hide();
            _this.batchImport(manualId, fileId)
          });

          return false;
        }

        //读取base64str
        let fileReader = new FileReader();
        //异步读取本地文件分片并转化为base64字符串
        fileReader.readAsDataURL(_this.fileShard);
        //本地异步读取成功，进行上传
        fileReader.onload = function (e) {
          let base64str = e.target.result;
          // data.base64=base64str;
          // data.fileShardIndex=fileShardIndex;
          params.append('base64', base64str)
          params.append('fileShardIndex', _this.fileShardIndex)
          if (switchC == false) {
            _this.uploadFile(params)
            // obj.upload(data.base64,data.fileName,data.fileKey,
            //     data.fileShardTotal,data.fileShardIndex,data.fileSuffix,data.fileSize,data.fileShardSize);
          }
        }
      })
    },
    batchImport(manualId, fileId) {
      this.$axios.get(cmsServerUrl + "cms/manual/directory/batchImport", {
        params: {
          manualId: manualId,
          fileId: fileId
        }
      }).then(res => {
        alert(res.code);
      })
    }
  },
};
</script>
