<template>
  <div class="elAutocomplete">
    <!-- 输入框 -->
    <el-autocomplete
      ref="autocomplete"
      popper-class="my-autocomplete"
      :inputParam="inputParam"
      :inputType="inputType"
      v-model="inputValue"
      :fetch-suggestions="querySearch"
      :placeholder="placeholder"
      @select="handleSelect"
      :disabled="disabled"
      @blur="handleBlur"
    >
      <template slot-scope="{item}">
        {{ item.name }}
      </template>
    </el-autocomplete>
    <!-- 右侧按钮 -->
    <el-button :disabled="disabled" type="text" icon="selectIcon-icon" @click="handleIconClick"></el-button>
    <!-- 详情弹框 -->
    <selectDialog v-if="isReload" @select="handleSelect" :isReload.sync="isReload" :type.sync="inputType" :inputValue.sync="inputValue" :columns.sync="tableColumns"></selectDialog>
    <el-dialog v-dialogDrag class="selectInputDialog" width="760px !important" :title="textMap[dialogStatus]" :visible.sync="dialogCategoryVisible" :append-to-body="true" v-if="dialogCategoryVisible">
      <div class="searchTreeArea">
        <el-form v-if="isType == 'list'" :inline="true" :label-width="$labelTwo" class="demo-form-inline">
          <el-form-item label="编码" prop="searchCode">
            <el-input v-model.trim="searchCode" placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="searchName">
            <el-input v-model.trim="searchName" placeholder="请输入名称"></el-input>
          </el-form-item>
        </el-form>
        <el-input v-else v-model.trim="searchVal" placeholder="请输入搜索关键词"></el-input>
        <el-button type="primary" @click="onSearch">{{ $t('button.search') }}</el-button>
        <el-button plain @click="onReset">{{ $t('button.reset') }}</el-button>
      </div>
      <div>
        <div>
          <div v-if="inputType == 'warehouseInfo' || inputType == 'locationInfo'">
            <el-table
              style="width: 100%;"
              ref="table"
              :data="categoryTree"
              border
              stripe
              row-key="id"
              :max-height="maxHeight"
              highlight-current-row
              @header-dragend="changeColWidth"
              @row-click="handleRowClick"
            >
              <el-table-column width="40" fixed="left" align="center">
                <template slot-scope="{ row }">
                  <el-radio 
                    v-model="selectedRowId"
                    :label="row.id"
                  >
                    &nbsp;
                  </el-radio>
                </template>
              </el-table-column>
              <el-table-column type="index" width="50" label="序号"></el-table-column>
              <template v-for="column in tableColumns">
                <el-table-column
                  v-if="!column.slotName"
                  :key="column.prop"
                  :prop="column.prop"
                  :label="column.label"
                  :min-width="column.minWidth"
                  :width="column.width"
                >
                  <template v-if="column.prop === 'status'" #default="scope">
                    <span class="successColor" v-if="scope.row.status === 1">启用</span>
                    <span class="errorColor" v-if="scope.row.status === 0">禁用</span>
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  :key="column.prop || column.label"
                  :label="column.label"
                  :width="column.width"
                >
                  <template slot-scope="scope">
                    <slot :name="column.slotName" :row="scope.row" :$index="scope.$index"></slot>
                    </template>
                </el-table-column>
              </template>
            </el-table>
            <Pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
          </div>
          <div v-else class="categoryTreeArea elTreeStyle">
            <el-tree
              :data="categoryTree"
              :render-content="renderContent"
              :default-expand-all="true"
              show-checkbox
              :check-strictly="true"
              @node-click="handleCategoryClick"
              @check-change="handleCheckChange"
              :filter-node-method="filterNode"
              :props="{
                label: 'name',
                children: 'children'
              }"
              ref="categoryTree"
              node-key="id"
            ></el-tree>
          </div>
        </div>
      </div>
      <div class="submitArea">
        <el-button type="primary" @click="onSubmit">{{ $t('button.submit') }}</el-button>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import selectDialog from "../dialogTable/selectDialog.vue";
import {
  productCategoryTree,
  getCategoryTreeData,
  getBrandData,
  getWarehouseData,
  getLocationData,
  getProductData
} from "@/views/basic/basicCommon";
import { renderTree } from "@/assets/js/common";
import { 
  productColumns, 
  brandColumns, 
  customerColumns, 
  supplierColumns, 
  warehouseColumns, 
  locationColumns,
  partnersColumns,
} from "@/assets/js/tableHeader";
export default {
  props: {
    modelValue: String,
    placeholder: { type: String, default: "请输入内容" },
    inputParam: { type: String, default: "" },
    inputType: { type: String, default: "" },
    warehouseId: { type: String, default: ""  },
    disabled: Boolean,
  },
  emits: ['update:modelValue', 'search'],
  components: { selectDialog, Pagination },
  data() {
    return {
      isReload: false,
      inputValue: "",
      categoryList: [],
      categoryTree: [], // 商品类别树结构
      dialogCategoryVisible: false,
      dialogStatus: '',
      textMap: {
        categoryName: "商品类别",
        brand: "品牌类别",
        customer: "客户类别",
        warehouse: "仓库类别",
        supplier: "供应商类别",
        warehouseInfo: "仓库信息",
        locationInfo: "库位信息",
      },
      tableColumns: [],
      searchVal: "", // 搜索值
      searchCode: "",
      searchName: "",
      status: 1,
      treeCurrentNode: null,
      selectedRowId: null,
      searchResult: [],
      selectedValue: [], // 记录是否选择
      pagesize: 10,
      currentPage: 1,
      total: 0,
      currentRow: [],
      maxHeight: "485px",
      isType: 'list',
    }
  },
  watch: {
    inputValue(val) {
      this.inputValue = val;
    },
    inputParam(val) {
      var _this = this;
      if(val != ""){
        _this.inputValue = val
      }
    },
    searchVal(val) {
      this.searchVal = val;
      this.$refs.categoryTree.filter(val); // 触发筛选
    },
    searchCode(val) {
      this.searchCode = val;
      this.getSearchData()
    },
    searchName(val) {
      this.searchName = val;
      this.getSearchData()
    },
  },
  mounted() {
    var _this = this;
    if(_this.inputParam != ""){
      _this.inputValue = _this.inputParam
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    handleRowClick(node) {
      this.treeCurrentNode = node
      this.selectedRowId = node.id
      // this.$refs.table.clearSelection();
      // this.$refs.table.toggleRowSelection(node);
    },
    // handleSelectionChange(selection) {
    //   if (selection.length > 1) {
    //     this.$refs.table.clearSelection();
    //     this.$refs.table.toggleRowSelection(selection.pop());
    //   }
    //   this.treeCurrentNode = selection[0];
    // },
    // 获取数据
    dataList() {
      this.categoryList = [];
      this.categoryTree = [];
      if (this.inputType == 'categoryName') {
        this.isType = 'tree';
        const productTree = async()=>{
          var result = await productCategoryTree();
          this.categoryTree = result;
        }
        productTree()
      } else if (this.inputType == 'brandInfo') {
        this.tableColumns = brandColumns;
      } else if (this.inputType == 'productInfo') {
        this.tableColumns = productColumns;
      } else if (this.inputType == "customerInfo") {
        this.tableColumns = customerColumns.slice(2);
      } else if (this.inputType == "supplierInfo") {
        this.tableColumns = supplierColumns;
      } else if (this.inputType == "partnerSupplierInfo" || this.inputType == "partnerCustomerInfo") {
        this.tableColumns = partnersColumns;
      } else if (this.inputType == 'warehouseInfo') {
        this.tableColumns = warehouseColumns;
        const warehouseData = async () => {
          var result = await getWarehouseData(this);
          this.categoryTree = result;
        }
        warehouseData()
      } else if (this.inputType == 'locationInfo') {
        this.tableColumns = locationColumns;
        const locationData = async () => {
          var result = await getLocationData(this, this.warehouseId);
          this.categoryTree = result;
        }
        locationData()
      } else {
        this.isType = 'tree';
        const tree = async() => {
          var result = await getCategoryTreeData(this.inputType);
          this.categoryList = [];
          this.categoryTree = result;
        }
        tree();
      }
    },
    // 查询数据
    async querySearch(queryString, cb) {
      var restaurants = this.categoryList;
      var results = queryString ? restaurants.filter(this.createAssemblyFilter(queryString)) : restaurants;
      // this.searchResult = results;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createAssemblyFilter(queryString) {
      return (restaurant) => {
        return (restaurant.name.indexOf(queryString) !== -1);
      };
    },
    // 选中值
    handleSelect(item) {
      this.selectedValue = item;
      this.inputValue = item.name
      this.$emit('select', item);
    },
    handleBlur() {
      if (!this.selectedValue && this.inputValue != "全部") {
        this.inputValue = ''; // 未选择则清空
        this.$emit('select', '');
      }
    },
    // 清楚
    clear() {
      this.selectedValue = '';
      this.inputValue = '';
      this.$emit('select', '');
      document.querySelectorAll('.secondFloat .el-autocomplete input').forEach((item, index) => {
        document.querySelectorAll('.secondFloat .el-autocomplete input')[index].value = ""
      });
    },
    // 搜索值
    handleSearch() {
      this.$emit('search', this.inputValue);
    },
    // 弹框
    // 搜索
    onSearch() {
      if (this.isType == 'list') {
        if (!this.searchName && !this.searchCode) {
          this.$DonMessage.warning("请输入编码或名称");
          return
        }
        this.getSearchData()
      } else {
        if (this.searchVal) {
          this.$DonMessage.warning("请输入搜索关键词");
          return
        }
      }
    },
    // 获取数据
    getSearchData() {
      this.dataList();
    },
    // 树结构筛选
    filterNode(value, data) {
      if(!value) return data;
      return data.name.includes(value);
    },
    // 重置
    onReset() {
      if (this.isType == "list") {
        this.searchCode = "";
        this.searchName = "";
        this.dataList();
      } else {
        this.searchVal = "";
      }

    },
    // 弹框
    async handleIconClick() {
      if (this.disabled == true) {
        return
      }
      if (this.inputType == 'locationInfo') {
        if (!this.warehouseId) {
          this.$DonMessage.warning("请选择默认仓库");
          return
        }
      }
      this.dataList();
      
      if (this.inputType.indexOf('Info') != -1 && this.inputType != 'warehouseInfo' && this.inputType != 'locationInfo') {
        this.isReload = true
      } else {
        this.dialogStatus = this.inputType;
        this.dialogCategoryVisible = true;
      }
    },
    // 树结构图标
    renderContent(h, { node, data }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".categoryTreeArea");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 数选中值
    handleCategoryClick(node) {
      this.treeCurrentNode = node;
      this.$nextTick(() => {
        this.$refs.categoryTree.setCheckedKeys([node.id])
        this.$refs.categoryTree.setCurrentKey(node.id);
      });
    },
    handleCheckChange(data, checked) {
      if (checked) {
        this.treeCurrentNode = data;
        this.$nextTick(() => {
          this.$refs.categoryTree.setCheckedKeys([data.id])
          this.$refs.categoryTree.setCurrentKey(data.id);
        });
      }
    },
    // 提交
    onSubmit() {
      if (this.treeCurrentNode == null) {
        this.$DonMessage.warning("请选择"+this.textMap[this.dialogStatus])
        return
      }
      this.selectedValue = this.treeCurrentNode;
      this.inputValue = this.treeCurrentNode.name;
      this.$emit('select', this.treeCurrentNode);
      this.dialogCategoryVisible = false;
    },
    // 取消
    onCancel() {
      this.dialogCategoryVisible = false;
    },
  }
}
</script>
<style>
.elAutocomplete {
  position: relative;
}
.selectInputDialog .searchTreeArea {
  display: flex;
  margin-bottom: 10px;
}
.selectInputDialog .searchTreeArea .el-input {
  width: 300px !important;
  margin-right: 10px;
}
.selectInputDialog .searchTreeArea .el-form .el-input {
  width: 200px !important;
}
.selectInputDialog .searchTreeArea .el-input .el-input__inner {
  width: 100% !important;
}
.selectInputDialog .el-dialog__body > div:nth-child(2) {
  height: 535px;
  overflow-y: auto;
}
.selectInputDialog .el-checkbox__inner {
  border-radius: 50%;
}
.selectInputDialog .elTreeStyle .el-tree .el-checkbox__inner::after {
  width: 4px !important;
  height: 4px !important;
  background: #fff;
  top: 50% !important;
  left: 50% !important;
  border: none !important;
  transform: translate(-50%, -60%) !important;
  border-radius: 50% !important;
}

</style>
