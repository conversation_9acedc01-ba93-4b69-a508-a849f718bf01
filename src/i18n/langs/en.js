import enLocale from 'element-ui/lib/locale/lang/en' // 引入element语言包
const en = {
  login: {
    title: 'TIS Technology Information System',
    mobile: 'phone number',
    userName: 'UseName',
    password: 'Password',
    code: 'Captcha',
    userTip: 'User name cannot be empty',
    passTip: 'Password cannot be empty',
    codeTip: 'Verification code cannot be empty',
    userInput: 'Please enter your username',
    passInput: 'Please enter your password',
    codeInput: 'Please enter your authentication code',
    button: 'Cancel'
  },
  nav: {
    signOut: 'Sign out',
    data: 'Base information',
    change: 'Change password'
  },
  catalogue: {
    oneTitle: 'My home page'
  },
  dashboard: {
    layTitle: 'Personal page',
    subTitle: 'Workbench',
    oneGreet: 'Good morning!',
    twoGreet: 'Good noon!',
    threeGreet: 'Good afternoon!',
    fourGreet: 'Good evening!',
    tipTitle: 'Work hard in the new day~',
    myProject: 'My projects',
    Incomplete: 'Unfinished projects',
    defer: 'defer projects',
    pTitle: 'Ongoing projects',
    rTitle: 'All items',
    dTitle: 'Dynamic'
  },
  button: {
    confirm: 'Confirm',
    submit: "Submit",
    cancel: "Cancel",
    search: "Search",
    reset: "Reset",
    upload: "Click to upload",
    uploadTip: "Drag and drop attachments here or click upload",
  },
  identifying: {
    limitTip: "The current limit is that a maximum of {count} files can be uploaded",
    loadTips: "Loading in progress ...",
    uploadTip: "Only files in {format} format can be uploaded, and the number of files cannot exceed {count}, and the file size cannot exceed {size}",
    fileTip: "Upload files only support {fileType} format",
    fileSize: "The size of the uploaded file cannot exceed {{size}}",
  },
  tips: {
    infoTip: "Please provide complete information",
  },
  successTip: {
    submitTip: "Submitted successfully",
    openTip: "Started successfully",
    closeTip: "Close successfully",
    releaseTip: "Published successfully",
    withdrawTip: "Revoked successfully",
    deleteTip: "Delete successfully",
    topTip: "Pinned successfully",
    noTopTip: "Unpinned successfully",
    uploadTip: "Uploaded successfully",
    importTip: "Imported successfully",
    resetTip: "Reset successfully",
    syncTip: "Synchronized successfully",
    onlineTip: "Offlined successfully",
    saveTip: "Saved successfully",
    operateTip: "Operation succeeded",
    refreshTip: "Refreshed successfully",
    updateTip: "Updated successfully",
    cancelTip: "Canceled successfully",
    copyTip: "Copy successful",
  },
  errorTip: {
    submitTip: "Submit failure",
    operateTip: "Operation failure",
    deleteTip: "Delete failed",
    systemTip: 'System error, please try again later ...',
  },
  foot: {
    name: ''
  },
  ...enLocale
}
export default en
