import upload from '@/components/SplitUpload/ajax'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN' // 引入element语言包
const cn = {
  login: {
    title: 'ERP进销存系统',
    mobile: '手机号',
    userName: '用户名',
    password: '密码',
    code: '验证码',
    userTip: '用户名不能为空',
    passTip: '密码不能为空',
    codeTip: '验证码不能为空',
    userInput: '请输入用户名',
    passInput: '请输入密码',
    codeInput: '请输入验证码',
    button: '确定',
    // 子账号选择页面相关
    backBtn: '切换登录方式',
    selectAccount: '选择登录账号',
    accountTip: '当前登录方式有 {count} 个关联账号，请选择登录账号',
    mainAccount: '主账号',
    continueBtn: '继续',
    selectAccountTip: '请选择一个账号进行登录'
  },
  nav: {
    signOut: '退出',
    data: '基本资料',
    change: '修改密码'
  },
  catalogue: {
    oneTitle: '我的主页'
  },
  dashboard: {
    layTitle: '个人页',
    subTitle: '工作台',
    oneGreet: '早上好！',
    twoGreet: '中文好！',
    threeGreet: '下午好！',
    fourGreet: '晚上好！',
    tipTitle: '新的一天要努力工作哦~',
    myProject: '我的项目',
    Incomplete: '未完成的项目',
    defer: '已延期的项目',
    pTitle: '进行中的项目',
    rTitle: '全部项目',
    dTitle: '动态'
  },
  button: {
    confirm: '确认',
    submit: "提交",
    cancel: "取消",
    search: "搜索",
    reset: "重置",
    upload: "点击上传",
    uploadTip: "拖拽附件至此或点击上传",
  },
  identifying: {
    limitTip: "当前限制最多可上传{count}个文件",
    loadTips: '正在加载中...',
    uploadTip: "只能上传{format}格式的文件，且文件数量不能超过{count}个，文件大小不能超过{size}",
    fileTip: "上传文件只支持{fileType}格式",
    fileSize: "上传文件大小不能超过{{size}}",
  },
  tips: {
    infoTip: "请完善信息",
  },
  successTip: {
    submitTip: "提交成功",
    openTip: "开启成功",
    closeTip: "关闭成功",
    releaseTip: "发布成功",
    withdrawTip: "撤回成功",
    deleteTip: "删除成功",
    topTip: "置顶成功",
    noTopTip: "取消置顶成功",
    uploadTip: "上传成功",
    importTip: "导入成功",
    resetTip: "重置成功",
    syncTip: "同步成功",
    onlineTip: "下线成功",
    saveTip: "保存成功",
    operateTip: "操作成功",
    refreshTip: "刷新成功",
    updateTip: "更新成功",
    cancelTip: "取消成功",
    copyTip: "复制成功",
  },
  errorTip: {
    submitTip: "提交失败",
    operateTip: "操作失败",
    deleteTip: "删除失败",
    systemTip: '系统开小差，请稍后再试...',
  },
  foot: {
    name: ''
  },
  ...zhLocale
}
export default cn
