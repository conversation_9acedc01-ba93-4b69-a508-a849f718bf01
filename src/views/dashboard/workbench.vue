<template>
  <div class="workbench">
    <div class="mineContainer">
      <el-row :gutter="12">
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16" class="leftContent">
          <div class="projectInfo">
            <div class="userInfo">
              <div class="headPortrait">
                <img v-if="baseData.headimgurl == null" src="../../assets/image/defaultAvatar.png" alt="">
                <img v-if="baseData.headimgurl" :src="imagePath + baseData.headimgurl" alt="">
              </div>
              <div class="greetContent">
                <p> {{baseData.realName}}，{{greetingsText}}</p>
              </div>
            </div>
            <!-- <div class="taskInfo" @click="jumpMenuGo('1')" v-if="false">
              <div>
                <img src="../../assets/image/indexIcon/handleIcon.png" alt="">
              </div>
              <div>
                <p>待办</p>
                <p class="numberInfo">{{ menuDataList.taskNum }}</p>
              </div>
            </div> -->
            <div class="replyInfo" @click="jumpMenuGo('2')">
              <div>
                <img src="../../assets/image/indexIcon/replyIcon.png" alt="">
              </div>
              <div>
                <p>待回复</p>
                <p class="numberInfo">{{ replyNum }}</p>
              </div>
            </div>
          </div>
          <div class="quickEntryInfo">
            <p>
              <span>快捷入口</span>
              <span @click="quickEntrySet()">设置</span>
            </p>
            <div class="quickEntryArea">
              <div v-for="(item, index) in quickEntryList" :key="index">
                <div @click="menuClick(item)">
                  <img :src="require('../../' + imgSrc + iconList.get(item.menuName))" alt="">
                  <p>{{ item.menuName }}</p>
                </div>
              </div>
            </div>
            <div v-if="quickEntryList.length == 0" class="tipNoneData">
              暂无数据
            </div>
          </div>
          <div class="statisticsInfo">
            <p>近一周</p>
            <div id="myChart" style="width:100%;height:300px"></div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8" class="rightContent">
          <!-- 系统公告 -->
          <div class="noticeDetail">
            <p>
              <span>系统公告</span>
              <span @click="bulletinAllClick()" class="noticeMore" v-if="noticeList.length > 0">
                更多
                <i class="el-icon-arrow-right"></i>
              </span>
            </p>
            <div v-if="noticeList.length > 0">
              <div v-for="(item, index) of noticeList.slice(0,7)" :key="index">
                <div class="noticeTitle" @click="detailClick(item)">
                  <div>
                    <p class="readState" v-if="item.isRead == true">[已读]</p>
                    <p class="readState errorColor"  v-if="item.isRead == false">[未读]</p>
                    <p :title="item.title">{{item.title}}</p>
                  </div>
                  <div>
                    详情
                    <i class="el-icon-arrow-right"></i>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="noticeList.length == 0" class="tipNoneData">
              暂无数据
            </div>
          </div>
          <!-- 在线反馈 -->
          <div class="latestNews">
            <p>
              <span>待回复信息</span>
              <span @click="trendsAllClick()" class="noticeMore" v-if="replyFeedbackData.length > 0">
                更多
                <i class="el-icon-arrow-right"></i>
              </span>
            </p>
            <div class="dynamicDetail" v-if="replyFeedbackData.length > 0">
              <div class="dynamicList" v-for="(item, index) of replyFeedbackData.slice(0,6)" :key="index">
                <div class="replayListDetail" @click="replyFeedbackClick()">
                  <div class="replayProblem">
                    <p>{{ item.problemTheme}}</p>
                  </div>
                  <div class="replayTitle">
                    <div>
                      <p :class="'partStyle ' + item.problemType">
                        {{ getFeedbackType(item.problemType)}}
                      </p>
                      <p>{{ item.stationName }}</p>
                    </div>
                    <p>{{ item.createdTime | conversion("yyyy-MM-dd HH:mm") }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="tipNoneData" v-if="replyFeedbackData.length == 0">
              暂无数据
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
      </el-row>
      <!-- 快捷入口设置 -->
      <el-dialog v-dialogDrag title="快捷入口设置" :visible.sync="dialogFormVisible">
        <div class="quickEntrySet">
          <div class="tipsTitle" v-if="addedMenuList.length > 0">
            <img :src="require('../../' + imgSrc + 'tipsIcon.png')" alt=""/>
            可通过上下拖拽图标，调整快捷入口显示顺序
          </div>
          <div class="menuDetail">
            <!-- 已添加-->
            <div class="addedArea">
              <draggable @end="dragend" :options="{animation:500}">
                <transition-group type="transition">
                  <div v-for="(item, index) of addedMenuList" :key="index" class="drag-item">
                    <div>
                      <div>
                        <img :src="require('../../' + imgSrc + 'slideIcon.png')" alt=""/>
                        <img :src="require('../../' + imgSrc + iconList.get(item.menuName))" alt=""/>
                        <p>{{ item.menuName }}</p>
                      </div>
                      <div class="removeArea">
                        <p @click="removeMenuClcik(index,item)">移除</p>
                      </div>
                    </div>
                  </div>
                </transition-group>
              </draggable>
            </div>
            <el-divider v-if="notAddedMenuList.length > 0 && addedMenuList.length > 0"></el-divider>
            <!-- 未添加 -->
            <div class="notAdded">
              <div v-for="(item, index) of notAddedMenuList" :key="index">
                <div>
                  <div>
                    <img :src="require('../../' + imgSrc + iconList.get(item.menuName))" alt=""/>
                    <p>{{ item.menuName }}</p>
                  </div>
                  <div class="addArea">
                    <p @click="addMenuClick(index,item)">添加</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="addedMenuList.length != 0 || notAddedMenuList.length != 0" class="submitArea" style="margin-top:15px;">
          <el-button type="primary" @click="submitClick()">
            确定
          </el-button>
          <el-button plain @click="cancelClick()">
            取消
          </el-button>
        </div>
        <div v-if="addedMenuList.length == 0 && notAddedMenuList.length == 0" class="tipNoneData">
          暂无数据
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { addTabs, quickEntryMap, sysServerUrl } from '@/assets/js/common.js'
import {
  // 快捷方式
  quickEntryMenuData,
  quickEntryEdit,
  // 个人信息
  getUserInfo,
  // 系统公告
  systemBulletinList,
  // 近一周访问量
  accessOneWeek,
  messageData,
} from '@/api/sysmgt.js'
import { feedbackData, feedbackTypeList } from '@/api/material.js';
import $ from 'jquery'

export default {
  name: 'dashboard',
  data() {
    return {
      imagePath: sysServerUrl,
      imgSrc: "assets/image/indexIcon/",
      greetingsText: '', // 问候
      baseData: [], // 个人信息
      menuDataList: [], // 主页数据
      quickEntryList: [], // 快捷入口
      iconList: [], // 快捷入口图标列表
      noticeList: [], // 系统公告
      replyFeedbackData: [], // 待回复信息
      problemTypeList: [], // 在线反馈问题类型
      lineSX: [], // 横坐标 时间
      lineSY1: [], // 纵坐标 订单
      lineSY2: [], // 纵坐标 访问
      dialogFormVisible: false,
      addedMenuList: [],
      notAddedMenuList: [],
      menuList: [],
      uploadMenuList: [],
      replyNum: 0,
    }
  },
  components: {
    draggable
  },
  methods: {
    // 在线反馈问题类型
    feedbackType() {
      var params = new URLSearchParams()
      params.append('page', 1)
      params.append('limit', 10)
      params.append('type', 1)
      feedbackData(params).then(res => {
        if (res.data.code == "100") {
          this.replyFeedbackData = res.data.data;
          this.total = res.data.total    // 总条数
        } else {
          this.$DonMessage.error(res.data.msg);
        }
        // this.tableHeightArea()
      })
    },
    getFeedbackTypeList() {
      feedbackTypeList().then(res => {
        this.problemTypeList = res.data.data
      }).catch(err => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    getFeedbackType(code) {
      let name = ""
      for (let i = 0; i < this.problemTypeList.length; i++) {
        if (code === this.problemTypeList[i].code) {
          name = this.problemTypeList[i].name;
          break;
        }
      }
      return name;
    },

    // 快捷入口跳转
    menuClick(item) {
      this.$router.push(item.menuUrl);
      setTimeout(() => {
        addTabs(item.menuUrl, item.menuName);
      })
    },

    // 快捷入口 设置点击
    quickEntrySet() {
      this.dialogFormVisible = true;
      this.addedMenuList = [];
      this.notAddedMenuList = [];
      let _this = this;
      _this.menuList.forEach((item) => {
        if (item.deleteValue == 0) {
          _this.addedMenuList.push(item);
        } else {
          _this.notAddedMenuList.push(item);
        }
      });
    },
    // 获取快捷入口
    quickEntryClick() {
      this.quickEntryList = [];
      this.menuList = [];
      this.addedMenuList = [];
      this.notAddedMenuList = [];
      let _this = this;
      quickEntryMenuData().then((res) => {
        if (res.data.code == "100") {
          _this.menuList = res.data.data;
          _this.menuList.forEach((item) => {
            if (item.deleteValue == 0) {
              _this.quickEntryList.push(item);
            }
          });
        }
      })
    },
    // 拖拽排序
    dragend(event) {
      this.uploadMenuList = event.target.innerText.split("移除");
    },
    // 移除
    removeMenuClcik(index, item) {
      this.addedMenuList.splice(index, 1);
      setTimeout(() => {
        this.notAddedMenuList.push(item);
      }, 10)
    },
    // 添加
    addMenuClick(index, item) {
      this.notAddedMenuList.splice(index, 1);
      this.addedMenuList.push(item);
    },
    // 确定
    submitClick() {
      let uploadList = [];
      let _this = this;
      let list1 = []
      this.uploadMenuList.forEach((item) => {
        list1.push(item.replaceAll(/(\r\n|\n|\r)/g, ''))
      })

      if (list1.length > 0) {
        // 排序
        this.addedMenuList.sort((a, b) => list1.indexOf(a.menuName) - list1.indexOf(b.menuName));
      }

      this.addedMenuList.forEach((item) => {
        uploadList.push({ "menuCode": item.menuCode, "deleteValue": false });
      });
      this.notAddedMenuList.forEach((item) => {
        uploadList.push({ "menuCode": item.menuCode, "deleteValue": true });
      });

      quickEntryEdit(uploadList).then((res) => {
        if (res.data.code == 100) {
          this.quickEntryClick();
          this.dialogFormVisible = false;
          this.$DonMessage.success(this.$t('successTip.submitTip'))
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(e => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    // 取消
    cancelClick() {
      this.dialogFormVisible = false;
      // this.quickEntryClick();
    },
    jumpMenuGo(p) {
      if (p == '1' && this.menuDataList.taskMenu && this.menuDataList.taskMenu != '') {
        // 待办任务
        this.menuClick(this.menuDataList.taskMenu)
      } else if (p == '2') {
        // 在线反馈
        this.menuClick({ "menuName": "在线反馈", "menuUrl": "/material/feedback/list" })
      } else {
        // 没有权限
        this.$DonMessage.error('您没有权限访问')
      }
    },
    // 2023-08-31 跳转菜单
    // jumpMenu(item){
    //   this.$router.push(item.targetUrl)
    //   setTimeout(() => {
    //       addTabs(item.targetUrl, item.action);
    //     }, 120)
    // },
    // 近一周统计
    getAccessOneWeek() {
      let _this = this;
      // accessOneWeek().then(res => {
      //   res.data.data.forEach((item) => {
      //     _this.lineSX.push(item.month);
      //     _this.lineSY2.push(item.count);
      //   });
      //   _this.drawLine(_this.lineSX, _this.lineSY1, _this.lineSY2, "myChart");
      // })
    },
    drawLine(lineX, lineY1, lineY2, id) {
      let myChart = this.$echarts.init(document.getElementById(id));
      let option = {
        backgroundColor: "#fff",
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["订单量", "访问量"],
          icon: 'line',
          right: '5%',
          top: '10',
        },
        grid: {
          left: "3%",
          right: "7%",
          bottom: "3%",
          containLabel: true
        },
        xAxis: {
          name: "时间",
          type: "category",
          boundaryGap: false,
          data: lineX,
        },
        yAxis: {
          name: "数量",
        },
        series: [
          // {
          //   name: "订单量",
          //   type: "line",
          //   data: lineY1,
          //   itemStyle: {
          //     color: '#FC8A28',
          //   },
          // },
          {
            name: "访问量",
            type: "line",
            data: lineY2,
            itemStyle: {
              color: '#0a8fe2',
            }
          }
        ]
      };
      window.addEventListener("resize", function () {
        myChart.resize();
      });
      myChart.setOption(option);
    },
    //系统公告跳转
    systemNotice() {
      systemBulletinList().then(res => {
        if (res.data.data === null) {
          this.noticeList = []
        } else {
          this.noticeList = res.data.data
        }
      });
    },
    bulletinAllClick() {
      this.$router.push({ name: "systemBulletin" });
      addTabs(this.$route.path, "系统公告");
    },
    detailClick(item) {
      this.$router.push({ name: 'bulletinDetail', params: { id: item.id } })
      addTabs(this.$route.path, item.title);
    },
    // 待回复
    replyFeedbackClick() {
      this.$router.push('/material/feedback/list')
      setTimeout(() => {
        addTabs(this.$route.path, "在线反馈");
      })
    },
    trendsAllClick() {
      this.replyFeedbackClick()
      // this.$router.push('/material/feedback/list')
      // addTabs(this.$route.path, "在线反馈");
      // this.$router.push({ name: "latestNews" });
      // addTabs(this.$route.path, "待回复信息");
    },
    // 问候语
    greetingsList() {
      const timeNow = new Date()
      // 获取当前小时
      const hours = timeNow.getHours()
      // 设置默认文字
      var text = ''
      // 判断当前时间段
      if (hours >= 5 && hours <= 8) {
        text = "早上好!"
      } else if (hours > 8 && hours <= 13) {
        text = "上午好!"
      } else if (hours > 13 && hours <= 18) {
        text = "下午好!"
      } else if (hours > 18 && hours <= 5) {
        text = "晚上好!"
      }
      this.greetingsText = text
    },
    // 获取用户信息
    userInfo() {
      getUserInfo().then(res => {
        this.baseData = res.data.data
        this.$store.commit("set_realName", res.data.data.realName);
      })
    },
    // 获取内容区域高度
    areaHeight() {
      var headHeight = $('.el-header').outerHeight(true)
      var layTitle = $('.layTitle').outerHeight(true)
      var headerLay = $('.headerLay').outerHeight(true)
      var tabHeight = $('.tabsTag').outerHeight(true)
      // var footHeight = $('.el-footer').outerHeight(true)
      if ($('.container').css('marginTop') !== undefined) {
        var topHeight = 2 * Number($('.container').css('marginTop').split('p')[0])
      } else {
        topHeight = 0
      }
      // var h = $(window).height() - headHeight - layTitle - headerLay - tabHeight - footHeight - topHeight
      var heightVal = $(window).height() - headHeight - layTitle - headerLay - tabHeight - topHeight
      $('.container').css('height', heightVal + 'px')
      // 获取进行中的项目内容改的和动态的内容高度
      $('.projectList').css('max-height', heightVal - $('.projectContent .pTitle').height())
      $('.dynamicDetail').css('height', heightVal - $('.projectContent .pTitle').height())
      $("#myChart").css({ "width": "100%", "height": "300px" });
    },
    sizeDetail() {
      var _this = this
      _this.areaHeight()
      window.addEventListener('resize', function () {
        _this.areaHeight()
      })
    },
    allClick(name, url) {
      this.$router.push({ name: "allProject" });
      addTabs(this.$route.path, name);
    },

    // 消息
    getMessageData() {
      // messageData().then(res => {
      //   this.replyNum = res.data.data.feedback ? res.data.data.feedback : 0
      // })
    },
  },
  mounted() {
    this.userInfo();
    this.systemNotice();
    this.greetingsList()
    // 获取 近一周访问量
    this.getAccessOneWeek();
    // 在线反馈记录
    this.feedbackType();
    this.getFeedbackTypeList();
    // 快捷入口
    this.quickEntryClick();

    this.getMessageData();

    this.iconList = quickEntryMap;
    window.uploadInfo = () => {
      this.userInfo();
    };
    document.body.ondrag = function (event) {
      event.stopPropagation()
      event.preventDefault()
    }
    document.body.ondragover = function (event) {
      event.stopPropagation()
      event.preventDefault()
    }
  },
  created() {
    document.body.ondrag = function (event) {
      event.stopPropagation()
      event.preventDefault()
    }
    document.body.ondragover = function (event) {
      event.stopPropagation()
      event.preventDefault()
    }
  },
  watch: {
    $route(to, from) {
      if (to.name == 'dashboard') {
        this.quickEntryClick();
        this.feedbackType();
        this.getFeedbackTypeList();
        this.systemNotice();
        this.getMessageData();
      }
    }
  },
}
</script>
<style>
/* header */
.workbench {
  padding: 12px 12px 0px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

.workbench>div {
  height: 100%;
  min-height: 500px;
}

.leftContent>div,
.rightContent>div {
  margin-bottom: 12px;
  border-radius: var(--border-radius);
}

.projectInfo,
.quickEntryInfo,
.statisticsInfo,
.noticeDetail,
.latestNews {
  background: #fff;
  padding: 12px;
}

.quickEntryInfo>p,
.statisticsInfo>p,
.noticeDetail>p,
.latestNews>p {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quickEntryInfo>p span:last-child,
.latestNews>p span:last-child,
.noticeDetail>p .noticeMore {
  font-size: 15px;
  color: var(--subColor);
  cursor: pointer;
}

.latestNews>p span .el-icon-arrow-right,
.noticeDetail>p span .el-icon-arrow-right {
  font-weight: bold;
}

/* 个人信息 */
.leftContent .projectInfo {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
}

/* 左 */
.leftContent .projectInfo>div {
  padding: 8px 3%;
}

.leftContent .projectInfo .userInfo {
  width: 34%;
  display: flex;
  align-items: center;
  border-right: 1px solid rgb(229, 229, 229);
}

/* 头像 */
.leftContent .projectInfo .userInfo .headPortrait {
  height: 40px;
  width: 40px;
  overflow: hidden;
  border-radius: 50%;
  margin: 0 10px;
}

.leftContent .projectInfo .userInfo .headPortrait img {
  width: 100%;
  height: 100%;
}

.leftContent .projectInfo .userInfo .greetContent {
  flex: 1;
}

.leftContent .projectInfo .userInfo .greetContent p {
  font-weight: bold;
  font-size: 16px;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 右 */
.leftContent .projectInfo .replyInfo {
  flex: 1;
  justify-content: left;
}

.leftContent .projectInfo>div:last-child {
  border-right: none;
}

.leftContent .projectInfo .userInfo,
.leftContent .projectInfo .taskInfo,
.leftContent .projectInfo .replyInfo {
  display: flex;
  align-items: center;
}

.leftContent .projectInfo .taskInfo,
.leftContent .projectInfo .replyInfo {
  cursor: pointer;
}

.leftContent .projectInfo .taskInfo>div:first-child,
.leftContent .projectInfo .replyInfo>div:first-child {
  width: 60px;
  height: 60px;
  margin-right: 5%;
}

.leftContent .projectInfo .taskInfo>div:first-child img,
.leftContent .projectInfo .replyInfo>div:first-child img {
  width: 100%;
}

.leftContent .projectInfo .taskInfo div p,
.leftContent .projectInfo .replyInfo div p {
  font-size: 15px;
  color: var(--table-text);
  margin-bottom: 10px;
}

.leftContent .projectInfo div div .numberInfo {
  color: #000;
  font-size: 30px;
  font-weight: bold;
  margin-bottom: 0px;
}

/* 快捷入口 */
.quickEntryInfo .quickEntryArea {
  display: flex;
  flex-wrap: wrap;
}

.quickEntryInfo .quickEntryArea>div {
  width: 20%;
  margin-bottom: 5px;
}

.quickEntryInfo .quickEntryArea>div div {
  width: 100px;
  padding: 8px 10px 2px;
  border-radius: var(--border-radius);
  margin: auto;
  text-align: center;
}

.quickEntryInfo .quickEntryArea>div div:hover {
  background: rgb(249, 249, 249);
}

.quickEntryInfo .quickEntryArea>div div img {
  width: 60px;
  height: 60px;
}

.quickEntryInfo .quickEntryArea>div div p {
  font-size: 15px;
  margin: 5px 0px 8px;
  white-space: nowrap;
}

/* 快捷入口设置 */
.quickEntrySet .tipsTitle {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--secondary-text);
  margin-bottom: 15px;
}

.quickEntrySet .tipsTitle img {
  width: 18px;
  margin-right: 5px;
}

.quickEntrySet,
.quickEntrySet .menuDetail,
.drag-item>div {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

.addedArea .sortable-ghost {
  background: #fff;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.addedArea .sortable-ghost .removeArea {
  display: none;
}

.quickEntrySet .menuDetail .addedArea,
.quickEntrySet .menuDetail .notAdded {
  color: #333;
  font-size: 14px;
}

.quickEntrySet .menuDetail .addedArea>div .drag-item,
.quickEntrySet .menuDetail .notAdded>div {
  padding: 8px 10px;
  border-radius: var(--border-radius);
}

.quickEntrySet .menuDetail .addedArea>div .drag-item>div,
.quickEntrySet .menuDetail .notAdded>div>div {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quickEntrySet .menuDetail .addedArea>div {
  -webkit-transition: 0.3s ease-in-out;
  -moz-transition: 0.3s ease-in-out;
  -o-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
}

.quickEntrySet .menuDetail .addedArea>div .drag-item>div>div:first-child,
.quickEntrySet .menuDetail .notAdded>div>div>div:first-child {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
  overflow: hidden;
  margin-right: 10px;
}

.quickEntrySet .menuDetail .addedArea>div .drag-item>div>div:first-child p,
.quickEntrySet .menuDetail .notAdded>div>div>div:first-child p {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.quickEntrySet .menuDetail .addedArea img:first-child {
  width: 16px;
  height: 16px;
  margin: 0;
}

.quickEntrySet .menuDetail img {
  width: 32px;
  height: 32px;
  margin: 0 12px 0 8px;
}

.addedArea .removeArea p,
.notAdded .addArea p {
  color: var(--font-textColor);
  border: 1px solid var(--border-color);
  background: #fff;
  box-sizing: border-box;
  padding: 3px 8px;
  border-radius: var(--border-radius);
  cursor: pointer;
}

.addedArea .removeArea p:hover,
.notAdded .addArea p:hover {
  color: var(--theme-color);
  border: 1px solid var(--theme-color);
}

.notAdded>div {
  margin-left: 16px;
}

/* 系统公告 */
.noticeTitle {
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  line-height: 25px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 5px;
  cursor: pointer;
}

.noticeTitle>div:first-child {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.noticeTitle>div:first-child p:last-child {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.noticeDetail .noticeTitle>div:last-child {
  color: var(--subColor);
  white-space: nowrap;
  margin-left: 5px;
  display: flex;
  align-items: center;
}

.noticeDetail .noticeTitle>div i {
  font-size: 14px;
}

.noticeDetail>div>div:last-child .noticeTitle {
  border: none;
  margin-bottom: 0;
}

/* 待回复信息 */
.tipNoneData {
  font-size: 15px;
}

.latestNews .dynamicDetail {
  font-size: 14px;
  color: var(--table-text);
  overflow-x: hidden;
  overflow-y: auto;
}

.dynamicDetail .dynamicList {
  margin-bottom: 5px;
  line-height: 1.5;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
}

.dynamicDetail .dynamicList:last-child {
  border-bottom: none;
  margin-bottom: 0px;
  padding-bottom: 0px;
}

.dynamicDetail .replayListDetail .replayProblem p,
.dynamicDetail .replayListDetail .replayTitle>div,
.dynamicDetail .replayListDetail .replayTitle>div p:last-child {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.dynamicDetail .replayListDetail .replayProblem {
  font-size: 16px;
  color: #000;
}

.dynamicDetail .replayListDetail .replayTitle {
  font-size: 14px;
  color: var(--table-text);
  margin: 4px 0 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.partStyle {
  background: rgba(35, 134, 238, 0.1);
  color: #2386ee;
}

/* 维修手册 */
.replayTitle .repair {
  font-size: 12px;
  color: #2386ee;
  background: rgb(35, 134, 238, 0.1);
}

/* 电路图册 */
.replayTitle .circuit {
  font-size: 12px;
  color: #2467ed;
  background: rgb(36, 103, 237, 0.1);
}

/* 备件工时 */
.replayTitle .spare_parts {
  font-size: 12px;
  color: #11a3b4;
  background: rgb(46, 190, 207, 0.1);
}

/* 用户手册 */
.replayTitle .user_manual {
  font-size: 12px;
  color: #11b490;
  background: rgb(45, 207, 174, 0.1);
}

/* 精选车品 */
.replayTitle .boutique {
  font-size: 12px;
  color: #2e45cf;
  background: rgb(45, 67, 207, 0.1);
}

.orderStyle {
  background: rgba(238, 154, 35, 0.1);
  color: #ee9a23;
}

.complaint {
  background: rgba(238, 35, 35, 0.1);
  color: #ee2323;
}

.dynamicDetail .replayListDetail .replayTitle>div {
  display: flex;
  align-items: center;
}

.dynamicDetail .replayListDetail .replayTitle>div p:first-child {
  font-size: 12px;
  margin-right: 5px;
  border-radius: var(--border-radius);
  padding: 0 6px;
  height: 22px;
  line-height: 22px;
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {

  /* header */
  .projectInfo,
  .quickEntryInfo,
  .statisticsInfo,
  .noticeDetail,
  .latestNews {
    padding: 10px;
  }

  .quickEntryInfo>p,
  .statisticsInfo>p,
  .noticeDetail>p,
  .latestNews>p {
    font-size: 16px;
  }

  .quickEntryInfo>p span:last-child,
  .latestNews>p span:last-child,
  .noticeDetail>p .noticeMore {
    font-size: 13px;
  }

  /* 信息 */

  .leftContent .projectInfo>div {
    padding: 5px 1%;
  }

  .leftContent .projectInfo .userInfo .headPortrait {
    height: 30px;
    width: 30px;
  }

  .leftContent .projectInfo .userInfo .greetContent p {
    font-size: 14px;
  }

  .leftContent .projectInfo .taskInfo>div:first-child,
  .leftContent .projectInfo .replyInfo>div:first-child {
    width: 40px;
    height: 40px;
  }

  .leftContent .projectInfo .taskInfo div p,
  .leftContent .projectInfo .replyInfo div p {
    font-size: 12px;
    margin-bottom: 5px;
  }

  .leftContent .projectInfo div div .numberInfo {
    font-size: 18px;
    margin-bottom: 0px;
  }

  /* 快捷入口 */
  /* 快捷入口 */
  .quickEntryInfo .quickEntryArea>div {
    width: 20%;
    margin-bottom: 5px;
  }

  .quickEntryInfo .quickEntryArea>div div {
    width: 80px;
  }

  .quickEntryInfo .quickEntryArea>div div img {
    width: 40px;
    height: 40px;
  }

  .quickEntryInfo .quickEntryArea>div div p {
    font-size: 14px;
  }

  /* 待回复信息 */
  .tipNoneData {
    font-size: 14px;
  }

  .dynamicDetail .replayListDetail .replayProblem {
    font-size: 14px;
  }

  .dynamicDetail .replayListDetail .replayTitle {
    font-size: 12px;
  }
}

@media screen and (max-width: 1024px) {

  /* header */
  .projectInfo,
  .quickEntryInfo,
  .statisticsInfo,
  .noticeDetail,
  .latestNews {
    padding: 8px;
  }

  .quickEntryInfo>p,
  .statisticsInfo>p,
  .noticeDetail>p,
  .latestNews>p {
    font-size: 15px;
  }

  .quickEntryInfo>p span:last-child,
  .latestNews>p span:last-child,
  .noticeDetail>p .noticeMore {
    font-size: 13px;
  }

  /* 信息 */

  .leftContent .projectInfo>div {
    padding: 5px 1%;
  }

  .leftContent .projectInfo .userInfo .headPortrait {
    height: 30px;
    width: 30px;
  }

  .leftContent .projectInfo .userInfo .greetContent p {
    font-size: 12px;
  }

  .leftContent .projectInfo .taskInfo>div:first-child,
  .leftContent .projectInfo .replyInfo>div:first-child {
    width: 40px;
    height: 40px;
  }

  .leftContent .projectInfo .taskInfo div p,
  .leftContent .projectInfo .replyInfo div p {
    font-size: 12px;
    margin-bottom: 5px;
  }

  .leftContent .projectInfo div div .numberInfo {
    font-size: 18px;
    margin-bottom: 0px;
  }

  /* 快捷入口 */
  .quickEntryInfo .quickEntryArea>div {
    width: 20%;
    margin-bottom: 5px;
  }

  .quickEntryInfo .quickEntryArea>div div {
    width: 80px;
  }

  .quickEntryInfo .quickEntryArea>div div img {
    width: 40px;
    height: 40px;
  }

  .quickEntryInfo .quickEntryArea>div div p {
    font-size: 14px;
  }

  /* 待回复信息 */
  .tipNoneData {
    font-size: 14px;
  }

  .dynamicDetail .replayListDetail .replayTitle {
    font-size: 12px;
  }

  .dynamicDetail .replayListDetail .replayProblem {
    font-size: 14px;
  }
}
</style>
