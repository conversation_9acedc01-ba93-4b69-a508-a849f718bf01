<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFour" :model="formInline" class="demo-form-inline">
        <el-form-item label="公司编码" prop="code">
          <el-input v-model.trim="formInline.code" placeholder="请输入公司编码"></el-input>
        </el-form-item>
        <el-form-item label="公司全称" prop="name">
          <el-input v-model.trim="formInline.name" placeholder="请输入公司全称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset()">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss1A3B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addHandle()">新增</el-button>
      </div>
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="公司编号" prop="code" max-width="100"></el-table-column>
        <el-table-column label="公司全称" prop="name" max-width="100"></el-table-column>
        <el-table-column label="联系人" prop="contacts" max-width="100"></el-table-column>
        <el-table-column label="固定号码" prop="telephone" max-width="100"></el-table-column>
        <el-table-column label="手机号" prop="mobile" max-width="100"></el-table-column>
        <el-table-column label="邮政编码" prop="postalCode" max-width="100"></el-table-column>
        <el-table-column label="详细地址" prop="address" max-width="100"></el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template slot-scope="{row}">
            <span :style="{ color: 'green' }" v-if="row.status === 1">启用</span>
            <span :style="{ color: 'red' }" v-if="row.status === 0">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_103')" size="small"
              @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_102')" class="deleteButton" size="small"
              @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false">
        <el-form ref='dataForm' :label-width="formLabelWidth" :model="dataForm" :rules="fromrules"
          label-position="center">
          <el-form-item label="公司编号" prop="code">
            <el-input v-model.trim="dataForm.code" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入公司编号"></el-input>
          </el-form-item>
          <el-form-item label="公司全称" prop="name">
            <el-input v-model.trim="dataForm.name" limit="limit" show-word-limit maxlength="30"
              placeholder="请输入公司全称"></el-input>
          </el-form-item>
          <el-form-item label="联系人" prop="contacts">
            <el-input v-model.trim="dataForm.contacts" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入联系人名称"></el-input>
          </el-form-item>
          <el-form-item label="固定号码" prop="telephone">
            <el-input v-model.trim="dataForm.telephone" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入固定号码"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model.trim="dataForm.mobile" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="邮政编码" prop="postalCode">
            <el-input v-model.trim="dataForm.postalCode" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="详细地址" prop="address">
            <el-input v-model.trim="dataForm.address" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="dataForm.remark" limit="limit" show-word-limit maxlength="100"
              placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="dataForm.status"
              :active-value="1"
              :inactive-value="0"
            ></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick() : editClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { addSalesCompany, deleteSalesCompany, editSalesCompany, getSalesCompanyList } from '@/api/basicmgt';
export default {
  name: "basic_salesCompany_list",
  components: { Pagination },
  data() {
    return {
      formInline: {
        name: '',
        code: ''
      },
      dataForm: {
        id: '',
        name: '',
        code: '',
        contacts: '',
        telephone: '',
        mobile: '',
        postalCode: '',
        address: '',
        remark: '',
        status: 1
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      dialogStatus: '',
      textMap: {
        edit: '编辑销售公司',
        add: '新增销售公司'
      },
      id: '',
      whether: false,
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      fromrules: {
        name: [{ required: true, message: '公司全称不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '公司编号不能为空', trigger: ['blur', 'change'] }]
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      let obj = {
        page: this.currentPage,
        limit: this.pagesize,
        name: this.formInline.name,
        code: this.formInline.code,
      }
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(obj)) {
        params.append(key, value);
      }
      getSalesCompanyList(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea()
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    reset() {
      if (this.$refs["formInline"].resetFields() !== undefined) {
        this.$refs["formInline"].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    resetTemp() {
      this.dataForm = {
        id: '',
        name: '',
        code: '',
        contacts: '',
        telephone: '',
        mobile: '',
        postalCode: '',
        address: '',
        remark: '',
        status: 1
      }
      this.$nextTick(function () {
        this.$refs.dataForm.clearValidate();
      })
    },
    // 新增
    addHandle() {
      this.resetTemp()
      this.dialogStatus = 'add'
      this.dialogFormVisible = true
    },
    addClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let params = {
            'name': this.dataForm.name,
            'code': this.dataForm.code,
            'contacts': this.dataForm.contacts,
            'telephone': this.dataForm.telephone,
            'mobile': this.dataForm.mobile,
            'postalCode': this.dataForm.postalCode,
            'address': this.dataForm.address,
            'remark': this.dataForm.remark,
            'status': this.dataForm.status
          }
          addSalesCompany(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 编辑
    handleEdit(row) {
      this.dialogStatus = 'edit'
      this.dialogFormVisible = true
      this.resetTemp()
      this.dataForm = Object.assign({}, row)
    },
    editClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let params = {
            'id': this.dataForm.id,
            'name': this.dataForm.name,
            'code': this.dataForm.code,
            'contacts': this.dataForm.contacts,
            'telephone': this.dataForm.telephone,
            'mobile': this.dataForm.mobile,
            'postalCode': this.dataForm.postalCode,
            'address': this.dataForm.address,
            'remark': this.dataForm.remark,
            'status': this.dataForm.status
          }
          editSalesCompany(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              if (res.data.code === 404) {
                this.$DonMessage.error('系统出现异常，更新失败')
              } else {
                this.$DonMessage.error(res.data.msg)
              }
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 删除
    handleDelete(row) {
      this.currentPage = 1
      this.$confirm('确定刪除【' + row.name + '】的相关信息?', '删除销售公司', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.companyClick(row.id);
      })
    },
    companyClick(id) {
      deleteSalesCompany(id).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          if (this.resultList != null && this.resultList.length == 1) {
            this.currentPage = this.currentPage - 1
          }
          this.dataList()
        } else {
          this.$DonMessage.error('删除失败: ' + res.data.msg)
        }
      })
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.dataList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
