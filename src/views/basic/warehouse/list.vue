<template>
  <div class="layoutContainer warehouseManage">
    <!-- 顶部搜索框 -->
    <div class="secondFloat">
      <el-form :inline="true" :label-width="$labelFour" ref="search" :model="search" class="demo-form-inline">
        <el-form-item label="类别名称" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="search.categoryName"
            :inputParam="search.categoryName"
            inputType="warehouse"
            placeholder="请选择类别名称"
            @select="onInputSearch('search', $event)"
          ></selectInput>
        </el-form-item>
        <el-form-item label="仓库编码" prop="code">
          <el-input v-model="search.code" placeholder="请输入仓库编码"/>
        </el-form-item>
        <el-form-item label="仓库名称" prop="name">
          <el-input v-model="search.name" placeholder="请输入仓库名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="onSearch">搜索</el-button>
          <el-button plain size="small" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton none">
              <span>仓库类别</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <el-scrollbar>
                <el-tree
                  :data="categoryTree"
                  node-key="id"
                  :render-content="renderContent"
                  :default-expand-all="true"
                  @node-click="handleCategorySelect"
                  @node-contextmenu="handleNodeContextmenu"
                  :props="{
                    label: 'name',
                    children: 'children'
                  }"
                  ref="categoryTree"
                >
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight">
          <div class="rightTitle none">
            <el-button type="text" icon="el-icon-plus" @click="addData()">新增</el-button>
          </div>
          <div class="detailInfo rightTableArea">
            <el-table style="width:100%;" ref="table" highlight-current-row :data="tableData" border stripe
              :max-height="maximumHeight" @header-dragend="changeColWidth">
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="code" label="仓库编码" min-width="130">
                <template slot-scope="{row}">
                  <span class="linkStyle" @click="infoData(row)">{{ row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="仓库名称" min-width="130"></el-table-column>
              <el-table-column prop="categoryName" label="类别" width="85"></el-table-column>
              <el-table-column prop="keeperUser" label="仓库管理员" width="110"></el-table-column>
              <el-table-column prop="mobile" label="手机号" width="120"></el-table-column>
              <el-table-column prop="address" label="详细地址" min-width="100"></el-table-column>
              <el-table-column label="状态" prop="status" width="80">
                <template slot-scope="{row}">
                  <span class="successColor" v-if="row.status === 1">启用</span>
                  <span class="errorColor" v-if="row.status === 0">禁用</span>
                </template>
              </el-table-column>
              <el-table-column prop="createdTime" label="创建时间" width="160">
                <template slot-scope="{row}">
                  <div>
                    {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="updatedTime" label="更新日期" width="160">
                <template slot-scope="{row}">
                  <div>
                    {{ row.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
              <el-table-column label="操作" fixed="right" width="160">
                <template slot-scope="{row}">
                  <el-button type="text" @click="infoData(row)">
                    详情
                  </el-button>
                  <el-button type="text" @click="addData(row)">
                    编辑
                  </el-button>
                  <el-button class="deleteButton" type="text" @click="delData(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="pagination.total > 0" :total="pagination.total" :page.sync="pagination.page"
              :limit.sync="pagination.size" @pagination="fetchTableData" />
          </div>
        </el-col>
      </el-row>
      <!-- 右键菜单 -->
      <div class="contextmenuArea">
        <div @click="onAddCategory" v-if="currentNode">
          <i class="el-icon-plus"></i>
          <span>新增</span>
        </div>
        <div @click="onEditCategory" v-if="currentNode && currentNode.id != ''">
          <i class="compile-icon"></i>
          <span>编辑</span>
        </div>
        <div class="deleteMark" @click="onDeleteCategory" v-if="currentNode && currentNode.id != ''">
          <i class="deleteRed-icon"></i>
          <span>删除</span>
        </div>
      </div>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogRightFormVisible" v-if="dialogRightFormVisible">
      <el-form  :label-width="formLabelWidth" :model="form" :rules="rules" ref="form">
        <el-form-item label="上级类别" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="form.categoryName"
            :inputParam="form.categoryName"
            inputType="warehouse"
            @select="onInputSearch('form', $event)"
            placeholder="请选择上级类别"
            :disabled="dialogStatus == 'addCategory' ? true : false"
          ></selectInput>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model.trim="form.name" placeholder="请输入名称" maxlength="50" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model.trim="form.sort" placeholder="请输入排序" controls-position="right" :min="1" :max="99"
          :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleCategoryUpdate()">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogRightFormVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination';
import selectInput from "@/components/selectInput/selectInput.vue";

import {
  getCategoryTree,
  getWarehouseList,
  addCategory,
  updateCategory,
  deleteCategory,
  deleteWarehouse
} from '@/api/basicmgt'
import { addTabs, tableHeight, contextmenuSeat, categoryTreeInfo, renderTree } from "@/assets/js/common";

export default {
  components: { Pagination, selectInput },
  data() {
    return {
      search: {
        code: '',
        name: '',
        categoryName: '',
        bizType: 'warehouse'
      },
      maximumHeight: 0,
      addDialogVisible: false,
      textMap: {
        editCategory: "编辑仓库类别",
        addCategory: "新增仓库类别",
      },
      addForm: {
        categoryId: "", // 类别id
        categoryName: '全部', // 类别名称（仅展示用）
        name: '', // 仓库名称
        code: '', // 仓库编码
        remark: '',
        keeperUser: '',
        mobile: '',
        address: '',
        status: 1
      },
      formLabelWidth: "100px",
      selectedCategoryId: '',
      tableData: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      categoryTree: [{ id: '', name: '全部', children: [] }],
      dialogStatus: "",
      dialogVisible: false,
      dialogRightFormVisible: false,
      form: {
        id: 0,
        pid: 0,
        categoryName: '',
        name: '',
        sort: 0,
        bizType: 'warehouse'
      },
      rules: {
        categoryName: [
          { required: true, message: '上级类别不能为空', trigger: ['blur', 'change'] },
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: ['blur', 'change'] },
        ],
      },
      currentNode: null, //
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    renderContent(h, { node, data, store }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".warehouseManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    onInputSearch(type, $event) {
      if (type == 'search') {
        this.search.categoryName = $event.name;
        this.selectedCategoryId = $event.id;
      } else if (type == 'form') {
        this.form.categoryName = $event.name;
        this.form.pid = $event.id;
      } else {
        this.addForm.categoryName = $event.name;
        this.addForm.categoryId = $event.id;
      }
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSearch()
      }
    },
    onSearch() {
      this.search.categoryName = "全部" ? '' : this.search.categoryName;
      this.initializedData()
    },
    onReset() {
      if (this.$refs['search'].resetFields() !== undefined) {
        this.$refs['search'].resetFields()
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.selectedCategoryId = '';
      this.search.categoryName = '';
      this.initializedData()
    },
    // 类别树相关
    async fetchCategoryTree() {
      try {
        const res = await getCategoryTree({ bizType: 'warehouse' })
        this.categoryTree = [{
          id: '',
          name: '全部',
          children: Array.isArray(res.data.data) ? res.data.data : [] // 确保children是数组
        }];
        this.$nextTick(() => {
          if (this.categoryTree.length > 0) {
            this.$refs.categoryTree.setCurrentKey(this.categoryTree[0]); // 选中第一个节点
          }
        });
      } catch (e) {
        this.categoryTree = [{ id: '', name: '全部', children: [] }];
      }
    },
    initializedData () {
      this.pagination.page = 1
      this.fetchTableData()
    },
    // 列表相关
    async fetchTableData() {
      const obj = {
        ...this.search,
        categoryId: this.selectedCategoryId,
        page: this.pagination.page,
        size: this.pagination.size
      }
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(obj)) {
        params.append(key, value);
      }
      const res = await getWarehouseList(params)
      this.tableData = res.data.data
      this.pagination.total = res.data.total
      this.tableHeightArea()
    },

    addFormClear() {
      this.addForm = {
        categoryId: '',
        categoryName: '全部',
        name: '',
        code: '',
        remark: '',
        keeperUser: '',
        mobile: '',
        address: '',
        status: 1
      }
      this.$nextTick(function () {
        this.$refs.addForm.clearValidate();
      });
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    infoData(row) {
      var title = row.code;
      this.$router.push({ name: 'warehouseDetail', params:{'id': row.id} })
      addTabs(this.$route.path, title);
    },

    addData(row) {
      var title = ""
      var id = ""
      if (row == undefined) {
        title = "新增仓库信息"
        id = "add"
      } else {
        title = "编辑 " + row.code
        id = row.id
      }
      if(id == "add") {
        var params = {
          name: "全部",
          id: "",
        }
        if (this.currentNode != null) {
          params.name = this.currentNode.name;
          params.id = this.currentNode.id;
        }
        sessionStorage.setItem('warehouseName', JSON.stringify(params));
      }
      this.$router.push({ name: 'addWarehouse', params: {'id': id} })
      if (id == "add") {
        this.$store.commit("removeContentCatch", this.$route.path);
      }
      addTabs(this.$route.path, title);
    },
    //删除
    delData(row) {
      this.$confirm('确定删除【' + row.name + '】仓库吗?', '删除仓库', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res =  await deleteWarehouse(row.id)
        if (res.data.code == '100') {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          if (this.tableData != null && this.tableData.length == 1) {
            this.pagination.page = this.pagination.page - 1
          }
          this.fetchTableData()
        } else {
          this.$DonMessage.success(res.data.msg)
        }
      })
    },
    // 初始化数结构内容
    formClear() {
      this.form = {
        id: 0,
        pid: 0,
        sort: 0,
        name: "",
        bizType: 'warehouse'
      };
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      });
    },
    // 树结构选中数据
    handleCategorySelect(node) {
      // node.id 就是当前选中的类别ID
      this.selectedCategoryId = node.id
      this.currentNode = node
      this.initializedData()
    },
    // 右键菜单相关
    handleNodeContextmenu(event, data) {
      event.preventDefault()
      this.currentNode = data
      contextmenuSeat(event, ".warehouseManage .contextmenuArea");
    },
    // 新增
    onAddCategory() {
      this.dialogStatus = "addCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      this.form.categoryName = this.currentNode.name;
      this.form.pid = this.currentNode.id
    },
    // 编辑
    onEditCategory() {
      this.dialogStatus = "editCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      console.log(this.currentNode);
      this.form = Object.assign({}, this.currentNode);
      this.form.categoryName = categoryTreeInfo(this.categoryTree, this.form.id);
    },
    // 删除
    onDeleteCategory() {
      this.handleCategoryDelete(this.currentNode)
    },
    async handleCategoryDelete(data) {
      this.$confirm('确定删除【' + data.name + '】类别吗?', '删除仓库类别', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          id: data.id,
          bizType: 'warehouse'
        }
        const res = await deleteCategory(new URLSearchParams(params))
        if (res.data.code == '100') {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          this.fetchCategoryTree()
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },
    // 提交
    async handleCategoryUpdate() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (this.dialogStatus === 'addCategory') {
            const res = await addCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dialogRightFormVisible = false
              this.fetchCategoryTree()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          } else {
            const res = await updateCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dialogRightFormVisible = false
              this.fetchCategoryTree()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          }
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.fetchCategoryTree()
    this.fetchTableData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
