<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="statePage == 'add'">新增仓库信息</div>
      <div v-if="statePage == 'edit'">编辑仓库信息</div>
      <div>
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button plain @click="cancel">取消</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activePanels">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <!-- 基本信息新增编辑-->
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFive">
                <el-form-item label="仓库编码" prop="code">
                  <el-input v-model="form.code" placeholder="仓库编码由系统生成" disabled/>
                </el-form-item>
                <el-form-item label="仓库名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入仓库名称" />
                </el-form-item>
                <el-form-item label="仓库类别" prop="categoryName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.categoryName"
                    :inputParam="form.categoryName"
                    inputType="warehouse"
                    @select="onInputSearch"
                    placeholder="请选择仓库类别"
                  >
                  </selectInput>
                </el-form-item>
                <el-form-item label="仓库管理员" prop="keeperUser">
                  <el-input v-model="form.keeperUser" placeholder="请输入仓库管理员" />
                </el-form-item>
                <el-form-item label="手机号" prop="mobile">
                  <el-input v-model="form.mobile" placeholder="请输入手机号"/>
                </el-form-item>
                <el-form-item label="地址" prop="address">
                  <el-input v-model="form.address" placeholder="请输入地址" />
                </el-form-item>
                <el-form-item label="启用状态" prop="status">
                  <el-switch
                    v-model="form.status"
                    :active-value="1"
                    :inactive-value="0"/>
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500" show-word-limit/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <!-- 库位信息 -->
          <el-collapse-item name="contact">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                库位信息
              </span>
            </template>
            <div class="tableHandle spaceBbetwee">
              <el-button type="primary" icon="addProducts-icon" @click="addContact" style="margin-bottom: 10px;">添加库位</el-button>
              <div>
                <el-button type="text" icon="import-icon" v-if="false">批量导入</el-button>
                <el-button type="text" icon="deleteRed-icon" @click="batchDelete">批量删除</el-button>
              </div>
            </div>
            <el-table
              :data="form.warehouseLocationList"
              ref="table"
              border
              stripe
              highlight-current-row
              style="width: 100%;"
              @header-dragend="changeColWidth"
              @selection-change="handleSelectChange"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column type="index" label="序号" width="50" />
              <el-table-column prop="code" label="库位编码" min-width="120">
                
                <template #default="{ row }">
                  <el-form :model="row" :rules="formRules" ref="locationCode">
                    <el-form-item prop="code">
                      <el-input v-model="row.code" disabled placeholder="库位编码由系统生产"></el-input>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="库位名称" min-width="120">
                <template #header>
                  <span class="required-field">库位名称</span>
                </template>
                <template #default="{ row }">
                  <el-form :model="row" :rules="formRules" ref="locationName">
                    <el-form-item prop="name">
                      <el-input v-model="row.name"></el-input>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>

              <el-table-column label="启用状态" prop="status" width="120">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.status"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="120">
                <template slot-scope="scope">
                  <div>
                    <el-input v-model="scope.row.remark"/>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="80">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton" @click="removeContact(scope.$index)" >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="创建人">
                <span>{{ form.createdName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                <span>{{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>
<script>
import selectInput from "@/components/selectInput/selectInput.vue";
import { sysServerUrl, removeTabs, addTabs, collapseArea, beforeRouteInfo, getContentData } from '@/assets/js/common.js'
import {addWarehouse, editWarehouse, warehouseInfo} from '@/api/basicmgt';
import { getBatchDeleteInfo, getDeleteInfo } from '@/views/basic/basicCommon'
import { getColumnNumber } from "@/assets/js/heightResize";
export default {
  name: 'addWarehouse',
  components: { selectInput },
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      rules: {
        code:[{ required: true,  validator: (rule, value, callback) => callback()}],
        name: [{ required: true, message: '仓库名称不能为空', trigger: ['blur', 'change'] }],
        categoryName: [{ required: true, message: '仓库类别不能为空', trigger: ['blur', 'change'] }]
      },
      statePage: "", // 页面状态
      form: {
        id: "",
        categoryId: "",
        categoryName:"",
        code: '',
        name: '',
        keeperUser: '',
        mobile: '',
        address: '',
        status: 1,
        remark: '',
        warehouseLocationList: [],
        createdTime: "",
        createdName: "",
      },
      activePanels: ['base', 'contact', 'more'], // 默认全部展开
      // 表格必填提示
      formRules: {
        code: [{ required: true,  validator: (rule, value, callback) => callback() }],
        name: [{ required: true,  message: '请输入库位名称', trigger: ['blur', 'change'] }]
      },
      selectList:[],
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    onInputSearch(val) {
      this.form.categoryId = val.id;
      this.form.categoryName = val.name;
    },
    formClear() {
      this.form = {
        id: "",
        categoryId: "",
        categoryName:"",
        code: '',
        name: '',
        keeperUser: '',
        mobile: '',
        address: '',
        status: 1,
        remark: '',
        warehouseLocationList: [],
        createdTime: "",
        createdName: "",
      }
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      })
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    async getDetailInfo() {
      try {
        const res = await warehouseInfo(this.form.id)
        if (res.data.code === 100) {
          if (res.data.data !== null){
            this.form = res.data.data;
            this.form.categoryName = this.form.categoryName ? this.form.categoryName : '全部';
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.cancel()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.cancel()
      }
    },
    // 添加库位
    addContact() {
      this.form.warehouseLocationList.push({ name: '', code:'', keeperUser:'',  mobile: '',  createdTime: '', status:1 ,  remark: '', codeError: false, nameError: false })
    },
    handleSelectChange(val) {
      this.selectList = val;
    },
    batchDelete() {
      getBatchDeleteInfo(this.form.warehouseLocationList, this.selectList, "删除库位信息")
    },
    removeContact(index) {
      getDeleteInfo(this.form.warehouseLocationList, index, "删除库位信息")
    },
    // 提交
    submit() {
      let isValid = true;
      // 判断必填项
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
          isValid = false
          return
        }
      });
      if (this.form.warehouseLocationList.length > 0){
        if (this.$refs["locationName"] != undefined) {
          this.$refs["locationName"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("库位名称不能为空");
              isValid = false
              return
            }
          });
        }
      } else {
        this.$DonMessage.warning("库位信息不能为空");
        isValid = false
        return
      }
      if (isValid) {
        // 添加
        if (this.form.id == 'add' || this.statePage == 'add'){
          addWarehouse(this.form).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              removeTabs(this.$route);
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          })
        }else {  // 修改
          editWarehouse(this.form).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              removeTabs(this.$route);
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          })
        }
      }
    },
    // 取消
    cancel() {
      removeTabs(this.$route);
    },
    // 初始化数据
    async initialState() {
      var _this = this;
      _this.form.id = _this.$route.params.id;
      if (_this.form.id !== 'add') {
        _this.statePage = 'edit'
        // 编辑获取详情
        await _this.getDetailInfo();
      } else {
        _this.statePage = 'add'
        await _this.formClear()
        _this.form.createdTime = new Date().getTime();
        _this.form.createdName = _this.$store.state.realName;
        var list = JSON.parse(sessionStorage.getItem('warehouseName'));
        _this.form.categoryName = list.name;
        _this.form.categoryId = list.id;
      }
      getContentData(_this)
      collapseArea();
    },
  },
  mounted() {
    this.initialState();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name == "addWarehouse") {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  },
}
</script>
