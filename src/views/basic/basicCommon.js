import Vue from 'vue'
import { MessageBox } from 'element-ui';
import router from '@/router';
import store from "@/store/index";
import i18n from "@/i18n/i18n";
import DonMessage from '@/plugins/message';
import {
  getCategoryTree,
  getProductcategoryTree,
  getProductcategoryList,
  getWarehouseList,
  warehouseInfo,
  getBrandList,
  getProductList,
  getSupplierList,
  getUnitList,
  getCustomerList,
} from '@/api/basicmgt';
import {getProductListBySaleNo} from "@/api/purchasemgt";
import { addTabs } from '@/assets/js/common';
import { getBomList } from "@/api/mpsmgt"
// 获取供应商
export let list = [
  {
    id:"",
    name: "全部",
    children: []
  }
];
export let treeData = [];
// 品牌类别 客户类别 仓库类别 供应商类别
export function getCategoryTreeData(type) {
  return new Promise(async (resolve) => {
    try {
      const listRes = await getCategoryTree({ bizType: type })
      if (listRes.data.code == '100') {
        list[0].children = Array.isArray(listRes.data.data) ? listRes.data.data: "";
      }
      resolve(list);
    } catch (error) {
      resolve();
    }
  })
}
// 商品类别
export function productCategoryTree() {
  return new Promise(async (resolve) => {
    try {
      var param = {};
      const treeRes = await getProductcategoryTree(param);
      if(treeRes.data.code == "100") {
        list[0].children = Array.isArray(treeRes.data.data) ? treeRes.data.data: ""
      }
      resolve(list);
    } catch (error) {
      resolve();
    }
  })
}

// 默认仓库
export function getWarehouseData(_this) {
  return new Promise(async (resolve) => {
    try {
      const params = {
        code: _this.searchCode,
        name: _this.searchName,
        bizType: "warehouse",
        status: _this.status,
        page: _this.currentPage,
        limit: _this.pagesize
      }
      const res = await getWarehouseList(params)
      if (res.data.code == '100') {
        treeData = res.data.data;
        _this.total= res.data.total;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 默认库位
export function getLocationData(_this,id) {
  return new Promise(async (resolve) => {
    try {
      const res = await warehouseInfo(id)
      if (res.data.code == '100') {
        let locationList = []
        let allList = res.data.data.warehouseLocationList;
        _this.total = allList.length;
        if (_this.total > 0) {
          allList = allList.filter((f) => f.status == 1);
          allList = allList.filter((f) => f.code.indexOf(_this.searchCode) !== -1);
          allList = allList.filter((f) => f.name.indexOf(_this.searchName) !== -1);
          _this.total = allList.length;
          let startIndex = (_this.currentPage - 1) * _this.pagesize;
          let endIndex = _this.currentPage * _this.pagesize;
          if (allList != null && _this.total > 0) {
            if (allList.length >= endIndex) {
              locationList = allList.slice(startIndex, endIndex);
            } else {
              if (allList.length >　startIndex) {
                locationList = allList.slice(startIndex)
              } else {
                locationList = [];
              }
            }
          } else {
            locationList = [];
          }
        }
        treeData = locationList;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 计量单位
export function getUnitListData() {
  return new Promise(async (resolve) => {
    try {
      var param = {
        status: 1,
      }
      const res = await getUnitList(param);
      if (res.data.code == '100') {
        treeData = res.data.data
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 品牌信息
export function getBrandData(_this) {
  return new Promise(async (resolve) => {
    try {
      const params = {
        code: _this.searchCode,
        name: _this.searchName,
        bizType: "brand",
        categoryId: _this.categoryId,
        status: _this.status,
        page: _this.currentPage,
        limit: _this.pagesize
      }
      const res = await getBrandList(params)
      if (res.data.code == '100') {
        treeData = res.data.data;
        _this.total= res.data.total;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 客户信息
export function getCustomerData(_this) {
  return new Promise(async (resolve) => {
    try {
      const params = {
        code: _this.searchCode,
        name: _this.searchName,
        categoryId: _this.categoryId,
        bizType: "customer",
        status: _this.status,
        page: _this.currentPage,
        limit: _this.pagesize
      }
      const res = await getCustomerList(params);
      if (res.data.code == '100') {
        treeData = res.data.data;
        _this.total= res.data.total;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 商品信息
export function getProductData(_this, categoryId, customerId, underStock, selectType) {
  return new Promise(async (resolve) => {
    try {
      if (customerId === undefined) {
        customerId = ""
      }
      if (_this.type === "material" || _this.type === "bomProduct"){
        _this.status = 1; //查询已启用的商品信息
      }
      const params = {
        code: _this.searchCode,
        name: _this.searchName,
        status: _this.status,
        underStock: underStock,
        page: _this.currentPage,
        limit: _this.pagesize,
        categoryId: categoryId,
        customerId: customerId,
        selectType: selectType,
        purchasable: 1
      }
      const res = await getProductList(params)
      if (res.data.code == '100') {
        treeData = res.data.data;
        _this.total = res.data.total;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 供应商信息
export function getSupplierData(_this) {
  return new Promise(async (resolve) => {
    try {
      // status: _this.status,
      const params = {
        code: _this.searchCode,
        name: _this.searchName,
        bizType: "supplier",
        categoryId: _this.categoryId,
        page: _this.currentPage,
        limit: _this.pagesize,
      }
      const res = await getSupplierList(params)
      if (res.data.code == 100) {
        treeData = res.data.data;
        _this.total= res.data.total;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 添加物流获取商品信息
export function getProductSaleNoData(_this, salesNo) {
  return new Promise(async (resolve) => {
    try {
      const params = {
        page: _this.currentPage,
        limit: _this.pagesize,
        salesNo: salesNo,
        productCode:_this.searchCode,
        productName:_this.searchName,
        purchasable: 1
      }
      const res = await getProductListBySaleNo(params)
      if (res.data.code === 100) {
        treeData = res.data.data;
        _this.total= res.data.total;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })
}
// 获取bom相关数据
export function getBomListData(_this, id) {
  return new Promise(async (resolve) => {
    try {
      const param = new URLSearchParams();
      param.append("productId", id);
      param.append("page", _this.detailPage);
      param.append("limit", _this.detailSize);
      const res = await getBomList(param);
      if (res.data.code == '100') {
        treeData = res.data.data;
        _this.detailTotal= res.data.total;
      }
      resolve(treeData);
    } catch (error) {
      resolve();
    }
  })

}
// 新增商品信息
export function addProductInfo(node) {
  var title = "新增商品信息";
  var params = {
    name: "全部",
    id: "",
  }
  if (node != null) {
    params.name = node.name;
    params.id = node.id;
  }
  sessionStorage.setItem("commodityName", JSON.stringify(params));
  router.push({ name: 'addProduct', params: {id: 'add'} });
  var path = router.history.current.path;
  store.commit("removeContentCatch", path);
  addTabs(router.history.current.path, title);
}
// 新增供应商
export function addSupplierInfo(node){
  var title = "新增供应商信息";
  var id = "add";
  var params = {
    name: "全部",
    id: "",
  }
  if (node != null) {
    params.name = node.name;
    params.id = node.id;
  }
  sessionStorage.setItem('supplierName', JSON.stringify(params));
  router.push({ name: 'addSupplier', params: {'id': id} })
  var path = router.history.current.path;
  store.commit("removeContentCatch", path);
  addTabs(path, title);
}
// 批量删除
export function getBatchDeleteInfo(list, selectList, title) {
  if (selectList.length == 0) {
    DonMessage.warning("请选择需要删除的数据")
    return
  }
  MessageBox.confirm('确定删除相关信息?', title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    for (var i = 0; i < list.length; i++) {
      for (var k = 0; k < selectList.length; k++) {
        if (selectList[k].id != undefined) {
          if (list[i].id == selectList[k].id) {
            list.splice(i, 1);
          }
        }
      }
    }
    for (var j = list.length -1; j >= 0; j--) {
      for (var k = 0; k < selectList.length; k++) {
        if (selectList[k].id == undefined) {
          if (selectList.includes(list[j])) {
            list.splice(j, 1);
          }
        }
      }
    }
    DonMessage.success(i18n.t('successTip.deleteTip'))
  })
}
// 单个删除
export function getDeleteInfo(list, index, title) {
  MessageBox.confirm('确定删除相关信息?', title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    list.splice(index, 1);
    DonMessage.success(i18n.t('successTip.deleteTip'));
  })
}
