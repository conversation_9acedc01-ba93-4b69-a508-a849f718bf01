<template>
  <div class="layoutContainer customerManage">
    <!-- 顶部搜索框 -->
    <div class="secondFloat">
      <el-form :inline="true" :label-width="$labelFour" ref="search" :model="search" class="demo-form-inline">
        <el-form-item label="客户类别" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="search.categoryName"
            :inputParam="search.categoryName"
            inputType="customer"
            @select="onInputSearch('search', $event)"
            placeholder="请选择客户类别"
          ></selectInput>
        </el-form-item>
        <el-form-item label="客户编码" prop="code">
          <el-input v-model="search.code" placeholder="请输入客户编码" />
        </el-form-item>
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="search.name" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="onReset">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton none">
              <span>客户类别</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <el-scrollbar>
                <el-tree :data="categoryTree" node-key="id" :render-content="renderContent" :default-expand-all="true"
                  @node-click="handleCategorySelect" @node-contextmenu="handleNodeContextmenu"
                  :props="{ label: 'name', children: 'children' }" ref="categoryTree">
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight">
          <div class="rightTitle none">
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addData(null)">新增</el-button>
              <el-button type="text" icon="bulkImport-icon" @click="batchImport()" v-if="false">批量导入</el-button>
              <el-button type="text" icon="bulkDown-icon" @click="exportClick()" v-if="false">导出</el-button>
              <el-button type="text" icon="enable-icon" @click="enableClick()" v-if="false">启用</el-button>
              <el-button type="text" icon="disable-icon" @click="disableClick()" v-if="false">禁用</el-button>
              <el-button type="text" icon="deleteRed-icon" @click="delClick()" v-if="false">删除</el-button>
            </div>
            <div v-if="false">
              <el-button type="text" icon="el-icon-setting">自定义列</el-button>
              <el-button type="text" icon="el-icon-refresh">刷新</el-button>
            </div>
          </div>
          <div class="detailInfo rightTableArea">
            <el-table style="width:100%;" ref="table" highlight-current-row :data="tableData" border stripe
              :max-height="maximumHeight" @header-dragend="changeColWidth">
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="code" label="客户编码" min-width="120" sortable>
                <template slot-scope="{row}">
                  <span class="linkStyle" @click="infoData(row)">{{ row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="客户名称" min-width="120"></el-table-column>
              <el-table-column prop="categoryName" label="类别" width="85"></el-table-column>
              <el-table-column prop="contactsName" label="联系人" width="100"></el-table-column>
              <el-table-column prop="contactsMobile" label="联系电话" width="120"></el-table-column>
              <el-table-column prop="contactsAddress" label="发货地址" min-width="150"></el-table-column>
              <el-table-column label="状态" prop="status" width="80">
                <template slot-scope="{row}">
                  <span class="successColor" v-if="row.status === 1">启用</span>
                  <span class="errorColor" v-if="row.status === 0">禁用</span>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="120" />
              <el-table-column label="操作" fixed="right" width="150">
                <template slot-scope="{row}">
                  <el-button type="text" size="small" @click="infoData(row)">
                    详情
                  </el-button>
                  <el-button type="text" size="small" @click="addData(row)">
                    编辑
                  </el-button>
                  <el-button class="deleteButton" type="text" size="small" @click="delData(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="pagination.total > 0" :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.size"
              @pagination="fetchTableData" />
          </div>
        </el-col>
      </el-row>
      <!-- 右键菜单 -->
      <div class="contextmenuArea">
        <div @click="onAddCategory" v-if="currentNode">
          <i class="el-icon-plus"></i>
          <span>新增</span>
        </div>
        <div @click="onEditCategory" v-if="currentNode && currentNode.id != ''">
          <i class="compile-icon"></i>
          <span>编辑</span>
        </div>
        <div class="deleteMark" @click="onDeleteCategory" v-if="currentNode && currentNode.id != ''">
          <i class="deleteRed-icon"></i>
          <span>删除</span>
        </div>
      </div>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogRightFormVisible" v-if="dialogRightFormVisible">
      <el-form v-if="dialogStatus === 'editCategory' || dialogStatus === 'addCategory'" :label-width="formLabelWidth"
        :model="form" :rules="rules" ref="form">
        <el-form-item label="上级类别" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="form.categoryName"
            :inputParam="form.categoryName"
            inputType="customer"
            @select="onInputSearch('form', $event)"
            placeholder="请选择上级类别"
            :disabled="dialogStatus == 'addCategory' ? true : false"
          ></selectInput>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" limit="limit" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort" v-if="dialogStatus != 'categoryRename'">
          <el-input-number v-model.trim="form.sort" placeholder="请输入排序" controls-position="right" :min="1" :max="9999" :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleCategoryUpdate()">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogRightFormVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import selectInput from "@/components/selectInput/selectInput.vue";
import {
  getCategoryTree,
  getCustomerList,
  addCategory,
  updateCategory,
  deleteCategory,
  deleteCustomer
} from '@/api/basicmgt'
import { addTabs, renderTree, tableHeight, contextmenuSeat, categoryTreeInfo } from "@/assets/js/common";

export default {
  name: "basic_customer_list",
  components: { Pagination, selectInput },
  data() {
    return {
      maximumHeight: 0,
      addDialogVisible: false,
      textMap: {
        editCategory: "编辑客户类别",
        addCategory: "新增客户类别",
        addData: "新增客户信息",
        editData: "编辑客户信息"
      },
      addForm: {
        categoryId: '', // 类别id
        categoryName: '全部', // 类别名称（仅展示用）
        name: '', // 客户信息名称
        code: '', // 客户信息编码
        remark: '',
        keeperUser: '',
        mobile: '',
        address: '',
        status: true
      },
      formLabelWidth: "100px",
      treeSearch: '',
      selectedCategoryId: '',
      tableData: [],
      search: {
        code: '',
        name: '',
        categoryName: '',
        bizType: 'customer',
      },
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      // 客户类别
      dialogStatus: "",
      categoryTree: [{ id: '', name: '全部', children: [] }],
      dialogRightFormVisible: false,
      dialogVisible: false,
      form: {
        categoryName: '',
        id: 0,
        pid: 0,
        name: '',
        sort: '',
        bizType: 'customer'
      },
      rules: {
        categoryName:  [{ required: true, message: '上级类别不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
      },
      currentNode: null,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    renderContent(h, { node, data, store }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".customerManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    onInputSearch(type, $event) {
      if (type == "search") {
        this.search.categoryName =  $event.name;
        this.selectedCategoryId =  $event.id;
      } else if (type == "form") {
        this.form.categoryName =  $event.name;
        this.form.pid =  $event.id;
      }
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSearch()
      }
    },
    onSearch() {
      this.search.categoryName = "全部" ? '' : this.search.categoryName;
      this.initializedData()
    },
    onReset() {
      if (this.$refs["search"].resetFields() !== undefined) {
        this.$refs["search"].resetFields();
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear();
      }
      this.search.categoryName = "";
      this.selectedCategoryId = '';
      this.initializedData()
    },
    // 类别树相关
    async fetchCategoryTree() {
      try {
        const res = await getCategoryTree({ bizType: 'customer' })
        this.categoryTree = [{
          id: '',
          name: '全部',
          children: Array.isArray(res.data.data) ? res.data.data : [] // 确保children是数组
        }];
        this.$nextTick(() => {
          if (this.categoryTree.length > 0) {
            this.$refs.categoryTree.setCurrentKey(this.categoryTree[0]); // 选中第一个节点
          }
        });
      } catch (e) {
        this.categoryTree = [{ id: '', name: '全部', children: [] }];
      }
    },
    initializedData() {
      this.pagination.page = 1
      this.fetchTableData()
    },
    // 列表相关
    async fetchTableData() {
      const obj = {
        ...this.search,
        categoryId: this.selectedCategoryId,
        page: this.pagination.page,
        limit: this.pagination.size
      }
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(obj)) {
        params.append(key, value);
      }
      const res = await getCustomerList(params)
      this.tableData = res.data.data
      this.pagination.total = res.data.total
      this.tableHeightArea()
    },
    // 新增编辑
    addData(row) {
      var title = ""
      var id = ""
      if (row == undefined) {
        title = "新增客户信息"
        id = "add"
      } else {
        title = '编辑 ' + row.code;
        id = row.id
      }

      if (id == "add") {
        var param = {
          name: "全部",
          id: "",
        }
        if (this.currentNode != null) {
          param.name = this.currentNode.name;
          param.id = this.currentNode.id;
        }
        sessionStorage.setItem('customerName', JSON.stringify(param));
      }
      this.$router.push({ name: 'addCustomer', params: {'id': id} })
      if (id == "add") {
        this.$store.commit("removeContentCatch", this.$route.path);
      }
      addTabs(this.$route.path, title);
    },
    // 详情
    infoData(row) {
      var title = row.code;
      this.$router.push({ name: 'customerDetail', params: { 'id': row.id } })
      addTabs(this.$route.path, title);
    },
    delData(row) {
      this.$confirm('确定删除【' + row.name + '】客户信息吗?', '删除客户信息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteCustomer(row.id)
        if (res.data.code == '100') {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          if (this.tableData != null && this.tableData.length == 1) {
            this.pagination.page = this.pagination.page - 1
          }
          this.fetchTableData()
        } else {
          this.$DonMessage.success(res.data.msg)
        }
      })
    },

    formClear() {
      this.form = {
        id: 0,
        pid: 0,
        categoryName: "",
        name: "",
        sort: 1,
        bizType: 'customer'
      };
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      });
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    // 树结构选中数据
    handleCategorySelect(node) {
      // node.id 就是当前选中的类别ID
      this.selectedCategoryId = node.id
      this.currentNode = node
      this.initializedData()
      window.closeRightMenu()
    },
    // 右键菜单相关
    handleNodeContextmenu(event, data) {
      event.preventDefault()
      this.currentNode = data
      contextmenuSeat(event, ".customerManage .contextmenuArea");
    },
    // 新增
    onAddCategory() {
      this.dialogStatus = "addCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      this.form.categoryName = this.currentNode.name;
      this.form.pid = this.currentNode.id
    },
    // 编辑
    onEditCategory() {
      this.dialogStatus = "editCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      this.form = Object.assign({}, this.currentNode);
      this.form.categoryName = categoryTreeInfo(this.categoryTree, this.form.pid);
    },
    // 删除
    onDeleteCategory() {
      this.handleCategoryDelete(this.currentNode)
    },
    async handleCategoryDelete(data) {
      this.$confirm('确定删除【' + data.name + '】类别吗?', '删除客户类别', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          id: data.id,
          bizType: 'customer'
        }
        const res = await deleteCategory(new URLSearchParams(params))
        if (res.data.code == '100') {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          this.fetchCategoryTree()
        } else {
          this.$DonMessage.error(res.data.msg);
        }

      })
    },
    // 新增取消
    async handleCategoryUpdate() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (this.dialogStatus === 'addCategory') {
            const res = await addCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dialogRightFormVisible = false
              this.fetchCategoryTree()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          } else {
            const res = await updateCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dialogRightFormVisible = false
              this.fetchCategoryTree()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          }
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      });

    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.fetchCategoryTree()
    this.fetchTableData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
