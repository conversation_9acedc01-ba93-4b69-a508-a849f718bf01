<template>
  <div class="layoutContainer actionFlowDetail addCustomer">
    <div class="elTabtitle">
      <div v-if="statePage == 'add'">新增客户信息</div>
      <div v-if="statePage == 'edit'">编辑客户信息</div>
      <div>
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button plain @click="cancel">取消</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <el-tabs v-model="activeTab" @tab-click="handleClick">
        <el-tab-pane label="基础信息" name="base">
          <el-collapse v-show="activeTab === 'base'" v-model="activePanels">
            <!-- 基本信息 -->
            <el-collapse-item name="base">
              <template #title>
                <span>
                  <i class="el-icon-arrow-right"></i>
                  基本信息
                </span>
              </template>
              <div class="secondFloat">
                <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFour">
                  <el-form-item label="客户编码" prop="code">
                    <el-input v-model="form.code" disabled placeholder="客户编码由系统生成" />
                  </el-form-item>
                  <el-form-item label="客户名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入客户名称"/>
                  </el-form-item>
                  <el-form-item label="客户类别" prop="categoryName">
                    <selectInput
                      ref="selectInput"
                      v-model="form.categoryName"
                      :inputParam="form.categoryName"
                      inputType="customer"
                      @select="onInputSearch"
                      placeholder="请选择客户类别"
                    >
                    </selectInput>
                  </el-form-item>

                  <el-form-item label="启用状态" prop="status">
                    <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-row>
                    <el-col :span="14">
                      <el-form-item label="备注" prop="remark" class="inlineTextArea">
                        <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" :rows="2" maxlength="500" show-word-limit />
                      </el-form-item>
                    </el-col>
                  </el-row>

                </el-form>
              </div>
            </el-collapse-item>
            <!-- 联系人信息 -->
            <el-collapse-item name="contact">
              <template #title>
                <span>
                  <i class="el-icon-arrow-right"></i>
                  联系人信息
                </span>
              </template>
              <div class="tableHandle spaceBbetwee">
                <el-button type="primary" icon="addProducts-icon"  @click="addContact" >添加联系人</el-button>
                <div>
                  <el-button type="text" icon="import-icon" v-if="false">批量导入</el-button>
                  <el-button type="text" icon="deleteRed-icon" @click="batchDelete">批量删除</el-button>
                </div>
              </div>
              <el-table
                ref="table"
                :data="form.contactsList"
                border
                stripe
                highlight-current-row
                style="width: 100%;"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectChange"
              >
                <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="name" label="联系人" min-width="120">
                  <template #header>
                    <span class="required-field">联系人</span>
                  </template>
                  <template #default="{ row }">
                    <el-form :model="row" :rules="contactsRules" ref="formContacts">
                      <el-form-item prop="name">
                        <el-input v-model="row.name"></el-input>
                      </el-form-item>
                    </el-form>
                  </template>
                </el-table-column>
                <el-table-column prop="mobile" label="联系电话" width="135">
                  <template #header>
                    <span class="required-field">联系电话</span>
                  </template>
                  <template #default="{ row }">
                    <el-form :model="row" :rules="contactsRules" ref="formMobile">
                      <el-form-item prop="mobile">
                        <el-input v-model="row.mobile"></el-input>
                      </el-form-item>
                    </el-form>
                  </template>
                </el-table-column>
                <el-table-column prop="telephone" label="座机" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      <el-input v-model="scope.row.telephone" />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="sex" label="性别" min-width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.sex" placeholder="" clearable>
                      <el-option label="男" value="男" />
                      <el-option label="女" value="女" />
                      <el-option label="未知" value="未知" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="wechat" label="微信" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      <el-input v-model="scope.row.wechat" />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="email" label="邮箱" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      <el-input v-model="scope.row.email" />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="发货地址" min-width="120">
                  <template #header>
                    <span class="required-field">发货地址</span>
                  </template>
                  <template #default="{ row }">
                    <el-form :model="row" :rules="contactsRules" ref="formAddress">
                      <el-form-item prop="address">
                        <el-input v-model="row.address"></el-input>
                      </el-form-item>
                    </el-form>
                  </template>
                </el-table-column>
                <el-table-column label="默认联系人" prop="defaultFlag" width="110">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.defaultFlag"
                      :active-value="1"
                      :inactive-value="0"
                      @change="handleSwitchChange(scope.$index, scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      <el-input v-model="scope.row.remark" />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="70">
                  <template slot-scope="scope">
                    <el-button type="text" class="deleteButton"
                      @click="removeContact(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <!-- 更多信息 -->
            <el-collapse-item name="more">
              <template #title>
                <span>
                  <i class="el-icon-arrow-right"></i>
                  更多信息
                </span>
              </template>
              <el-descriptions :column="dynamicColumn">
                <el-descriptions-item label="创建人">
                  <span>{{ form.createdUserName }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="创建日期">
                  <span>{{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="附件">
                  <el-upload
                    style="width: 500px;"
                    class="upload-demo"
                    :action="uploadUrl"
                    :headers="importHeader"
                    :file-list="fileList"
                    :on-remove="handleOnRemove"
                    :before-remove="beforeOnRemove"
                    :before-upload="beforeAvatarUpload"
                    :on-exceed="handleOnExceed"
                    :on-success="handleOnSuccess"
                    :limit="1"
                  >
                    <span class="linkStyle" v-if="this.count == 0">点击上传</span>
                  </el-upload>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane label="商品信息" name="product">
          <div class="tableDetail">
            <div class="tableHandle spaceBbetwee">
              <el-button type="primary" icon="addProducts-icon" @click="addProduct">添加商品</el-button>
              <div>
                <el-button type="text" icon="import-icon" v-if="false">批量导入</el-button>
                <el-button type="text" icon="deleteRed-icon" @click="productBatchDel">批量删除</el-button>
              </div>
            </div>
            <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
              :data="form.customerProductList" @header-dragend="changeColWidth" @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column type="index" label="序号" max-width="50" />
              <el-table-column label="客户货号" prop="customerProCode" min-width="120">
                <template #header>
                  <span class="required-field">客户货号</span>
                </template>
                <template #default="{ row }">
                  <el-form  :model="row" :rules="infoRules" ref="formCode">
                    <el-form-item prop="customerProCode">
                      <div class="rowEditShow">
                        <el-input v-model="row.customerProCode"></el-input>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="客户货名" prop="customerProName" min-width="120">
                <template #header>
                  <span class="required-field">客户货名</span>
                </template>
                <template #default="{ row }">
                  <el-form :model="row" :rules="infoRules" ref="formName">
                    <el-form-item prop="customerProName">
                      <div class="rowEditShow">
                        <el-input v-model="row.customerProName"></el-input>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="商品编码" prop="productCode" min-width="120"></el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="120"></el-table-column>
              <el-table-column label="规格" prop="model" min-width="80"></el-table-column>
              <el-table-column label="单位" prop="unit" width="80"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
              <div>
                <el-table-column label="操作" fixed="right"  width="80" >
                  <template slot-scope="scope" >
                    <el-button class="deleteButton" type="text" @click="productInfoDel(scope.$index)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </div>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <dialogTable
      v-if="isReload"
      :isReload.sync="isReload"
      type="product"
      :formList.sync="form.customerProductList"
      :columns.sync="productColumns"
    >
      <template #imageUrl="scope">
        <img v-if="scope.row.imageUrl != ''" class="pictureShow" :src="$filePath + scope.row.imageUrl" alt="">
      </template>
    </dialogTable>
  </div>
</template>
<script>
import selectInput from "@/components/selectInput/selectInput.vue";
import { sysServerUrl, removeTabs, collapseArea, beforeRouteInfo, getContentData } from '@/assets/js/common.js'
import { addCustomer, editCustomer, customerInfo } from '@/api/basicmgt'
import dialogTable from "@/components/dialogTable/dialogTable.vue";
import { getBatchDeleteInfo, getDeleteInfo } from '@/views/basic/basicCommon'
import { productColumns } from "@/assets/js/tableHeader";
import { tabHeightArea, getColumnNumber } from "@/assets/js/heightResize";
export default {
  name: 'addCustomer',
  components: { dialogTable, selectInput },
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      count : 0,
      productColumns: productColumns,
      fileList: [], // 文件列表
      uploadUrl: sysServerUrl + 'sys/upload/attach?flag=customer', // 文件上传地址
      rules: {
        categoryName: [{ required: true, message: "客户类别不能为空", trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '客户名称不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, validator: (rule, value, callback) => callback() }],
      },
      contactsRules: {
        name: [{ required: true, message: '联系人不能为空', trigger: ['blur', 'change'] }],
        mobile:[
          { required: true, message: '联系电话不能为空', trigger: ['blur', 'change'] },
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的联系电话格式",
          },
        ],
        address: [{ required: true, message: '发货地址不能为空', trigger: ['blur', 'change'] }]
      },
      statePage: "",
      activeTab: "base",
      form: {
        id: "",
        categoryId: "",
        categoryName: "",
        code: "",
        name: "",
        keeperUser: "",
        mobile: "",
        address: "",
        status: 1,
        remark: "",
        customerProductList: [],
        contactsList: [],
        accessoryList: [],
        createdUserName: "",
        createdTime: "",
      },
      activePanels: ['base', 'contact', 'more'], // 默认全部展开
      maximumHeight: 0,
      pagesize: 10,
      currentPage: 1,
      total: 0,
      infoRules: {
        customerProCode: [{ required: true, message: "客户货号不能为空", trigger: ['blur', 'change'] }],
        customerProName: [{ required: true, message: "客户货名不能为空", trigger: ['blur', 'change'] }],
      },
      selectList: [],
      productSelectList: [],
      // 添加商品
      isReload: false,
    }
  },
  computed: {
    // 设置请求上传的头部
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    onInputSearch(val) {
      this.form.categoryId = val.id;
      this.form.categoryName = val.name;
    },
    handleClick(tab) {
      if (tab.name == 'product') {
        tabHeightArea(this);
      }
    },
    formClear() {
      this.form = {
        id: "",
        categoryId: "",
        categoryName: "",
        code: "",
        name: "",
        keeperUser: "",
        mobile: "",
        address: "",
        status: 1,
        remark: "",
        customerProductList: [],
        contactsList: [],
        accessoryList: [],
        createdUserName: "",
        createdTime: "",
      }
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      })
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    async getInfo() {
      try {
        const res = await customerInfo(this.form.id);
        if (res.data.code === 100) {
          if (res.data.data !== null) {
            this.form = res.data.data;
            this.fileList = this.form.accessoryList;
            this.form.accessoryList.forEach((item) => {
              item.name = item.fileName
            })
            this.fileList = this.form.accessoryList;
            this.form.categoryName = this.form.categoryName ? this.form.categoryName : '全部';
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.cancel()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.cancel()
      }
    },
    // 添加联系人信息
    addContact() {
      var list = {
        name: '',
        mobile: '',
        telephone: '',
        sex: '',
        wechat: '',
        email: '',
        address: '',
        defaultFlag: 1,
        remark: ''
      }
      if (this.form.contactsList.length == 0) {
        this.form.contactsList.push(list);
      } else {
        var defaultStatus = this.form.contactsList.find(item => item.defaultFlag == 1);
        if (defaultStatus == undefined) {
          list.defaultFlag = 1
        } else {
          list.defaultFlag = 0
        }
        this.form.contactsList.push(list);
      }
    },
    // 默认联系人
    handleSwitchChange(index, row) {
      var flagVal = row.defaultFlag
      this.form.contactsList.forEach((item) => {
        item.defaultFlag = 0
      })
      this.form.contactsList[index].defaultFlag = flagVal
    },
    // 批量删除
    handleSelectChange(val) {
      this.selectList = val;
    },
    batchDelete() {
      getBatchDeleteInfo(this.form.contactsList, this.selectList, "删除联系人信息")
    },
    // 删除
    removeContact(index) {
      getDeleteInfo(this.form.contactsList, index , "删除联系人信息");
    },
    // 附件上传
    handleOnSuccess(res, obj) {
      this.fileList = []
      var file = {
        fileName: res.data.fileName,
        name: res.data.fileName,
        path: res.data.fileUrl,
      }
      this.fileList.push(file)
      this.count = 1
    },
    // 文件移除前的钩子
    beforeOnRemove() {
      if (this.fileList.length >　0) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    // 文件移除时的钩子
    handleOnRemove() {
      this.fileList = []
      this.count = 0
    },
    beforeAvatarUpload(file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const isLt2M = file.size / 1024 / 1024 < 100
      var suffix = [
        'jpg',
        'png',
        'mp4',
        'mp3',
        'xls',
        'xlsx',
        'doc',
        'docx',
        'zip',
      ];
      if (!suffix.includes(fileName)) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip' }));
        return false;
      }
      if (!isLt2M) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }))
        return false;
      }
    },
    // 超过文件数量限制时的钩子
    handleOnExceed() {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 1}))
      return
    },
    // 提交
    submit() {
      var isStatus = true;
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
          isStatus = false
        }
      });
      if (this.form.contactsList.length > 0) {
        if (this.$refs["formContacts"] != undefined) {
          this.$refs["formContacts"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("联系人不能为空");
              isStatus = false
            }
          });
        }
        if (this.$refs["formMobile"] != undefined) {
          this.$refs["formMobile"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("联系电话不能为空");
              isStatus = false
            }
          });
        }
        if (this.$refs["formAddress"] != undefined) {
          this.$refs["formAddress"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("发货地址不能为空");
              isStatus = false
            }
          });
        }
      } else {
        this.$DonMessage.warning('联系人信息不能为空');
        isStatus = false
      }
      if (this.form.contactsList.length > 0) {
        var defaultNum = 0;
        this.form.contactsList.forEach(row => {
          if (row.defaultFlag) {
            defaultNum++;
          }
        });
        // 只能有一个默认联系人
        if (defaultNum > 1 || defaultNum == 0) {
          this.$DonMessage.warning('只能有一个默认联系人')
          isStatus = false
        }
      }
      if (this.form.customerProductList != undefined) {
        if (this.form.customerProductList.length > 0) {
          if (this.$refs["formCode"] != undefined) {
            this.$refs["formCode"].validate(valid => {
              if (!valid) {
                this.$DonMessage.warning('客户货号不能为空')
                isStatus = false
              }
            });
          }
          if (this.$refs["formName"] != undefined) {
            this.$refs["formName"].validate(valid => {
              if (!valid) {
                isStatus = false
                this.$DonMessage.warning('客户货名不能为空')
              }
            });
          }
        }
      }
      if (isStatus == false) {
        return
      }
      this.form.accessoryList = this.fileList;
      this.form.createdTime = ""
      this.form.createdUserName = "";
      if (this.statePage == 'add') {
        this.form.id = '';
        addCustomer(this.form).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.submitTip'))
            removeTabs(this.$route);
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      } else {
        // 修改
        editCustomer(this.form).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.submitTip'))
            removeTabs(this.$route);
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      }
    },
    // 取消
    cancel() {
      removeTabs(this.$route);
    },
    // 添加商品信息
    addProduct() {
      this.isReload = true;
    },
    // 表格选择数据
    handleSelectionChange(val) {
      this.productSelectList = val;
      // this.deleteList = val
    },
    // 批量删除
    productBatchDel() {
      getBatchDeleteInfo(this.form.customerProductList, this.productSelectList, "删除商品信息")
    },
    // 删除
    productInfoDel(index){
      getDeleteInfo(this.form.customerProductList, index, "删除商品信息");
    },

    // productHeight() {
      // setTimeout(() => {
      //   var allHeight = $(".layoutContainer").height();
      //   var titleHeight = $(".elTabtitle").outerHeight(true);
      //   var tabHeight = $(".elTabsContent .el-tabs__header").outerHeight(true);
      //   var paddingVal = 2 * Number($(".elTabsContent .el-tabs__content").css("marginTop").split("px")[0]);
      //   var marginVal = 2 * Number($(".elTabsContent .tableDetail").css("marginTop").split("px")[0]);
      //   var searchHeight = 0;
      //   if ($(".secondFloat.productInfo").length != 0) {
      //     searchHeight = $(".secondFloat.productInfo").outerHeight(true);
      //     marginVal = marginVal / 2;
      //   }
      //   var val = allHeight - titleHeight - tabHeight - paddingVal - marginVal - searchHeight;
      //   $(".tableDetail").css("height", val);
      //   var handleHeight = $(".elTabsContent .tableHandle").outerHeight(true);
      //   var pageHeight = 0;
      //   if ($(".pagination-container").length > 0) {
      //     pageHeight = $(".pagination-container").outerHeight(true);
      //   }
      //   this.maximumHeight = val - handleHeight - paddingVal - marginVal - pageHeight;
      // });
    // },
    // async productArea() {
    //   const loadHeight = async () => {
    //     var result = await productHeight();
    //     this.maximumHeight = result;
    //   }
    //   loadHeight()
    //   // var _this = this;
    //   // await _this.productHeight();
    //   // window.addEventListener("resize", async() => {
    //   //   await _this.productHeight();
    //   // });
    // },
    async initialState() {
      var _this = this;
      _this.form.id = _this.$route.params.id;
      if (_this.form.id != 'add') {
        _this.statePage = 'edit';
        await _this.getInfo()
      } else {
        _this.statePage = 'add';
        await _this.formClear()
        _this.form.createdTime = new Date().getTime();
        _this.form.createdUserName = _this.$store.state.realName;
        var list = JSON.parse(sessionStorage.getItem('customerName'));
        _this.form.categoryName = list.name;
        _this.form.categoryId = list.id;
      }
      getContentData(_this)
      collapseArea()
    },
  },
  mounted() {
    this.initialState();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name == 'addCustomer') {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  },
}
</script>
