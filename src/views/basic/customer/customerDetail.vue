<template>
  <div class="layoutContainer actionFlowDetail addCustomer">
    <div class="elTabtitle">
      <div>客户信息</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <el-tabs v-model="activeTab" @tab-click="handleClick">
        <el-tab-pane label="基础信息" name="base">
          <el-collapse v-show="activeTab === 'base'" v-model="activePanels">
            <!-- 基本信息 -->
            <el-collapse-item name="base">
              <template #title>
                <span>
                  <i class="el-icon-arrow-right"></i>
                  基本信息
                </span>
              </template>
              <el-descriptions :column="dynamicColumn">
                <el-descriptions-item label="客户编码">
                  <span>{{ detailInfo.code }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="客户名称">
                  <span>{{ detailInfo.name }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="客户类别">
                  <span>{{ detailInfo.categoryName }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="启用状态">
                  <el-switch v-model="detailInfo.status" :active-value="1" :inactive-value="0" disabled />
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="备注">
                  <span>{{ detailInfo.remark }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
            <!-- 联系人信息 -->
            <el-collapse-item name="contact">
              <template #title>
                <span>
                  <i class="el-icon-arrow-right"></i>
                  联系人信息
                </span>
              </template>
              <el-table
                ref="table"
                :data="detailInfo.contactsList"
                border
                stripe
                highlight-current-row
                style="width: 100%;"
                @header-dragend="changeColWidth"
              >

                <el-table-column type="index" label="序号" width="50"></el-table-column>
                <el-table-column prop="name" label="联系人" min-width="120">
                </el-table-column>
                <el-table-column prop="mobile" label="联系电话" width="135"></el-table-column>
                <el-table-column prop="telephone" label="座机" min-width="120">
                  <template slot-scope="scope">
                    {{ scope.row.telephone }}
                  </template>
                </el-table-column>
                <el-table-column prop="sex" label="性别" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.sex }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="wechat" label="微信" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.wechat }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="email" label="邮箱" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.email }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="发货地址" min-width="120">
                  <template slot-scope="{ row }">
                    <div>
                      {{ row.address }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="默认联系人" prop="defaultFlag" width="110">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.defaultFlag"
                      :active-value="1"
                      :inactive-value="0"
                      :disabled="true"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="120">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.remark }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <!-- 更多信息 -->
            <el-collapse-item name="more">
              <template #title>
                <span>
                  <i class="el-icon-arrow-right"></i>
                  更多信息
                </span>
              </template>
              <el-descriptions :column="dynamicColumn">
                <el-descriptions-item label="创建人">
                  <span>{{ detailInfo.createdUserName }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="创建日期">
                  <span>{{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="附件">
                  <div v-for="(item, index) of detailInfo.accessoryList" :key="index">
                    <img v-if="item.format == 'jpg' || item.format == 'png'" :src="$filePath + item.path" alt="" style="width: 100px; max-height: 100px;" />
                    <span v-else>{{item.fileName}}</span>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
        <el-tab-pane label="商品信息" name="product">
          <div class="secondFloat productInfo">
            <el-form :inline="true" :model="form" ref="productForm" :label-width="$labelFour">
              <el-form-item label="客户货号" prop="customerProCode">
                <el-input v-model="form.customerProCode" placeholder="请输入客户货号"></el-input>
              </el-form-item>
              <el-form-item label="客户货名" prop="customerProName">
                <el-input v-model="form.customerProName" placeholder="请输入客户货名"></el-input>
              </el-form-item>
              <el-form-item label="商品编码" prop="productCode">
                <el-input v-model="form.productCode" placeholder="请输入商品编码"></el-input>
              </el-form-item>
              <el-form-item label="商品名称" prop="productName">
                <el-input v-model="form.productName" placeholder="请输入商品名称"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="productClick">{{ $t('button.search') }}</el-button>
                <el-button plain @click="productReset">{{ $t('button.reset') }}</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="tableDetail">
            <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
              :data="detailInfo.customerProductList" @header-dragend="changeColWidth"
            >
              <el-table-column type="index" label="序号" max-width="50" />
              <el-table-column label="客户货号" prop="customerProCode" min-width="120">
                <template slot-scope="{ row }">
                  <div>{{ row.customerProCode }}</div>
                </template>
              </el-table-column>
              <el-table-column label="客户货名" prop="customerProName" min-width="120">
                <template slot-scope="{ row }">
                  <div>{{ row.customerProName }}</div>
                </template>
              </el-table-column>
              <el-table-column label="商品编码" prop="productCode" min-width="120"></el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="120"></el-table-column>
              <el-table-column label="规格" prop="model" min-width="80"></el-table-column>
              <el-table-column label="单位" prop="unit" width="80"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import { collapseArea, removeTabs } from '@/assets/js/common.js'
import { customerInfo, cusProductInfo } from '@/api/basicmgt'
import { tabHeightArea, getColumnNumber } from "@/assets/js/heightResize";
export default {
  name: 'customerDetail',
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      id: "",
      detailInfo: {}, // 基本信息
      fileList: [], // 文件列表
      activeTab: "base",
      form: {
        customerProCode: "",
        customerProName: "",
        productCode: "",
        productName: "",
      },
      activePanels: ['base', 'contact', 'more'], // 默认全部展开
      maximumHeight: 0,
      pagesize: 10,
      currentPage: 1,
      total: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    handleClick(tab) {
      if (tab.name == 'product') {
        tabHeightArea(this);
      }
    },
    async getInfo() {
      try {
        const res = await customerInfo(this.id);
        if (res.data.code === 100) {
          if (res.data.data !== null) {
            this.detailInfo = res.data.data;
            this.fileList = this.detailInfo.accessoryList;
            this.detailInfo.accessoryList.forEach((item) => {
              item.name = item.fileName
            })
            this.fileList = this.detailInfo.accessoryList;
            this.detailInfo.categoryName = this.detailInfo.categoryName ? this.detailInfo.categoryName : '全部';
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          removeTabs(this.$route);
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        removeTabs(this.$route);
      }
    },
    // 商品信息
    // 搜索
    productClick() {
      let params = {
        id: this.id,
        customerProCode: this.form.customerProCode,
        customerProName: this.form.customerProName,
        productCode: this.form.productCode,
        productName: this.form.productName,
      }
      cusProductInfo(params).then(res => {
        if (res.data.code === 100) {
          if (res.data.data !== null) {
            this.detailInfo.customerProductList = res.data.data;
          }
        }
      })
    },
    // 重置
    productReset() {
      if (this.$refs["productForm"].resetFields() !== undefined) {
        this.$refs["productForm"].resetFields()
      }
      this.productClick()
    },
    async initialState() {
      var _this = this;
      _this.id = _this.$route.params.id;
      await _this.getInfo()
      collapseArea()
    },
  },
  mounted() {
    this.initialState();
  },
}
</script>
