<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFour" :model="formInline" class="demo-form-inline">
        <el-form-item label="单位编码" prop="code">
          <el-input v-model.trim="formInline.code" placeholder="请输入单位编码"></el-input>
        </el-form-item>
        <el-form-item label="单位名称" prop="name">
          <el-input v-model.trim="formInline.name" placeholder="请输入单位名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset()">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" icon="el-icon-plus" @click="addHandle()">新增</el-button>
        <el-button type="text" icon="import-icon" @click="importHandle()">导入</el-button>
        <el-button type="text" icon="bulkDown-icon" @click="uploadHandle()">导出</el-button>
      </div>
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="单位编码" prop="code" min-width="100"></el-table-column>
        <el-table-column label="单位名称" prop="name" min-width="100"></el-table-column>
        <el-table-column label="小数位数" prop="point" min-width="100"></el-table-column>
        <el-table-column label="状态" prop="status" min-width="100">
          <template slot-scope="{row}">
            <span class="successColor" v-if="row.status === 1">启用</span>
            <span class="errorColor" v-if="row.status === 0">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_103')" size="small"
              @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_102')" class="deleteButton" size="small"
              @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false">
        <el-form ref='dataForm' :label-width="formLabelWidth" :model="dataForm" :rules="fromrules"
          label-position="center">
          <el-form-item label="单位编码" prop="code">
            <el-input v-model.trim="dataForm.code" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入单位编码"></el-input>
          </el-form-item>
          <el-form-item label="单位名称" prop="name">
            <el-input v-model.trim="dataForm.name" limit="limit" show-word-limit maxlength="30"
              placeholder="请输入单位名称"></el-input>
          </el-form-item>
          <el-form-item label="小数位数" prop="contacts">
            <el-input-number v-model.trim="dataForm.point" placeholder="请输入小数位数" controls-position="right" :min="1" :max="9999" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="dataForm.remark" limit="limit" show-word-limit maxlength="100"
              placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="dataForm.status"
              :active-value="1"
              :inactive-value="0"
            ></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick() : editClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { deleteUnit, editUnit, addUnit, getUnitList } from '@/api/basicmgt';
export default {
  name: 'basic_unit_list',
  components: { Pagination },
  data() {
    return {
      formInline: {
        name: '',
        code: ''
      },
      dataForm: {
        id: '',
        name: '',
        code: '',
        point: 2,
        remark: '',
        status: 1,
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      dialogStatus: '',
      textMap: {
        edit: '编辑单位信息',
        add: '新增单位信息'
      },
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      fromrules: {
        name: [{ required: true, message: '单位名称不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '单位编码不能为空', trigger: ['blur', 'change'] }]
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      let obj = {
        page: this.currentPage,
        limit: this.pagesize,
        name: this.formInline.name,
        code: this.formInline.code,
      }
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(obj)) {
        params.append(key, value);
      }
      getUnitList(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea()
      })
    },
    initializedData() {
      this.currentPage = 1
      this.dataList()
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.initializedData();
    },
    // 重置
    reset() {
      if (this.$refs['formInline'].resetFields() !== undefined) {
        this.$refs['formInline'].resetFields()
      }
      this.initializedData();
    },
    resetTemp() {
      this.dataForm = {
        id: '',
        name: '',
        code: '',
        point: 2,
        remark: '',
        status: 1,
      }
      this.$nextTick(function () {
        this.$refs.dataForm.clearValidate();
      })
    },
    // 新增
    addHandle() {
      this.dialogStatus = 'add'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    addClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let params = {
            name: this.dataForm.name,
            code: this.dataForm.code,
            point: this.dataForm.point,
            remark: this.dataForm.remark,
            status: this.dataForm.status
          }
          addUnit(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 编辑
    handleEdit(row) {
      this.dialogStatus = 'edit'
      this.dialogFormVisible = true
      this.resetTemp()
      this.dataForm = Object.assign({}, row)
    },
    editClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let params = {
            id: this.dataForm.id,
            name: this.dataForm.name,
            code: this.dataForm.code,
            point: this.dataForm.point,
            remark: this.dataForm.remark,
            status: this.dataForm.status
          }
          editUnit(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 导入
    importHandle() {},
    // 导出
    uploadHandle() {},
    // 删除
    handleDelete(row) {
      this.$confirm('确定刪除【' + row.name + '】的相关信息?', '删除销售单位', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUnit(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.dataList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
