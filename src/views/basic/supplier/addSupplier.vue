<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="statePage == 'add'">新增供应商信息</div>
      <div v-if="statePage == 'edit'">编辑供应商信息</div>
      <div>
        <el-button type="primary" @click="submit">提交</el-button>
        <el-button plain @click="cancel">取消</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activePanels">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFive">
                <el-form-item label="供应商编码" prop="code">
                  <el-input v-model="form.code" disabled placeholder="供应商编码由系统生成" />
                </el-form-item>
                <el-form-item label="供应商名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入供应商名称"/>
                </el-form-item>
                <el-form-item label="供应商类别" prop="categoryName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.categoryName"
                    :inputParam="form.categoryName"
                    inputType="supplier"
                    @select="onInputSearch"
                    placeholder="请选择供应商类别"
                  >
                  </selectInput>
                </el-form-item>
                <el-form-item label="启用状态" prop="status">
                  <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" :rows="2" maxlength="500" show-word-limit />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <!-- 联系人信息 -->
          <el-collapse-item name="contact">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                联系人信息
              </span>
            </template>
            <div class="tableHandle spaceBbetwee">
              <el-button type="primary" icon="addProducts-icon" @click="addContact" >添加联系人</el-button>
              <div>
                <el-button type="text" icon="import-icon" v-if="false">批量导入</el-button>
                <el-button type="text" icon="deleteRed-icon" @click="batchDelete">批量删除</el-button>
              </div>
            </div>
            <el-table
              ref="table"
              :data="form.contactsList"
              border
              stripe
              highlight-current-row
              style="width: 100%;"
              @header-dragend="changeColWidth"
              @selection-change="handleSelectChange"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="name" label="联系人" min-width="120">
                <template #header>
                  <span class="required-field">联系人</span>
                </template>
                <template #default="{ row }">
                  <el-form :model="row" :rules="contactsRules" ref="formContacts">
                    <el-form-item prop="name">
                      <el-input v-model="row.name"></el-input>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column prop="mobile" label="联系电话" min-width="120">
                <template #header>
                  <span class="required-field">联系电话</span>
                </template>
                <template #default="{ row }">
                  <el-form :model="row" :rules="contactsRules" ref="formMobile">
                    <el-form-item prop="mobile">
                      <el-input v-model="row.mobile"></el-input>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column prop="telephone" label="座机" min-width="120">
                <template slot-scope="scope">
                  <div>
                    <el-input v-model="scope.row.telephone" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="sex" label="性别" min-width="120">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.sex" placeholder="" clearable>
                    <el-option label="男" value="男" />
                    <el-option label="女" value="女" />
                    <el-option label="未知" value="未知" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="wechat" label="微信" min-width="120">
                <template slot-scope="scope">
                  <div>
                    <el-input v-model="scope.row.wechat" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="email" label="邮箱" min-width="120">
                <template slot-scope="scope">
                  <div>
                    <el-input v-model="scope.row.email" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="address" label="地址" min-width="120">
                <template slot-scope="scope">
                  <div>
                    <el-input v-model="scope.row.address" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="默认联系人" prop="defaultFlag" width="110">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.defaultFlag"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleSwitchChange(scope.$index, scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="120">
                <template slot-scope="scope">
                  <div>
                    <el-input v-model="scope.row.remark" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="70">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton" @click="removeContact(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <!-- 更多信息 -->
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="创建人">
                <span>{{ form.createdName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                <span>{{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>
<script>
import selectInput from "@/components/selectInput/selectInput.vue";
import { sysServerUrl, removeTabs, addTabs, collapseArea, beforeRouteInfo, getContentData } from '@/assets/js/common.js'
import { addSupplier, editSupplier, getSupplierInfo } from '@/api/basicmgt'
import { getBatchDeleteInfo, getDeleteInfo } from '@/views/basic/basicCommon'
import { getColumnNumber } from "@/assets/js/heightResize";
export default {
  name: 'addSupplier',
  components: { selectInput },
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      rules: {
        categoryName: [{ required: true, message: "供应商类别不能为空", trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '供应商名称不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, validator: (rule, value, callback) => callback() }],
      },
      contactsRules: {
        name: [{ required: true, message: '联系人不能为空', trigger: ['blur', 'change'] }],
        mobile:[
          { required: true, message: '联系电话不能为空', trigger: ['blur', 'change'] },
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的联系电话格式",
          },
        ]
      },
      statePage: "",
      form: {
        id: "",
        categoryId: "",
        categoryName: "",
        code: "",
        name: "",
        keeperUser: "",
        mobile: "",
        address: "",
        status: 1,
        remark: "",
        contactsList: [],
        createdName: "",
        createdTime: "",
      },
      activePanels: ['base', 'contact', 'more'], // 默认全部展开
      selectList: [],
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    onInputSearch(val) {
      this.form.categoryId = val.id;
      this.form.categoryName = val.name;
    },
    formClear() {
      this.form = {
        id: "",
        categoryId: "",
        categoryName: "",
        code: "",
        name: "",
        keeperUser: "",
        mobile: "",
        address: "",
        status: 1,
        remark: "",
        contactsList: [],
        createdName: "",
        createdTime: "",
      }
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      })
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    async getInfo() {
      try {
        const res = await getSupplierInfo(this.form.id)
        if (res.data.code === 100) {
          if (res.data.data !== null) {
            this.form = res.data.data;
            this.form.categoryName = this.form.categoryName ? this.form.categoryName : '全部';
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.cancel()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.cancel()
      }
    },
    // 添加联系人信息
    addContact() {
      var list = {
        name: '',
        mobile: '',
        telephone: '',
        sex: '',
        wechat: '',
        email: '',
        address: '',
        defaultFlag: 1,
        remark: ''
      }
      if (this.form.contactsList.length == 0) {
        this.form.contactsList.push(list);
      } else {
        var defaultStatus = this.form.contactsList.find(item => item.defaultFlag == 1);
        if (defaultStatus == undefined) {
          list.defaultFlag = 1;
        } else {
          list.defaultFlag = 0;
        }
        this.form.contactsList.push(list);
      }
    },
    // 默认联系人
    handleSwitchChange(index, row) {
      var flagVal = row.defaultFlag
      this.form.contactsList.forEach((item) => {
        item.defaultFlag = 0
      })
      this.form.contactsList[index].defaultFlag = flagVal
    },
    // 批量删除
    handleSelectChange(val) {
      this.selectList = val;
    },
    batchDelete() {
      getBatchDeleteInfo(this.form.contactsList, this.selectList, "删除供应商信息")
    },
    // 删除
    removeContact(index) {
      getDeleteInfo(this.form.contactsList, index, "删除供应商信息");
    },
    // 提交
    submit() {
      var isStatus = true;
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
          isStatus = false
          return
        }
      });
      if (this.form.contactsList.length > 0) {
        if (this.$refs["formContacts"] != undefined) {
          this.$refs["formContacts"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("联系人不能为空");
              isStatus = false
              return
            }
          });
        }
        if (this.$refs["formMobile"] != undefined) {
          this.$refs["formMobile"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("联系电话不能为空");
              isStatus = false
              return
            }
          });
        }
      } else {
        this.$DonMessage.warning('联系人信息不能为空');
        isStatus = false
        return
      }

      var defaultNum = 0;
      this.form.contactsList.forEach(row => {
        if (row.defaultFlag) {
          defaultNum++;
        }
      });
      // 只能有一个默认联系人
      if (defaultNum > 1 || defaultNum == 0) {
        this.$DonMessage.warning('只能有一个默认联系人')
        isStatus = false
        return
      }
      if (isStatus == false) {
        return
      }
      if (this.statePage == 'add') {
        this.form.id = '';
        addSupplier(this.form).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.submitTip'))
            removeTabs(this.$route);
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      } else {
        // 修改
        editSupplier(this.form).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.submitTip'))
            removeTabs(this.$route);
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      }
    },
    // 取消
    cancel() {
      removeTabs(this.$route);
    },
    async initialState() {
      var _this = this;
      _this.form.id = _this.$route.params.id;
      if (_this.form.id != 'add') {
        _this.statePage = 'edit';
        await _this.getInfo()
      } else {
        _this.statePage = 'add';
        await _this.formClear()
        var list = JSON.parse(sessionStorage.getItem('supplierName'))
        _this.form.categoryName = list.name;
        _this.form.categoryId = list.id;
        _this.form.createdTime = new Date().getTime();
        _this.form.createdName = _this.$store.state.realName;
      }
      getContentData(_this)
      collapseArea()
    },
  },
  mounted() {
    this.initialState();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name == 'addSupplier') {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  },
}
</script>
