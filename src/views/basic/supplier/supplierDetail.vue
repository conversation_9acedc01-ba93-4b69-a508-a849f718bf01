<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>供应商信息</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activePanels">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="供应商编码">
                <span>{{ detailInfo.code }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="供应商名称">
                <span>{{ detailInfo.name }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="供应商类别">
                <span>{{ detailInfo.categoryName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="启用状态">
                <el-switch v-model="detailInfo.status" :active-value="1" :inactive-value="0" disabled />
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ detailInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <!-- 联系人信息 -->
          <el-collapse-item name="contact">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                联系人信息
              </span>
            </template>
            <el-table
              ref="table"
              :data="detailInfo.contactsList"
              border
              stripe
              highlight-current-row
              style="width: 100%;"
              @header-dragend="changeColWidth"
            >
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="name" label="联系人" min-width="120"></el-table-column>
              <el-table-column prop="mobile" label="联系电话" min-width="120"></el-table-column>
              <el-table-column prop="telephone" label="座机" min-width="120"></el-table-column>
              <el-table-column prop="sex" label="性别" min-width="120"></el-table-column>
              <el-table-column prop="wechat" label="微信" min-width="120"></el-table-column>
              <el-table-column prop="email" label="邮箱" min-width="120"></el-table-column>
              <el-table-column prop="address" label="地址" min-width="120"></el-table-column>
              <el-table-column label="默认联系人" prop="defaultFlag" width="110">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.defaultFlag"
                    :active-value="1"
                    :inactive-value="0"
                    :disabled="true"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
            </el-table>
          </el-collapse-item>
          <!-- 更多信息 -->
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="创建人">
                <span>{{ detailInfo.createdName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                <span>{{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>
<script>
import { collapseArea, removeTabs } from '@/assets/js/common.js'
import { getSupplierInfo } from '@/api/basicmgt'
import { getColumnNumber } from "@/assets/js/heightResize";
export default {
  name: 'supplierDetail',
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      id: "", // 供应商ID
      detailInfo: {}, // 基本信息
      activePanels: ['base', 'contact', 'more'], // 默认全部展开
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    async getInfo(){
      try {
        const res = await getSupplierInfo(this.id)
        if (res.data.code === 100) {
          if (res.data.data !== null) {
            this.detailInfo = res.data.data;
            this.detailInfo.categoryName = this.detailInfo.categoryName ? this.detailInfo.categoryName : '全部';
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          removeTabs(this.$route);
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        removeTabs(this.$route);
      }
    },
    async initialState() {
      var _this = this;
      _this.id = _this.$route.params.id;
      await _this.getInfo()
      collapseArea()
    },
  },
  mounted() {
    this.initialState();
  },
}
</script>