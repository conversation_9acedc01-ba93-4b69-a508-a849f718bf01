<template>
  <div class="layoutContainer supplierManage">
    <!-- 顶部搜索框 -->
    <div class="secondFloat">
      <el-form :inline="true" :label-width="$labelFive" ref="search" :model="search" class="demo-form-inline">
        <el-form-item label="供应商类别" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="search.categoryName"
            :inputParam="search.categoryName"
            inputType="supplier"
            placeholder="请选择供应商类别"
            @select="onInputSearch('search', $event)"
          ></selectInput>
        </el-form-item>
        <el-form-item label="供应商编码" prop="code">
          <el-input v-model="search.code" placeholder="请输入供应商编码" />
        </el-form-item>
        <el-form-item label="供应商名称" prop="name">
          <el-input v-model="search.name" placeholder="请输入供应商名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="onReset">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton none">
              <span>供应商类别</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <el-scrollbar>
                <el-tree
                  :data="categoryTree"
                  node-key="id"
                  :render-content="renderContent"
                  :default-expand-all="true"
                  @node-click="handleCategorySelect"
                  @node-contextmenu="handleNodeContextmenu"
                  :props="{
                    label: 'name',
                    children: 'children'
                  }"
                  ref="categoryTree"
                ></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight">
          <div class="rightTitle none">
            <el-button type="text" icon="el-icon-plus" @click="addData()">新增</el-button>
          </div>
          <div class="detailInfo rightTableArea">
            <el-table style="width:100%" ref="table" highlight-current-row :data="tableData" border stripe
              :max-height="maximumHeight" @header-dragend="changeColWidth">
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="code" label="供应商编码" min-width="180">
                <template slot-scope="{row}">
                  <span class="linkStyle" @click="infoData(row)">{{ row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="供应商名称" min-width="180"></el-table-column>
              <el-table-column prop="categoryName" label="类别" width="85"></el-table-column>
              <el-table-column prop="contacts" label="联系人" width="100"></el-table-column>
              <el-table-column prop="mobile" label="联系电话" width="100"></el-table-column>
              <el-table-column prop="address" label="发货地址" min-width="110"></el-table-column>
              <el-table-column label="状态" prop="status" width="80">
                <template slot-scope="{row}">
                  <span class="successColor" v-if="row.status === 1">启用</span>
                  <span class="errorColor" v-if="row.status === 0">禁用</span>
                </template>
              </el-table-column>
              <el-table-column prop="createdTime" label="创建时间" width="160">
                <template slot-scope="{row}">
                  <div>
                    {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="updatedTime" label="更新日期" width="160">
                <template slot-scope="{row}">
                  <div>
                    {{ row.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="200" />
              <el-table-column label="操作" fixed="right" width="150">
                <template slot-scope="{row}">
                  <el-button type="text" @click="infoData(row)">
                    详情
                  </el-button>
                  <el-button type="text" @click="editData(row)">
                    编辑
                  </el-button>
                  <el-button class="deleteButton" type="text"  @click="delData(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="pagination.total > 0" :total="pagination.total" :page.sync="pagination.page"
              :limit.sync="pagination.size" @pagination="fetchTableData" />
          </div>
        </el-col>
      </el-row>
      <!-- 右键菜单 -->
      <div class="contextmenuArea">
        <div @click="onAddCategory" v-if="currentNode">
          <i class="el-icon-plus"></i>
          <span>新增</span>
        </div>
        <div @click="onEditCategory" v-if="currentNode && currentNode.id != ''">
          <i class="compile-icon"></i>
          <span>编辑</span>
        </div>
        <div class="deleteMark" @click="onDeleteCategory" v-if="currentNode && currentNode.id != ''">
          <i class="deleteRed-icon"></i>
          <span>删除</span>
        </div>
      </div>
    </div>

    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogRightFormVisible">
      <el-form :label-width="formLabelWidth" :model="form" :rules="rules" ref="form">
        <el-form-item label="上级类别" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="form.categoryName"
            :inputParam="form.categoryName"
            inputType="supplier"
            @select="onInputSearch('form', $event)"
            placeholder="请选择上级类别"
            :disabled="dialogStatus == 'addCategory' ? true : false"
          ></selectInput>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" limit="limit" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model.trim="form.sort" placeholder="请输入排序" controls-position="right" :min="1" :max="99"
          :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleCategoryUpdate()">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogRightFormVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import selectInput from "@/components/selectInput/selectInput.vue";
import {
  getCategoryTree,
  getSupplierList,
  addCategory,
  updateCategory,
  deleteCategory,
  deleteSupplier
} from '@/api/basicmgt'

import {
  addTabs,
  categoryTreeInfo,
  renderTree,
  tableHeight,
  contextmenuSeat
} from "@/assets/js/common";
import { addSupplierInfo, getCategoryTreeData } from "@/views/basic/basicCommon";
export default {
  name:"basic_supplier_list",
  components: { Pagination, selectInput },
  data() {
    return {
      search: {
        code: '',
        name: '',
        categoryName: '',
        bizType: 'supplier'
      },
      selectedCategoryId: '',
      maximumHeight: 0,
      dialogStatus: "",
      textMap: {
        editCategory: "编辑供应商类别",
        addCategory: "新增供应商类别",
      },
      formLabelWidth: "100px",
      tableData: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      // 品牌类别
      categoryTree: [{ id: '', name: '全部', children: [] }],
      dialogRightFormVisible: false,
      form: {
        id: 0,
        pid: 0,
        categoryName: '',
        sort: 1,
        name: '',
        bizType: 'supplier'
      },
      rules: {
        categoryName: [
          {required: true, message: '上级类别不能为空', trigger: ['blur', 'change'] }
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: ['blur', 'change'] }
        ],
      },
      editId: null,
      currentNode: null,
    }

  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    renderContent(h, { node, data, store }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".supplierManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    onInputSearch(type, $event) {
      if (type == "search") {
        this.search.categoryName = $event.name;
        this.selectedCategoryId = $event.id;
      } else if (type == 'form') {
        this.form.categoryName = $event.name;
        this.form.pid = $event.id;
      }
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSearch()
      }
    },
    onSearch() {
      this.search.categoryName = "全部" ? '' : this.search.categoryName;
      this.initializedData();
    },
    onReset() {
      if (this.$refs['search'].resetFields() !== undefined) {
        this.$refs['search'].resetFields()
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.selectedCategoryId = '';
      this.search.categoryName = '';
      this.initializedData();
    },
    // 类别树相关
    async fetchCategoryTree() {
      const treeData = async () => {
        var result = await getCategoryTreeData('supplier');
        this.categoryTree = result;
        this.$nextTick(() => {
          if (this.categoryTree.length > 0) {
            this.$refs.categoryTree.setCurrentKey(this.categoryTree[0]); // 选中第一个节点
          }
        });
      }
      treeData();
    },
    initializedData() {
      this.pagination.page = 1;
      this.fetchTableData()
    },
    // 列表相关
    async fetchTableData() {
      const obj = {
        ...this.search,
        categoryId: this.selectedCategoryId,
        page: this.pagination.page,
        limit: this.pagination.size
      }
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(obj)) {
        params.append(key, value);
      }
      const res = await getSupplierList(params)
      this.tableData = res.data.data
      this.pagination.total = res.data.total
      this.tableHeightArea()
    },
    addData() {
      addSupplierInfo(this.currentNode)
    },
    editData(row) {
      var title = '编辑 ' + row.code;
      var id = row.id
      this.$router.push({ name: 'addSupplier', params: {'id': id} })
      addTabs(this.$route.path, title);
    },
    // 详情
    infoData(row) {
      var title = row.code;
      this.$router.push({ name: 'supplierDetail', params:{'id': row.id} })
      addTabs(this.$route.path, title);
    },
    delData(row) {
      this.$confirm('确定删除【' + row.name + '】供应商吗?', '删除供应商', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteSupplier(row.id)
        if (res.data.code == '100') {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          if (this.tableData != null && this.tableData.length == 1) {
            this.pagination.page = this.pagination.page - 1
          }
          this.fetchTableData()
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },
    // 初始化数据
    formClear() {
      this.form = {
        id: 0,
        pid: 0,
        categoryName: "",
        name: "",
        sort: 1,
        bizType: 'supplier'
      };
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      });
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    // 树结构选中数据
    handleCategorySelect(node) {
      // node.id 就是当前选中的类别ID
      this.selectedCategoryId = node.id;
      this.currentNode = node;
      this.initializedData();
      window.closeRightMenu()
    },
    // 右键菜单相关
    handleNodeContextmenu(event, data) {
      event.preventDefault()
      this.currentNode = data
      contextmenuSeat(event, ".supplierManage .contextmenuArea");
    },
    // 新增
    onAddCategory() {
      this.dialogStatus = "addCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      this.form.categoryName = this.currentNode.name;
      this.form.pid = this.currentNode.id
    },
    // 编辑
    onEditCategory() {
      this.dialogStatus = "editCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      this.form = Object.assign({}, this.currentNode);
      this.form.categoryName = categoryTreeInfo(this.categoryTree, this.form.pid);
    },
    // 删除
    onDeleteCategory() {
      this.handleCategoryDelete(this.currentNode)
    },
    async handleCategoryDelete(data) {
      this.$confirm('确定删除【' + data.name + '】类别吗?', '删除供应商类别', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          id: data.id,
          bizType: 'supplier'
        }
        const res = await deleteCategory(new URLSearchParams(params))
        if (res.data.code == '100') {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
        } else {
          this.$DonMessage.error(res.data.msg);
        }
        this.fetchCategoryTree()
      })
    },
    // 提交
    async handleCategoryUpdate() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (this.dialogStatus === 'addCategory') {
            const res = await addCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dialogRightFormVisible = false
              this.fetchCategoryTree()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          } else {
            const res = await updateCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dialogRightFormVisible = false
              this.fetchCategoryTree()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          }
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      });
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.fetchCategoryTree()
    this.fetchTableData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
