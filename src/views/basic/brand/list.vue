<template>
  <div class="layoutContainer productBrandManage">
    <!-- 顶部搜索框 -->
    <div class="secondFloat">
      <el-form :inline="true" ref="search" :label-width="$labelFour" :model="search" class="demo-form-inline">
        <el-form-item label="品牌类别" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="search.categoryName"
            :inputParam="search.categoryName"
            inputType="brand"
            placeholder="请选择品牌类别"
            @select="onInputSearch('search', $event)"
          ></selectInput>
        </el-form-item>
        <el-form-item label="品牌编码" prop="code">
          <el-input v-model="search.code" placeholder="请输入品牌编码" size="small" />
        </el-form-item>
        <el-form-item label="品牌名称" prop="name">
          <el-input v-model="search.name" placeholder="请输入品牌名称" size="small" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="onReset">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton none">
              <span>品牌类别</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <el-scrollbar>
                <el-tree
                  :data="categoryTree"
                  node-key="id"
                  :render-content="renderContent"
                  :default-expand-all="true"
                  @node-click="handleCategorySelect"
                  @node-contextmenu="handleNodeContextmenu"
                  :props="{
                    label: 'name',
                    children: 'children'
                  }" ref="categoryTree">
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight">
          <div class="rightTitle none">
            <el-button type="text" icon="el-icon-plus" @click="addData()">新增</el-button>
          </div>
          <div class="detailInfo rightTableArea">
            <el-table style="width:100%" ref="table" highlight-current-row :data="tableData" border stripe
              :max-height="maximumHeight" @header-dragend="changeColWidth">
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="code" label="品牌编码" min-width="180">
                <template slot-scope="{row}">
                  <span class="linkStyle" @click="detailData(row)">{{ row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="品牌名称" min-width="180"></el-table-column>
              <el-table-column prop="categoryName" label="类别" min-width="100"></el-table-column>
              <el-table-column label="状态" prop="status" width="100">
                <template slot-scope="{row}">
                  <span class="successColor" v-if="row.status === 1">启用</span>
                  <span class="errorColor" v-if="row.status === 0">禁用</span>
                </template>
              </el-table-column>
              <el-table-column prop="createdTime" label="创建时间" width="160">
                <template slot-scope="{row}">
                  <div>
                    {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="updatedTime" label="更新日期" width="160">
                <template slot-scope="{row}">
                  <div>
                    {{ row.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="200"></el-table-column>
              <el-table-column label="操作" fixed="right" width="100">
                <template slot-scope="{row}">
                  <el-button type="text" size="small" @click="editData(row)">
                    编辑
                  </el-button>
                  <el-button class="deleteButton" type="text" size="small" @click="delData(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="pagination.total > 0" :total="pagination.total" :page.sync="pagination.page"
              :limit.sync="pagination.size" @pagination="fetchTableData" />
          </div>
        </el-col>
      </el-row>
      <!-- 右键菜单 -->
      <div class="contextmenuArea">
        <div @click="onAddCategory" v-if="currentNode">
          <i class="el-icon-plus"></i>
          <span>新增</span>
        </div>
        <div @click="onEditCategory" v-if="currentNode && currentNode.id != ''">
          <i class="compile-icon"></i>
          <span>编辑</span>
        </div>
        <div class="deleteMark" @click="onDeleteCategory" v-if="currentNode && currentNode.id != ''">
          <i class="deleteRed-icon"></i>
          <span>删除</span>
        </div>
      </div>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogRightFormVisible"
        v-if="dialogRightFormVisible">
        <el-form :label-width="formLabelWidth" :model="form" ref="form" :rules="rules">
          <el-form-item label="上级类别" prop="categoryName">
            <selectInput
              ref="selectInput"
              v-model="form.categoryName"
              :inputParam="form.categoryName"
              inputType="brand"
              @select="onInputSearch('form', $event)"
              placeholder="请选择上级类别"
              :disabled="dialogStatus == 'addCategory' ? true : false"
            ></selectInput>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="form.name" placeholder="请输入名称" maxlength="50" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model.trim="form.sort" placeholder="请输入排序" controls-position="right" :min="1" :max="99"
              :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item class="submitArea">
            <el-button type="primary" @click="handleCategoryUpdate()">{{ $t('button.submit') }}</el-button>
            <el-button plain @click="dialogRightFormVisible = false">重置</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="addDialogVisible" v-if="addDialogVisible">
        <el-form :model="addForm" ref="addForm" :rules="addRules" :label-width="formLabelWidth">
          <el-form-item label="上级类别" prop="categoryName">
            <selectInput
              ref="selectInput"
              v-model="addForm.categoryName"
              :inputParam="addForm.categoryName"
              inputType="brand"
              @select="onInputSearch('addForm', $event)"
              placeholder="请选择上级类别"
              :disabled="dialogStatus == 'editData' ? false : true"
            ></selectInput>
          </el-form-item>
          <el-form-item label="品牌编码" prop="code">
            <el-input v-model="addForm.code" disabled placeholder="品牌编码系统自动生成" />
          </el-form-item>
          <el-form-item label="品牌名称" prop="name">
            <el-input v-model="addForm.name" placeholder="请输入品牌名称" :disabled="dialogStatus == 'detail' ? true : false" />
          </el-form-item>
          <el-form-item label="创建日期" prop="createdTime" v-if="dialogStatus == 'detail'">
            <el-input v-model="addForm.createdTime" placeholder="请输入创建日期"  :disabled="dialogStatus == 'detail' ? true : false" />
          </el-form-item>
          <el-form-item label="更新日期" prop="updatedTime" v-if="dialogStatus == 'detail'">
            <el-input v-model="addForm.updatedTime" placeholder="请输入更新日期"  :disabled="dialogStatus == 'detail' ? true : false" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="addForm.remark" placeholder="请输入备注"  :disabled="dialogStatus == 'detail' ? true : false" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch v-model="addForm.status" :active-value="1" :inactive-value="0"  :disabled="dialogStatus == 'detail' ? true : false"></el-switch>
          </el-form-item>
          <el-form-item class="submitArea" v-if="dialogStatus != 'detail'">
            <el-button type="primary" @click="handleAddBrand">{{ $t('button.submit') }}</el-button>
            <el-button plain @click="addDialogVisible = false">{{ $t('button.cancel') }}</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {
  getCategoryTree,
  getBrandList,
  addCategory,
  updateCategory,
  deleteCategory,
  addBrand,
  editBrand,
  deleteBrand
} from '@/api/basicmgt'
import selectInput from "@/components/selectInput/selectInput.vue";
import { tableHeight, contextmenuSeat, categoryTreeInfo, renderTree } from "@/assets/js/common";

export default {
  name: "basic_brand_list",
  components: { Pagination, selectInput },
  data() {
    return {
      // 搜索
      search: {
        code: '',
        name: '',
        categoryName: '',
        bizType: 'brand',
      },
      maximumHeight: 0,
      addDialogVisible: false,
      textMap: {
        editCategory: "编辑品牌类别",
        addCategory: "新增品牌类别",
        addData: "新增品牌信息",
        editData: "编辑品牌信息",
        detail: "品牌信息详情",
      },
      addForm: {
        categoryId: '', // 类别id
        categoryName: '全部', // 类别名称（仅展示用）
        name: '', // 品牌名称
        code: '', // 品牌编码
        createdTime: '',
        updatedTime: '',
        remark: '',
        status: 1
      },
      addRules: {
        categoryName: [
          { required: true, message: '上级类别不能为空', trigger: ['blur', 'change'] },
        ],
        code: [
          { required: true, validator: (rule, value, callback) => callback() },
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: ['blur', 'change'] },
        ],
      },
      formLabelWidth: "100px",
      selectedCategoryId: '',
      tableData: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      // 品牌类别
      categoryTree: [{ id: '', name: '全部', children: [] }],
      dialogStatus: "",
      dialogVisible: false,
      dialogRightFormVisible: false,
      form: {
        id: 0,
        pid: 0,
        categoryName: '',
        name: '',
        sort: 1,
        bizType: "brand"
      },
      rules: {
        categoryName: [
          { required: true, message: '上级类别不能为空', trigger: ['blur', 'change'] },
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: ['blur', 'change'] },
        ],
      },
      currentNode: null,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    renderContent(h, { node, data, store }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".productBrandManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    onInputSearch(type, $event) {
      if (type == 'search') {
        this.search.categoryName = $event.name;
        this.selectedCategoryId = $event.id;
      } else if (type == 'form') {
        this.form.categoryName = $event.name;
        this.form.pid = $event.id;
      } else {
        this.addForm.categoryName = $event.name;
        this.addForm.categoryId = $event.id;
      }
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSearch()
      }
    },
    onSearch() {
      this.search.categoryName = "全部" ? '' : thissearch.categoryName;
      this.initializedData()
    },
    // 重置
    onReset() {
      if (this.$refs['search'].resetFields() !== undefined) {
        this.$refs['search'].resetFields()
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.selectedCategoryId = '';
      this.search.categoryName = '';
      this.initializedData()
    },
    // 类别树相关
    async fetchCategoryTree() {
      try {
        const res = await getCategoryTree({ bizType: 'brand' })
        this.categoryTree = [{
          id: '',
          name: '全部',
          children: Array.isArray(res.data.data) ? res.data.data : [] // 确保children是数组
        }];
        this.$nextTick(() => {
          if (this.categoryTree.length > 0) {
            this.$refs.categoryTree.setCurrentKey(this.categoryTree[0]); // 选中第一个节点
          }
        });
      } catch (e) {
        this.categoryTree = [{ id: '', name: '全部', children: [] }];
      }
    },
    initializedData() {
      this.pagination.page = 1
      this.fetchTableData()
    },
    // 列表相关
    async fetchTableData() {
      const obj  = {
        ...this.search,
        categoryId: this.selectedCategoryId,
        page: this.pagination.page,
        limit: this.pagination.size
      }
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(obj)) {
        params.append(key, value);
      }
      const res = await getBrandList(params)
      this.tableData = res.data.data
      this.pagination.total = res.data.total
      this.tableHeightArea()
    },
    addFormClear() {
      this.addForm = {
        categoryId: '',
        categoryName: '全部',
        name: '',
        code: '',
        remark: '',
        createdTime: '',
        updatedTime: '',
        status: 1
      }
      this.$nextTick(function () {
        this.$refs.addForm.clearValidate();
      });
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    async addData() {
      // 获取左侧选中类别
      this.dialogStatus = "addData"
      this.addDialogVisible = true
      await this.addFormClear();
      if (this.currentNode != null) {
        this.addForm.categoryId = this.currentNode.id;
        this.addForm.categoryName = this.currentNode.name
      }
    },
    editData(row) {
      this.dialogStatus = "editData";
      this.getInfoData(row)
    },
    getInfoData(row) {
      this.addDialogVisible = true;
      this.addFormClear();
      this.addForm = Object.assign({}, row);
      this.addForm.categoryName = row.categoryName ? row.categoryName : '全部';
      this.addForm.createdTime = this.$options.filters.conversion(this.addForm.createdTime, "yyyy-MM-dd HH:mm:ss");
      this.addForm.updatedTime = this.$options.filters.conversion(this.addForm.updatedTime, "yyyy-MM-dd HH:mm:ss");
    },
    detailData(row) {
      this.dialogStatus = "detail";
      this.getInfoData(row)
    },
    delData(row) {
      this.$confirm('确定删除【' + row.name + '】品牌吗?', '删除品牌', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteBrand(row.id)
        this.$DonMessage.success(this.$t('successTip.deleteTip'))
        if (this.tableData != null && this.tableData.length == 1) {
          this.pagination.page = this.pagination.page - 1
        }
        this.fetchTableData()
      })
    },
    // 新增品牌提交
    async handleAddBrand() {
      this.$refs['addForm'].validate(async (valid) => {
        if (valid) {
          this.addForm.createdTime = new Date(this.addForm.createdTime).getTime();
          this.addForm.updatedTime = new Date(this.addForm.updatedTime).getTime();
          const params = {
            ...this.addForm,
            categoryId: this.addForm.categoryId
          }
          if (this.dialogStatus === "addData") {
            const res = await addBrand(params)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.addDialogVisible = false
              this.fetchTableData()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          } else if (this.dialogStatus === "editData") {
            const res = await editBrand(params)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.addDialogVisible = false
              this.fetchTableData()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          }
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },

    formClear() {
      this.form = {
        id: 0,
        pid: 0,
        categoryName: "",
        name: "",
        sort: 1,
        bizType: "brand"
      };
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      });
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    // 树结构选中数据
    handleCategorySelect(node) {
      this.currentNode = node;
      // node.id 就是当前选中的类别ID
      this.selectedCategoryId = node.id
      this.initializedData()
      window.closeRightMenu()
    },
    // 右键菜单相关
    handleNodeContextmenu(event, data) {
      event.preventDefault()
      this.currentNode = data
      contextmenuSeat(event, ".productBrandManage .contextmenuArea");
    },
    // 新增
    onAddCategory() {
      this.dialogStatus = "addCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      this.form.categoryName = this.currentNode.name;
      this.form.pid = this.currentNode.id
    },
    // 编辑
    onEditCategory() {
      this.dialogStatus = "editCategory";
      this.dialogRightFormVisible = true;
      this.formClear();
      this.form = Object.assign({}, this.currentNode);
      this.form.categoryName = categoryTreeInfo(this.categoryTree, this.form.pid);
    },
    // 删除
    onDeleteCategory() {
      this.handleCategoryDelete(this.currentNode)
    },
    async handleCategoryDelete(data) {
      this.$confirm('确定删除【' + data.name + '】类别吗?', '删除品牌类别', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          id: data.id,
          bizType: 'brand'
        }
        const res = await deleteCategory(new URLSearchParams(params))
        if (res.data.code == '100') {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
        } else {
          this.$DonMessage.error(res.data.msg);
        }
        this.fetchCategoryTree()
      })
    },
    async handleCategoryUpdate() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          if (this.dialogStatus === 'addCategory') {
            const res = await addCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          } else {
            const res = await updateCategory(this.form)
            if (res.data.code == '100') {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          }
          this.fetchCategoryTree()
          this.dialogRightFormVisible = false
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      });
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.fetchCategoryTree()
    this.fetchTableData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
