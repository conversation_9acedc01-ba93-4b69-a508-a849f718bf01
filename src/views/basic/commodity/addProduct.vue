<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="stateType == 'add'">新增商品信息</div>
      <div v-if="stateType == 'edit'">编辑商品信息</div>
      <div>
        <el-button type="primary" @click="onSubmit()">{{ $t('button.submit') }}</el-button>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFour">
                <el-form-item label="商品编码" prop="code">
                  <el-input v-model="form.code" placeholder="商品编码由系统生成" disabled />
                </el-form-item>
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入商品名称" />
                </el-form-item>
                <el-form-item label="商品类别" prop="categoryName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.categoryName"
                    :inputParam="form.categoryName"
                    inputType="categoryName"
                    placeholder="请选择商品类别"
                    @select="onInputSearch('name', $event)"
                  ></selectInput>
                </el-form-item>
                <el-form-item label="条形码" prop="barCode">
                  <el-input v-model="form.barCode" placeholder="条形码由系统生成" disabled/>
                </el-form-item>
                <el-form-item label="品牌" prop="brand">
                  <selectInput
                    ref="selectInput"
                    v-model="form.brand"
                    :inputParam="form.brand"
                    inputType="brandInfo"
                    placeholder="请选择品牌"
                    @select="onInputSearch('brand', $event)"
                  ></selectInput>
                </el-form-item>
                <el-form-item label="型号" prop="model">
                  <el-input v-model="form.model" placeholder="请输入型号"></el-input>
                </el-form-item>
                <el-form-item label="计量单位" prop="unit">
                  <el-select v-model="form.unit">
                    <el-option v-for="(item, index) of unitData" :key="index" :label="item.name" :value="item.name"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="默认仓库" prop="warehouseName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.warehouseName"
                    :inputParam="form.warehouseName"
                    inputType="warehouseInfo"
                    placeholder="请选择默认仓库"
                    @select="onInputSearch('warehouse', $event)"
                  ></selectInput>
                </el-form-item>
                <el-form-item label="默认库位" prop="locationName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.locationName"
                    :inputParam="form.locationName"
                    inputType="locationInfo"
                    :warehouseId="form.warehouseId"
                    placeholder="请选择默认库位"
                    @select="onInputSearch('location', $event)"
                  ></selectInput>
                </el-form-item>

                <el-form-item label="工艺" prop="process">
                  <el-input v-model="form.process" placeholder="请输入工艺"></el-input>
                </el-form-item>
                <el-form-item label="尺寸" prop="dimensions">
                  <el-input v-model="form.dimensions" placeholder="请输入尺寸"></el-input>
                </el-form-item>
                <el-form-item label="材质" prop="materialType">
                  <el-input v-model="form.materialType" placeholder="请输入材质"></el-input>
                </el-form-item>
                <el-form-item label="重量" prop="weight">
                  <el-input v-model="form.weight" placeholder="请输入重量"></el-input>
                </el-form-item>
                <el-form-item label="规格" prop="specifications">
                  <el-input v-model="form.specifications" placeholder="请输入规格"></el-input>
                </el-form-item>


                <el-form-item label="启用状态" prop="status">
                  <el-switch
                    v-model="form.status"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500" show-word-limit/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品特性
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" ref="formSwitch" :label-width="$labelFour">
                <el-form-item label="可销售" prop="salable">
                  <el-switch
                    v-model="form.salable"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
                <el-form-item label="可采购" prop="purchasable">
                  <el-switch
                    v-model="form.purchasable"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
                <el-form-item label="可为子件" prop="canBeSubitem">
                  <el-switch
                    v-model="form.canBeSubitem"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
                <el-form-item label="可为组件" prop="canBeComponent">
                  <el-switch
                    v-model="form.canBeComponent"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
                <el-form-item label="可委外" prop="outsourceable">
                  <el-switch
                    v-model="form.outsourceable"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
                <el-form-item label="可自制" prop="selfProducible">
                  <el-switch
                    v-model="form.selfProducible"
                    :active-value="1"
                    :inactive-value="0"
                  />
                </el-form-item>
              </el-form>
            </div>
            <div class="tableHandle spaceBbetwee">
              <el-button type="primary" icon="addProducts-icon" @click="addSupplier">添加供应商</el-button>
              <div>
                <el-button type="text" icon="import-icon" v-if="false">批量导入</el-button>
                <el-button type="text" icon="setIcon-icon" v-if="false">批量设置单价</el-button>
                <el-button type="text" icon="deleteRed-icon" @click="batchDelete">批量删除</el-button>
              </div>
            </div>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.productSupplierList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              @selection-change="handleSelectChange"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="供应商编码" min-width="110" prop="supplierCode">
                <template slot-scope="{row}">
                  <span>{{row.supplierCode}}</span>
                </template>
              </el-table-column>
              <el-table-column label="供应商名称" min-width="110" prop="supplierName">
                <template slot-scope="{row}">
                  <span>{{row.supplierName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价(￥)" prop="price" width="100">
                <template #header>
                  <span class="required-field">单价(￥)</span>
                </template>
                <template #default="scope">
                  <el-form :model="scope.row" :rules="supplierRules" ref="supplierPrice">
                    <el-form-item prop="price">
                      <div class="rowEditShow">
                        <el-input-number
                          v-model.trim="scope.row.price"
                          controls-position="right"
                          :min="0.01"
                          :precision="2"
                          :step="0.1"
                        ></el-input-number>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="参考链接" prop="referLink" min-width="140">
                <template slot-scope="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.referLink"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="地址" prop="address" min-width="140"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="110">
                <template slot-scope="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.remark"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="80">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton" @click="delClick(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="image">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                图片信息 <span class="subTitle">支持.png，.jpg的图片类型，最多上传5张，单张图片不超过5MB</span>
              </span>
            </template>
            <div>
              <el-upload
                ref="upload"
                class="upload-demo"
                drag
                multiple
                action="#"
                :limit="5"
                :http-request="uploadAttach"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :before-upload="beforeAvatarUpload"
                :on-exceed="handleExceed"
                :file-list="imageFileLsit"
                accept=".jpg,.png"
                list-type="picture-card"
              >
                <div class="uploadTip">
                  <div class="el-icon-plus"></div>
                  <div>点击或拖拽上传</div>
                </div>
              </el-upload>
            </div>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="创建人">
                {{ form.createdName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="this.stateType == 'edit' && form.updatedName">
                {{ form.updatedName }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="this.stateType == 'edit' && form.updatedTime">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <dialogTable
      v-if="isReload"
      :isReload.sync="isReload"
      type="supplier"
      :formList.sync="form.productSupplierList"
      :columns.sync="supplierColumns"></dialogTable>
  </div>
</template>
<script>
import { removeTabs, sysServerUrl, collapseArea, beforeRouteInfo, getContentData } from "@/assets/js/common";
import selectInput from "@/components/selectInput/selectInput.vue";
import dialogTable from "@/components/dialogTable/dialogTable.vue";
import { getProductInfo, getProductAdd , getProductEdit, getUnitList } from "@/api/basicmgt";
import { importAttach } from '@/api/sysmgt';
import { getBatchDeleteInfo, getDeleteInfo, getUnitListData } from '@/views/basic/basicCommon'
import { supplierColumns } from "@/assets/js/tableHeader";
import { getColumnNumber } from "@/assets/js/heightResize";
export default {
  name: "addProduct",
  components: { selectInput, dialogTable },
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      supplierColumns: supplierColumns,
      filePath : sysServerUrl + "sys/upload/display?filePath=",
      productId: "", //id
      stateType: "", //类型
      // 基本信息
      form: {
        id: "",
        tenantId: "",
        code: "",
        name: "",
        brand: "",
        model: "",
        imageUrl: "",
        categoryName: "",
        categoryId: "",
        barCode: "",
        unit: "",
        warehouseName: "",
        warehouseId: "",
        locationName: "",
        materialType: "",
        weight: "",
        specifications: "",
        dimensions: "",
        process: "",
        locationId: "",
        status: 1,
        remark: "",
        salable: 1,
        purchasable: 1,
        canBeSubitem: 1,
        canBeComponent: 1,
        outsourceable: 1,
        selfProducible: 1,
        createdName: "",
        createdTime: "",
        updatedName: "",
        updatedTime: "",
        productSupplierList: [],
        productAccessoryList: [],
      },
      rules: {
        code: [{ required: true, validator: (rule, value, callback) => callback() }],
        name: [{ required: true, message: '商品名称不能为空', trigger: ['blur', 'change'] }],
        categoryName: [{ required: true, message: '商品类别不能为空', trigger: ['blur', 'change'] }],
        unit: [{ required: true, message: '计量单位不能为空', trigger: ['blur', 'change'] }],
        warehouseName: [{ required: true, message: '默认仓库不能为空', trigger: ['blur', 'change'] }],
        locationName: [{ required: true, message: '默认库位不能为空', trigger: ['blur', 'change'] }],
      },
      supplierRules: {
        price: [{ required: true, message: '单价不能为空', trigger: ['blur', 'change'] }]
      },
      unitData: [], //计量单位
      detailInfo: {}, // 商品信息
      tableData: [], // 商品特性 供应商
      rowEditIndex: "",
      colimnEditIndex: "",
      accessoryList: [], // 图片信息
      fileList: [], // 图片上传列表
      imageFileLsit: [],
      activeNames: ["base", "product", "image", "more"], // 全部展开
      // 进度条
      progressFlag: false,
      percentage: 0,
      isFlag: true,
      fileImage: [],
      fileNum: 0,
      selectList: [],
      // 添加供应商
      isReload: false,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    onInputSearch(type, $event) {
      if (type == "name") {
        this.form.categoryName = $event.name;
        this.form.categoryId = $event.id;
      } else if (type == "warehouse") {
        this.form.warehouseName = $event.name;
        this.form.warehouseId = $event.id;
      } else if (type == "location") {
        this.form.locationName = $event.name;
        this.form.locationId = $event.id;
      } else if (type == "brand") {
        this.form.brand = $event.name
      }
    },
    resetForm() {
      this.form = {
        id: "",
        tenantId: "",
        code: "",
        name: "",
        brand: "",
        model: "",
        imageUrl: "",
        categoryName: "",
        barCode: "",
        unit: "",
        warehouseName: "",
        warehouseId: "",
        locationName: "",
        locationId: "",
        status: 1,
        remark: "",
        salable: 0,
        purchasable: 0,
        canBeSubitem: 0,
        canBeComponent: 0,
        outsourceable: 0,
        selfProducible: 0,
        createdName: "",
        createdTime: "",
        updatedName: "",
        updatedTime: "",
        productSupplierList: [],
        productAccessoryList: [],
      }
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
        this.$refs.formSwitch.clearValidate();
      })
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    // 获取计量单位
    getUnitData() {
      const unitTree = async()=>{
        var result = await getUnitListData();
        this.unitData = result;
      }
      unitTree();
    },
    // 获取当前商品信息
    async productInfo() {
      try {
        const res = await getProductInfo(this.productId);
        if (res.data.code == "100") {
          this.form = Object.assign({}, res.data.data);
          this.form.categoryName = this.form.categoryName ? this.form.categoryName : "全部";
          var accessoryList = this.form.productAccessoryList
          var imgList = accessoryList.length;
          if (imgList > 0) {
            this.accessoryList = accessoryList;
            accessoryList.forEach((item) => {
              item.url = `${this.filePath}${item.path}`
            })
            this.imageFileLsit = [...this.fileList, ...accessoryList];
          }
          this.uploadShow();
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.onCancel()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.onCancel()
      }
    },
    // 添加供应商
    addSupplier() {
      this.isReload = true;
    },
    // 点击单元格
    dbClickCell(scope) {
      var _this = this;
      _this.rowEditIndex = scope.$index
      _this.colimnEditIndex = scope.column.id
      _this.$nextTick(() => {
        _this.$refs.tableRowInputRef.focus();
      });
    },
    onInputTableBlur() {
      var _this = this;
      _this.rowEditIndex = "";
      _this.colimnEditIndex = "";
    },
    // 批量删除
    handleSelectChange(val) {
      this.selectList = val;
    },
    batchDelete() {
      getBatchDeleteInfo(this.form.productSupplierList, this.selectList, "删除供应商信息")
    },
    // 删除
    delClick(index) {
      getDeleteInfo(this.form.productSupplierList, index, "删除供应商信息");
    },
    // 图片上传
    beforeAvatarUpload(file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase();
      let isLimit = file.size / 1024 / 1024 < 5;
      var suffix = [
        "jpg",
        "png",
      ];
      if (!suffix.includes(fileName)) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'jpg, png' }));
        return false;
      }
      if (!isLimit) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '5MB' }))
        return false;
      }
    },
    uploadAttach(param) {
      this.fileImage.push(param);
      this.isFlag = true
      var formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', "product");
      importAttach(formData).then(res => {
        if (res.data.code === 100) {
          let obj = {
            fileName: res.data.data.fileName,
            name: res.data.data.fileName,
            path: res.data.data.fileUrl,
            uid: param.file.uid,
          }
          this.fileList.push(obj);
          this.fileNum += 1;
        } else {
          this.fileNum += 1;
        }
        if (this.fileImage.length == this.fileNum) {
          this.fileImage = [];
          this.fileNum = 0;
        }
        this.uploadShow();
        this.isFlag = true;
      })
    },
    uploadShow() {
      var list = [...this.fileList, ...this.accessoryList]
      if (list.length == 5) {
        $(".el-upload.el-upload--picture-card").hide()
      } else {
        $(".el-upload.el-upload--picture-card").show()
      }
    },
    handleRemove(file, fileList) {
      for (let i = 0; i < this.fileList.length; i++) {
        const obj = this.fileList[i];
        if (obj.uid === file.uid) {
          this.fileList.splice(i, 1)
          break;
        }
      }
      for (let i = 0; i < this.accessoryList.length; i++) {
        const obj = this.accessoryList[i];
        if (obj.uid === file.uid) {
          this.accessoryList.splice(i, 1)
          break;
        }
      }
      this.uploadShow();
      if (this.fileList.length <= 0) {
        this.fileList = []
      }
      if (this.accessoryList <= 0 ) {
        this.accessoryList = []
      }
      if (fileList.length <= 0) {
        this.fileList = []
        this.accessoryList = []
        this.imageFileLsit = []
        this.isFlag = true;
      }

    },
    handleExceed() {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 5}))
      return;
    },
    beforeRemove(file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    // 提交
    onSubmit() {
      var isStatus = true;
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
          isStatus = false
        }
      });
      if (this.form.productSupplierList.length > 0) {
        if (this.$refs["supplierPrice"] != undefined) {
          this.$refs["supplierPrice"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning('单价不能为空');
              isStatus = false
            }
          });
        }
      } else {
        this.$DonMessage.warning('供应商信息不能为空');
        isStatus = false
      }
      if (isStatus == false) {
        return
      }
      this.form.productAccessoryList = [...this.fileList, ...this.accessoryList];
      var params = {
        id: this.form.id,
        name: this.form.name,
        tenantId: this.form.tenantId,
        brand: this.form.brand,
        model: this.form.model,
        unit: this.form.unit,
        imageUrl: this.form.imageUrl,
        warehouseId: this.form.warehouseId,
        locationId: this.form.locationId,
        salable: this.form.salable,
        purchasable: this.form.purchasable,
        canBeSubitem: this.form.canBeSubitem,
        canBeComponent: this.form.canBeComponent,
        outsourceable: this.form.outsourceable,
        selfProducible: this.form.selfProducible,
        process: this.form.process,
        dimensions: this.form.dimensions,
        materialType: this.form.materialType,
        weight: this.form.weight,
        specifications: this.form.specifications,
        status: this.form.status,
        remark: this.form.remark,
        categoryName: this.form.categoryName,
        categoryId: this.form.categoryId,
        productSupplierList: this.form.productSupplierList,
        productAccessoryList: this.form.productAccessoryList
      }
      if(this.stateType == "add") {
        getProductAdd(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            removeTabs(this.$route);
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        });
      } else {
        getProductEdit(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            removeTabs(this.$route);
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      }
    },
    // 取消
    onCancel() {
      removeTabs(this.$route);
    },
    async initialState() {
      var _this = this;
      await _this.resetForm();
      _this.productId = _this.$route.params.id;
      if (_this.productId == "add") {
        _this.stateType = "add";
        _this.form.createdTime = new Date().getTime();
        _this.form.createdName = _this.$store.state.realName;
        var list = JSON.parse(sessionStorage.getItem("commodityName"));
        _this.form.categoryName = list.name;
        _this.form.categoryId = list.id;
      } else {
        _this.stateType = "edit";
        await _this.productInfo();
      }
      getContentData(_this);
      _this.getUnitData();
      collapseArea();
    },
  },
  mounted() {
    this.initialState();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name == "addProduct") {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  }
}
</script>
