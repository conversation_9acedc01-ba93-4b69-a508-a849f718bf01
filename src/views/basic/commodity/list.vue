<template>
  <div class="layoutContainer commodityManage">
    <div class="secondFloat">
      <el-form :inline="true" :label-width="$labelFour" ref="from" :model="from" class="demo-form-inline">
        <el-form-item label="商品类别" prop="categoryName">
          <selectInput
            ref="selectInput"
            v-model="from.categoryName"
            :inputParam="from.categoryName"
            inputType="categoryName"
            @select="onInputSearch('from', $event)"
            placeholder="请选择商品类别"
          ></selectInput>
        </el-form-item>
        <el-form-item label="商品编码" prop="code">
          <el-input v-model.trim="from.code" placeholder="请输入商品编码"></el-input>
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model.trim="from.name" placeholder="请输入商品名称"></el-input>
        </el-form-item>
        <el-form-item label="商品品牌" prop="brand">
          <selectInput
            ref="selectInput"
            v-model="from.brand"
            :inputParam="from.brand"
            inputType="brandInfo"
            placeholder="请选择品牌"
            @select="onInputSearch('brand', $event)"
          ></selectInput>
          <!-- <el-input v-model.trim="from.brand" placeholder="请选择商品名称"></el-input> -->
        </el-form-item>
        <el-form-item label="可销售" prop="salable" v-if="isShow">
          <el-select v-model.trim="from.salable" clearable filterable>
            <el-option v-for="(item, index) of switchType" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可采购" prop="purchasable" v-if="isShow">
          <el-select v-model.trim="from.purchasable" clearable filterable>
            <el-option v-for="(item, index) of switchType" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可为子件" prop="canBeSubitem" v-if="isShow">
          <el-select v-model.trim="from.canBeSubitem" clearable filterable>
            <el-option v-for="(item, index) of switchType" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可为组件" prop="canBeComponent" v-if="isShow">
          <el-select v-model.trim="from.canBeComponent" clearable filterable>
            <el-option v-for="(item, index) of switchType" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可委外" prop="outsourceable" v-if="isShow">
          <el-select v-model.trim="from.outsourceable" clearable filterable>
            <el-option v-for="(item, index) of switchType" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可自制" prop="selfProducible" v-if="isShow">
          <el-select v-model.trim="from.selfProducible" clearable filterable>
            <el-option v-for="(item, index) of switchType" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="fromReset()">{{ $t('button.reset') }}</el-button>
<!--   更多搜索条件暂时隐藏       -->
<!--          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton none">
              <span>商品类别</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <el-scrollbar>
                <el-tree
                  :data="categoryTree"
                  :render-content="renderContent"
                  :default-expand-all="true"
                  @node-click="handleCategorySelect"
                  @node-contextmenu="handleNodeContextmenu"
                  :props="{
                    label: 'name',
                    children: 'children'
                  }"
                  ref="categoryTree"
                  node-key="id"
                ></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight">
          <div class="rightTitle none">
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
              <el-button type="text" icon="bulkImport-icon" @click="batchImport()" v-if="false">批量导入</el-button>
              <el-button type="text" icon="bulkDown-icon" @click="exportClick()" v-if="false">导出</el-button>
              <el-button type="text" icon="enable-icon" @click="enableClick()" v-if="false">启用</el-button>
              <el-button type="text" icon="disable-icon" @click="disableClick()" v-if="false">禁用</el-button>
              <el-button type="text" icon="deleteRed-icon" @click="delClick()" v-if="false">删除</el-button>
            </div>
            <div v-if="false">
              <el-button type="text" icon="el-icon-setting">自定义列</el-button>
              <el-button type="text" icon="el-icon-refresh">刷新</el-button>
            </div>
          </div>
          <div class="detailInfo rightTableArea">
            <el-table
              style="width: 100%;"
              ref="table"
              highlight-current-row
              :data="commodityList"
              border
              stripe
              :max-height="maximumHeight"
              @header-dragend="changeColWidth"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="商品编码" prop="code" min-width="130" sortable>
                <template slot="header" slot-scope="scope">
                  <FilterHeader
                    sort="sort"
                    :column="scope.column"
                    field-name="商品编码"
                    field-prop="code"
                    filter-type="text"
                    filter-label="商品编码"
                    @tableFilter="tableFilter"
                    @resetFilter="tableFilterReset"
                  />
                </template>
                <template slot-scope="{row}">
                  <span class="linkStyle" @click="detailClick(row)">{{ row.code }}</span>
                </template>
              </el-table-column>
              <el-table-column label="商品名称" prop="name" min-width="140">
                <template slot="header" slot-scope="scope">
                  <FilterHeader
                    :column="scope.column"
                    field-name="商品名称"
                    field-prop="name"
                    filter-type="text"
                    filter-label="商品名称"
                    @tableFilter="tableFilter"
                    @resetFilter="tableFilterReset"
                  />
                </template>
                <template slot-scope="{row}">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="60">
                <template slot-scope="{row}">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="类别" prop="categoryName" width="85" sortable>
                <template slot="header" slot-scope="scope">
                  <FilterHeader
                    sort="sort"
                    :column="scope.column"
                    field-name="类别"
                    field-prop="categoryName"
                    filter-type="text"
                    @tableFilter="tableFilter"
                    @resetFilter="tableFilterReset"
                  />
                </template>
                <template slot-scope="{row}">
                  <span>{{ row.categoryName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="条形码" prop="barCode" width="100">
                <template slot="header" slot-scope="scope">
                  <FilterHeader
                    :column="scope.column"
                    field-name="条形码"
                    field-prop="barCode"
                    filter-type="text"
                    @tableFilter="tableFilter"
                    @resetFilter="tableFilterReset"
                  />
                </template>
                <template slot-scope="{row}">
                  <span class="linkStyle" @click="detailClick(row)">{{ row.barCode }}</span>
                </template>
              </el-table-column>
              <el-table-column label="品牌" prop="brand" width="80"></el-table-column>
              <el-table-column label="规则型号" prop="model" width="120"></el-table-column>
              <el-table-column label="计量单位" prop="unit" width="80"></el-table-column>
              <el-table-column label="状态" prop="status" width="70">
                <template slot="header" slot-scope="scope">
                  <FilterHeader
                    :column="scope.column"
                    field-name="状态"
                    field-prop="status"
                    filter-type="radio"
                    :custom-arr-list="statusList"
                    @tableFilter="tableFilter"
                    @resetFilter="tableFilterReset"
                  />
                </template>
                <template slot-scope="{row}">
                  <span v-if="row.status === 0" class="errorColor">禁用</span>
                  <span v-if="row.status === 1" class="successColor">启用</span>
                </template>
              </el-table-column>
              <el-table-column label="默认仓库" porp="warehouseName" width="100">
                <template slot-scope="{row}">
                <span>{{row.warehouseName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
              <el-table-column label="操作" width="180" fixed="right">
                <template slot-scope="{row}">
                  <el-button type="text" @click="detailClick(row)">详情</el-button>
                  <el-button type="text" @click="editClick(row)">编辑</el-button>
                  <el-button type="text" v-if="row.status == 1" @click="statusClick(row)">禁用</el-button>
                  <el-button type="text" v-if="row.status == 0" @click="statusClick(row)">启用</el-button>
                  <el-button type="text" v-if="row.status == 0" class="deleteButton" @click="deleteClick(row)">删除</el-button>                </template>
              </el-table-column>
            </el-table>
            <Pagination v-show="total > 0" :total="total"
              :page.sync="currentPage"
              :limit.sync="pagesize" @pagination="dataList" />
          </div>
        </el-col>
      </el-row>
      <!-- 右键菜单 -->
      <div class="contextmenuArea">
        <div @click="onAddCategory" v-if="currentNode">
          <i class="el-icon-plus"></i>
          <span>新增</span>
        </div>
        <div @click="onEditCategory" v-if="currentNode && currentNode.id != '' ">
          <i class="compile-icon"></i>
          <span>编辑</span>
        </div>
        <div @click="onRenameCategory" v-if="currentNode && currentNode.id != '' ">
          <i class="rename-icon"></i>
          <span>重命名</span>
        </div>
        <div class="deleteMark" @click="onDeleteCategory" v-if="currentNode && currentNode.id != '' ">
          <i class="deleteRed-icon"></i>
          <span>删除</span>
        </div>
       </div>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" v-if="dialogFormVisible">
      <el-form :label-width="formLabelWidth" :model="temp" ref="temp" :rules="tempRules">
        <el-form-item label="上级类别" prop="categoryName" v-if="dialogStatus != 'categoryRename'">
          <selectInput
            ref="selectInput"
            v-model="temp.categoryName"
            :inputParam="temp.categoryName"
            inputType="categoryName"
            @select="onInputSearch('temp', $event)"
            placeholder="请选择上级类别"
            :disabled="dialogStatus == 'categoryAdd' ? true : false"
          ></selectInput>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model.trim="temp.name" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort" v-if="dialogStatus != 'categoryRename'">
          <el-input-number v-model.trim="temp.sort" placeholder="请输入排序" controls-position="right" :min="1" :max="9999" :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleCategorySubmit">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from "@/components/Pagination"
import selectInput from "@/components/selectInput/selectInput.vue";
import FilterHeader from '@/components/filterHeader/filterHeader.vue';
import { sysServerUrl, addTabs, renderTree, tableHeight, contextmenuSeat, categoryTreeInfo } from "@/assets/js/common";
import {
  getProductcategoryTree,
  getProductcategoryAdd,
  getProductcategoryEdit,
  getProductcategoryDel,
  getProductList,
  getProductDel,
  getProductEdit,
} from "@/api/basicmgt";
import { addProductInfo, productCategoryTree } from "@/views/basic/basicCommon";
export default {
  name: "basic_commodity_list",
  components: { Pagination, selectInput, FilterHeader },
  data() {
    return {
      isShow: false,
      // 搜索
      from: {
        code: "",
        name: "",
        categoryName: "",
        categoryId: "",
        brand: "",
        salable: "",
        purchasable: "",
        canBeSubitem: "",
        canBeComponent: "",
        outsourceable: "",
        selfProducible: "",
      },
      categoryTree: [], // 商品类别树结构
      categoryList: [], // 商品类别平铺数据
      commodityList: [], // 商品详情展示
      currentNode: null, // 右键获取节点信息
      // 分页
      pagesize: 10,
      currentPage: 1,
      total: 0,
      maximumHeight: 0,
      tableDataCopy: {}, // 深度拷贝的tableData对象，用来筛选数据
      conditionsFields: [], // 记录参与筛选的列信息
      statusList: [
        { label: "禁用", value: 0 },
        { label: "启用", value: 1 },
      ],
      switchType: [
        {name: "是", value: 1},
        {name: "否", value: 0}
      ],
      // 商品类别弹框
      temp: {
        id: "",
        pid: "",
        name: "",
        sort: 1,
        categoryName: "",
      },
      tempRules: {
        categoryName: [{ required: true, message: '上级类别不能为空', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '姓名不能为空', trigger: ['blur', 'change'] }],
      },
      formLabelWidth: "100px",
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        categoryAdd: "新增商品类别",
        categoryEdit: "编辑商品类别",
        categoryRename: "重命名",
      },
      dialogCategoryVisible: false,
      selectType: "",
    }
  },
  methods: {
    onInputSearch(type, $event) {
      if (type == 'from') {
        this.from.categoryName = $event.name;
        this.from.categoryId = $event.id;
      } else if (type == "brand") {
        this.from.brand = $event.name
      } else {
        this.temp.categoryName = $event.name;
        this.temp.pid = $event.id;
      }
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSearch()
      }
    },
    onSearch() {
      this.from.categoryName = "全部" ?  '' : this.from.categoryName
      this.resetDataList();
    },
    // 重置
    fromReset(){
      if (this.$refs["from"].resetFields() !== undefined) {
        this.$refs["from"].resetFields();
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear();
      }
      this.from.brand = '';
      this.resetDataList();
    },
    // 商品类别树
    getCommodityTree() {
      const treeData = async () => {
        var result = await productCategoryTree();
        this.categoryTree = result;
        this.$nextTick(() => {
          if (this.categoryTree.length > 0) {
            this.$refs.categoryTree.setCurrentKey(this.categoryTree[0]); // 选中第一个节点
          }
        });
      }
      treeData();
    },
    renderContent(h, { node, data }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".commodityManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 点击商品类别获取详情
    handleCategorySelect(node) {
      this.currentNode = node;
      this.from.categoryId = node.id;
      this.resetDataList();
      window.closeRightMenu()
    },
    // 右键操作
    handleNodeContextmenu(event, data) {
      event.preventDefault()
      this.currentNode = data
      contextmenuSeat(event, ".commodityManage .contextmenuArea");
    },
    resetTemp() {
      this.temp = {
        id: "",
        pid: "",
        categoryName: "",
        name: "",
        sort: 1,
      }

      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
      if(this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    // 新增
    onAddCategory() {
      this.dialogStatus = "categoryAdd";
      this.dialogFormVisible = true;
      this.resetTemp();
      this.temp.categoryName = this.currentNode.name;
      this.temp.pid = this.currentNode.id
    },
    // 编辑
    onEditCategory() {
      this.dialogStatus = "categoryEdit";
      this.dialogFormVisible = true;
      this.resetTemp();
      this.temp = Object.assign({}, this.currentNode);
      this.temp.categoryName = categoryTreeInfo(this.categoryTree, this.temp.pid);
    },
    // 重命名
    onRenameCategory() {
      this.dialogStatus = "categoryRename";
      this.dialogFormVisible = true;
      this.resetTemp();
      this.temp = Object.assign({}, this.currentNode);
    },
    // 删除
    onDeleteCategory() {
      var name = this.currentNode.name
      var id = this.currentNode.id
      this.$confirm("确定删除【" + name + "】的相关信息?", '删除商品类别', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getProductcategoryDel(id).then((res) => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.getCommodityTree();
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },
    // 提交
    handleCategorySubmit() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == "categoryAdd") {
            getProductcategoryAdd(this.temp).then((res) => {
              if (res.data.code == '100') {
                this.$DonMessage.success(this.$t('successTip.submitTip'))
                this.getCommodityTree();
                this.dialogFormVisible = false;
                this.dialogStatus = "";
              } else {
                this.$DonMessage.error(res.data.msg)
              }
            });
          } else {
            getProductcategoryEdit(this.temp).then((res) => {
              if (res.data.code == '100') {
                this.$DonMessage.success(this.$t('successTip.submitTip'))
                this.getCommodityTree();
                this.dialogFormVisible = false;
                this.dialogStatus = "";
              } else {
                this.$DonMessage.error(res.data.msg)
              }
            });
          }
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 列表数据
    dataList() {
      var obj = {
        code: this.from.code,
        name: this.from.name,
        categoryName: this.from.categoryName,
        categoryId: this.from.categoryId,
        brand: this.from.brand,
        salable: this.from.salable,
        purchasable: this.from.purchasable,
        canBeSubitem: this.from.canBeSubitem,
        canBeComponent: this.from.canBeComponent,
        outsourceable: this.from.outsourceable,
        selfProducible: this.from.selfProducible,
        page: this.currentPage,
        limit: this.pagesize,
      };
      const param = new URLSearchParams();
      for (const [key, value] of Object.entries(obj)) {
        param.append(key, value);
      }
      getProductList(param).then(res => {
        if(res.data.code == "100") {
          this.commodityList = res.data.data
          this.total = res.data.total;
          this.tableDataCopy = JSON.parse(JSON.stringify(this.commodityList))
        }
        this.tableHeightArea();
      })
    },
    resetDataList() {
      this.currentPage = 1
      this.dataList()
    },
    // 新增
    addClick() {
      addProductInfo(this.currentNode)
    },
    // 批量导入
    batchImport() {},
    // 导出
    exportClick() {},
    // 启用
    enableClick() {},
    // 禁用
    disableClick() {},
    // 删除
    delClick() {},
    // 筛选
    tableFilter(data, reset) {
      let flag = true // 判断条件里有没有该列，用来判断是新增还是更新
      let filterData = this.tableDataCopy // 最终过滤信息
      if (!reset) {
        // 参与筛选的列信息，有则更新
        this.conditionsFields.forEach(item => {
          if (item.fieldName === data.fieldName) {
          item.conditions = data.conditions
          flag = false
          }
        })
        // 没有则添加
        if (flag) {
          this.conditionsFields.push(data)
        }
      }
      // 遍历所有筛选条件进行过滤
      this.conditionsFields.filter((fields, index) => {
        filterData = filterData.filter(item => {
          // 文本
          if (fields.filterType === 'text' && fields.conditions.text !== '') {
            return item[fields.fieldProp] && item[fields.fieldProp].indexOf(fields.conditions.text) > -1
          } else if (fields.filterType === 'radio' && fields.conditions.radio !== '') {
            return item[fields.fieldProp] !== null && item[fields.fieldProp].toString() == fields.conditions.radio.toString()
          }  else {
            // 遍历完没找到符合条件的，则直接返回
            return item;
          }
        })
      })
      this.commodityList = this.$set(this, 'commodityList', filterData);
    },
    tableFilterReset(data) {
      // 清空当前列筛选条件
      this.conditionsFields.forEach((item, index) => {
        if (item.fieldName === data.fieldName) {
          this.conditionsFields.splice(index, 1);
        }
      });
      if (this.conditionsFields.length === 0) {
        // 没有筛选条件了直接请求列表
        this.resetDataList();
      } else {
        // 有筛选条件就再去筛选
        this.tableFilter(data, true);
      }
    },
    // 详情
    detailClick(row) {
      var title = row.code;
      this.$router.push({ name: 'productDetail', params: { id: row.id }});
      addTabs(this.$route.path, title);
    },
    // 编辑
    editClick(row) {
      var title = "编辑 " + row.code;
      this.$router.push({ name: 'addProduct', params: {id: row.id}});
      addTabs(this.$route.path, title);
    },
    // 禁用
    statusClick(row) {
      if(row.status == 0) {
        row.status = 1;
      } else {
        row.status = 0;
      }
      getProductEdit(row).then((res) => {
        if (res.data.code == '100') {
          if(row.status == 0) {
            this.$DonMessage.success("禁用成功");
          } else {
            this.$DonMessage.success("启用成功");
          }
          this.dataList();
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },
    deleteClick(row) {
      this.$confirm("确定删除【" + row.name + "】的相关信息?", '删除商品类别', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getProductDel(row.id).then((res) => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.commodityList != null && this.commodityList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList();
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },
    // 表格高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    initialization() {
      this.dataList()
      this.getCommodityTree()
    },
  },
  mounted() {
    this.initialization()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
