<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>商品详情</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="商品编码">
                <span>{{ detailInfo.code }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="商品名称">
                <span>{{ detailInfo.name }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="商品类别">
                <span>{{ detailInfo.categoryName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="条形码">
                <span>{{ detailInfo.barCode }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="品牌">
                <span>{{ detailInfo.brand }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="型号">
                <span>{{ detailInfo.model }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计量单位">
                <span>{{ detailInfo.unit }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="默认仓库">
                <span>{{ detailInfo.warehouseName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="默认库位">
                <span>{{ detailInfo.locationName }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="工艺">
                <span>{{ detailInfo.process }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="尺寸">
                <span>{{ detailInfo.dimensions }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="材质">
                <span>{{ detailInfo.materialType }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="重量">
                <span>{{ detailInfo.weight }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="规格">
                <span>{{ detailInfo.specifications }}</span>
              </el-descriptions-item>


              <el-descriptions-item label="启用状态">
                <el-switch
                  v-model="detailInfo.status"
                  :active-value="1"
                  :inactive-value="0"
                  disabled
                ></el-switch>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ detailInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品特性
              </span>
            </template>
            <div class="secondFloat switchArea">
              <el-form :inline="true" :label-width="$labelFour">
                <el-form-item label="可销售">
                  <el-switch
                    v-model="detailInfo.salable"
                    :active-value="1"
                    :inactive-value="0"
                    disabled
                  />
                </el-form-item>
                <el-form-item label="可采购" prop="isPurchase">
                  <el-switch
                    v-model="detailInfo.purchasable"
                    :active-value="1"
                    :inactive-value="0"
                    disabled
                  />
                </el-form-item>
                <el-form-item label="可为子件" prop="isSubitem">
                  <el-switch
                    v-model="detailInfo.canBeSubitem"
                    :active-value="1"
                    :inactive-value="0"
                    disabled
                  />
                </el-form-item>
                <el-form-item label="可为组件">
                  <el-switch
                    v-model="detailInfo.canBeComponent"
                    :active-value="1"
                    :inactive-value="0"
                    disabled
                  />
                </el-form-item>
                <el-form-item label="可委外" prop="isOutsource">
                  <el-switch
                    v-model="detailInfo.outsourceable"
                    :active-value="1"
                    :inactive-value="0"
                    disabled
                  />
                </el-form-item>
                <el-form-item label="可自制" prop="isSelfmade">
                  <el-switch
                    v-model="detailInfo.selfProducible"
                    :active-value="1"
                    :inactive-value="0"
                    disabled
                  ></el-switch>
                </el-form-item>
              </el-form>
            </div>
            <el-table
              style="width:100%"
              ref="table"
              highlight-current-row
              :data="detailInfo.productSupplierList"
              border
              stripe
              @header-dragend="changeColWidth"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="供应商编码" prop="supplierCode" min-width="110"></el-table-column>
              <el-table-column label="供应商名称" prop="supplierName" min-width="120"></el-table-column>
              <el-table-column label="单价(￥)" prop="price" width="90" align="right">
                <template slot-scope="{row}">
                  <span>{{row.price}}</span>
                </template>
              </el-table-column>
              <el-table-column label="参考链接" prop="referLink" min-width="140">
                <template slot-scope="{row}">
                  <span class="linkStyle">{{ row.referLink }}</span>
                </template>
              </el-table-column>
              <el-table-column label="地址" prop="address" min-width="180"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
              <el-table-column label="操作" fixed="right" width="150">
                <template slot-scope="{row}">
                  <el-button type="text" size="small" @click="contactsInfo(row)">
                    联系人
                  </el-button>
                  <el-button type="text" size="small" @click="priceInfo(row)">
                    历史价格
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="image">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                图片信息
              </span>
            </template>
            <div v-if="accessoryList.length > 0" class="imageShowArea">
              <div v-for="(item, index) of accessoryList" :key="index">
                <img :src="$filePath + item.path" alt="" />
              </div>
            </div>
            <div v-else>
              暂无图片信息
            </div>
          </el-collapse-item>
          <el-collapse-item name="more" :column="dynamicColumn">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="5">
              <el-descriptions-item label="创建人">
                {{ detailInfo.createdName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="detailInfo.updatedName">
                {{ detailInfo.updatedName }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="detailInfo.updatedTime">
                {{ detailInfo.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <el-dialog v-dialogDrag :width="dialogStatus == 'contacts' ? '950px !important' : '800px !important'" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <div v-if="dialogStatus == 'contacts'">
        <el-table
          style="width: 100%;"
          ref="table"
          border
          stripe
          :data="contactsList"
          highlight-current-row
          @header-dragend="changeColWidth"
        >
          <el-table-column label="序号" width="50" type="index"></el-table-column>
          <el-table-column label="联系人" prop="name" width="100"></el-table-column>
          <el-table-column label="手机号" prop="mobile" min-width="100"></el-table-column>
          <el-table-column label="座机" prop="telephone" min-width="100"></el-table-column>
          <el-table-column label="性别" prop="sex" width="50"></el-table-column>
          <el-table-column label="邮箱" prop="email" min-width="100"></el-table-column>
          <el-table-column label="地址" prop="address" min-width="120"></el-table-column>
          <el-table-column label="默认联系人" prop="defaultFlag" width="100">
            <template slot-scope="{row}">
              <span v-if="row.defaultFlag == 0">否</span>
              <span v-if="row.defaultFlag == 1">是</span>
            </template>
          </el-table-column>
        </el-table>
        <Pagination v-show="contactsPage.total > 0" :total="contactsPage.total" :page.sync="contactsPage.currentPage"
          :limit.sync="contactsPage.pagesize" @pagination="getSupplierList" />
      </div>
      <div v-if="dialogStatus == 'price'">
        <el-table
          style="width: 100%;"
          ref="table"
          :data="priceList"
          border
          stripe
          highlight-current-row
          @header-dragend="changeColWidth"
        >
          <el-table-column label="序号" width="50" type="index"></el-table-column>
          <el-table-column label="供应商名称" min-width="120">
            <template>
              {{ supplierName }}
            </template>
          </el-table-column>
          <el-table-column label="商品名称" min-width="120">
            <template>
              {{ detailInfo.name }}
            </template>
          </el-table-column>
          <el-table-column label="价格(￥)" prop="price" align="right" width="100"></el-table-column>
          <el-table-column label="创建日期" prop="createTime" width="160">
            <template slot-scope="{row}">
              {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </template>
          </el-table-column>
        </el-table>
        <Pagination v-show="pricePage.total > 0" :total="pricePage.total" :page.sync="pricePage.currentPage"
          :limit.sync="pricePage.pagesize" @pagination="getHistPriceData" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from "@/components/Pagination";
import { removeTabs, collapseArea } from "@/assets/js/common";
import { getProductInfo, getSupplierData, getHistPriceList } from "@/api/basicmgt";
import { getColumnNumber } from "@/assets/js/heightResize";
export default {
  name: "productDetail",
  components: { Pagination },
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      id: "", // 商品ID
      detailInfo: {}, // 基本信息
      accessoryList: [], // 图片信息
      activeNames: ["base", "product", "image", "more"], // 全部展开
      // 弹框
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        contacts: "联系人",
        price: "历史价格",
      },
      productSupplierId: "", // 供应商ID
      supplierName: "", // 供应商名称
      contactsList: [], //联系人数据
      contactsPage: {
        pagesize: 10,
        currentPage: 1,
        total: 0,
      },
      priceList: [], // 历史价格数据
      pricePage: {
        pagesize: 10,
        currentPage: 1,
        total: 0,
      },
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    async dataList() {
      var _this = this;
      try {
        const res = await getProductInfo(_this.id);
        if (res.data.code == "100") {
          _this.detailInfo = res.data.data
          var imgList = _this.detailInfo.productAccessoryList.length;
          if (imgList > 0) {
            _this.accessoryList = _this.detailInfo.productAccessoryList;
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          removeTabs(this.$route);
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        removeTabs(this.$route);
      }

      collapseArea()
    },
    // 联系人
    contactsInfo(row) {
      var _this = this;
      _this.dialogStatus = "contacts";
      _this.dialogFormVisible = true;
      _this.contactsList = [];
      _this.productSupplierId = row.supplierId;
      _this.getSupplierList();
    },
    getSupplierList() {
      var params = {
        page: this.contactsPage.currentPage,
        limit: this.contactsPage.pagesize,
      }
      getSupplierData(this.productSupplierId, params).then((res) => {
        if (res.data.code == "100") {
          this.contactsList = res.data.data;
          this.contactsPage.total = res.data.total;

        }
      })
    },
    // 历史价格
    priceInfo(row) {
      var _this = this;
      _this.dialogStatus = "price";
      _this.dialogFormVisible = true;
      _this.priceList = [];
      _this.productSupplierId = row.id;
      _this.supplierName = row.supplierName;
      _this.getHistPriceData();
    },
    getHistPriceData() {
      var params = {
        page: this.pricePage.currentPage,
        limit: this.pricePage.pagesize,
      }
      getHistPriceList(this.productSupplierId, params).then((res) => {
        if (res.data.code == "100") {
          this.priceList = res.data.data.records;
          this.pricePage.total = res.data.data.total;
        }
      })
    },
  },
  mounted() {
    var _this = this;
    _this.id = _this.$route.params.id;
    _this.dataList();
  },
}
</script>
