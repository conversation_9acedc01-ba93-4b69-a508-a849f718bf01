<template>
  <div class="supplierContainer">
    <div class="dialogSearch">
      <el-input placeholder="输入编码/名称搜索"></el-input>
      <el-checkbox v-model="enableState">仅显示启用</el-checkbox>
      <el-button type="primary" >搜索</el-button>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span>商品分类</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <!-- <el-scrollbar> -->
                <el-tree :data="productTreeList" node-key="id" :render-content="renderContent"
                  :default-expand-all="true" @node-click="handleCategorySelect" :props="{
                    label: 'name',
                    children: 'children'
                  }" ref="categoryTree"></el-tree>
              <!-- </el-scrollbar> -->
            </div>
          </div>
        </el-col>
        <el-col :span="19">
          <el-row>
            <el-col :span="18" class="nationalInfo">
              <div>
                <el-table
                  :data="productList"
                  :height="tableHeight"
                  style="width: 100%;"
                  ref="table"
                  highlight-current-row
                  border
                  stripe
                  @header-dragend="changeColWidth"
                  @selection-change="handleSelection"
                >
                  <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
                  <el-table-column label="客户货号" prop="customerProCode"></el-table-column>
                  <el-table-column label="客户货名" prop="customerProName"></el-table-column>
                  <el-table-column label="商品编码" prop="code"></el-table-column>
                  <el-table-column label="商品名称" prop="name"></el-table-column>
                  <el-table-column label="图片" prop="imageUrl" width="60">
                    <template slot-scope="{row}">
                      <img :src="row.imageUrl" alt="" />
                    </template>
                  </el-table-column>

                  <el-table-column label="类别" prop="categoryName"></el-table-column>
                  <el-table-column label="规格型号" prop="model"></el-table-column>
                  <el-table-column label="计量单位" prop="unit"></el-table-column>
                  <el-table-column label="状态">
                    <template slot-scope="{row}">
                      <span class="successColor" v-if="row.status === 1">生效</span>
                      <span class="errorColor" v-if="row.status === 0">失效</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="备注" prop="remark"></el-table-column>
                </el-table>
                <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
              </div>
            </el-col>
            <el-col :span="6" class="nationalSelect">
              <el-table
                style="width:100%"
                :height="maxHeight"
                border
                stripe
                highlight-current-row
                :data="determineModelList"
                ref="applytable"
                :header-cell-style="{}"

              >
            <el-table-column prop="title">
              <template slot="header">
                <div class="authorityTitle">
                  <div>
                    <span>已选(<b> {{ selectNum }} </b>)</span>
                  </div>
                  <div>
                    <span class="clearAction" @click="emptyCountry">清空</span>
                  </div>
                </div>
              </template>
              <template slot-scope="scope">
                <div :class="'nationalList ' + '_' + scope.row.id">
                  <span>{{ scope.row.code }} {{  scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="submitArea">
      <div style="float: left;">
        <el-button plain>新增</el-button>
      </div>
      <el-button type="primary" @click="supplierSubmit()">{{ $t('button.submit') }}</el-button>
      <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { productCategoryTree, getCategoryTreeData } from "@/views/basic/basicCommon";
import {getCategoryTree, getProductList, getSupplierList} from '@/api/basicmgt'
import { addTabs, renderTree, tableHeight, contextmenuSeat } from "@/assets/js/common";
export default {
  name: "productInfo",
  components: { Pagination },
  data() {
    return {
      enableState: false,
      productTreeList: [],
      productList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      tableHeight:0,
      maxHeight:0,
      // 选中的对象
      determineModelList: [],
      // 选中的数量
      selectNum: 0,
      customerId: this.$parent.$parent.form.customerId
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },

    renderContent(h, { node, data }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".commodityManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 商品分类
    getProductTree() {
      const tree = async () => {
        var result = await productCategoryTree();
        this.productTreeList = result;
      }
      tree();
    },
    // 树结构点击事件
    handleCategorySelect(row) {
      this.selectedCategoryId = row.id;
      this.currentPage = 1;
      this.dataList();
    },
    // 商品列表
    dataList() {
      const params = {
        categoryId: this.selectedCategoryId,
        page: this.currentPage,
        size: this.pagesize,
        customerId: this.customerId,
        selectType: 'sales'
      }
      getProductList(params).then((res) => {
        if (res.data.code == '100') {
          this.productList = res.data.data
          this.total = res.data.total;
          this.sizeArea()
        }
      });
    },
    // 表格多选
    handleSelection(val) {
      this.determineModelList = val
      this.selectNum = this.determineModelList.length
      console.log(this.determineModelList)
    },
    // 清空
    emptyCountry() {
      this.handleSelection([])
      this.$refs.applytable.clearSelection();
    },
    // 提交
    supplierSubmit() {
      // this.determineModelList
      var list = this.$parent.$parent.form.salesOrderDetailList;
      this.determineModelList.forEach(row => {
          row.productId = row.id;
          row.productCode = row.code;
          row.productName = row.name;
      })
      list = list.concat(this.determineModelList);
      this.$parent.$parent.form.salesOrderDetailList = list;
      // console.log(this.$parent.$parent.form.productSupplierList);
      // this.$parent.$parent.form.productSupplierList.concat(this.determineModelList);
      this.$parent.$parent.dialogFormVisible = false;
      // console.log(this.$parent.$parent.form.productSupplierList);
      // this.$parent.this.form.produ
      // ctSupplierList
    },
    cancelClick() {
      this.$parent.$parent.dialogFormVisible = false;
    },
    // selectapply() {
    //   this.dialogCountryVisible = false
    //   let list = []
    //   if (this.determineModelList.length > 0) {
    //     this.determineModelList.forEach(row => {
    //       list.push(row.countryCode)
    //     })
    //   }
    //   this.temp.country = list

    //   this.targetName = list.length > 0 ? "已选择" : "未选择"
    // },
    // 分配国家 权限选择
    //  cellMouseEnter(row) {
    //   $(".nationalList._" + row.countryId + " .el-icon-close").show()
    // },
    // cellLeaveEnter() {
    //   $(".nationalList .el-icon-close").hide()
    // },
    heightArea() {
      var allHeight = $(".supplierContainer .infoDetail").height();
      var topHeight = $(".infoDetail .topButton").outerHeight(true);
      var leftVal = allHeight - topHeight;
      $(".supplierContainer .infoDetail .scrollClass").css("height", leftVal);
      var pageHeight = $(".nationalInfo .pagination-container").outerHeight(true);
      this.tableHeight = allHeight - pageHeight;
      this.maxHeight = allHeight;
    },
    sizeArea() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function() {
        _this.heightArea();
      })
    },
  },
  mounted() {
    this.getProductTree();
    this.dataList()
  },
}
</script>
<style>
.el-dialog .dialogSearch .el-input {
  width: 350px !important;
  margin-right: 10px;
}

.dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.dialogSearch .el-input .el-input__inner {
  width: 100% !important;
}
.dialogSearch .el-checkbox {
  margin-right: 10px !important;
}
/* .el-dialog */
.supplierContainer .infoDetail {
  height: 550px;
}
.supplierContainer .infoDetail .scrollClass {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
.supplierContainer .pagination-container {
  border-bottom: 1px solid var(--table-border);
  border-left: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border);
}
</style>
