<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="this.stateType == 'add'">新增销售订单</div>
      <div v-if="this.stateType == 'edit'">编辑销售订单</div>
      <div>
        <el-button type="primary" @click="onSubmit()">保存</el-button>
        <el-button plain @click="onCancel()">提交</el-button>
        <el-button plain @click="onCancelAndAudit()">提交并审核</el-button>
        <el-button plain @click="removeClick()">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFive">
                <el-form-item label="销售订单号" prop="salesNo">
                  <el-input v-model="form.salesNo" placeholder="销售订单号由系统生成" disabled />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.customerName"
                    :inputParam="form.customerName"
                    inputType="customerInfo"
                    placeholder="请选择客户名称"
                    @select="onInputSearch('name', $event)"
                  ></selectInput>
                </el-form-item>
                <el-form-item label="客户订单号" prop="customerNo">
                  <el-input v-model="form.customerNo" placeholder="请输入客户订单号"  />
                </el-form-item>
                <el-form-item label="销售员" prop="salesUser">
                  <el-select v-model="form.salesUser" clearable placeholder="请选择">
                    <el-option v-for="(item, index) of userDataList" :key="index" :label="item.realName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="销售公司" prop="salesCompany">
                  <el-select v-model="form.salesCompany" clearable>
                    <el-option v-for="(item, index) of salesCompanyList" :key="index" :label="item.name" :value="item.name"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="交货日期" prop="deliveryTime">
                  <el-date-picker
                    v-model="form.deliveryTime"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择日期"
                    :disabled="form.readonly"
                    :picker-options="pickerOptions"
                  ></el-date-picker>
                </el-form-item>

                <el-form-item label="联系人" prop="contactsName">
                  <el-input
                    ref="selectInput"
                    v-model="form.contactsName"
                    :inputParam="form.contactsName"
                    inputType="customerInfo"
                    placeholder="请选择客户联系人"
                  ></el-input>
                </el-form-item>


                <el-form-item label="联系电话" prop="contactsMobile">
                  <el-input v-model="form.contactsMobile" placeholder="请输入联系电话"  minlength="250"/>
                </el-form-item>
                <el-form-item label="交货地址" prop="deliveryAddress">
                  <el-input v-model="form.deliveryAddress" placeholder="请输入交货地址"  minlength="250"/>
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500" show-word-limit/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <div class="tableHandle spaceBbetwee">
              <el-button type="primary" icon="addProducts-icon" @click="addProduct">添加商品</el-button>
              <div>
                <el-button type="text" icon="import-icon">批量导入</el-button>
                <el-button type="text" icon="setIcon-icon">批量设置</el-button>
                <el-button type="text" icon="deleteRed-icon" @click="batchDelete">批量删除</el-button>
              </div>
            </div>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.salesOrderDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              @selection-change="handleSelectChange"
              show-summary
              :summary-method="getSummaries"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="客户货号" prop="customerProCode" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.customerProCode !== undefined">{{row.customerProCode}}</span>
                </template>
              </el-table-column>
              <el-table-column label="客户货名" prop="customerProName" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.customerProName !== undefined">{{row.customerProName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="商品编码" prop="productCode" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productCode !== undefined">{{row.productCode}}</span>
                </template>
              </el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productName !== undefined">{{row.productName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="品牌" prop="brand" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.brand !== undefined">{{row.brand}}</span>
                </template>
              </el-table-column>
              <el-table-column label="规格型号" prop="model" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.model !== undefined">{{row.model}}</span>
                </template>
              </el-table-column>
              <el-table-column label="颜色" prop="colour" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.colour !== undefined">{{row.colour}}</span>
                </template>
              </el-table-column>
              <el-table-column label="订单数量" prop="quantity" min-width="100">
                <template #header>
                  <span class="required-field">订单数量</span>
                </template>
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <el-input v-model.number="row.quantity"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="仓库" prop="warehouseName" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.warehouseName !== undefined">{{row.warehouseName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="库位" prop="locationName" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.locationName !== undefined">{{row.locationName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="库存数量" prop="currentInventory" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.currentInventory !== undefined">{{row.currentInventory}}</span>
                </template>
              </el-table-column>
<!--              <el-table-column label="单位" min-width="100">-->
<!--                <template #header>-->
<!--                  <span class="required-field">单位</span>-->
<!--                </template>-->
<!--                <template slot-scope="{row}">-->
<!--                  <span v-if="row.unit !== undefined">{{row.unit}}</span>-->
<!--                </template>-->
<!--              </el-table-column>-->
              <el-table-column label="单价(￥)" prop="unitPrice" min-width="100">
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <el-input v-model.number="row.unitPrice"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="金额(￥)" prop="amount" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.quantity!=undefined && row.unitPrice != undefined">
                     {{row.quantity * row.unitPrice }}
                  </span>
                  <!-- <div class="rowEditShow">
                    <el-input v-model.number="row.amount{{row.quantity *row.unitPrice }}"></el-input>
                  </div> -->
                </template>
              </el-table-column>
              <el-table-column label="折扣(%)" prop="discount" min-width="100">
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <el-input v-model.number="row.discount"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="折后单价(￥)" prop="discountPrice" min-width="100">
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <el-input v-model.number="row.discountPrice"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="折后金额(￥)" min-width="100" prop="discountAmount">
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <el-input v-model.number="row.discountAmount"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="80">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton" @click="delClick(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="创建人">
                {{ form.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="this.stateType == 'edit'">
                {{ form.updatedUser }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="this.stateType == 'edit'">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="附件">
                <FileUpload :upload-flag="'sales'" :drag="false" v-model="form.productAccessoryList"></FileUpload>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
      <dialogTable
        v-if="isReload"
        :isReload.sync="isReload"
        type="product"
        :paramId="form.customerId"
        :selectType="'sales'"
        :formList.sync="form.salesOrderDetailList"
        :columns="productColumns"
      >
        <template #imageUrl="scope">
          <img v-if="scope.row.imageUrl != ''" class="pictureShow" :src="$filePath + scope.row.imageUrl" alt="">
        </template>
      </dialogTable>
  </div>
</template>

<script>
import selectInput from "@/components/selectInput/selectInput.vue";
import {getSalesCompanyList} from "@/api/basicmgt";
import {findUserPage} from "@/api/sysmgt";
import {beforeRouteInfo, collapseArea, getContentData, removeTabs} from "@/assets/js/common";
import {getSalesOrderAdd, getSalesOrderEdit, getSalesOrderInfo} from "@/api/salesmgt";
import dialogTable from "@/components/dialogTable/dialogTable.vue";
import {customerColumns, productColumns} from "@/assets/js/tableHeader";
import {getBatchDeleteInfo, getDeleteInfo} from '@/views/basic/basicCommon'
import FileUpload from "@/components/FileUpload/FileUpload.vue";

export default {
  name: "salesAdd",
  // eslint-disable-next-line vue/no-unused-components
  components: {FileUpload, selectInput, dialogTable},
  data() {
    return {
      productColumns: customerColumns.slice(0, 2).concat(productColumns),
      id:'',
      selectedCustomer: '', // 客户列表选中数据
      productId: "", //id
      stateType: "", //类型
      count : 0,
      // 基本信息
      form: {
        id: "",
        salesNo:'',
        customerNo:'',
        salesUser:'',
        salesCompany:'',
        deliveryAddress:'',
        deliveryTime:'',
        tenantId: "",
        code: "",
        name: "",
        brand: "",
        imageUrl: "",
        categoryName: "",
        contactsName: "",
        contactsMobile: "",
        barCode: "",
        unit: "",
        customerName: "",
        customerId: "",
        warehouseName: "",
        warehouseId: "",
        locationName: "",
        locationId: "",
        status: 1,
        remark: "",
        salable: 1,
        purchasable: 1,
        canBeSubitem: 1,
        canBeComponent: 1,
        outsourceable: 1,
        selfProducible: 1,
        createdUser: "",
        createdUserName: "",
        createdTime: "",
        updatedUser: "",
        updatedTime: "",
        salesOrderDetailList: [],
        productAccessoryList: [],
      },
      rules: {
        salesNo: [{  validator: (rule, value, callback) => callback() }],
        customerName: [{ required: true, message: '客户名称不能为空', trigger: ['blur', 'change']}],
        salesUser: [{ required: true, message: '销售员不能为空', trigger: ['blur', 'change'] }],
        salesCompany: [{ required: true, message: '销售公司不能为空', trigger: ['blur', 'change'] }],
        deliveryTime: [{ required: true, message: '交货日期不能为空', trigger: ['blur', 'change'] }],
        contactsName: [{ required: true, message: '客户联系人不能为空', trigger: ['blur', 'change'] }],
        contactsMobile: [{ required: true, message: '联系人电话不能为空', trigger: ['blur', 'change'] }],
        deliveryAddress: [{ required: true, message: '交货地址不能为空', trigger: ['blur', 'change'] }],
      },
      pagesize: 999,
      currentPage: 1,
      salesCompanyList: [], // 销售公司
      userDataList: [], // 销售人员
      unitData: [], //计量单位
      detailInfo: {}, // 商品信息
      tableData: [], // 商品特性 供应商
      rowEditIndex: "",
      colimnEditIndex: "",
      selectList: [],
      activeNames: ["base", "product", "more"], // 全部展开
      // 添加商品
      isReload: false,
      // 进度条
      progressFlag: false,
      percentage: 0,
      pickerOptions: {
        disabledDate(time) {
          // 禁用今天之前的日期，只允许选择今天及以后的日期
          return time.getTime() <= Date.now() - 8.64e7; // 8.64e7 是一天的毫秒数（24 * 60 * 60 * 1000）
          // return time.getTime() < Date.now(); // 禁用今天及之前的日期（包括今天）
        }
      }
    }

  },
  methods:{
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    addProduct() {
      if (this.form.customerId === '' || this.form.customerName === ''){
        this.$DonMessage.warning("请先选择客户！");
        return
      }
      this.isReload = true;
    },
    onInputSearch(type, $event) {
      if (type == "name") {
        this.form.customerName = $event.name;
        this.form.customerId = $event.id;
        this.form.deliveryAddress = $event.contactsAddress;
        this.form.contactsMobile = $event.contactsMobile;
        this.form.contactsName = $event.contactsName;
      }
    },

    getSalesCompanyList(){
      getSalesCompanyList({}).then((res) => {
        if (res.data.code == '100') {
          this.salesCompanyList = res.data.data
        }
      });
    },
    getUserList(){
      findUserPage(this.currentPage + "/" + this.pagesize,{}).then(res => {
        if (res.data.code == '100') {
          this.userDataList = res.data.data
        }
      });
    },
    removeClick() {
      removeTabs(this.$route);
    },
    // 批量删除
    handleSelectChange(val) {
      this.selectList = val;
    },
    batchDelete() {
      getBatchDeleteInfo(this.form.salesOrderDetailList, this.selectList, "删除商品信息")
    },
    // 删除
    delClick(index) {
      getDeleteInfo(this.form.salesOrderDetailList, index, "删除商品信息");
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        switch (column.property) {
          case "quantity":
            this.form.salesOrderDetailList.forEach((item) => {
              if (item.quantity) {
                count += item.quantity;
              }
            });
            sums[index] = count;
            break;
          case "amount":
            this.form.salesOrderDetailList.forEach((item) => {
              if (item.amount) {
                count += item.amount;
              }
            });
            sums[index] = count;
            break;
          case "discountAmount":
            this.form.salesOrderDetailList.forEach((item) => {
              if (item.discountAmount) {
                count += item.discountAmount;
              }
            });
            sums[index] = count;
            break;
        }
      })
      return sums;
    },
    // 保存
    onSubmit(){
      this.form.status = 0
      this.submit()
    },
    onCancel(){
      this.form.status = 1
      this.submit()
    },
    onCancelAndAudit(){
      this.form.status = 2
      this.submit()
    },
    submit(){
      var params = {
        id: this.form.id,
        salesNo:this.form.salesNo,
        customerNo:this.form.customerNo,
        salesUser:this.form.salesUser,
        salesCompany:this.form.salesCompany,
        deliveryAddress:this.form.deliveryAddress,
        contactsName: this.form.contactsName,
        contactsMobile: this.form.contactsMobile,
        deliveryTime:this.form.deliveryTime,
        customerName:this.form.customerName,
        customerId:this.form.customerId,
        tenantId: this.form.tenantId,
        status: this.form.status,
        remark: this.form.remark,
        // categoryName: this.form.categoryName,
        salesOrderDetailList: this.form.salesOrderDetailList,
        productAccessoryList: this.form.productAccessoryList
      }
      // 验证
      var flag = false;
      if (params.salesOrderDetailList.length > 0) {
        params.salesOrderDetailList.forEach(item => {
          if (item.quantity == null || item.quantity == '') {
            flag = true;
          }
        })
      }else {
        this.$DonMessage.warning("销售商品不能为空！");
        return;
      }
      if (flag){
        this.$DonMessage.warning("销售商品数量不能为空！");
        return;
      }
      // 验证商品的id是否唯一
      let sameProduct = false;
      const map = new Map();
      for (const item of this.form.salesOrderDetailList) {
        if (!item.productId) continue;
        const key = `${item.productId}`;
        if (map.has(key)) {
          sameProduct = true; // 存在重复
          this.$DonMessage.error("商品："+ item.name + " 存在相同的销售商品信息，请检查")
          return;
        }
        map.set(key, true);
      }

      if(this.stateType === "add") {
        getSalesOrderAdd(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        });
      } else {
        getSalesOrderEdit(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      }
    },
    // 获取订单信息
    async salesOrderInfo() {
      // this.resetForm();

      const res = await getSalesOrderInfo(this.id);
      if (res.data.code == "100") {
        this.form = Object.assign({}, res.data.data);
        console.log(this.form);
        this.form.updatedUser = this.$store.state.realName;
        this.form.updatedTime = new Date().getTime();
      }

    },
    async initialState() {
      var _this = this;
      _this.id = _this.$route.params.id;
      if (_this.id == "add") {
        _this.stateType = "add";
        _this.form.createdTime = new Date().getTime();
        _this.form.createdUser = this.$store.state.realName;
        _this.form.createdUserName = this.$store.state.realName;
      } else {
        _this.stateType = "edit";
        _this.form.updatedUser = this.$store.state.realName;
        await _this.salesOrderInfo();
      }
      _this.getSalesCompanyList();
      _this.getUserList();
      getContentData(_this)
      collapseArea()
      // this.contentSize();
    },
  },
  mounted() {
    this.initialState();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name == "salesAdd") {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  }
}
</script>
