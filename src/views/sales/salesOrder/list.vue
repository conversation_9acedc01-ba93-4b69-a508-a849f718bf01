<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" ref="queryParams" :label-width="$labelFour" class="demo-form-inline">
        <el-form-item label="单据日期">
          <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
        </el-form-item>
        <el-form-item label="订单号" prop="salesNo">
          <el-input
            v-model="queryParams.salesNo"
            placeholder="请输入销售订单号"
            clearable
          />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <selectInput
            ref="selectInput"
            v-model="queryParams.customerName"
            :inputParam="queryParams.customerName"
            inputType="customerInfo"
            placeholder="请选择客户名称"
            @select="onInputSearch('customer',$event)"
          ></selectInput>
        </el-form-item>

        <el-form-item label="商品" prop="productName">
          <selectInput
            ref="selectInput"
            v-model="queryParams.productName"
            :inputParam="queryParams.productName"
            inputType="productInfo"
            placeholder="请选择商品"
            @select="onInputSearch('product', $event)"
          ></selectInput>
        </el-form-item>

        <el-form-item label="状态" prop="status" v-if="isShow">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option v-for="item in auditStatusDicts" :value="item.code" :key="item.code" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购状态" prop="applyOrderStatus" v-if="isShow">
          <el-select v-model.trim="queryParams.applyOrderStatus" clearable filterable>
            <el-option v-for="(item, index) of orderStatus" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生产状态" prop="taskOrderStatus" v-if="isShow">
          <el-select v-model.trim="queryParams.taskOrderStatus" clearable filterable>
            <el-option v-for="(item, index) of orderStatus" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发货状态" prop="shippingStatus" v-if="isShow">
          <el-select v-model.trim="queryParams.shippingStatus" clearable filterable>
            <el-option v-for="(item, index) of shipStatus" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收款状态" prop="collectionStatus" v-if="isShow">
          <el-select v-model.trim="queryParams.collectionStatus" clearable filterable>
            <el-option v-for="(item, index) of collStatus" :key="index" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="销售员" prop="salesUser" v-if="isShow">
          <el-select v-model="queryParams.salesUser" placeholder="请选择">
            <el-option v-for="(item, index) of userDataList" :key="index" :label="item.realName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i
            class="el-icon-arrow-up el-icon--right"></i></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
          <el-button type="text" icon="bulkImport-icon" @click="batchImport()">批量导入</el-button>
          <el-dropdown>
            <span>
              <i class="process-icon"></i>
              审核
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
                <el-dropdown-item @click.native ="auditClick('reviewPass')">审核通过</el-dropdown-item>
                <el-dropdown-item @click.native ="auditClick('reviewReject')">审核驳回</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>

          <el-dropdown>
            <span>
              <i class="receipt-icon"></i>
              生成单据
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
<!--                <el-dropdown-item @click="auditClick('reviewPass')">生成待出库单</el-dropdown-item>-->
                <el-dropdown-item @click.native="applyClick()">生成采购申请单</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>

          <el-dropdown>
            <span>
              <i class="production-tasks"></i>
              生成生产任务单
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
                <el-dropdown-item @click="auditClick('reviewPass')">自制加工任务单</el-dropdown-item>
                <el-dropdown-item @click="auditClick('reviewReject')">委外加工任务单</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>

          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
          <el-button type="text" icon="disable-icon" @click="abolishClick('abrogate')">作废</el-button>
          <el-button type="text" icon="deleteRed-icon" @click="delClick()">删除</el-button>
        </div>
        <!--        <div>-->
        <!--          <el-button type="text" icon="el-icon-setting">自定义列</el-button>-->
        <!--          <el-button type="text" icon="el-icon-refresh">刷新</el-button>-->
        <!--        </div>-->
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange"
                row-key="id"
                show-summary
                :summary-method="getSummaries"
      >
        <el-table-column type="selection" width="50" :reserve-selection="true" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="createdTime" label="单据日期" width="150">
          <template #default="scope">
            {{ scope.row.createdTime | conversion('yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="salesNo" label="销售订单号" width="160">
          <template #default="{row}">
            <span class="linkStyle" @click="handleDetail(row)">{{ row.salesNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <div :class="getAuditStatusClass(scope.row.status)">
              <!--              {{ getAuditStatusText(scope.row.status) }}-->
              {{ formatAuditStatus(scope.row.status) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="deliveryTime" label="交货日期" width="150">
          <template #default="scope">
            {{ scope.row.deliveryTime | conversion('yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="客户名称" width="120"/>
        <el-table-column prop="customerNo" label="客户订单号" width="150"/>
        <el-table-column prop="quantity" label="订单数量" width="150"/>
        <el-table-column prop="stockQty" label="库存数量" width="150"/>
        <el-table-column prop="shippingQty" label="发货数量" width="150"/>
        <el-table-column prop="applyOrderStatus" label="采购申请" width="140">
          <template slot-scope="scope">
            <div :class="getPurchaseStatusClass(scope.row.applyOrderStatus)">
              {{ getStatusText(scope.row.applyOrderStatus) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="taskOrderStatus" label="生产任务" width="140">
          <template slot-scope="scope">
            <div :class="getPurchaseStatusClass(scope.row.taskOrderStatus)">
              {{ getStatusText(scope.row.taskOrderStatus) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="shippingStatus" label="发货状态" width="140">
          <template slot-scope="scope">

            <div class="linkStyle" @click="shipDetail(scope.row)" v-if="scope.row.shippingStatus != 0 ">
              <div :class="getPurchaseStatusClass(scope.row.shippingStatus)">
                {{ getShipStatus(scope.row.shippingStatus) }}
              </div>
            </div>

            <div v-if="scope.row.shippingStatus == 0 ">
              <div :class="getPurchaseStatusClass(scope.row.shippingStatus)">
                {{ getShipStatus(scope.row.shippingStatus) }}
              </div>
            </div>



          </template>
        </el-table-column>
        <el-table-column prop="collectionStatus" label="收款状态" width="140">
          <template slot-scope="scope">
            <div :class="scope.row.collectionStatus === 1 ? 'successColor' : 'errorColor' ">
              {{ scope.row.collectionStatus === 1 ? '已收款' : '未收款' }}
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column prop="needInvoice" label="发票需求" width="140">-->
<!--          <template slot-scope="scope">-->
<!--            <div :class="scope.row.needInvoice === 1 ? 'successColor' : 'errorColor' ">-->
<!--              {{ scope.row.needInvoice === 1 ? '需要' : '不需要' }}-->
<!--            </div>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column prop="invoiceStatus" label="发票状态" width="140">-->
<!--          <template slot-scope="scope">-->
<!--            <div :class="scope.row.invoiceStatus === 1 ? 'successColor' : 'errorColor' ">-->
<!--              {{ scope.row.invoiceStatus === 1 ? '已开票' : '未开票' }}-->
<!--            </div>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column prop="salesUserName" label="销售员" width="100"/>
        <el-table-column prop="auditUser" label="审核人" width="100"/>
        <el-table-column prop="auditTime" label="审核时间" width="150">
          <template slot-scope="scope">
            {{ scope.row.auditTime |conversion('yyyy-MM-dd HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注"/>
        <el-table-column prop="createdUserName" label="制单人" width="100"/>
        <el-table-column prop="createdTime" label="制单时间" width="150">
          <template slot-scope="scope">
            {{ scope.row.createdTime | conversion('yyyy-MM-dd HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
            <el-button type="text" size="small" v-if="row.status == 0 || row.status == 3" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize" @pagination="dataList"/>
    </div>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="reviewRejectDialogVisible" v-if="reviewRejectDialogVisible">
      <el-form :model="reason"  ref="reason" >
        <el-form-item label="驳回理由" prop="reason">
          <el-input v-model="reason" />
        </el-form-item>

        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleAuditReject">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="reviewRejectDialogVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog v-dialogDrag :width="'800px !important'" :title="'查看发货状态'" :visible.sync="dialogShipStatus">
      <el-descriptions>
        <el-descriptions-item label="销售订单号">
          <span>{{this.orderShip.salesNo}}</span>
        </el-descriptions-item>
        <el-descriptions-item label="发货状态">
          <div :class="getPurchaseStatusClass( this.orderShip.shippingStatus)">
            {{ getShipStatus( this.orderShip.shippingStatus) }}
          </div>
<!--          <span>{{ this.orderShip.shippingStatus }}</span>-->
        </el-descriptions-item>
        <el-descriptions-item label="订单数量">
          <span>{{ this.orderShip.quantity }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <el-table
        style="width: 100%;"
        ref="table"
        border
        stripe
        :data="shippingList"
        highlight-current-row
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" width="50" type="index"></el-table-column>
        <el-table-column prop="createdTime" label="发货日期" width="120">
          <template slot-scope="scope">
            {{ scope.row.createdTime | conversion('yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column label="物流单号" prop="logisticsNo" min-width="100"></el-table-column>
        <el-table-column label="物流公司" prop="logisticsCompany" min-width="100"></el-table-column>
        <el-table-column label="发货数量" prop="shipQty" min-width="100"></el-table-column>
        <el-table-column label="备注" prop="remark" min-width="120"></el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" size="small" @click="shippingDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-show="orderShip.total > 0" :total="orderShip.total" :page.sync="orderShip.page"
                  :limit.sync="orderShip.pageSize" @pagination="orderShipDetailList" />

    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import selectInput from "@/components/selectInput/selectInput.vue";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {
  salesList,
  salesOrderDel,
  salesOrderDiscard,
  salesOrderPass,
  salesOrderReject,
  shipDetailList
} from "@/api/salesmgt";
import {addTabs, tableHeight} from "@/assets/js/common";
import {dictTypeQueryData, findDeptAll, findUserPage} from "@/api/sysmgt";
import {commentDel} from "@/api/material";
import {getStatusClass} from "@/assets/js/utils";

export default {
  name: "list",
  components: { selectInput, DateRangeSelector, Pagination},
  data() {
    return {
      orderStatus: [
        { name: '未生成', value: '0' },
        { name: '部分生成', value: '1' },
        { name: '已生成', value: '2' }

      ],
      shipStatus: [
        { name: '未发货', value: '0' },
        { name: '部分发货', value: '1' },
        { name: '已发货', value: '2' }
      ],
      collStatus:[
      { name: '未收款', value: '0' },
      { name: '已收款', value: '1' }
      ],
      pagesize: 999,
      currentPage: 1,
      userDataList: [],
      reason:'',
      reviewRejectDialogVisible: false,
      auditStatusDicts: [],
      selectedRows: [],
      deleteList: [],
      maximumHeight: 0,
      total: 0,
      dialogStatus: '',
      dialogShipStatus: false,
      textMap: {
        reviewReject: "审核驳回"
      },
      orderShip:{
        id : '',
        salesNo:'',
        quantity:'',
        shippingStatus:'',
        page: 1,
        pageSize: 10,
        total: 0
      },
      shippingList:[],
      isShow: false,
      resultList: [],

      // form:{
      //   customerName: "",
      //   customerId: "",
      // },
      queryParams: {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        salesNo: '',
        applyOrderStatus: '',
        taskOrderStatus: '',
        shippingStatus: '',
        collectionStatus: '',
        salesUser: '',
        customerName: "",
        productName: "",
        productId: "",
        customerId: "",
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        page: 1,
        pageSize: 10
      },
    }
  },
  methods: {
    getStatusClass,
    onInputSearch(type, $event) {
      if (type  == 'product'){
        this.queryParams.productName = $event.name;
        this.queryParams.productId = $event.id;
      }else {
        this.queryParams.customerName = $event.name;
        this.queryParams.customerId = $event.id;
      }
    },
    getPurchaseStatusClass(status) {
      switch(status) {
        case 0:
          return 'errorColor'; // 未生成 - 红色或其他错误颜色
        case 1:
          return 'warningColor'; // 部分生成 - 黄色或其他警告颜色
        case 2:
          return 'successColor'; // 已生成 - 绿色或其他成功颜色
        default:
          return 'errorColor'; // 默认情况，可根据需要调整
      }
    },
    getStatusText(status) {
      switch(status) {
        case 0:
          return '未生成';
        case 1:
          return '部分生成';
        case 2:
          return '已生成';
        default:
          return '未知状态'; // 默认情况，可根据需要调整
      }
    },
    getShipStatus(status) {
      switch(status) {
        case 0:
          return '未发货';
        case 1:
          return '部分发货';
        case 2:
          return '已发货';
        default:
          return '未知状态';
      }
    },
    getAuditStatusClass(status) {
      switch(status) {
        case 1:
          return 'errorColor'; // 待审核
        case 2:
          return 'successColor'; // 审核通过 - 绿色
        case 3:
          return 'abnormalColor'; // 审核驳回 -
        case 4:
          return 'abrogateColor';
        default:
          return ''; //
      }
    },
    getAuditStatusText(status) {
      switch(status) {
        case 0:
          return '草稿';
        case 1:
          return '待审核';
        case 2:
          return '已审核';
        case 3:
          return '审核驳回';
        default:
          return '未知状态'; // 默认情况，可根据需要调整
      }
    },

    //转换状态显示
    formatAuditStatus(status){
      const match = this.auditStatusDicts.find(item => item.code == status);
      return match ? match.name : '';
    },
    getUserList(){
      findUserPage(this.currentPage + "/" + this.pagesize,{}).then(res => {
        if (res.data.code == '100') {
          this.userDataList = res.data.data
        }
      });
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        switch (column.property) {
          case "quantity":
            this.resultList.forEach((item) => {
              count += item.quantity;
            });
            sums[index] = count;
            break;
        }
      })
      return sums;
    },
    //新增
    addClick() {
      const title = "新增销售订单";
      this.$router.push({
        name: 'salesAdd',
        params: {id: 'add'}
      });
      addTabs(this.$route.path, title);
    },
    //批量导入
    batchImport() {

    },
    handleAuditReject(){
      let ids = ""
      this.selectedRows.forEach(item => {
        ids += item.id + ","
      })
      var params = new URLSearchParams()
      params.append('ids', ids)
      params.append('reason', this.reason)
      salesOrderReject(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success("审核驳回成功！")
          this.reviewRejectDialogVisible = false;
          this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },
    //批量作废
    abolishClick() {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要作废的数据");
        return false;
      }
      this.$confirm('确定作废【' + this.selectedRows.length + '】条数据吗?', '作废订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        salesOrderDiscard(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(res.data.data)
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },
    //跳转生成采购申请单
    applyClick(){
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要生成采购申请单的数据");
        return;
      }
      if (this.selectedRows.length > 1){
        this.$DonMessage.warning("只能选择一条订单数据");
        return;
      }
      const title = "新增采购申请单";
      this.$router.push({
        name: 'addPurchaseApply',
        params: {
          id: 'salesAdd',
          salesOrderNo: this.selectedRows[0].salesNo,
          customerNo: this.selectedRows[0].customerNo
        }
      });
      addTabs(this.$route.path, title);
    },
    //审核
    auditClick(type) {
      this.reason='';
      if(type == 'reviewReject'){
        this.reviewRejectDialogVisible = true;
        return;
      }
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要审核的数据");
        return false;
      }
      this.$confirm('确定审核【' + this.selectedRows.length + '】条数据吗?', '审核订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        if (type == 'reviewPass'){
          salesOrderPass(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(res.data.data)
              this.dataList()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          })
        }
      })
    },
    //批量删除
    delClick() {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要删除的数据");
        return false;
      }
      this.$confirm('确定删除【' + this.selectedRows.length + '】条数据吗?', '删除订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        salesOrderDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },
    //导出
    exportClick() {

    },
    //详情
    handleDetail(row) {
      var title = row.salesNo;
      this.$router.push({ name: 'salesOrderDetail', params: { id: row.id }});
      addTabs(this.$route.path, title);
    },
    shippingDetail(row) {
      // 跳转到详情页面
      const title = row.sourceNo;
      this.$router.push({
        name: 'logisticsDetail',
        params: {
          id: row.id,
          type: 'pending'
        }
      })
      addTabs(this.$route.path, title)
    },



    // 收货信息弹窗
    shipDetail(row){
      this.id = row.id;
      this.orderShip.salesNo = row.salesNo;
      this.orderShip.quantity = row.quantity;
      this.orderShip.shippingStatus = row.shippingStatus;
      this.dialogShipStatus = true
      this.orderShipDetailList();
    },
    orderShipDetailList() {

      const params = {
        id: this.id,
        page: this.orderShip.page,
        limit: this.orderShip.pageSize,
      }
      shipDetailList(params).then(res => {
        if (res.data.code === 100) {
          this.shippingList = res.data.data;
          this.orderShip.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },
    //编辑
    handleEdit(row) {
      var title = "编辑 " + row.salesNo;
      this.$router.push({ name: 'salesAdd', params: {id: row.id}});
      addTabs(this.$route.path, title);
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.handleSearch()
      }
    },
    handleSearch() {
      this.resetDataList();
    },
    //  处理选中的行
    handleSelectionChange(val) {
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },
    //重置查询条件
    handleReset() {
      if (this.$refs["queryParams"].resetFields() !== undefined) {
        this.$refs["queryParams"].resetFields();
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear();
      }
      this.queryParams.productId = '';
      //重置时间选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      this.resetDataList();
    },
    //分页查询数据
    dataList() {
      const params = new URLSearchParams();
      params.append('applyOrderStatus', this.queryParams.applyOrderStatus);
      params.append('taskOrderStatus', this.queryParams.taskOrderStatus);
      params.append('shippingStatus', this.queryParams.shippingStatus);
      params.append('collectionStatus', this.queryParams.collectionStatus);
      params.append('salesUser', this.queryParams.salesUser);
      params.append('salesOrderNo', this.queryParams.salesOrderNo);
      params.append('salesNo', this.queryParams.salesNo);
      params.append('status', this.queryParams.status);
      params.append('startDate', this.queryParams.startDate);
      params.append('endDate', this.queryParams.endDate);
      params.append('customerOrderNo', this.queryParams.customerOrderNo);
      params.append('page', this.queryParams.page);
      params.append('limit', this.queryParams.pageSize);
      params.append('customerName', this.queryParams.customerName);
      params.append('customerId', this.queryParams.customerId);
      params.append('productId', this.queryParams.productId);
      console.log("销售订单分页查询参数：",params)
      salesList(params).then(res => {
        console.log("销售订单数据", res)
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    resetDataList() {
      this.queryParams.page = 1;
      this.dataList()
    },
    // 所属部门
    getDepartment() {
      let _this = this;
      _this.departmentMap = new Map();
      findDeptAll().then(res => {
        if (res.data.data) {
          _this.departmentList = res.data.data
          _this.getDepartmentMap(_this.departmentList, "");
        } else {
          _this.departmentList = []
        }
      })
    },
    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['form'].validateField('applyDept')
      }
    },
    // 获取日期范围选择
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      this.resetDataList();
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    this.dataList()
    //获取审核状态数据字典
    this.getAuditStatusDict();
    this.getUserList();
    window.addEventListener('keydown', this.keyDown)

  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>

<style scoped>
.abrogateColor {
  color: #909399;
}
.abnormalColor {
  color: #E6A23C;
}
</style>
