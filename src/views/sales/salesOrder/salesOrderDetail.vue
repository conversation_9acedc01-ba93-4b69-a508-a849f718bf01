<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>销售订单详情</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="销售订单号">
                <span>{{ detailInfo.salesNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户名称">
                <span>{{ detailInfo.customerName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户订单号">
                <span>{{ detailInfo.customerNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="销售员">
                <span>{{ detailInfo.salesUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="销售公司">
                <span>{{ detailInfo.salesCompany }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="交货日期">
                <span>{{ detailInfo.deliveryTime | conversion('yyyy-MM-dd') }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="联系人">
                <span>{{ detailInfo.contactsName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="联系人电话">
                <span>{{ detailInfo.contactsMobile }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="交货地址">
                <span>{{ detailInfo.deliveryAddress }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ detailInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="detailInfo.salesOrderDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              show-summary
              :summary-method="getSummaries"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="客户货号" prop="customerProCode" min-width="120"></el-table-column>
              <el-table-column label="客户货名" prop="customerProName" min-width="120"></el-table-column>
              <el-table-column label="商品编码" prop="productCode" min-width="120"></el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="120"> </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="80">
                <template slot-scope="{row}">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" prop="brand" width="100"> </el-table-column>
              <el-table-column label="规格型号" prop="model" width="100"> </el-table-column>
              <el-table-column label="颜色" prop="colour" width="80"> </el-table-column>
              <el-table-column label="订单数量" prop="quantity" width="100"> </el-table-column>
              <el-table-column label="库存数量" prop="currentInventory" width="100"> </el-table-column>
              <el-table-column label="发货数量" prop="shipQty" width="100"> </el-table-column>
              <el-table-column label="仓库" prop="warehouseName" width="100"> </el-table-column>
              <el-table-column label="库位" prop="locationName" width="100"> </el-table-column>
              <el-table-column label="单位" prop="unit" width="100"> </el-table-column>
              <el-table-column label="单价(￥)" prop="unitPrice" width="100"> </el-table-column>
              <el-table-column label="金额(￥)" prop="amount" width="100"> </el-table-column>
              <el-table-column label="折扣(%)" prop="discount" width="100"> </el-table-column>
              <el-table-column label="折后单价(￥)" prop="discountPrice" width="100"> </el-table-column>
              <el-table-column label="折后金额(￥)" prop="discountAmount" width="100"> </el-table-column>
              <el-table-column label="赠品" prop="presentFlag" width="80">
                <template slot-scope="{row}">
                  <span>{{ row.presentFlag === 1 ? '是' : '否' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
            </el-table>
          </el-collapse-item>

<!--          <el-collapse-item title="状态信息" name="status">-->
<!--            <div style="height: 200px;">-->
<!--              <el-steps direction="vertical">-->
<!--                <el-step title="步骤 1"></el-step>-->
<!--                <el-step title="步骤 2"></el-step>-->
<!--                <el-step title="步骤 3" description="这是一段很长很长很长的描述性文字"></el-step>-->
<!--              </el-steps>-->
<!--            </div>-->
<!--          </el-collapse-item>-->


          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="制单人">
                {{ detailInfo.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
<!--              <el-descriptions-item label="修改人">-->
<!--                {{ detailInfo.updatedUser }}-->
<!--              </el-descriptions-item>-->
<!--              <el-descriptions-item label="修改日期">-->
<!--                {{ detailInfo.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}-->
<!--              </el-descriptions-item>-->
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="附件">
                <FileUpload :is-detail="true" :drag="false" v-model="detailInfo.productAccessoryList"></FileUpload>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <!-- <el-dialog v-dialogDrag width="1200px !important"  title="添加商品" :visible.sync="dialogFormVisible">
      <productInfo></productInfo>
    </el-dialog>

    <el-dialog v-dialogDrag width="1200px !important"  title="添加客户" :visible.sync="customerDialogVisible">
      <customerInfo></customerInfo>
    </el-dialog> -->

  </div>
</template>

<script>
import { getSalesOrderInfo } from "@/api/salesmgt";
import { removeTabs, collapseArea } from "@/assets/js/common";
import FileUpload from "@/components/FileUpload/FileUpload.vue";
export default {
  name: "salesOrderDetail",
  components: {FileUpload},
  data() {
    return {
      id: "",
      detailInfo: {},
      accessoryList: [], // 附件信息
      salesOrderDetailList:[],
      fileList: [], // 图片上传列表
      activeNames: ["base", "product", "status", "more"], // 全部展开
    }
  },
  methods:{
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    async dataList() {
      var _this = this;
      try {
        const res = await getSalesOrderInfo(_this.id);
        if (res.data.code == "100") {
          _this.detailInfo = res.data.data
          if(_this.detailInfo.productAccessoryList) {
            var imgList = _this.detailInfo.productAccessoryList.length;
            if (imgList > 0) {
              _this.accessoryList = _this.detailInfo.productAccessoryList;
            }
          }
          var productList = _this.detailInfo.salesOrderDetailList.length;
          if (productList > 0) {
            _this.salesOrderDetailList = _this.detailInfo.salesOrderDetailList;
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          removeTabs(this.$route);
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        removeTabs(this.$route);
      }
      collapseArea()
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        switch (column.property) {
          case "quantity":
            this.detailInfo.salesOrderDetailList.forEach((item) => {
              if (item.quantity) {
                count += item.quantity;
              }
            });
            sums[index] = count;
            break;
          case "amount":
            this.detailInfo.salesOrderDetailList.forEach((item) => {
              if (item.amount) {
                count += item.amount;
              }
            });
            sums[index] = count;
            break;
          case "discountAmount":
            this.detailInfo.salesOrderDetailList.forEach((item) => {
              if (item.discountAmount) {
                count += item.discountAmount;
              }
            });
            sums[index] = count;
            break;
        }
      })
      return sums;
    },
  },
  mounted() {
    var _this = this;
    _this.id = _this.$route.params.id;
    _this.dataList();
  },
}
</script>
