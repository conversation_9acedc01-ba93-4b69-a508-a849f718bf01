<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :model="formInline" :label-width="$labelFour" class="demo-form-inline">
        <el-form-item label="产品类别" prop="carTrain">
          <!-- <el-input v-model.trim="formInline.carTrain" placeholder="请输入车型"></el-input> -->
          <el-select v-model="formInline.carTrain" placeholder="请选择产品类别" clearable filterable @change="getYear">
            <el-option-group v-for="group in brandTree" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh"
                :value="item.id"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="车型" prop="year">
          <el-select v-model="formInline.year" placeholder="请选择车型" clearable filterable>
            <el-option v-for="item in yearList" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年款" prop="power">
          <el-input v-model.trim="formInline.power" placeholder="请输入年款"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle"
        v-if="hasPerm('menuAsimss2A2B_101') || hasPerm('menuAsimss2A2B_107') || hasPerm('menuAsimss2A2B_108')">
        <el-button type="text" @click="addYear()" icon="el-icon-plus">新增年款</el-button>
        <el-upload class="upload-demo inline-block" ref="melUpload" action="#" :show-file-list="false"
          :limit="1" :file-list="fileList" :before-upload="onBeforeUpload" :http-request="uploadModel"
          accept="xls、xlsx">
          <el-button type="text" v-if="hasPerm('menuAsimss2A2B_107')" icon="bulkImport-icon">批量导入配置</el-button>
        </el-upload>
        <el-button type="text" v-if="hasPerm('menuAsimss2A2B_107') || hasPerm('menuAsimss2A2B_108')"
          icon="el-icon-download" @click="downModelClick()">
          下载配置模板
        </el-button>
      </div>
      <el-table style="width:100%;" ref="table" row-key="id" border highlight-current-row :max-height="maximumHeight"
        :data="resultList" default-expand-all :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :row-style="rowStyle" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="品牌" prop="brandName" min-width="150"></el-table-column>
        <el-table-column label="产品类别" prop="modelName" min-width="110"></el-table-column>
        <el-table-column label="车型" prop="year" min-width="110"></el-table-column>
        <el-table-column label="年款" prop="powerType" min-width="110"></el-table-column>
        <el-table-column label="配置名称" prop="nameCh" min-width="150">
          <template slot-scope="{row}">
            <span v-if="row.modelType === 0"></span>
            <span v-else>{{ row.nameCh }}</span>
          </template>
        </el-table-column>
        <!--     2024-08-21 添加配置简码   -->
        <el-table-column label="简码" prop="shortCode" min-width="150"></el-table-column>
        <!--     2024-09-14 添加配置代码       -->
        <el-table-column label="配置代码" prop="code" min-width="100"></el-table-column>
        <el-table-column label="排序" prop="sort" width="70"></el-table-column>
        <el-table-column label="状态" prop="useFlag" width="70">
          <template slot-scope="{row}">
            <span v-if="row.useFlag" class="successColor">启用</span>
            <span v-else class="errorColor">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="260">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_101') && row.modelType == '0'" size="small"
              @click="handeladd(row)">
              新增配置
            </el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.useFlag == 1" size="small"
              @click="forbidden(row)">
              停用
            </el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.useFlag == 0" size="small"
              @click="editUseFlag(row, 1)">
              启用
            </el-button>
            <!-- 年款的编辑 -->
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.modelType == '0'" size="small"
              @click="editYearHand(row)">
              编辑
            </el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.modelType == '1'" size="small"
              @click="handeledit(row)">
              编辑
            </el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_102')" class="deleteButton" size="small"
              @click="handeldelete(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
      <el-dialog v-dialogDrag :width="dialogStatus == 'config' ? '700px !important' : ''" :title="textMap[dialogStatus]"
        :visible.sync="dialogFormVisible" v-if="isDialog">
        <!-- 查看 -->
        <div v-if="dialogStatus === 'check'" style="text-align: center; ">
          <img :src="urlImg" alt="" style="width:90%;height:70%">
        </div>
        <!-- 详情 -->
        <el-form v-if="dialogStatus === 'detail'" ref='dataForm' :label-width="formLabelWidth" :model="dataForm"
          label-position="center" :validate-on-rule-change="false">
          <el-form-item label="车型" prop="trainId">
            <span>{{ dataForm.trainName }}</span>
          </el-form-item>
          <el-form-item label="年款" prop="year">
            <span>{{ dataForm.year }}</span>
          </el-form-item>
          <el-form-item label="配置名称" prop="nameCh">
            <span>{{ dataForm.nameCh }}</span>
          </el-form-item>

          <el-form-item label="排序" prop="sort">
            <span>{{ dataForm.sort }}</span>
          </el-form-item>
        </el-form>
        <!-- 编辑 -->
        <el-form v-if="dialogStatus === 'edit'" :rules="rules" :label-width="formLabelWidth" ref='dataForm'
          :model="dataForm" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="产品类别" prop="trainId">
            <el-input v-model.trim="dataForm.modelName" disabled></el-input>
          </el-form-item>
          <el-form-item label="车型" prop="year">
            <el-input v-model.trim="dataForm.year" disabled></el-input>
          </el-form-item>
          <el-form-item label="年款" prop="powerType">
            <el-input v-model.trim="dataForm.powerType" disabled></el-input>
          </el-form-item>
          <el-form-item label="配置名称" prop="nameCh">
            <el-input v-model.trim="dataForm.nameCh" placeholder="请输入配置名称" limit="limit" show-word-limit
              maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="简码" prop="shortCode">
            <el-input v-model.trim="dataForm.shortCode" placeholder="请输入简码" limit="limit" show-word-limit
              maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="配置代码" prop="code">
            <el-input v-model.trim="dataForm.code" placeholder="请输入配置代码" limit="limit" show-word-limit
              maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model.trim="dataForm.description" placeholder="请输入描述" limit="limit" show-word-limit
              maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model.trim="dataForm.sort" placeholder="请输入排序" controls-position="right" :min="1"
              :max="9999" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item class="submitArea">
            <el-button type="primary" @click="editClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 新增 -->
        <el-form v-if="dialogStatus === 'add'" :rules="rules" ref='dataForm' :label-width="formLabelWidth"
          :model="dataForm" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="产品类别" prop="trainId">
            <el-input v-model.trim="dataForm.trainName" disabled></el-input>
          </el-form-item>
          <el-form-item label="车型" prop="year">
            <el-input v-model.trim="dataForm.year" disabled></el-input>
          </el-form-item>
          <el-form-item label="年款" prop="year">
            <el-input v-model.trim="dataForm.power" disabled></el-input>
          </el-form-item>
          <el-form-item label="配置名称" prop="nameCh">
            <el-input v-model.trim="dataForm.nameCh" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入配置名称"></el-input>
          </el-form-item>
          <el-form-item label="简码" prop="shortCode">
            <el-input v-model.trim="dataForm.shortCode" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入简码"></el-input>
          </el-form-item>
          <el-form-item label="配置代码" prop="code">
            <el-input v-model.trim="dataForm.code" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入配置代码"></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model.trim="dataForm.description" placeholder="请输入描述" limit="limit" show-word-limit
              maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model.trim="dataForm.sort" placeholder="请输入排序" controls-position="right" :min="1"
              :max="9999" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="状态" prop="useFlag">
            <el-switch v-model="dataForm.whether"></el-switch>
          </el-form-item>
          <el-form-item class="submitArea">
            <el-button type="primary" @click="addClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </el-form-item>
        </el-form>
        <!-- 产品类别配置 -->
        <el-form v-if="dialogStatus === 'config'">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" v-if="hasPerm('menuAsimss2A2B_101')" @click="addConfig">新增
            </el-button>
            <el-button type="text" class="deleteButton" v-if="hasPerm('menuAsimss2A2B_102')" @click="delAllConfig">
              <i class="el-icon-delete"></i>
              删除全部配置
            </el-button>
          </div>
          <el-table style="width:800px" highlight-current-row :data="modelCfgList" border stripe>
            <el-table-column label="配置代码" min-width="160">
              <template slot-scope="{row}">
                <el-input v-model.trim="row.code" oninput="value=value.replace(/^\s+|\s+$/g,'')" limit="limit"
                  show-word-limit maxlength="50" placeholder="请输入车型代码"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="配置名称" min-width="150">
              <template slot-scope="{row}">
                <el-input v-model.trim="row.alias" oninput="value=value.replace(/^\s+|\s+$/g,'')" limit="limit"
                  show-word-limit maxlength="50" :value="row.alias" placeholder="请输入手册编码"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="排序" width="80">
              <template slot-scope="{row}">
                <el-input-number v-model.trim="row.sort" placeholder="请输入排序" controls-position="right" :min="1"
                  :max="9999" :precision="0" :step="1"></el-input-number>
                <!-- <el-input type="number"  :min="1" :max="9999"  @input="e => row.sort=parserNumber(e,1,9999)"  v-model="row.sort" :value="row.sort" placeholder="请输入排序号"></el-input> -->
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="130">
              <template slot-scope="{row}">
                <el-button type="text" size="small" @click="handelSaveConfig(row)">保存</el-button>
                <el-button type="text" size="small" class="deleteButton" v-if="hasPerm('menuAsimss2A2B_102')"
                  @click="handelDelConfig(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-dialog>
      <el-dialog v-dialogDrag width="700px !important" title="新增年款" :visible.sync="dialogYearFormVisible"
        v-if="dialogYearFormVisible" :destroy-on-close="true">
        <el-form :rules="rulesYear" :label-width="formLabelWidth" :model="yearForm" ref="rulesYear">
          <el-form-item label="产品类别" prop="trainName" class="is-required">
            <el-select v-model="carTrainId" placeholder="请选择产品类别" @change="getCarYear">
              <el-option-group v-for="group in brandTree" :key="group.id" :label="group.nameCh">
                <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id">
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="车型" prop="trainId">
            <!-- <select-tree ref="addSelectTree"
              :options="trainList"
              v-model="yearForm.trainId"
              :props="defaultProps"
              :expand_on_click_node="true"
              :check_on_click_node="false"
              @slectNode="slectTreeNode"
              placeholder="请选择车型年款"
            /> -->
            <el-select v-model="yearForm.trainId" placeholder="请选择车型">
              <el-option v-for="item in carYearList" :key="item.id" :label="item.nameCh" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年款" prop="year">
            <el-input v-model.trim="yearForm.year" @change="codeValue" limit="limit" show-word-limit maxlength="50"
              placeholder="请输入年款"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model.trim="yearForm.sort" placeholder="请输入排序" controls-position="right" :min="1"
              :max="9999" :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="状态" prop="useFlag">
            <el-switch v-model="yearForm.whether"></el-switch>
          </el-form-item>
          <el-form-item class="submitArea">
            <el-button type="primary" @click="addYearClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogYearFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 编辑年款 -->
      <el-dialog v-dialogDrag width="700px !important" title="编辑年款" :visible.sync="dialogEditYearFormVisible"
        :destroy-on-close="true">
        <el-form :rules="rulesEditYear" ref="editYear" :label-width="formLabelWidth" :model="editYear">
          <el-form-item label="产品类别" prop="trainName">
            <el-input v-model.trim="editYear.trainName" disabled></el-input>
          </el-form-item>
          <el-form-item label="车型" prop="year">
            <el-input v-model.trim="editYear.year" disabled></el-input>
          </el-form-item>
          <el-form-item label="年款" prop="power">
            <el-input v-model.trim="editYear.power" limit="limit" show-word-limit maxlength="50"
              placeholder="请输入年款"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="editYearClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogEditYearFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, tableHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import SelectTree from '@/components/TreeView/SelectTree.vue';
import {
  modelData,
  modelTrainList,
  modelAdd,
  modelEdit,
  modelDel,
  modelImport,
  modelConfigData,
  configBatchDel,
  modelConfigDel,
  configSave,
  modelTemplate,
  configTemplate,
  modelYearAdd,
  editUseFlag,
  modelYearDel,
  modelEditYear,
} from '@/api/sysmgt.js'

export default {
  name: 'carmgt_model_list',
  components: { Pagination },
  // components: { SelectTree },
  data() {
    return {
      formInline: {
        brand: '',
        carTrain: '',
        code: '',
        nameCh: '',
        year: '',
        power: '',
      },
      dataForm: {
        id: '',
        pid: '',
        trainId: '',
        trainName: '',
        file: '',
        year: '',
        code: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        modelType: '',
        marketTime: '',
        createdTime: '',
        createdUser: '',
        sort: 1,
        image: '',
        whether: true,
        power: '',
        shortCode: '',
        description: '',
      },
      // 默认选中值
      sltTrainId: '',
      trainList: [],
      trainCode: '',
      // 数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },
      imgList: [],
      isFlag: true,
      isDialog: false,
      fileList: [],
      urlImg: '',
      dialogFormVisible: false,
      formLabelWidth: '100px',
      dialogStatus: '',
      textMap: {
        edit: '编辑配置',
        add: '新增配置',
        detail: '详情信息',
        config: '配置',
        check: '图片'
      },
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      modelId: '',
      modelCfgList: [],
      rules: {
        trainId: [{ required: true, message: '产品类别不能为空', trigger: ['blur', 'change'] }],
        year: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
        // code: [{ required: true, message: '编码不能为空', trigger: ['blur', 'change'] }],
        nameCh: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
        powerType: [{ required: true, message: '年款不能为空', trigger: ['blur', 'change'] }],
        shortCode: [{ required: true, message: '简码不能为空', trigger: ['blur', 'change'] }],
      },
      rulesYear: {
        trainId: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
        year: [{ required: true, message: '年款不能为空', trigger: ['blur', 'change'] }],
      },
      rulesEditYear: {
        trainName: [{ required: true, message: '产品类别不能为空', trigger: ['blur', 'change'] }],
        year: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
        power: [{ required: true, message: '年款不能为空', trigger: ['blur', 'change'] }],
      },
      trainName: '',
      dialogYearFormVisible: false,
      yearForm: {
        trainId: '',
        year: '',
        code: '',
        nameCh: '',
        sort: 1,
        whether: true,
        trainName: "",
      },
      // 品牌-车型-年款树
      brandTree: [],
      yearList: [],
      maximumHeight: 0,

      dialogEditYearFormVisible: false,
      editYear: {
        trainName: '',
        id: '',
        year: '',
        power: '',
        trainId: '',
      },

      carTrainId: "",
      carYearList: "",
    }
  },
  methods: {
    // 表格样式
    rowStyle({ row, rowIndex }) {
      let styleJson = {
        "background": "var(--striped-color)",
      }
      if (row.children == null) {
        return styleJson;
      } else {
        return {};
      }
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    getTrainList() {
      modelTrainList().then(res => {
        this.trainList = res.data.data
        this.brandTree = []
        if (this.trainList && this.trainList.length > 0) {
          for (let i = 0; i < this.trainList.length; i++) {
            const item = this.trainList[i];
            for (let j = 0; j < item.children.length; j++) {
              this.brandTree.push(item.children[j]);
            }
          }
        }
      })
    },
    getYear() {
      this.yearList = [];
      this.formInline.year = '';
      let trainId = this.formInline.carTrain;
      for (let i = 0; i < this.brandTree.length; i++) {
        const brand = this.brandTree[i];
        for (let j = 0; j < brand.children.length; j++) {
          const itm = brand.children[j];
          if (itm.id === trainId) {
            this.yearList = itm.children
            break;
          }
        }
        if (this.yearList.length > 0) {
          break;
        }
      }
    },

    getCarYear() {
      this.yearForm.trainId = "";
      this.carYearList = [];
      for (let i = 0; i < this.brandTree.length; i++) {
        const item = this.brandTree[i];
        for (let j = 0; j < item.children.length; j++) {
          if (this.carTrainId === item.children[j].id) {
            this.carYearList = item.children[j].children;
            break;
          }
        }
        if (this.carYearList.length > 0) {
          break;
        }
      }
    },

    // 数据
    dataList() {
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('modeId', this.formInline.carTrain)
      params.append('trainId', this.formInline.year)
      params.append('nameCh', this.formInline.power)
      modelData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
          if (!this.resultList) {
            this.resultList = []
          }
        } else {
          this.$DonMessage.error(res.data.msg);
        }
        this.tableHeightArea()
      })
    },
    slectTreeNode(v) {
      this.trainCode = '';
      this.trainName = '';
      this.yearForm.code = '';
      this.yearForm.nameCh = '';
      this.yearForm.whether = true;
      for (let i = 0; i < this.trainList.length; i++) {
        for (let j = 0; j < this.trainList[i].children.length; j++) {
          for (let k = 0; k < this.trainList[i].children[j].children.length; k++) {
            for (let l = 0; l < this.trainList[i].children[j].children[k].children.length; l++) {
              if (this.trainList[i].children[j].children[k].children[l].id == v) {
                this.trainCode = this.trainList[i].children[j].children[k].children[l].code;
                this.trainName = this.trainList[i].children[j].children[k].children[l].nameCh;
                this.yearForm.trainName = this.trainList[i].children[j].children[k].nameCh;
              }
            }
          }
        }
      }
      this.codeValue();
    },
    handlesuccess(file, fileList) {
      this.dataForm.image = file.data.fileUrl
      this.imgList = []
      if (this.dataForm.image != null && this.dataForm.image.length > 0) {
        var img = { url: sysServerUrl + 'sys/upload/display?filePath=' + this.dataForm.image }
        this.imgList.push(img)
      }
      this.isFlag = true;
    },
    beforeAvatarUpload(file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const extension = fileName === 'png'
      const extension2 = fileName === 'jpg'
      const extension3 = fileName === 'jpeg'
      const extension4 = fileName === 'gif'
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!extension && !extension2 && !extension3 && !extension4) {
        this.$DonMessage.warning('上传模板只能是 png、jpg、jpeg、gif格式!')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        this.$DonMessage.warning('上传模板大小不能超过 10MB!')
        this.isFlag = false;
        return false;
      }
      // return isLt2M
    },
    handleRemove(file, fileList) {
      if (fileList.length == "0") {
        this.imgList = []
        this.dataForm.image = ''
        this.isFlag = true;
      }
    },
    handleExceed(files, fileList) {
      this.$DonMessage.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },

    // 添加年款组
    addReset() {
      this.yearForm = {
        trainId: '',
        year: '',
        code: '',
        nameCh: '',
        sort: 1,
        whether: true,
        trainName: "",
      }
      this.$nextTick(() => {
        this.$refs.rulesYear.clearValidate();
      });
    },
    addYear() {
      this.dialogYearFormVisible = true;
      this.addReset();
      this.trainCode = '';
      this.trainName = '';
      this.carTrainId = "";
      this.carYearList = [];
    },
    // 提交 添加年款组
    addYearClick() {
      this.$refs['rulesYear'].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('trainId', this.yearForm.trainId)
          params.append('nameCh', this.yearForm.year)
          params.append('sort', this.yearForm.sort)
          params.append('modelType', 0)
          params.append('pid', "0")
          params.append('useFlag', this.yearForm.whether)
          modelAdd(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogYearFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          }).catch(e => {
            this.$DonMessage.error(this.$t('errorTip.systemTip'));
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit();
      }
    },
    onSubmit() {
      this.currentPage = 1;
      this.dataList();
    },
    resetTemp() {
      let trainId = this.dataForm.trainId
      let trainName = this.dataForm.trainName
      let year = this.dataForm.year
      this.dataForm = {
        id: '',
        trainId: trainId,
        trainName: trainName,
        file: '',
        year: year,
        code: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        modelType: '',
        marketTime: '',
        createdTime: '',
        createdUser: '',
        sort: 1,
        image: '',
        whether: true,
      }
      this.$nextTick(function () {
        this.$refs.dataForm.clearValidate();
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      var _this = this
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 50
      if (!docExt && !docxExt) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'xls, xlsx' }))
        return false;
      }
      if (!isLimit) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '50MB' }))
        return false;
      }
      return true;
    },
    // 批量上传车型
    uploadModel(param) {
      var _this = this
      var formData = new FormData();
      formData.append('file', param.file);
      modelImport(formData).then(res => {
        _this.fileList = []
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.importTip'))
          this.dataList()
        } else {
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
        }
      }).catch(function (error) {
        _this.fileList = []
        _this.$DonMessage.error(_this.$t('errorTip.systemTip'));
      })
    },
    // 批量上传车型配置
    uploadConfig(param) {
      var _this = this
      var formData = new FormData();
      formData.append('file', param.file);
      _this.$axios({
        method: 'post',
        url: sysServerUrl + 'sys/car/model/batchImportCarCfg',
        data: formData
      }).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.importTip'))
          this.dataList()
        } else {
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
        }
        _this.fileList = []
      }).catch(function (error) {
        _this.fileList = []
        _this.$DonMessage.error(_this.$t('errorTip.systemTip'));
      })
    },
    // 下载车型模板
    downModelClick() {
      var params = '';
      modelTemplate(params).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        var name = "配置导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })

    },
    // 下载配置模板
    downConfigClick() {
      var params = '';
      configTemplate(params).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        var name = "车型配置导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 新增
    handeladd(row) {
      var _this = this
      _this.dialogFormVisible = true
      _this.dialogStatus = 'add'
      _this.resetTemp()
      _this.isDialog = true
      _this.imgList = []
      _this.dataForm.pid = row.id
      _this.dataForm.year = row.year
      _this.dataForm.power = row.nameCh
      _this.dataForm.trainId = row.yearId
      _this.dataForm.trainName = row.modelName
      _this.dataForm.code = row.code
    },
    addClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('modelType', 1)
          params.append('nameCh', this.dataForm.nameCh)
          params.append('useFlag', this.dataForm.whether)
          params.append('sort', this.dataForm.sort)
          params.append('trainId', this.dataForm.trainId)
          params.append('pid', this.dataForm.pid)
          params.append('shortCode', this.dataForm.shortCode)
          if (!this.dataForm.code) {
            params.append('code', "")
          } else {
            params.append('code', this.dataForm.code)
          }
          if (!this.dataForm.description) {
            params.append('description', "")
          } else {
            params.append('description', this.dataForm.description)
          }
          // params.append('description', this.dataForm.description)
          modelAdd(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 查看
    check(row) {
      this.isDialog = true
      this.dialogStatus = 'check'
      this.dialogFormVisible = true
      this.urlImg = sysServerUrl + 'sys/upload/display?filePath=' + row.image
    },
    // 详情
    handelDetile(row) {
      this.dialogStatus = 'detail'
      this.dialogFormVisible = true
      this.isDialog = true
      this.resetTemp()
      this.dataForm = Object.assign({}, row)
      this.imgList = []
      if (row.image !== '') {
        var img = { url: sysServerUrl + 'sys/upload/display?filePath=' + row.image }
        this.imgList.push(img)
      }
    },

    // 编辑年款
    yearReset() {
      this.editYear = {
        trainName: '',
        id: '',
        year: '',
        power: '',
        trainId: '',
      }
      this.$nextTick(() => {
        this.$refs.editYear.clearValidate();
      });
    },
    editYearHand(row) {
      this.dialogEditYearFormVisible = true;
      this.yearReset();
      this.editYear.trainName = row.modelName;
      this.editYear.id = row.id;
      this.editYear.trainId = row.yearId;
      this.editYear.year = row.year;
      this.editYear.power = row.nameCh;
    },
    editYearClick() {
      this.$refs['editYear'].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('id', this.editYear.id)
          params.append('trainId', this.editYear.trainId)
          params.append('nameCh', this.editYear.power)
          modelEdit(params).then(res => {
            if (res.data.code == 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogEditYearFormVisible = false;
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 编辑
    handeledit(row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.resetTemp()
      this.isDialog = true
      this.dataForm = Object.assign({}, row)
      this.dataForm.sort = row.sort ? row.sort : 1;
      // this.dataForm.whether = row.useFlag == 1
      this.imgList = []
      if (row.image !== null && row.image !== '' && row.image !== 'null') {
        var img = { url: sysServerUrl + 'sys/upload/display?filePath=' + row.image }
        this.imgList.push(img)
      }
      this.trainCode = row.trainCode
    },
    editClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('id', this.dataForm.id)
          if (this.dataForm.nameCh == 'null') {
            this.dataForm.nameCh = ''
          }
          params.append('nameCh', this.dataForm.nameCh)
          if (this.dataForm.nameEn == 'null') {
            this.dataForm.nameEn = ''
          }
          params.append('nameEn', this.dataForm.nameEn)

          if (!this.dataForm.alias || this.dataForm.alias == 'null' || this.dataForm.alias == 'undefined') {
            this.dataForm.alias = ''
          }
          params.append('alias', this.dataForm.alias)
          if (this.dataForm.image == 'null') {
            this.dataForm.image = ''
          }
          params.append('image', this.dataForm.image)
          if (!this.dataForm.sort || this.dataForm.sort == 'null' || this.dataForm.sort == 'undefined') {
            this.dataForm.sort = 1
          }
          params.append('sort', this.dataForm.sort)
          params.append('modelType', this.dataForm.modelType)
          params.append('shortCode', this.dataForm.shortCode)
          if (!this.dataForm.code) {
            params.append('code', "")
          } else {
            params.append('code', this.dataForm.code)
          }

          if (!this.dataForm.description) {
            params.append('description', "")
          } else {
            params.append('description', this.dataForm.description)
          }

          modelEdit(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 删除
    handeldelete(row) {
      this.$confirm('确定删除【' + row.nameCh + '】的配置信息?', '删除配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        modelDel(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1 > 0 ? this.currentPage - 1 : this.currentPage
            }
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },

    forbidden(row) {
      let _this = this
      this.$confirm('确定要将配置【' + (row.modelType === 0 ? row.year : row.nameCh) + '】停用吗?', '停用配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.editUseFlag(row, 0)
      })
    },

    editUseFlag(row, type) {
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('nameCh', row.nameCh)
      params.append('useFlag', type)
      modelEdit(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.operateTip'))
          this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },


    // 车型配置
    config(row) {
      this.isDialog = true
      this.textMap.config = ""
      this.modelId = row.id
      this.dialogStatus = 'config'
      this.textMap.config = '[' + row.nameCh + '] ' + " 车型配置"
      this.dialogFormVisible = true
      this.getCfgList()
    },
    getCfgList() {
      var params = '?id=' + this.modelId
      modelConfigData(params).then(res => {
        this.modelCfgList = res.data.data
      })
    },
    addConfig() {
      var sortVal = ''
      if (this.modelCfgList.length !== 0) {
        sortVal = Number(this.modelCfgList[this.modelCfgList.length - 1].sort) + Number(1)
        if (sortVal > 9999) {
          sortVal = 9999
        }
      } else {
        sortVal = 1
      }

      var params = '?id=' + this.modelId
      modelConfigData(params).then(res => {
        this.modelCfgList = res.data.data
        this.modelCfgList.push({
          code: '',
          alias: '',
          sort: sortVal
        })
      })


    },
    delAllConfig() {
      this.$confirm('确认删除全部配置代码？', '删除全部配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = '?modelId=' + this.modelId
        configBatchDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.getCfgList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    // 保存当前信息
    handelSaveConfig(row) {
      let curCode = row.code
      let curAlias = row.alias
      if (curCode == null || curCode.length == 0) {
        this.$DonMessage.warning('请输入配置代码')
        return;
      }
      if (curAlias == null || curAlias.length == 0) {
        this.$DonMessage.warning('请输入手册编码')
        return;
      }
      var params = new URLSearchParams()
      if (row.id === undefined) {
        row.id = ''
      }
      if (row.pid === undefined) {
        row.pid = this.modelId
      }
      params.append('id', row.id)
      params.append('pid', row.pid)
      params.append('code', row.code)
      params.append('modelType', '2')
      params.append('alias', row.alias)
      params.append('sort', row.sort)
      configSave(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.saveTip'))
          this.getCfgList()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 删除当前行
    handelDelConfig(row) {
      this.$confirm('确定删除当前行配置代码?', '删除配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = '?id=' + row.id
        modelConfigDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.getCfgList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$tefs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    // // 车型编码自动赋值
    codeValue() {
      if (this.trainCode != '' && this.yearForm.year != '') {
        this.yearForm.code = this.trainCode + "-" + this.yearForm.year
        this.yearForm.nameCh = this.trainName + " " + this.yearForm.year
      }
    },
    isInTrainList(value) {
      for (let i = 0; i < this.trainList.length; i++) {
        for (let j = 0; j < this.trainList[i].children.length; j++) {
          for (let k = 0; k < this.trainList[i].children[j].children.length; k++) {
            if (this.trainList[i].children[j].children[k].nameCh == value) {
              return true;
            }
          }
        }
      }
      return false;
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.dataList()
    this.getTrainList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
