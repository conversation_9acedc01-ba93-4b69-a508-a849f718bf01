<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="8" class="leftData">
          <div>
            <div class="topButton">
              <el-button type="text" icon="el-icon-plus" @click="addRoot">新增根节点</el-button>
              <el-button type="text" v-if="typeFlag && addTitle !== '新增主机厂'" icon="el-icon-plus"
                @click="addSub">新增子节点</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss2A1B_107')" size="min" icon="bulkImport-icon"
                @click="uploadTrain">批量上传</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss2A1B_107') || hasPerm('menuAsimss2A1B_108')"
                icon="el-icon-download" @click="downModelClick()">下载模板</el-button>
              <el-button type="text" v-show="hasPerm('menuAsimss2A1B_102')" icon="el-icon-delete"
                @click="del">删除</el-button>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :default-expanded-keys="nodeKeyList" :data="listdata"
                  :props="defaultProps" @node-click="handleNodeClick"></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="16" class="fromRight">
          <div class="formTitle" v-html="editTitle"></div>
          <el-form ref="form" :model="form" :rules="rules" :label-width="formLabelWidth"
            :validate-on-rule-change="false">
            <el-form-item label="节点类型" prop="trainCode">
              <el-select v-model="form.trainCode" :disabled="true">
                <el-option v-for="(item, index) in typeList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="名称" prop="nameCh">
              <el-input v-model="form.nameCh" placeholder="请输入名称" limit="limit" show-word-limit
                maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="编码" prop="code">
              <el-input v-model.trim="form.code" placeholder="请输入编码" limit="limit" show-word-limit
                maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="内部代号" prop="alias">
              <el-input v-model.trim="form.alias" placeholder="请输入内部代号" limit="limit" show-word-limit
                maxlength="50"></el-input>
            </el-form-item>

            <!-- 2020-08-02 添加适用国家carTypeList -->
            <el-form-item v-if="form.trainCode == nodeEnergy" label="车型类型" prop="carType">
              <el-select v-model="form.carType" filterable>
                <el-option v-for="(item, index) of carTypeList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="form.trainCode == nodeCountry" label="适用国家" prop="country">
              <el-button type="primary" @click="suitTarget()">{{ targetName }}</el-button>
            </el-form-item>
            <el-form-item label="图片" prop="avatar">
              <el-upload class="upload-demo" action="#" :on-remove="handleRemove" :before-remove="beforeRemove"
                :before-upload="beforeAvatarUpload" :on-exceed="handleExceed" :limit="1" :file-list="fileList"
                :http-request="uploadCar" accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg" list-type="picture">
                <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
              </el-upload>
            </el-form-item>
            <el-form-item label="说明" prop="remark">
              <el-input limit="limit" show-word-limit maxlength="50" placeholder="请输入说明"
                v-model.trim="form.remark"></el-input>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model.trim="form.sort" placeholder="请输入排序" controls-position="right" :min="1"
                :max="9999" :precision="0" :step="1"></el-input-number>
            </el-form-item>
            <el-form-item label="状态" prop="useFlag" id="state_whether">
              <el-switch v-model="form.useFlag"></el-switch>
            </el-form-item>
            <el-form-item class="butArea">
              <el-button v-if="formShow" v-show="butType === ''" type="primary" @click="preserve()">{{
                $t('button.submit')
              }}</el-button>
              <el-button v-show="butType === 'addNodeBut'" type="primary" @click="preserve()">{{ $t('button.submit')
              }}</el-button>
              <!-- <el-button v-show="butType==='addNodeBut'" plain @click="resetForm()">{{ $t('button.reset') }}</el-button> -->
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-dialog v-dialogDrag title="批量上传" :visible.sync="dialogFileVisible" :close-on-click-modal="false">
        <!-- 选择主机厂 -->
        <el-form :label-width="formLabelWidth" ref="uploadForm" :model="uploadForm" :rules="uploadRules"
          :validate-on-rule-change="false">
          <el-form-item label="主机厂" prop="firmId">
            <el-select v-model="uploadForm.firmId" clearable filterable>
              <el-option v-for="(item, index) of listdata" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上传文件" prop="excelList">
            <el-upload
              v-if="hasPerm('menuAsimss2A1B_107')"
              class="upload-demo"
              ref="elUpload"
              action="#"
              drag
              :limit="1"
              :on-remove="handleUploadRemove"
              :before-remove="beforeUploadRemove"
              :on-exceed="handleExceed"
              :file-list="uploadForm.excelList"
              :before-upload="onBeforeUpload"
              :http-request="uploadFile"
              accept=".xls, .xlsx"
            >
              <div class="el-upload_text">
                <svg-icon icon-class="fileUpload"></svg-icon>
                <p>{{ $t("button.uploadTip") }}</p>
              </div>
              <div slot="tip" class="el-upload__tip">
                {{ $t("identifying.uploadTip", {format: 'xls, xlsx', count: 1, size: '100MB'}) }}
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item class="submitArea">
            <el-button v-if="hasPerm('menuAsimss2A1B_107')" type="primary" @click="submitUpload">{{ $t('button.submit')
              }}</el-button>
            <el-button plain @click="dialogFileVisible = false">{{ $t('button.cancel') }}</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <el-dialog v-dialogDrag lock-scroll width="1100px !important" class="assignCountryArea" title="适用国家"
        :visible.sync="dialogCountryVisible" v-if="dialogCountryVisible" :close-on-click-modal="false">
        <div class="secondFloat">
          <el-form :inline="true" ref="countryData" :label-width="$labelTwo" :model="countryInfo" class="demo-form-inline">
            <el-form-item label="大洲" prop="continent">
              <el-select v-model="countryInfo.continent" filterable clearable>
                <el-option v-for="(item, index) of continentList" :key="index" :label="item.continentName"
                  :value="item.continentCode"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="国家" prop="country">
              <el-input v-model.trim="countryInfo.country" placeholder="请输入国家"></el-input>
            </el-form-item>
            <el-form-item class="submitArea">
              <el-button type="primary" @click="searchApply">{{ $t('button.search') }}</el-button>
              <el-button plain @click="resetApply()">{{ $t('button.reset') }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- 适用车型 -->
        <el-row>
          <el-col :span="17" class="nationalInfo">
            <el-table :row-key="selRowKey" ref="applytable" style="width:100%;" max-height="550px" border stripe
              highlight-current-row :data="applyCountrylLists" @header-dragend="changeColWidth"
              @selection-change="handleSelectionApply">
              <el-table-column type="selection" width="40" fixed="left" align="center"
                reserve-selection></el-table-column>
              <el-table-column label="序号" type="index" width="60"></el-table-column>
              <el-table-column label="大洲" prop="continentName" min-width="100"></el-table-column>
              <el-table-column label="国家" prop="countryName" min-width="100"></el-table-column>
              <el-table-column label="代码" prop="countryCode" min-width="100"></el-table-column>
            </el-table>
            <pagination v-show="totalApply > 0" :total="totalApply" :page.sync="currentPageApply"
              :limit.sync="pagesizeApply" @pagination="selectCountry" />
          </el-col>
          <el-col :span="7" class="nationalSelect">
            <el-table style="width:100%" :height="accreditTableHeight" border stripe highlight-current-row
              :data="determineModelList" @cell-mouse-enter="cellMouseEnter" :header-cell-style="{}"
              @cell-mouse-leave="cellLeaveEnter">
              <el-table-column prop="title">
                <template slot="header">
                  <div class="authorityTitle">
                    <div>
                      <span>已选(<b> {{ selectNum }} </b>)</span>
                    </div>
                    <div>
                      <span class="warrantAction" @click="authorizeAll">选择全部</span>
                      <span class="clearAction" @click="emptyCountry">清空</span>
                    </div>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div :class="'nationalList ' + '_' + scope.row.countryId">
                    <span>{{ scope.row.countryName }}</span>
                    <i class="el-icon-close" @click="deleteEmpower(scope.row)"></i>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <div class="submitArea">
          <el-button type="primary" @click="selectapply">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogCountryVisible = false">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, contentSize, uploadIcon } from '@/assets/js/common.js'
import {
  importAttach,
  trainTypeDate,
  getCountryAll,
  importTrain,
  trainTemplate,
  trainCarTypeList,
  findCarAll, findCarType, addCarApi, editCarApi, delCarApi, findCarImgList, carBatchImport
} from '@/api/sysmgt.js'
import Pagination from '@/components/Pagination'
export default {
  name: 'carmgt_train_list',
  components: { Pagination },
  data() {
    return {
      currentNodeIsRootNode: true,
      // 和国家关联的节点
      nodeCountry: "vehicle_coding",
      // 和 能源关联的节点
      nodeEnergy: "vehicle_coding",

      defaultProps: {
        children: 'children',
        label: 'name'
      },
      nodeKeyList: [],
      editTitle: '当前信息',
      sltNodeId: '',
      sltNodeType: '',
      sltNodeName: '',
      form: {
        id: '',
        path: '',
        code: '',
        trainCode: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        image: '',
        imgName: '',
        remark: '',
        sort: 1,
        country: [],
        carType: 1,
        useFlag: true,
        whether: true,
        avatar: '',
        imageList: []
      },
      carTypeList: [],
      butType: '',
      listdata: [],
      userCountryList: [],
      trainTypeList: [],
      trainTypeMap: {},
      formLabelWidth: '100px',
      fileList: [],
      uploadForm: {
        firmId: '',
        excelList: [],
      },
      uploadRules: {
        firmId: [{ required: true, message: '主机厂不能为空', trigger: ['blur', 'change'] }],
        excelList: [{ required: true, message: '上传文件不能为空', trigger: ['blur', 'change'] }]
      },
      dialogFileVisible: false,
      formShow: true,
      isFlag: true,
      rules: {
        // code: [{ required: true, message: '编码不能为空', trigger: ['blur', 'change'] }],
        trainCode: [{ required: true, message: '节点类型不能为空', trigger: ['blur', 'change'] }],
        nameCh: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }]
      },

      // 适用国家
      dialogCountryVisible: false,
      targetName: '',
      totalApply: 0,
      currentPageApply: 1,
      pagesizeApply: 10,
      // 选中的对象
      determineModelList: [],
      // 选中的数量
      selectNum: 0,
      // 列表中的
      applyCountrylLists: [],
      // 所有的
      applyCountrylList: [],

      accreditTableHeight: 0,
      continentList: [],

      countryInfo: {
        country: '',
        continent: '',
      },

      // 类型索引
      typeList: [],
      typeIndex: 0,
      addTitle: '',
      addTitlePrefix: '新增',
      typeFlag: true,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.applytable.doLayout();
      })
    },
    // 查询所有车系
    dataList() {
      findCarAll().then(res => {
        this.listdata = res.data.data
        this.handleNodeClick(this.listdata[0])
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.form.id);
        });
      })
    },
    getTrainTypeList() {
      this.trainTypeList = []
      this.trainTypeMap = new Map();
      trainTypeDate().then(res => {
        if (res.data.code === 100) {
          this.trainTypeList = res.data.data;
          for (let i = 0; i < this.trainTypeList.length - 1; i++) {
            this.trainTypeMap.set(this.trainTypeList[i].code, this.trainTypeList[i + 1].code)
          }
        }
      }).catch(e => {
        this.trainTypeMap = new Map();
        this.trainTypeList = []
      })
    },
    getCarTypeList() {
      trainCarTypeList().then(res => {
        this.carTypeList = res.data.data
        // console.log("---", this.carTypeList);
      })
    },
    // 获取国家
    getUserCountryList() {
      getCountryAll().then(res => {
        this.applyCountrylList = res.data.data
      })
    },
    resetTemp() {
      let code = this.form.trainCode
      let pid = this.form.pid
      this.form = {
        id: '',
        pid: pid,
        code: '',
        trainCode: code,
        nameCh: '',
        nameEn: '',
        alias: '',
        image: '',
        imgName: '',
        remark: '',
        sort: 1,
        country: [],
        carType: 1,
        useFlag: 1,
        whether: true,
        imageList: []
      },
        this.$nextTick(function () {
          this.$refs.form.clearValidate();
        })
    },
    addRoot() {
      this.resetTemp();
      this.form.type = 'firm'
      this.form.path = ''
      this.butType = 'addNodeBut'
      this.editTitle = '新增主机厂'
      this.fileList = []
      this.form.trainCode = 'firm'
      this.sltNodeId = ''
      this.sltNodeType = ''
      this.sltNodeName = ''
    },
    // 新增子节点
    addSub() {
      this.form.typeName = this.typeList[this.typeIndex].name
      this.resetTemp()
      this.butType = 'addNodeBut'
      this.editTitle = this.addTitle
      this.fileList = []
      this.form.trainCode = this.typeList[this.typeIndex].code
      this.sltNodeId = ''
      this.sltNodeType = ''
      this.sltNodeName = ''
    },
    del() {
      var _this = this
      if (this.sltNodeId == '') {
        this.$DonMessage.warning('请先选中要删除的节点')
        return false
      } else {
        _this.$confirm('确定删除【' + this.sltNodeName + '】的目录节点?', '删除目录节点', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          //删除节点
          _this.deleteNode(this.sltNodeId)
        })
      }
    },
    // 显示详情，点击获取当前车系信息
    handleNodeClick(data) {
      console.log(data);
      this.resetTemp()
      this.fileList = []
      this.butType = ''
      this.editTitle = '当前信息'
      this.formShow = true
      this.sltNodeId = data.id
      this.sltNodeType = data.type
      this.sltNodeName = data.name
      this.form.id = data.id
      this.form.path = data.path
      this.form.code = data.code
      this.form.trainCode = data.type
      this.form.nameCh = data.name
      this.form.alias = data.alias
      this.form.image = data.image
      this.form.remark = data.remark

      this.form.whether = data.useFlag
      this.form.sort = data.sort
      this.form.useFlag = data.useFlag
      //从后端获取图片集合数据
      // findCarImgList(data.id).then(res =>{
      //   if(res.data.code === '100' && res.data.data.length > 0){
      //     this.form.imageList = res.data.data.map(item => {
      //       item = sysServerUrl + 'sys/upload/display?filePath=' + item
      //       return item
      //     })
      //   }
      // })
      this.form.avatar = data.avatar
      if (this.form.avatar) {
        var clickName = { url: sysServerUrl + 'sys/upload/display?filePath=' + this.form.avatar }
        this.fileList.push(clickName)
      }
      //     this.form.country = []
      //     let clist = res.data.data.countryList
      //     if (clist && clist.length > 0) {
      //       for (let i = 0; i < clist.length; i++) {
      //         this.form.country.push(clist[i].code) ;
      //       }
      //     }
      //
      //     if(this.form.country.length > 0){
      //       this.targetName = "已选择";
      //     }else{
      //       this.targetName = "未选择";
      //     }
      //   }
      // })
      // 修改 新增xx
      setTimeout(() => {
        const len = this.typeList.length
        for (let i = 0; i < len; i++) {
          if (this.form.trainCode === this.typeList[i].code) {
            this.typeIndex = i + 1;
            this.typeFlag = this.typeIndex < len
            break
          }
        }
        if (this.typeFlag) {
          this.addTitle = this.addTitlePrefix + this.typeList[this.typeIndex].name
        }
      }, 10)
    },

    beforeAvatarUpload(file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const extension = fileName === 'png'
      const extension2 = fileName === 'jpg'
      const extension3 = fileName === 'jpeg'
      const extension4 = fileName === 'gif'
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!extension && !extension2 && !extension3 && !extension4) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'png, jpg, jpeg, gif' }))
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '10MB' }))
        this.isFlag = false;
        return false;
      }
      // return isLt2M
    },

    uploadCar(param) {
      var _this = this
      var formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', 'carTrain');
      importAttach(formData).then(res => {
        if (res.data.code === 100) {
          _this.form.avatar = res.data.data.fileUrl
          _this.isFlag = true;
        } else {
          _this.$DonMessage.error(res.data.msg);
        }
      })
    },

    handleRemove(file, fileList) {
      this.fileList = []
      this.form.avatar = ''
      this.isFlag = true;
    },
    handleExceed(files, fileList) {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 1}))
      return
    },
    beforeRemove(file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    preserve() {
      var _this = this
      _this.nodeKeyList = []
      _this.$refs['form'].validate((valid) => {
        if (valid) {
          let params = {
            'path': _this.form.path,
            'code': _this.form.code,
            'name': _this.form.nameCh.trim(),
            'type': _this.form.trainCode,
            'remark': _this.form.remark,
            'sort': _this.form.sort,
            'avatar': _this.form.avatar,
            'alias': _this.form.alias,
            'useFlag': _this.form.useFlag
          }
          if (_this.form.id != null && _this.form.id != '' && _this.form.id != 'null') {
            _this.updateNode(_this.form.id, params)
            _this.nodeKeyList.push(_this.form.id)
          } else {
            _this.addNode(params)
            _this.nodeKeyList.push(_this.form.pid)
          }
        } else {
          _this.$DonMessage.warning(_this.$t('tips.infoTip'));
        }
      })
    },
    // 新增节点
    addNode(params) {
      var _this = this
      addCarApi(params).then(res => {
        if (res.data.code == 100) {
          _this.$DonMessage.success(_this.$t('successTip.submitTip'))
          _this.dataList()
          _this.butType = ''
          _this.editTitle = '当前信息'
        } else {
          _this.$DonMessage.error(res.data.msg)
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    // 修改
    updateNode(id, params) {
      var _this = this
      editCarApi(id, params).then(res => {
        if (res.data.code == 100) {
          _this.$DonMessage.success(_this.$t('successTip.submitTip'))
          _this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    // 删除节点
    deleteNode(nodeId) {
      var _this = this
      delCarApi(nodeId).then(res => {
        if (res.data.code == 100) {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          _this.dataList()
          this.nodeKeyList.push(_this.form.pid)
          _this.resetTemp()
          _this.form.trainCode = '';
          _this.form.carType = '';
        } else {
          this.$DonMessage.error('删除失败：' + res.data.msg)
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    // 重置
    resetForm() {
      this.resetTemp()
    },


    // ============= 批量上传
    uploadTrain() {
      this.uploadForm = {
        firmId: '',
        excelList: [],
      }
      this.dialogFileVisible = true;
      this.$nextTick(() => {
        this.$refs.uploadForm.clearValidate();
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      if (this.uploadForm.firmId == null || this.uploadForm.firmId.length <= 0) {
        this.$DonMessage.warning("请选择主机厂")
        this.isFlag = false;
        return false
      }
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 100
      if (!docxExt && !docExt) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'xls, xlsx' }) );
        this.isFlag = false;
        return false;
      }
      if (!isLimit) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }));
        this.isFlag = false;
        return false;
      }
      uploadIcon();
      return true;
    },
    // 批量上传
    uploadFile(param) {
      this.uploadForm.excelList.push(param.file);
    },
    handleUploadRemove() {
      this.uploadForm.excelList = []
      this.isFlag = true;
    },
    beforeUploadRemove() {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    submitUpload() {
      var _this = this;
      _this.$refs['uploadForm'].validate((valid) => {
        if (valid) {
          var formData = new FormData();
          formData.append('file', _this.uploadForm.excelList[0]);
          formData.append('firmId', _this.uploadForm.firmId);
          carBatchImport(formData).then(res => {
            if (res.data.code === 100) {
              _this.$DonMessage.success(this.$t('successTip.uploadTip'))
              _this.dataList()
              _this.$refs.elUpload.submit();
              _this.dialogFileVisible = false;
            } else {
             _this.$confirm(res.data.msg, '导入失败', { type: 'warning' });
            }
          }).catch(function (error) {
            _this.$confirm('系统出现异常，导入失败', '信息提示', { type: 'warning' });
            _this.uploadForm.excelList = []
          })          
        } else {
          _this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 下载车型模板
    downModelClick() {
      trainTemplate().then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        var name = "车型导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })

    },


    selRowKey(row) {
      return row.countryCode
    },
    // 适用对象
    suitTarget() {
      this.dialogCountryVisible = true
      let countrys = this.form.country
      let _this = this
      let list = []
      setTimeout(() => {
        _this.applyCountrylList.forEach(itm => {
          if (countrys.includes(itm.countryCode)) {
            list.push(itm)
            _this.$refs.applytable.toggleRowSelection(itm, true)
          }
        })
        this.handleSelectionApply(list)
        this.resetApply();
      })

    },
    handleSelectionApply(val) {
      // console.log("选中的车型：", val);
      this.determineModelList = val
      this.selectNum = this.determineModelList.length
    },
    // 获取全部
    authorizeAll() {
      let list = []
      let _this = this
      _this.emptyCountry()
      _this.applyCountrylList.forEach(row => {
        list.push(row)
        _this.$refs.applytable.toggleRowSelection(row, true)
      })
      this.handleSelectionApply(list)
    },
    // 清空
    emptyCountry() {
      this.handleSelectionApply([])
      this.$refs.applytable.clearSelection();
    },
    // 删除授权
    deleteEmpower(row) {
      let _this = this
      _this.determineModelList.forEach(itm => {
        if (itm.countryCode === row.countryCode) {
          _this.$refs.applytable.toggleRowSelection(row, false)
        }
      })
    },

    // 搜索
    searchApply() {
      this.currentPageApply = 1
      this.selectCountry();
    },
    // 重置
    resetApply() {
      this.countryInfo.country = '';
      this.countryInfo.continent = '';
      this.currentPageApply = 1;
      this.pagesizeApply = 10;
      this.selectCountry();
    },

    // 获取数据
    selectCountry() {
      this.applyCountrylLists = []
      let country = this.countryInfo.country
      let continent = this.countryInfo.continent
      let list = []
      if (!country && !continent) {
        list = this.applyCountrylList
      } else {
        for (let i = 0; i < this.applyCountrylList.length; i++) {
          let row = this.applyCountrylList[i]
          if (continent && continent.length > 0) {
            if (country && country.length > 0) {
              if (continent == row.continentCode && row.countryName.indexOf(country) !== -1) {
                list.push(row)
              }
            } else {
              if (continent == row.continentCode) {
                list.push(row)
              }
            }
          } else if (country && country.length > 0) {
            if (row.countryName.indexOf(country) !== -1) {
              list.push(row)
            }
          } else {
            list.push(row)
          }
        }
      }
      if (list.length <= 0) {
        this.totalApply = 0
        this.applyCountrylLists = []
        return;
      }
      // 分页
      this.totalApply = list.length
      let i = (this.currentPageApply - 1) * this.pagesizeApply
      let j = i + this.pagesizeApply
      this.applyCountrylLists = list.slice(i, j)
      this.nationalArea()
    },


    // 确定
    selectapply() {
      this.dialogCountryVisible = false
      let list = []
      if (this.determineModelList.length > 0) {
        this.determineModelList.forEach(row => {
          list.push(row.countryCode)
        })
      }
      this.form.country = list

      this.targetName = list.length > 0 ? "已选择" : "未选择"
    },

    // 分配国家 权限选择
    cellMouseEnter(row) {
      $(".nationalList._" + row.countryId + " .el-icon-close").show()
    },
    cellLeaveEnter() {
      $(".nationalList .el-icon-close").hide()
    },
    // 分配国家 表格高度
    nationalArea() {
      var _this = this;
      setTimeout(() => {
        if ($(".nationalInfo").length != 0) {
          var allTable = $(".nationalInfo").outerHeight(true);
          _this.accreditTableHeight = allTable;
        }
      }, 80)
      window.addEventListener("resize", function () {
        setTimeout(() => {
          if ($(".nationalInfo").length != 0) {
            var tableArea = $(".nationalInfo").outerHeight(true);
            _this.accreditTableHeight = tableArea;
          }
        }, 80)
      })
    },
    // 获取类型
    getTypeList() {
      findCarType().then(res => {
        console.log(this.typeList);
        this.typeList = res.data.data
        this.typeIndex = 0
        this.typeFlag = true;
        this.addTitle = this.addTitlePrefix + this.typeList[this.typeIndex].name
      }).catch(e => {
        this.typeList = []
      })
    },
    initialState() {
      var _this = this;
      _this.getTypeList()
      _this.getUserCountryList()
      _this.getCarTypeList()
      _this.dataList()
      // _this.getTrainTypeList()
      contentSize()
    },
  },
  mounted() {
    this.typeIndex = 0
    this.initialState();
  },
}
</script>
