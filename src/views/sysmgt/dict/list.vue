<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFive" :model="formInline" class="demo-form-inline">
        <el-form-item label="类型" prop="type">
          <el-select v-model="formInline.type" clearable filterable>
            <el-option v-for="(item, index) of typeList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称(或编码)" prop="name">
          <el-input v-model.trim="formInline.name" placeholder="请输入名称(或编码)"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss1A1B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addData()">新增</el-button>
      </div>
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="类型" prop="classify" min-width="150" maxlength="50">
          <template slot-scope="{row}">
            <span>{{ row.classify }}</span>
          </template>
        </el-table-column>
        <el-table-column label="编码" prop="code" min-width="150" maxlength="50">
          <template slot-scope="{row}">
            <span>{{ row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="name" min-width="150">
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="100">
          <template slot-scope="{row}">
            <span>{{ row.sort }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150">
          <template slot-scope="{row}">
            <span>{{ row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" class="state" prop="useFlag" width="100">
          <template slot-scope="{row}">
            <span v-if="row.useFlag" class="successColor">生效</span>
            <span v-if="!row.useFlag" class="errorColor">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss1A1B_103')" @click="handleEdit(row)" type="text"
              size="small">编辑</el-button>
            <el-button v-if="!row.useFlag && hasPerm('menuAsimss1A1B_102')" class="deleteButton"
              @click="handleDelete(row, row.id)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />

      <!-- 添加或编辑 -->
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false">
        <el-form :model="temp" ref="temp" :label-width="formLabelWidth" label-position="center" :rules="rules">
          <el-form-item label="类型" prop="classify">
            <el-input v-show="classifyStatus === 'add'" v-model.trim="temp.classify" style="width: 62% !important;"
              placeholder="请输入类型[只能输入字母]" oninput="value=value.replace(/[^A-Za-z]+/g,'')" limit="limit" show-word-limit
              maxlength="40"></el-input>
            <el-select v-show="dialogStatus === 'addpage' && classifyStatus !== 'add'" clearable filterable ref="select"
              @focus="focus" @visible-change="visibleChange" v-model="temp.classify" style="width:60%">
              <el-option v-for="(item, index) of typeList" :key="index" :label="item" :value="item"></el-option>
            </el-select>
            <el-input v-show="dialogStatus === 'update' && classifyStatus !== 'add'" :disabled="true" filterable
              v-model="temp.classify" oninput="value=value.replace(/[^[A-Za-z]+$]/g,'')"></el-input>
            <el-button v-show="dialogStatus === 'addpage' && classifyStatus !== 'add'"
              style="padding: 9px 15px;margin: 0 10px;margin-top: 5px !important" type="primary" @click="addClassify">
              添加类型
            </el-button>
            <el-button v-show="classifyStatus === 'add'" style="padding: 9px 15px;;margin: 0 10px 0 50px !important"
              type="primary" @click="changeClassify">
              选择类型
            </el-button>
          </el-form-item>
          <el-form-item label="编码" prop="code">
            <el-input v-model.trim="temp.code" placeholder="请输入编码[只能输入字母、数字、下划线、短横线]"
              oninput="value=value.replace(/[^\w+-$]/g,'')" limit="limit" show-word-limit maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="temp.name" placeholder="请输入名称" limit="limit" show-word-limit
              maxlength="45"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model.trim="temp.sort" placeholder="请输入排序" controls-position="right" :min="1" :max="9999"
              :precision="0" :step="1"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input placeholder="请输入备注" v-model.trim="temp.remark"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="whether"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'addpage' ? addClick() : updateData()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { dictData, dictTypeList, dictAdd, dictEdit, dictDel } from '@/api/sysmgt.js'
import { tableHeight } from '@/assets/js/common.js'
export default {
  name: 'sysmgt_dict_list',
  components: { Pagination },
  data() {
    return {
      formInline: {
        name: '',
        type: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      classifyStatus: '',
      textMap: {
        update: '编辑字典',
        addpage: '新增字典'
      },
      typeList: [],
      temp: {
        id: '',
        classify: '',
        code: '',
        name: '',
        sort: 1,
        useFlag: '',
        remark: ''
      },
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      whether: false,
      inputFlag: null,
      formLabelWidth: '100px',
      rules: {
        classify: [{ required: true, message: '请输入类型', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '请输入编码', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入名称', trigger: ['blur', 'change'] }],
        sort: [{ required: true, message: '请输入排序', trigger: ['blur', 'change'] }]
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      let params = {
        'name': this.formInline.name,
        'classify': this.formInline.type
      }
      dictData(this.currentPage + "/" + this.pagesize, params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea()
      })
    },
    // 类型数据
    getDictList() {
      var _this = this
      dictTypeList().then(res => {
        _this.typeList = res.data.data
      })
    },
    refreshData() {
      this.dataList()
      this.getDictList()
    },
    focus() {
      if (this.inputFlag) {
        this.$refs.select.blur()
      }
    },
    visibleChange(flag) {
      setTimeout(() => {
        this.inputFlag = flag
      }, 0)
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    // 搜索重置
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.refreshData()
    },
    resetTemp() {
      this.temp = {
        classify: '',
        code: '',
        name: '',
        sort: 1,
        useFlag: true
      }
      this.whetherStates()
      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
    },
    whetherStates() {
      // if (this.temp.useFlag === 0 ) {
      //   this.whether = false
      // } else {
      //   this.whether = true
      // }
      this.whether = this.temp.useFlag
    },
    useFlagState() {
      // if (this.whether === false) {
      //   this.temp.useFlag = 0
      // } else {
      //   this.temp.useFlag = 1
      // }
      this.temp.useFlag = this.whether
    },
    // 增加
    addData() {
      this.resetTemp()
      this.dialogStatus = 'addpage'
      this.classifyStatus = ''
      this.dialogFormVisible = true
    },
    // 增加分类
    addClassify() {
      this.classifyStatus = 'add'
      this.temp.classify = ""
    },
    changeClassify() {
      this.classifyStatus = ''
      this.temp.classify = ""
    },
    addClick() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          this.useFlagState()
          var params = {
            'classify': this.temp.classify,
            'code': this.temp.code,
            'name': this.temp.name,
            'useFlag': this.temp.useFlag,
            'sort': this.temp.sort,
            'remark': this.temp.remark
          }
          dictAdd(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.refreshData()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          }).catch(err => {
            if (err !== null && err !== '' && err.responseText !== null) {
              this.$DonMessage.error(this.$t('errorTip.systemTip'));
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 编辑
    handleEdit(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.classifyStatus = ''
      this.whetherStates()
      this.whether = row.useFlag == 1
    },
    updateData() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          this.useFlagState()
          var params = {
            classify: this.temp.classify,
            code: this.temp.code,
            name: this.temp.name,
            sort: this.temp.sort,
            useFlag: this.temp.useFlag,
            remark: this.temp.remark
          }
          dictEdit(this.temp.id, params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.refreshData()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 删除
    handleDelete(row, id) {
      this.$confirm('确定删除【' + row.name + '】的相关信息?', '删除数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dictDel(id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.refreshData()
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    var _this = this
    _this.dataList()
    _this.getDictList()
    window.addEventListener('keydown', _this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
