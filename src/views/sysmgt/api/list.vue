<template>
  <div class="layoutContainer">
    <div>
      <div class="tableDetail">
        <!--        <div class="tableHandle">
          <el-button type="text"  icon="el-icon-plus" @click="configPermission">配置权限</el-button>
      </div>-->
        <el-table :data="apiTreeData" row-key="id" border style="width:100%" :lazy="true" :load="loadChildData"
          ref="table" @cell-dblclick="updateInEditMode" highlight-current-row :max-height="maximumHeight"
          :row-class-name="tableRowClassName" :row-style="rowStyle"
          :tree-props="{ children: 'groupList', hasChildren: 'hasChildren' }">
          <el-table-column type="index" label="序号" width="50">
          </el-table-column>
          <el-table-column prop="url" label="地址" sortable min-width="180">
          </el-table-column>
          <el-table-column prop="desc" label="描述" sortable min-width="150">
            <template slot-scope="scope">
              <el-input @blur="updateDesc(scope)" @keyup.enter.native="updateDesc(scope)" v-show="scope.row.edit"
                v-model="scope.row.desc" />
              <span v-show="!scope.row.edit">{{ scope.row.desc }}</span>
              <!--          <div>
            <span v-show="!scope.row.edit">{{scope.row.desc}} </span>
            <el-input v-show="scope.row.edit" v-model="scope.row.desc" :onbulr="updateDesc(scope)"></el-input>
          </div>-->
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160">
            <template slot-scope="scope">
              <span>{{ scope.row.updateTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="controller" label="控制器" min-width="150">
          </el-table-column>
          <el-table-column prop="grantInfos" label="权限清单" min-width="300">
            <template slot-scope="scope">
              <div style="margin:2px 0;white-space:normal">
                <div v-for="(item, index) in convertPermissionData(scope.row.grantInfos)" :key="index">
                  <span>{{ item.permissionTypeName }}：</span>
                  <el-tag type="success" style="margin: 2px;" v-for="(option, index) in item.permissions" :key="index">
                    {{ option.permissionName }}
                  </el-tag>
                </div>
                <!--          <el-tag type="success"  v-for="(item,index) in scope.row.grantInfos" :key="index">
            {{item.permissionName}}
          </el-tag>-->
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="grantInfos" label="禁用状态" width="85">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.disableStatus" active-color="#ff4949" inactive-color="#13ce66" size="mini"
                @change="updateDesc(scope)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="grantInfos" label="操作" fixed="right" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="configPermission(scope)">配置权限</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-dialog v-dialogDrag lock-scroll :title="dialogTitle" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false" v-if="dialogFormVisible">
        <div class="dialogForm">
          <el-form :model="configRow" label-width="100px" ref="configForm">
            <el-form-item v-for="(item, index) in permissionList" :key="index" :label="item.permissionTypeName">
              <el-select multiple collapse-tags clearable
                v-model="configRow.permission[type = item.permissionType].grants">
                <el-option v-for="option in item.permissions" :key="option.permissionCode"
                  :label="option.permissionName" :value="option.permissionCode">
                </el-option>
              </el-select>
            </el-form-item>
            <div class="submitArea">
              <el-button type="primary" @click="updatePermission">{{ $t('button.submit') }}</el-button>
              <el-button plain @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
            </div>
          </el-form>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import {
  requestSysApiGroupList,
  requestPermissionList,
  requestUpdateSysPermissionList,
  requestUpdateSysApiInfo,
  requestSysGroupApiList,
} from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_api_list',
  data() {
    return {
      dialogTitle: '',
      grantInfos: [],
      permissionList: [],
      dialogFormVisible: false,
      maximumHeight: 0,
      apiTreeData: [],
      configRow: {}
    }
  },
  methods: {
    // 表格样式
    rowStyle({ row, rowIndex }) {
      let styleJson = {
        "background": "var(--striped-color)",
      }
      if (row.hasChildren == false) {
        return styleJson;
      } else {
        return {};
      }
    },
    loadChildData(dataObj, treeNode, resolve) {
      let id = dataObj.id;
      let urlSearchParams = new URLSearchParams();
      urlSearchParams.append("groupId", id)
      requestSysGroupApiList(urlSearchParams).then(res => {
        if (res.data.code == 100) {
          let dataList = res.data.data
          dataList.forEach(item => item.hasChildren = false)
          this.setEditMode(dataList)
          resolve(dataList)
        } else {
          this.$DonMessage.error("加载数据失败")
        }
      })
    },
    /**
     * 得到对象
     * @param {*} list
     * @param {*} fn
     * @returns
     */
    groupBy(list, fn) {
      const map = {}
      list.forEach(item => {
        const key = fn(item)
        map[key] = map[key] || []
        map[key].push(item)
      })
      return map
    },
    convertPermissionData(permissionData) {
      let groupMap = this.groupBy(permissionData, item => { return item.grantTypeName });
      let groupArr = Object.keys(groupMap).map(key => {
        let group = {
          permissionTypeName: key,
          permissions: groupMap[key]
        }
        return group;
      })
      return groupArr;
    },
    updatePermission: function () {
      //权限列表
      let permission = this.configRow.permission;
      let submitPermission = [];
      if (permission) {
        for (let [key, value] of Object.entries(permission)) {
          for (let i = 0; i < value.grants.length; i++) {
            let permissionCode = value.grants[i];
            let submitItem = {
              grantType: key,
              sysApiId: this.configRow.id,
              permissionCode: permissionCode,
            }
            submitPermission.push(submitItem)
          }
        }

      }
      let body = {
        sysApiId: this.configRow.id,
        sysApiPermissions: submitPermission
      }
      requestUpdateSysPermissionList(body).then(res => {
        if (res.data.code === 100) {
          this.dialogFormVisible = false;
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          // this.dataList();
          this.reRenderLayout();
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    /**
     * 重新渲染表格
     */
    reRenderLayout() {
      if (this.configRow.type == 1) {
        requestSysApiGroupList().then(res => {
          if (res.data.code == 100) {
            let dataList = res.data.data
            dataList.forEach(item => {
              item.hasChildren = true;
              item.edit = false;
            })
            this.$refs.table.store.states.data = dataList
          } else {
            this.$DonMessage.error("加载数据失败")
          }
        })
      } else {
        let groupId = this.configRow.groupId;
        let urlSearchParams = new URLSearchParams();
        urlSearchParams.append("groupId", groupId)
        requestSysGroupApiList(urlSearchParams).then(res => {
          if (res.data.code == 100) {
            let dataList = res.data.data
            dataList.forEach(item => item.hasChildren = false)
            this.setEditMode(dataList)
            //设置回去
            this.$refs.table.store.states.lazyTreeNodeMap[groupId + ""] = dataList;
          } else {
            this.$DonMessage.error("加载数据失败")
          }
        })
      }
    },
    updateDesc(scope) {
      //更新详情
      let id = scope.row.id;
      let desc = scope.row.desc;
      this.configRow = scope.row;
      let postData = {
        id: id,
        desc: desc,
        disableStatus: scope.row.disableStatus
      }
      requestUpdateSysApiInfo(postData).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.updateTip'))
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).finally(() => {
        this.reRenderLayout();
      })
    },
    updateInEditMode(row, column, cell, event) {
      if (column.property == "desc") {
        row.edit = true;
        this.$refs.table.doLayout();
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.type == 2) {
        return 'group-row';
      } else {
        return "api-row";
      }
    },
    // 用户信息报告高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    configPermission(row) {
      let configRow = JSON.parse(JSON.stringify(row.row));
      let grants = configRow.grantInfos ? configRow.grantInfos : [];
      requestPermissionList().then(res => {
        if (res.data.code === 100) {
          let data = res.data.data
          this.permissionList = data;
          configRow.permission = {}
          for (let item of data) {
            configRow.permission[item.permissionType] = {
              name: item.permissionTypeName,
              type: item.permissionType,
              // grants:grants.filter(permission=>permission.grantType == item.permissionType).map(item=>item.permissionCode);
            }
            let list = grants.filter(permission => permission.grantType == item.permissionType).map(item => item.permissionCode);
            configRow.permission[item.permissionType].grants = list;
          }
          this.configRow = configRow;
          this.dialogFormVisible = true;
          if (row.row.desc) {
            this.dialogTitle = "更新 " + row.row.desc + "-" + row.row.url + " 权限";
          } else {
            this.dialogTitle = "更新 " + row.row.url + " 权限";
          }
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 数据
    dataList() {
      requestSysApiGroupList().then(res => {
        if (res.data.code === 100) {
          this.apiTreeData = []
          setTimeout(() => {
            let apiTreeData = res.data.data
            apiTreeData.forEach(item => item.hasChildren = true)
            this.setEditMode(apiTreeData)
            this.apiTreeData = apiTreeData
          })
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    setEditMode: function (apiTreeData) {
      for (let i = 0; i < apiTreeData.length; i++) {
        let apiTreeDatum = apiTreeData[i];
        apiTreeDatum.edit = false;
        if (apiTreeDatum.groupList && apiTreeDatum.groupList.length > 0) {
          this.setEditMode(apiTreeDatum.groupList)
        }
      }
    }
  },
  mounted() {
    this.dataList()
    setTimeout(() => {
      this.maximumArea()
    }, 50)
  }
}
</script>
