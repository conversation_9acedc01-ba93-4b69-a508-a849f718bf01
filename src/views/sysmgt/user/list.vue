<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFour" :model="formInline" class="demo-form-inline">
        <el-form-item label="姓名" prop="realName">
          <el-input v-model.trim="formInline.realName" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="登录账户" prop="loginName">
          <el-input v-model.trim="formInline.loginName" placeholder="请输入登录账号"></el-input>
        </el-form-item>
        <!--        <el-form-item label="角色" prop="userrole">-->
        <!--          <el-select v-model="formInline.userrole" clearable filterable>-->
        <!--            <el-option v-for="(item,index) in userRoleList" :key="index" :label="item.name" :value="item.code"></el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset()">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle"
        v-if="hasPerm('menuAsimss1A2B_101') || hasPerm('menuAsimss1A2B_102') || hasPerm('menuAsimss1A2B_107') || hasPerm('menuAsimss1A2B_108')">
        <el-button type="text" v-if="hasPerm('menuAsimss1A2B_101')" icon="el-icon-plus"
          @click="addClcik()">新增</el-button>
        <!-- <el-button type="text" v-if="hasPerm('menuAsimss1A2B_102')" icon="el-icon-delete" @click="batchDelClick()">批量删除</el-button> -->
        <el-upload class="upload-demo inline-block" ref="elUpload" action="#"
          :show-file-list="false" :limit="1" :file-list="fileList" :before-upload="onBeforeUpload"
          :http-request="uploadFile" :on-change="onUploadChange">
          <el-button type="text" v-if="hasPerm('menuAsimss1A2B_107') && userInfo.defaultRoleCode != 'generalUser'"
            size="min" icon="bulkImport-icon">批量上传</el-button>
        </el-upload>
        <el-button type="text" v-if="hasPerm('menuAsimss1A2B_108')" icon="bulkDown-icon"
          @click="batchExport()">批量下载</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss1A2B_108') && userInfo.defaultRoleCode != 'generalUser'"
          icon="el-icon-download" @click="downTemplateClick()">下载模板</el-button>
        <!--        <el-button type="text" v-if="hasPerm('menuAsimss1A2B_102')" icon="el-icon-s-promotion" @click="batchSyncClick()">同步至TIS</el-button>-->
      </div>
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="姓名" prop="realName" min-width="100"></el-table-column>
        <el-table-column label="登录账户" prop="username" min-width="100"></el-table-column>
        <el-table-column label="默认角色" prop="defaultRoleName" min-width="100">
          <template slot-scope="{row}">
            <span>{{ getRoleName(row) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="所属国家" prop="defaultCountryName" min-width="100"></el-table-column> -->
        <el-table-column label="所属部门" prop="deptName" min-width="120">
          <template slot-scope="{row}">
            <span>{{ getDeptName(row) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="联系人" prop="contacts" min-width="100"></el-table-column> -->

        <el-table-column label="手机号" prop="mobile" min-width="120"></el-table-column>
        <el-table-column label="邮箱" prop="email" min-width="150"></el-table-column>
        <!-- <el-table-column label="地址" prop="address" min-width="150"></el-table-column> -->

        <el-table-column label="状态" prop="useFlag" width="60">
          <template slot-scope="{row}">
            <span v-if="row.useFlag" class="successColor">生效</span>
            <span v-else class="errorColor">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="260">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss1A2B_105')" type="text" size="small"
              @click="distriRole(row)">分配角色</el-button>
            <!-- <el-button v-if="hasPerm('menuAsimss1A2B_105') && row.userType==1" type="text" size="small" @click="distriCountry(row)">分配国家</el-button> -->
            <el-button v-if="hasPerm('menuAsimss1A2B_103')" type="text" size="small"
              @click="initPsw(row)">重置密码</el-button>
            <el-button v-if="hasPerm('menuAsimss1A2B_103')" type="text" size="small"
              @click="editopen(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A2B_102')" size="small" class="deleteButton"
              @click="delectClick(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
      <!-- 新增，编辑 -->
      <el-dialog v-dialogDrag lock-scroll :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false" v-if="dialogFormVisible">
        <!-- 新增，编辑 -->
        <el-form v-if="dialogStatus === 'edit' || dialogStatus === 'add'" :label-width="formLabelWidth" ref='temp'
          :rules="fromTemp" :model="temp" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="登录账户" prop="username">
            <el-input v-if="dialogStatus === 'add'" v-model.trim="temp.username" placeholder="请输入账号" limit="limit"
              show-word-limit maxlength="20"></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username !== ''" placeholder="请输入账号"
              v-model.trim="temp.username" limit="limit" show-word-limit maxlength="20" :disabled="true"></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username === ''" placeholder="请输入账号"
              v-model.trim="temp.username" limit="limit" show-word-limit maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="realName">
            <el-input v-model.trim="temp.realName" placeholder="请输入姓名" limit="limit" show-word-limit
              maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="默认角色" prop="defaultRoleCode">
            <el-select v-model="temp.defaultRoleCode" clearable filterable>
              <el-option v-for="(item, index) in userRoleList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
          </el-form-item>

          <!-- 2023-08-01 添加默认国家 -->
          <!--          <el-form-item label="所属国家" prop="defaultCountryCode">-->
          <!--            <el-select v-model="temp.defaultCountryCode" clearable filterable>-->
          <!--              <el-option v-for="(item,index) in userCountryList" :key="index" :label="item.name" :value="item.code"></el-option>-->
          <!--            </el-select>-->
          <!--          </el-form-item>-->

          <el-form-item label="所属部门" prop="deptId">
            <select-tree ref="modelSelectTree" :options="departmentList" v-model.trim="temp.deptId"
              :props="defaultProps" :expand_on_click_node="true" :check_on_click_node="false"
              @getCurrentNode="getCurrentNode" placeholder="请选择部门" />
          </el-form-item>
          <el-form-item label="联系人" prop="contacts">
            <el-input v-model.trim="temp.contacts" show-word-limit maxlength="20" placeholder="请输入联系人"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="temp.sex">
              <el-radio :label="1">男</el-radio>
              <el-radio :label="2">女</el-radio>
              <el-radio :label="0">未知</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model.trim="temp.mobile" maxlength="11" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="temp.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input rows="2" v-model.trim="temp.address" show-word-limit maxlength="100"
              placeholder="请输入地址"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="whether"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'edit' ? editClick() : addClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>

        <!-- 分配角色 -->
        <el-form v-if="dialogStatus === 'assignRole'" ref="alloter" :label-width="formLabelWidth" :model="sltUserRole"
          :validate-on-rule-change="false">
          <el-form-item label="默认角色" prop="defaultRoleCode">
            <el-radio-group v-model="sltUserRole.defaultRoleCode">
              <el-radio v-for="(item, cindex) in userRoleList" :key="cindex" :label="item.code">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择角色">
            <el-checkbox-group v-model="sltUserRole.roleCodeArr">
              <el-checkbox v-for="(item, cindex) in userRoleList" :key="cindex"
                :label="item.code">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="updateUserRole()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>
      <!-- 2023-12-16 分配国家 -->
      <el-dialog v-dialogDrag lock-scroll width="1110px !important" class="assignCountryArea" :title="countryTitle"
        :visible.sync="dialogCountryVisible" v-if="dialogCountryVisible" :close-on-click-modal="false">
        <div class="secondFloat">
          <el-form :inline="true" ref="countryData" :label-width="$labelTwo" :model="countryInfo" class="demo-form-inline">
            <el-form-item label="大洲" prop="continent">
              <el-select v-model="countryInfo.continent" filterable clearable>
                <el-option v-for="(item, index) of continentList" :key="index" :label="item.continentName"
                  :value="item.continentCode"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="国家" prop="country">
              <el-input v-model.trim="countryInfo.country" placeholder="请输入国家"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchApply">{{ $t('button.search') }}</el-button>
              <el-button plain @click="resetApply()">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- 适用车型 -->
        <el-row>
          <el-col :span="17" class="nationalInfo">
            <el-table :row-key="selRowKey" ref="applytable" style="width:100%;" max-height="550px" border stripe
              highlight-current-row :data="applyCountrylLists" @selection-change="handleSelectionApply">
              <el-table-column type="selection" width="40" fixed="left" align="center"
                reserve-selection></el-table-column>
              <el-table-column label="序号" type="index" width="60"></el-table-column>
              <el-table-column label="大洲" prop="continentName" min-width="100"></el-table-column>
              <el-table-column label="国家" prop="countryName" min-width="100"></el-table-column>
              <el-table-column label="代码" prop="countryCode" min-width="100"></el-table-column>
            </el-table>
            <pagination v-show="totalApply > 0" :total="totalApply" :page.sync="currentPageApply"
              :limit.sync="pagesizeApply" @pagination="selectCountry" />
          </el-col>
          <el-col :span="7" class="nationalSelect">
            <el-table style="width:100%" :height="accreditTableHeight" border stripe highlight-current-row
              :header-cell-style="{}" :data="determineModelList" @cell-mouse-enter="cellMouseEnter"
              @cell-mouse-leave="cellLeaveEnter">
              <el-table-column prop="title">
                <template slot="header">
                  <div class="authorityTitle">
                    <div>
                      <span>已选(<b> {{ selectNum }} </b>)</span>
                    </div>
                    <div>
                      <span class="warrantAction" @click="authorizeAll">授权全部海外</span>
                      <span class="clearAction" @click="emptyCountry">清空</span>
                    </div>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div :class="'nationalList ' + '_' + scope.row.countryId">
                    <span>{{ scope.row.countryName }}</span>
                    <i class="el-icon-close" @click="deleteEmpower(scope.row)"></i>
                  </div>
                </template>
              </el-table-column>
            </el-table>

          </el-col>
        </el-row>
        <el-row style="margin-top: 5px;display:flex;align-items: center;">
          <img src="../../../assets/image/noticeIcon/tipsIcon.png" style="width: 18px;" alt="" />
          <span style="color: #999;font-size: 12px;margin: 0px 6px;">
            为了数据的完整性，中国和其他国家 不要同时授权给一个用户
          </span>
        </el-row>
        <div class="submitArea">
          <el-button type="primary" @click="selectapply">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogCountryVisible = false">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import SelectTree from '@/components/TreeView/SelectTree.vue'
import {
  // 分页查询
  userDataList,
  // 授权角色
  userRoleData,
  // 添加
  userAdd,
  // 编辑
  userEdit,
  // 删除
  userDel,
  // 下载
  userDown,
  // 模板
  downTemplate,
  // 批量添加
  batchAdd,

  // 部门
  departmentData,

  assignRole,
  updateAssignRole,
  userPasswordReset,
  userCountryData,
  getUserCountry,
  updatedCountry,

  getUserInfo,
  getCountryAll, syncUserSendApi, findUserPage, findDeptAll, findRoleAll, addUser, editUser,
} from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_user_list',
  components: { Pagination, SelectTree },
  data() {
    return {
      formInline: {
        realName: '',
        loginName: '',
        userrole: '',
        userCountry: ''
      },
      temp: {
        id: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: '',
        defaultCountryCode: '',
        defaultCountryName: '',
        defaultRoleId: '',
        deptId: '',
        deptName: '',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 1,
        useFlag: 1
      },
      perms: [],
      sltUserRole: {
        id: '',
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      },
      sltUserCountry: {
        userName: '',
        defaultCountry: '',
        countryArr: []
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      resultList: [],
      dialogStatus: '',
      textMap: {
        edit: '用户编辑',
        add: '新增用户',
        restPwd: '重置密码',
        assignRole: '分配角色',
        assignCountry: '分配国家',
      },
      defaultProps: {
        parent: 'path',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          return val.children && val.children.length > 0;
        }
      },
      deleteList: [],
      aboriginalRow: {},
      userInfo: {},
      pagesize: 10,
      currentPage: 1,
      total: 0,
      whether: false,
      userRoleList: [],
      roleMap: new Map(),
      userCountryList: [],
      fileList: [],
      uploadFileList: [],
      fromTemp: {
        username: [{ required: true, message: '账号不能为空', trigger: ['blur', 'change'] }],
        realName: [{ required: true, message: '姓名不能为空', trigger: ['blur', 'change'] }],
        mobile: [{ required: true, message: '手机号不能为空', trigger: ['blur', 'change'] }],
        defaultRoleCode: [{ required: true, message: '默认角色不能为空', trigger: ['blur', 'change'] }],
        // mobile: [{ pattern: /^1\d{10}$/, message: '请输入正确的手机格式', trigger: ['blur', 'change'] }],
        // email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
        // defaultCountryCode: [{ required: true, message: '默认国家不能为空', trigger: ['blur', 'change']}]
        // deptId: [{ required: true, message: '所属部门不能为空', trigger: ['blur', 'change']}]
      },
      departmentList: [],
      // 车型和人员的关联
      filterText: '',
      trainUser: '',
      userTrainTree: [],
      checkedTrain: [],
      dialogTrainTreeVisible: false,
      defaultTrainProps: {
        children: 'children',
        label: 'name'
      },
      maximumHeight: 0,
      // 分配国家
      // 搜索条件
      countryInfo: {
        country: '',
        continent: '',
      },
      countryTitle: '分配国家',
      totalApply: 0,
      currentPageApply: 1,
      pagesizeApply: 10,
      // 列表中的
      applyCountrylLists: [],
      // 所有的
      applyCountrylList: [],
      applyCountryTotalLists: [],
      determineModelList: [],
      dialogCountryVisible: false,
      selectNum: 0,
      continentList: [],
      accreditTableHeight: 0,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.trainTree.filter(val);
    },
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 列表数据
    dataList() {
      let param = {
        'realName': this.formInline.realName,
        'username': this.formInline.loginName
      }

      findUserPage(this.currentPage + "/" + this.pagesize, param).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea()
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    // 角色信息
    getUserRoleList(callbackfn, thisArg) {
      let _this = this;
      findRoleAll().then(res => {
        _this.userRoleList = res.data.data
        _this.roleMap = new Map();
        _this.userRoleList.forEach(o => {
          _this.roleMap.set(o.code, o);
        })
      }).catch(e => {
        this.userRoleList = []
      })
    },
    // 获取国家
    getUserCountryList() {
      userCountryData().then(res => {
        this.userCountryList = res.data.data
      })
    },
    // 所属部门
    getDepartment() {
      let _this = this;
      _this.departmentMap = new Map();
      findDeptAll().then(res => {
        if (res.data.data) {
          _this.departmentList = res.data.data
          _this.getDepartmentMap(_this.departmentList, "");
        } else {
          _this.departmentList = []
        }
      })
    },

    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },

    resetTemp() {
      this.temp = {
        id: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: '',
        defaultCountryCode: '',
        deptId: '',
        deptName: '',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 1,
        useFlag: 1
      }
      this.whetherState()
      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
    },
    whetherState() {
      // if (this.temp.useFlag) {
      //   this.whether = false
      // } else {
      //   this.whether = true
      // }
      this.whether = this.temp.useFlag
    },
    selectState() {
      // if (this.whether === false) {
      //   this.temp.useFlag = 0
      // } else {
      //   this.temp.useFlag = 1
      // }
      this.temp.useFlag = this.whether
    },
    changeUserType(val) {
      this.temp.userType = val
    },
    // 新增
    addClcik() {
      var _this = this
      _this.dialogStatus = 'add'
      _this.resetTemp()
      _this.whether = true
      _this.dialogFormVisible = true
    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['temp'].validateField('deptId')
      }
    },
    addClick() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          this.selectState()
          let roleId = '';
          for (let i = 0; i < this.userRoleList.length; i++) {
            if (this.temp.defaultRoleCode == this.userRoleList[i].code) {
              roleId = this.userRoleList[i].id
              break
            }
          }
          let params = {
            'userType': 1, // 用户类型需要查询，暂时写死
            'realName': this.temp.realName,
            'username': this.temp.username,
            'roleId': roleId,
            'mobile': this.temp.mobile,
            'useFlag': this.whether ? 1 : 0,
            'email': this.temp.email,
            'address': this.temp.address,
            'contacts': this.temp.contacts,
            'deptId': this.temp.deptId,
            'sex': this.temp.sex
          }
          addUser(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    reset() {
      if (this.$refs['formInline'].resetFields() !== undefined) {
        this.$refs['formInline'].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    // 批量删除
    handleSelectionChange(val) {
      this.deleteList = val
    },
    // 附件上传
    onBeforeUpload(file) {
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 100
      if (!docxExt && !docExt) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'xls, xlsx' }))
        return false;
      }
      if (!isLimit) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }))
        return false;
      }
      return true;
    },
    onUploadChange(file) {
      //this.files.push(file.raw)
    },
    // 批量上传
    uploadFile(param) {
      let _this = this
      let formData = new FormData();
      formData.append('file', param.file);
      batchAdd(formData).then(res => {
        if (res.data.code === 100) {
          _this.$DonMessage.success(_this.$t('successTip.uploadTip'))
          _this.dataList()
        } else {
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
        }
        _this.fileList = []
      }).catch(function (error) {
        _this.fileList = []
        _this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })

    },
    handlesuccess(file, fileList) {
      this.form.image = file.data.fileName
    },
    handleRemove(file, fileList) {
    },
    handlePreview(file) {
    },
    handleExceed(files, fileList) {
      this.$DonMessage.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
    },
    uploadFileError(err, file, fileList) {
      this.$DonMessage.error('导入失败')
      this.$refs.upload.clearFiles();
    },
    // 批量下载
    batchExport() {
      var params = new URLSearchParams()
      params.append('realName', this.formInline.realName)
      params.append('username', this.formInline.loginName)
      params.append('defaultRoleCode', this.formInline.userrole)
      params.append('defaultCountryCode', this.formInline.userCountry)
      userDown(params).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        var name = "用户信息列表.xls";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 下载模板
    downTemplateClick() {
      var params = '';
      downTemplate(params).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        var name = "用户导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 分配角色
    roleReset() {
      this.sltUserRole = {
        id: '',
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      }
      this.$nextTick(() => {
        this.$refs.alloter.clearValidate();
      })
    },
    distriRole(row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'assignRole'
      this.roleReset()
      this.sltUserRole.id = row.id
      // var params ='?id='+ row.id
      assignRole(row.id).then(res => {
        this.sltUserRole.defaultRoleCode = res.data.data.defaultRoleCode
        this.sltUserRole.userName = res.data.data.username;
        this.sltUserRole.roleCodeArr = res.data.data.roleList
        var roleCodeArr = res.data.data.roleList
        //获取当前用户选择的角色
        if (roleCodeArr != null && roleCodeArr.length > 0) {
          if (this.userRoleList.length > 0) {
            for (var i = 0; i < this.userRoleList.length; i++) {
              var curRoleCode = this.userRoleList[i].code
              if (roleCodeArr.indexOf(curRoleCode) > -1) {
                if (this.sltUserRole.roleCodeArr.indexOf(curRoleCode) == -1) {
                  this.sltUserRole.roleCodeArr.push(curRoleCode)
                }
              }
            }
          }
        }
      })
    },
    // 分配车型
    distriTrain(row) {
      this.filterText = ''
      // 获取用户已经关联的车型
      this.trainUser = row.username

      this.checkedTrain = []
      var params = new URLSearchParams()
      params.append('userId', row.id)
      getUserBrandTrainTree(params).then(res => {
        this.userTrainTree = res.data.data
        params = new URLSearchParams()
        params.append('username', row.username)
        getUserTrain(params).then(res => {
          if (res.data.code == 100) {
            let list = res.data.data
            for (let i = 0; i < list.length; i++) {
              this.checkedTrain.push(list[i].trainId);
            }
            this.dialogTrainTreeVisible = true
          }
        })
      })

    },
    // 获取选中
    getCheckedKeys() {
      let list = this.$refs.trainTree.getCheckedKeys()
      if (!list || list.length <= 0) {
        this.$DonMessage.warning("请选择车型")
        return false
      }
      var params = new URLSearchParams()
      params.append('username', this.trainUser)
      params.append('trainIds', list.toString())
      updateUserTrain(params).then(res => {
        if (res.data.code == 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dialogTrainTreeVisible = false
        } else {
          this.$DonMessage.error("分配失败，" + res.data.msg)
        }
      }).catch(e => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    // filterNode(value, data) {
    //     if (!value) return true;
    //     return data.name.indexOf(value) !== -1;
    //   },
    filterNode(value, data, node) {
      if (!value) return true;
      let names = this.getParents(node, node.data.name, 'name');
      return names.indexOf(value) !== -1;
    },
    getParents(node, name, key) {
      if (node.parent && node.parent.data[key]) {
        name += node.parent.data[key]
        return this.getParents(node.parent, name, key)
      }
      return name
    },
    selRowKey(row) {
      return row.countryCode
    },
    // 分配国家// 获取 大洲 国家信息
    distriCountry(row) {
      this.countryTitle = '分配国家【' + row.realName + ' (' + row.username + ')】'
      this.sltUserCountry.userName = row.username
      let _this = this
      getUserCountry(row.username).then(res => {
        let list = []
        let lst = res.data.data
        if (lst && lst.length > 0) {
          for (let i = 0; i < lst.length; i++) {
            const itm = lst[i];
            if (itm.isDefault !== 1) {
              list.push(itm)
            }
          }
        }
        _this.determineModelList = []
        _this.dialogCountryVisible = true
        setTimeout(() => {
          _this.emptyCountry()
          _this.applyCountrylList.forEach(row => {
            list.forEach(o => {
              if (row.countryCode === o.countryCode) {
                _this.determineModelList.push(row)
                _this.$refs.applytable.toggleRowSelection(row, true)
              }
            })
            _this.selectNum = _this.determineModelList.length
            _this.resetApply()
          })
        })
      }).catch(e => {
        _this.dialogCountryVisible = true
      })
      _this.nationalArea()
    },
    resetApply() {
      this.countryInfo.country = '';
      this.countryInfo.continent = '';
      this.currentPageApply = 1;
      this.pagesizeApply = 15;
      this.selectCountry();
    },
    searchApply() {
      this.currentPageApply = 1;
      this.selectCountry()
    },
    // 获取数据
    selectCountry() {
      this.applyCountrylLists = []
      let country = this.countryInfo.country
      let continent = this.countryInfo.continent
      let list = []
      if (!country && !continent) {
        list = this.applyCountrylList
      } else {
        for (let i = 0; i < this.applyCountrylList.length; i++) {
          let row = this.applyCountrylList[i]
          if (continent && continent.length > 0) {
            if (country && country.length > 0) {
              if (continent == row.continentCode && row.countryName.indexOf(country) !== -1) {
                list.push(row)
              }
            } else {
              if (continent == row.continentCode) {
                list.push(row)
              }
            }
          } else if (country && country.length > 0) {
            if (row.countryName.indexOf(country) !== -1) {
              list.push(row)
            }
          } else {
            list.push(row)
          }
        }
      }
      if (list.length <= 0) {
        this.totalApply = 0
        this.applyCountrylLists = []
        return;
      }
      // 分页
      this.totalApply = list.length
      let i = (this.currentPageApply - 1) * this.pagesizeApply
      let j = i + this.pagesizeApply
      this.applyCountrylLists = list.slice(i, j)
      this.nationalArea()
    },
    handleSelectionApply(val) {
      this.determineModelList = val
      this.selectNum = this.determineModelList.length
    },
    // 删除授权
    deleteEmpower(row) {
      let _this = this
      _this.determineModelList.forEach(itm => {
        if (itm.countryCode === row.countryCode) {
          _this.$refs.applytable.toggleRowSelection(row, false)
        }
      })
    },
    // 清空
    emptyCountry() {
      this.handleSelectionApply([])
      this.$refs.applytable.clearSelection();
    },
    // 获取全部
    authorizeAll() {
      let list = []
      let _this = this
      _this.emptyCountry()
      _this.applyCountrylList.forEach(row => {
        if (row.countryCode !== 'cn') {
          list.push(row)
          _this.$refs.applytable.toggleRowSelection(row, true)
        }
      })
      this.handleSelectionApply(list)
    },
    // 确定
    selectapply() {
      let list = []
      let sltCountry = ''
      if (this.determineModelList.length > 0) {
        this.determineModelList.forEach(row => {
          list.push(row.countryCode)
        })
        sltCountry = list.toString()
      } else {
        sltCountry = ''
      }

      let defaultCountry = ""
      var params = new FormData()
      params.append('username', this.sltUserCountry.userName)
      params.append('sltCountry', sltCountry)
      params.append('defaultCountry', defaultCountry)
      updatedCountry(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dataList()
          this.dialogFormVisible = false
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
      this.dialogSelectApplyFormVisible = false
    },

    updateUserCountry() {
      var defaultCountry = this.sltUserCountry.defaultCountry
      var countryArr = this.sltUserCountry.countryArr
      let sltCountry = ''
      if (countryArr && countryArr.length > 0) {
        sltCountry = countryArr.toString()
      }
      var params = new FormData()
      params.append('username', this.sltUserCountry.userName)
      params.append('sltCountry', sltCountry)
      params.append('defaultCountry', defaultCountry)
      updatedCountry(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dataList()
          this.dialogFormVisible = false
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },

    updateUserRole() {
      var roleCode = this.sltUserRole.defaultRoleCode
      var sltRoleCodeArr = this.sltUserRole.roleCodeArr
      let ids = []
      let defaultId = ''
      let role;
      for (let i = 0; i < this.userRoleList.length; i++) {
        role = this.userRoleList[i]
        if (role.code == roleCode) {
          defaultId = role.id
        } else if (sltRoleCodeArr.includes(role.code)) {
          ids.push(role.id)
        }
      }
      // if(sltRoleCodeArr.length==0){
      //   this.$DonMessage.warning('请选择角色')
      //   return false
      // }
      if (defaultId === '') {
        this.$DonMessage.warning('请选择默认角色')
        return false
      }
      var params = new FormData()
      // params.append('userName', this.sltUserRole.userName)
      params.append('ids', ids)
      params.append('defaultId', defaultId)
      updateAssignRole(this.sltUserRole.id, params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dataList()
          this.dialogFormVisible = false
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    resetCode() {
      this.sltUserRole = {
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      }
    },
    // 密码重置
    initPsw(row) {
      this.$confirm('确定重置【' + row.username + '】的密码吗?', '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // var params = '?id=' + row.id
        userPasswordReset(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.resetTip'))
          } else if (res.data.code === 101) {
            this.$DonMessage.error(res.data.msg)
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    // 编辑
    editopen(row) {
      var _this = this
      _this.aboriginalRow = row;
      _this.dialogStatus = 'edit'
      _this.dialogFormVisible = true
      _this.resetTemp()
      _this.temp = Object.assign({}, row)
      // if (row.useFlag === 0) {
      //   _this.whether = false
      // } else {
      //   _this.whether = true
      // }
      _this.whether = row.useFlag
      if (row === null || row.sex === null) {
        _this.temp.sex = 0
      } else {
        _this.temp.sex = row.sex
      }
      // console.log("---row.deptName", row.deptName);
      // if(row.deptId){
      //   _this.temp.deptId = String(row.deptId)
      //   let depN = row.deptName.split(' ')[1]
      //   if (!depN || depN.trim().length <= 0) {
      //     depN = row.deptName
      //   }
      //   setTimeout(function(){
      //     if(row.deptName){
      //       _this.$refs.modelSelectTree.initSelected(depN, _this.temp.deptId)
      //     }else{
      //       _this.$refs.modelSelectTree.initSelected('', '')
      //     }

      //   });
      // }else{
      //   setTimeout(function(){
      //     _this.$refs.modelSelectTree.initSelected('', '')
      //   })
      // }
    },
    // 提交编辑
    editClick() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          this.selectState()
          let roleId = '';
          for (let i = 0; i < this.userRoleList.length; i++) {
            if (this.temp.defaultRoleCode == this.userRoleList[i].code) {
              roleId = this.userRoleList[i].id
              break
            }
          }
          let params = {
            'userType': 1, // 用户类型需要查询，暂时写死
            'realName': this.temp.realName,
            'username': this.temp.username,
            'roleId': roleId,
            'mobile': this.temp.mobile,
            'email': this.temp.email,
            'address': this.temp.address,
            'contacts': this.temp.contacts,
            'useFlag': this.whether ? 1 : 0,
            'sex': this.temp.sex,
          }
          if (this.temp.deptId && this.temp.deptId != 'null' && this.temp.deptId != 'undefined') {
            params.deptId = this.temp.deptId
          }
          editUser(this.temp.id, params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 删除
    delectClick(row) {
      this.$confirm('确定删除【' + row.realName + '】的相关信息?', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userDel(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },

    // 获取 品牌-车型 结构树
    // getUserTrainTree(uid){
    //   var params = new URLSearchParams()
    //   params.append('userId', uid)
    //   getUserBrandTrainTree(params).then(res => {
    //       this.userTrainTree = res.data.data
    //   })
    // },

    // 用户信息
    userData() {
      getUserInfo().then(res => {
        this.userInfo = res.data.data
      })
    },
    // 获取全部的国家信息包含大洲
    findCountryAll() {
      this.continentList = []
      // getCountryAll().then(res => {
      //   this.applyCountrylList = res.data.data
      //   this.totalApply = res.data.total
      //   for (let i = 0; i < this.applyCountrylList.length; i++) {
      //     const row = this.applyCountrylList[i];
      //     let b = true
      //     for (let j = 0; j < this.continentList.length; j++) {
      //       const itm = this.continentList[j];
      //       if (itm.continentId === row.continentId) {
      //         b = false
      //         break;
      //       }
      //     }
      //     if(b){
      //       this.continentList.push(row)
      //     }
      //   }
      // }).catch(e => {
      //   this.applyCountrylList = []
      //   this.totalApply = 0
      //   this.continentList = []
      // })
    },
    // 分配国家 权限选择
    cellMouseEnter(row) {
      $(".nationalList._" + row.countryId + " .el-icon-close").show()
    },
    cellLeaveEnter() {
      $(".nationalList .el-icon-close").hide()
    },
    // 分配国家 表格高度
    nationalArea() {
      var _this = this;
      setTimeout(() => {
        if ($(".nationalInfo").length != 0) {
          var allTable = $(".nationalInfo").outerHeight(true);
          _this.accreditTableHeight = allTable;
        }
      }, 80)
      window.addEventListener("resize", function () {
        setTimeout(() => {
          if ($(".nationalInfo").length != 0) {
            var tableArea = $(".nationalInfo").outerHeight(true);
            _this.accreditTableHeight = tableArea;
          }
        }, 80)
      })
    },

    getDepartmentName(row) {
      let name = "";
      let dId = row.deptId
      if (!dId) {
        return "";
      }
      for (let i = 0; i < this.departmentList.length; i++) {
        const en = this.departmentList[i];
        if (en.id == dId) {
          name = en.name
          break
        }
        const arr = en.children
        if (!arr || arr.length <= 0) {
          continue;
        }
        for (let j = 0; j < arr.length; j++) {
          const item = arr[j];
          if (item.id == dId) {
            name = en.name + " " + item.name
            break
          }
        }
        if (name.length > 0) {
          break
        }
      }
      return name;
    },

    // 用户信息报告高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },


    // 同步
    batchSyncClick() {
      if (!this.deleteList || this.deleteList.length <= 0) {
        this.$DonMessage.warning("请选择要推送的用户");
        return;
      }
      let list = [];
      this.deleteList.forEach(u => list.push(u.id));
      this.$confirm('确定要将选中的用户同步至TIS系统吗?', '同步用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        syncUserSendApi(list).then(res => {
          if (res.data.code == 100) {
            this.$DonMessage.success(this.$t('successTip.syncTip'));
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        }).catch(e => {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        })
      })
    },

    // 查询角色名称
    getRoleName(row) {
      let role = this.roleMap.get(row.defaultRoleCode);
      return role ? role.name : "";
    },

    // 查询部门名称
    getDeptName(row) {
      let name = this.departmentMap.get(row.deptId);
      return name ? name : "";
    }


  },
  mounted() {
    // this.findCountryAll()
    this.dataList()
    this.getUserRoleList()
    this.getDepartment()
    // this.getUserCountryList()
    // this.getUserTrainTree()
    this.userData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
