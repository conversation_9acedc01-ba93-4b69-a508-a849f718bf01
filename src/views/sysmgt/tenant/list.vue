<!--租户管理-->
<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFour" :model="formInline" class="demo-form-inline">
        <el-form-item label="租户名称" prop="realName">
          <el-input v-model.trim="formInline.name" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="loginName">
          <el-input v-model.trim="formInline.contacts" placeholder="请输入联系人"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle"
        v-if="hasPerm('menuAsimss6ATenant_101') || hasPerm('menuAsimss6ATenant_102') || hasPerm('menuAsimss6ATenant_103') || hasPerm('menuAsimss6ATenant_122')">
        <el-button type="text" v-if="hasPerm('menuAsimss6ATenant_101')" icon="el-icon-plus"
          @click="addClcik()">新增</el-button>
      </div>
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="名称" prop="name" min-width="100"></el-table-column>
        <el-table-column label="联系人" prop="contacts" min-width="100"></el-table-column>
        <el-table-column label="手机号" prop="mobile" min-width="120"></el-table-column>
        <!--        <el-table-column label="电话" prop="telephone" min-width="120"></el-table-column>-->
        <el-table-column label="邮箱" prop="email" min-width="150"></el-table-column>
        <el-table-column label="状态" prop="useFlag" width="60">
          <template slot-scope="{row}">
            <span v-if="row.useFlag" class="successColor">生效</span>
            <span v-else class="errorColor">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="160">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss6ATenant_103')" type="text" size="small"
              @click="editopen(row)">编辑</el-button>
            <el-button v-if="hasPerm('menuAsimss6ATenant_122')" type="text" size="small"
              @click="accredit(row)">授权</el-button>
            <el-button v-if="hasPerm('menuAsimss6ATenant_102')" type="text" size="small" class="deleteButton"
              @click="delectClick(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
      <!-- 新增，编辑 -->
      <el-dialog v-dialogDrag lock-scroll :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false" v-if="dialogFormVisible">
        <!-- 新增，编辑 -->
        <el-form v-if="dialogStatus === 'edit' || dialogStatus === 'add'" :label-width="formLabelWidth" ref='temp'
          :model="temp" :rules="submitTenantRole" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="temp.name" placeholder="请输入租户名称" limit="limit" show-word-limit
              maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="管理员" prop="administratorsId" v-if="dialogStatus === 'edit'">
            <el-select v-model="administratorsId" filterable @change="getAdministrators">
              <el-option v-for="(item, index) in userLists" :key="index"
                :label="item.realName + '(' + item.username + ')'" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="管理员账户" prop="username" v-if="dialogStatus === 'add'">
            <el-input v-model.trim="temp.username" placeholder="请输入管理员账户" limit="limit" show-word-limit
              maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="管理员名称" prop="contacts">
            <el-input :disabled="dialogStatus === 'edit'" v-model.trim="temp.contacts" placeholder="请输入管理员名称"
              limit="limit" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="管理员手机号" prop="mobile">
            <el-input :disabled="dialogStatus === 'edit'" v-model.trim="temp.mobile" placeholder="请输入管理员手机号"
              limit="limit" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="管理员邮箱" prop="email">
            <el-input :disabled="dialogStatus === 'edit'" v-model.trim="temp.email" placeholder="请输入管理员邮箱" limit="limit"
              show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item v-if="dialogStatus === 'edit'" label="发布控制级别" prop="email">
            <el-select v-model.trim="temp.publishLeave" placeholder="请选择">
              <el-option v-for="item in publishLeaveOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="introduce">
            <el-input v-model.trim="temp.introduce" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input rows="2" v-model.trim="temp.address" limit="limit" show-word-limit maxlength="100"
              placeholder="请输入地址"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="temp.useFlag"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'edit' ? editClick() : addClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>

        <!-- 授权 -->
        <el-form v-if="dialogStatus === 'accredit'" :label-width="formLabelWidth" ref='accreditForm'
          :model="accreditForm" :rules="accreditFormRules" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="租户名称" prop="name">
            <el-input v-model.trim="aboriginalRow.name" disabled></el-input>
          </el-form-item>
          <el-form-item label="授权手机号" prop="mobile" required>
            <el-input v-model.trim="accreditForm.mobile" placeholder="请输入授权手机号" limit="limit" show-word-limit
              maxlength="11"></el-input>
          </el-form-item>
          <el-form-item label="授权默认角色" prop="defaultRoleId" required>
            <el-select v-model="accreditForm.defaultRoleId" placeholder="请选择默认角色" style="width: 100%;">
              <el-option v-for="role in defaultRoleOptions" :key="role.id" :label="role.name" :value="role.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账户" prop="username">
            <el-input v-model.trim="accreditForm.username" placeholder="默认是手机号" limit="limit" show-word-limit
              maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label="用户名称" prop="realName">
            <el-input v-model.trim="accreditForm.realName" placeholder="可随机生成" limit="limit" show-word-limit
              maxlength="30"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="submitAccredit">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  tenantFindPage,
  tenantAddApi,
  tenantEditApi,
  tenantDelApi,
  tenantAllUserApi,
  tenantAdministratorsApi,
  userRoleData,
  tenantDeptApi,
  accreditUser, requestTenantDetail
} from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_tenant_list',
  components: { Pagination },
  data() {
    return {
      publishLeaveOptions: [
        {
          id: 0,
          name: '严格模式'
        },
        {
          id: 1,
          name: '宽松模式'
        }
      ],
      formInline: {
        contacts: '',
        name: '',
      },
      temp: {
        // 租户ID
        id: '',
        // 租户名称
        name: '',
        // 联系人
        contacts: '',
        // 手机号
        mobile: '',
        // 电话
        telephone: '',
        // 邮箱
        email: '',
        // 地址
        address: '',
        // 图标
        icon: '',
        // 备注
        introduce: '',
        // 状态
        useFlag: '',
        // 账户
        username: '',
        /**
         * 发布控制级别
         * 0:严格模式
         * 1:宽松模式
         */
        publishLeave: 0,
      },

      // 授权表单
      accreditForm: {
        mobile: '',
        username: '',
        realName: '',
        defaultRoleId: '',
        deptId: ''
      },
      submitTenantRole: {
        name: [{ required: true, message: '租户名称不能为空', trigger: ['blur', 'change'] }],
        username: [{ required: true, message: '管理员账户不能为空', trigger: ['blur', 'change'] }],
        contacts: [{ required: true, message: '联系人不能为空', trigger: ['blur', 'change'] }],
        mobile: [{ required: true, message: '手机号不能为空', trigger: ['blur', 'change'] }],
        logoPath: [{ required: true, message: '图片不能为空', trigger: ['blur', 'change'] }],
      },
      // 授权表单验证规则
      accreditFormRules: {
        mobile: [
          { required: true, message: '手机号不能为空', trigger: ['blur', 'change'] },
          { pattern: /^1\d{10}$/, message: '请输入正确的手机号格式', trigger: ['blur', 'change'] }
        ],
        defaultRoleId: [
          { required: true, message: '默认角色不能为空', trigger: ['blur', 'change'] }
        ]
      },
      // 默认角色选项
      defaultRoleOptions: [],
      // 部门选项
      deptOptions: [],

      // 租户下的用户
      userLists: [],
      administratorsId: '',

      perms: [],
      sltUserRole: {
        id: '',
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      },
      sltUserCountry: {
        userName: '',
        defaultCountry: '',
        countryArr: []
      },
      dialogFormVisible: false,
      formLabelWidth: '120px',
      resultList: [],
      dialogStatus: '',
      textMap: {
        edit: '编辑租户',
        add: '新增租户',
        accredit: '授权租户',
      },
      defaultProps: {
        parent: 'path',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          return val.children && val.children.length > 0;
        }
      },
      deleteList: [],
      aboriginalRow: {},
      userInfo: {},
      pagesize: 10,
      currentPage: 1,
      total: 0,
      whether: false,
      userRoleList: [],
      roleMap: new Map(),
      userCountryList: [],
      fileList: [],
      uploadFileList: [],

      departmentList: [],
      // 车型和人员的关联
      filterText: '',
      trainUser: '',
      userTrainTree: [],
      checkedTrain: [],
      dialogTrainTreeVisible: false,
      defaultTrainProps: {
        children: 'children',
        label: 'name'
      },
      maximumHeight: 0,
      // 分配国家
      // 搜索条件
      countryInfo: {
        country: '',
        continent: '',
      },
      countryTitle: '分配国家',
      totalApply: 0,
      currentPageApply: 1,
      pagesizeApply: 15,
      // 列表中的
      applyCountrylLists: [],
      // 所有的
      applyCountrylList: [],
      applyCountryTotalLists: [],
      determineModelList: [],
      dialogCountryVisible: false,
      selectNum: 0,
      continentList: [],
      accreditTableHeight: 0,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.trainTree.filter(val);
    },
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 列表数据
    dataList() {
      let param = {
        'name': this.formInline.name,
        'contacts': this.formInline.contacts
      }

      tenantFindPage(this.currentPage + "/" + this.pagesize, param).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea()
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },


    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },

    resetTemp() {
      this.temp = {
        // 租户ID
        id: '',
        // 租户名称
        name: '',
        // 联系人
        contacts: '',
        // 手机号
        mobile: '',
        // 电话
        telephone: '',
        // 邮箱
        email: '',
        // 地址
        address: '',
        // 图标
        icon: '',
        // 备注
        introduce: '',
        // 状态
        useFlag: '',
        // 账户
        username: '',
        /**
         * 发布级别
         */
        publishLeave: 0
      }
      this.whetherState()
      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
    },
    whetherState() {

      this.whether = this.temp.useFlag
    },
    selectState() {

      this.temp.useFlag = this.whether
    },
    changeUserType(val) {
      this.temp.userType = val
    },
    // 新增
    addClcik() {
      var _this = this
      _this.dialogFormVisible = true
      _this.dialogStatus = 'add'
      _this.resetTemp()
      _this.whether = true
    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['temp'].validateField('deptId')
      }
    },
    addClick() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          this.selectState()
          let roleId = '';
          for (let i = 0; i < this.userRoleList.length; i++) {
            if (this.temp.defaultRoleCode == this.userRoleList[i].code) {
              roleId = this.userRoleList[i].id
              break
            }
          }
          let params = {
            // 租户名称
            name: this.temp.name,
            // 联系人
            contacts: this.temp.contacts,
            // 手机号
            mobile: this.temp.mobile,
            // 邮箱
            email: this.temp.email,
            // 地址
            address: this.temp.address,
            // 图标
            icon: this.temp.icon,
            // 备注
            introduce: this.temp.introduce,
            // 状态
            useFlag: this.temp.useFlag,
            // 账户
            userDTO: {
              username: this.temp.username,
              realName: this.temp.contacts,
              // 手机号
              mobile: this.temp.mobile,
              // 邮箱
              email: this.temp.email,
              userType: 1
            }

          }

          tenantAddApi(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.formInline = {
        contacts: '',
        name: '',
      }
      this.currentPage = 1
      this.dataList()
    },

    // 编辑
    editopen(row) {
      var _this = this
      _this.resetTemp()
      requestTenantDetail(row.id).then(res => {
        if (res.data.code === 100) {
          _this.temp = res.data.data
          if (!_this.temp.publishLeave) {
            _this.temp.publishLeave = 0
          }
          _this.aboriginalRow = _this.temp;
          _this.dialogStatus = 'edit'
          _this.whether = row.useFlag
          if (row === null || row.sex === null) {
            _this.temp.sex = 0
          } else {
            _this.temp.sex = row.sex
          }
          // 查询租户下的管理员
          tenantAdministratorsApi(row.id).then(res => {
            _this.administratorsId = res.data.data[0].id
            _this.dialogFormVisible = true
          }).catch(e => {
            _this.administratorsId = ''
            _this.dialogFormVisible = true
          })
          // 查询租户下的用户
          tenantAllUserApi(row.id).then(res => {
            _this.userLists = res.data.data
          }).catch(e => {
            _this.userLists = []
          })
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 授权
    accreditReset() {
      this.accreditForm = {
        mobile: '',
        username: '',
        realName: '',
        defaultRoleId: '',
        deptId: ''
      }
      this.$nextTick(function () {
        this.$refs.accreditForm.clearValidate();
      })
    },
    accredit(row) {
      var _this = this
      _this.aboriginalRow = row;
      _this.dialogStatus = 'accredit'
      _this.textMap.accredit = '授权租户'
      // 初始化对话框
      _this.dialogFormVisible = true
      _this.accreditReset();
      // 加载角色数据 - 使用userRoleData接口获取所有角色
      userRoleData().then(res => {
        if (res.data.code === 100) {
          _this.defaultRoleOptions = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg || '获取角色列表失败')
        }
      }).catch(err => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })

      // 加载租户部门数据
      tenantDeptApi(row.id).then(res => {
        if (res.data.code === 100) {
          _this.deptOptions = res.data.data || []
        } else {
          this.$DonMessage.error(res.data.msg || '获取部门列表失败')
        }
      }).catch(err => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },

    // 提交授权表单
    submitAccredit() {
      this.$refs['accreditForm'].validate((valid) => {
        if (!valid) {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
          return;
        }
        // 构造授权参数
        const params = {
          tenantId: this.aboriginalRow.id,
          mobile: this.accreditForm.mobile,
          username: this.accreditForm.username,
          realName: this.accreditForm.realName,
          roleId: this.accreditForm.defaultRoleId,
          deptId: this.accreditForm.deptId || null
        }

        // 调用授权接口
        accreditUser(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.submitTip'))
            this.dialogFormVisible = false
            this.dataList() // 刷新列表
          } else {
            this.$DonMessage.error(res.data.msg || "授权失败")
          }
        }).catch(err => {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        })
      });
    },
    getAdministrators(id) {
      for (let i = 0; i < this.userLists.length; i++) {
        if (id == this.userLists[i].id) {
          let administrators = this.userLists[i];
          this.temp.contacts = administrators.realName;
          this.temp.mobile = administrators.mobile ? administrators.mobile : "";
          this.temp.email = administrators.email ? administrators.email : "";
          break
        }
      }

    },
    // 提交编辑
    editClick() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          this.selectState()
          let params = {
            // 租户名称
            name: this.temp.name,
            // 联系人
            contacts: this.temp.contacts,
            // 手机号
            mobile: this.temp.mobile,
            // 邮箱
            email: this.temp.email,
            // 地址
            address: this.temp.address,
            // 图标
            icon: this.temp.icon,
            // 备注
            introduce: this.temp.introduce,
            // 状态
            useFlag: !this.temp.useFlag,
            publishLeave: this.temp.publishLeave,
            // 账户
            userDTO: {
              id: this.administratorsId
            }
          }

          tenantEditApi(this.temp.id, params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 删除
    delectClick(row) {
      this.$confirm('确定删除【' + row.name + '】的相关信息?', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tenantDelApi(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },

    // 获取 品牌-车型 结构树
    // getUserTrainTree(uid){
    //   var params = new URLSearchParams()
    //   params.append('userId', uid)
    //   getUserBrandTrainTree(params).then(res => {
    //       this.userTrainTree = res.data.data
    //   })
    // },

    // 用户信息
    userData() {
      getUserInfo().then(res => {
        this.userInfo = res.data.data
      })
    },
    // 获取全部的国家信息包含大洲
    findCountryAll() {
      this.continentList = []
      // getCountryAll().then(res => {
      //   this.applyCountrylList = res.data.data
      //   this.totalApply = res.data.total
      //   for (let i = 0; i < this.applyCountrylList.length; i++) {
      //     const row = this.applyCountrylList[i];
      //     let b = true
      //     for (let j = 0; j < this.continentList.length; j++) {
      //       const itm = this.continentList[j];
      //       if (itm.continentId === row.continentId) {
      //         b = false
      //         break;
      //       }
      //     }
      //     if(b){
      //       this.continentList.push(row)
      //     }
      //   }
      // }).catch(e => {
      //   this.applyCountrylList = []
      //   this.totalApply = 0
      //   this.continentList = []
      // })
    },
    // 分配国家 权限选择
    cellMouseEnter(row) {
      $(".nationalList._" + row.countryId + " .el-icon-close").show()
    },
    cellLeaveEnter() {
      $(".nationalList .el-icon-close").hide()
    },
    // 分配国家 表格高度
    nationalArea() {
      var _this = this;
      setTimeout(() => {
        if ($(".nationalInfo").length != 0) {
          var allTable = $(".nationalInfo").outerHeight(true);
          _this.accreditTableHeight = allTable;
        }
      }, 80)
      window.addEventListener("resize", function () {
        setTimeout(() => {
          if ($(".nationalInfo").length != 0) {
            var tableArea = $(".nationalInfo").outerHeight(true);
            _this.accreditTableHeight = tableArea;
          }
        }, 80)
      })
    },

    getDepartmentName(row) {
      let name = "";
      let dId = row.deptId
      if (!dId) {
        return "";
      }
      for (let i = 0; i < this.departmentList.length; i++) {
        const en = this.departmentList[i];
        if (en.id == dId) {
          name = en.name
          break
        }
        const arr = en.children
        if (!arr || arr.length <= 0) {
          continue;
        }
        for (let j = 0; j < arr.length; j++) {
          const item = arr[j];
          if (item.id == dId) {
            name = en.name + " " + item.name
            break
          }
        }
        if (name.length > 0) {
          break
        }
      }
      return name;
    },

    // 用户信息报告高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },


    // 同步
    batchSyncClick() {
      if (!this.deleteList || this.deleteList.length <= 0) {
        this.$DonMessage.warning("请选择要推送的用户");
        return;
      }
      let list = [];
      this.deleteList.forEach(u => list.push(u.id));
      this.$confirm('确定要将选中的用户同步至TIS系统吗?', '同步用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        syncUserSendApi(list).then(res => {
          if (res.data.code == 100) {
            this.$DonMessage.success(this.$t('successTip.syncTip'));
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        }).catch(e => {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        })
      })
    },

    // 查询角色名称
    getRoleName(row) {
      let role = this.roleMap.get(row.defaultRoleCode);
      return role ? role.name : "";
    },

    // 查询部门名称
    getDeptName(row) {
      let name = this.departmentMap.get(row.deptId);
      return name ? name : "";
    }


  },
  mounted() {
    // this.findCountryAll()
    this.dataList()
    // this.getUserRoleList()
    // this.getDepartment()
    // this.getUserCountryList()
    // this.getUserTrainTree()
    // this.userData()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style>
/* 授权角色样式 */
.role-select {
  border: 1px solid #EBEEF5;
  border-radius: var(--border-radius);
  margin-bottom: 10px;
}

.role-select .role-title {
  background-color: #F5F7FA;
  padding: 10px 15px;
  font-weight: bold;
  border-bottom: 1px solid #EBEEF5;
}

.role-select .role-options {
  padding: 15px;
  min-height: 150px;
  max-height: 250px;
  overflow-y: auto;
}

.role-select .el-radio {
  display: block;
  margin-left: 0;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  width: 100%;
}

.role-select .el-checkbox {
  display: block;
  margin-left: 0;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  width: 100%;
}

.role-select .el-radio:last-child,
.role-select .el-checkbox:last-child {
  border-bottom: none;
}

.role-select .el-radio+.el-radio,
.role-select .el-checkbox+.el-checkbox {
  margin-left: 0;
}
</style>
