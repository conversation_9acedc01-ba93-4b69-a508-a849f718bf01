<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" :label-width="$labelFour" :model="searchForm"
        class="demo-form-inline">
        <el-form-item label="用户名" prop="operatorName">
          <el-input v-model.trim="searchForm.operatorName" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="模块" prop="module">
          <el-select v-model="searchForm.module" clearable filterable>
            <el-option v-for="(item, index) in moduleList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker align="center" prop="startDate" value-format="yyyy-MM-dd" type="date" placeholder="选择开始日期"
            v-model="searchForm.startDate" :picker-options="pickerBeginTime"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker align="center" prop="endDate" value-format="yyyy-MM-dd" type="date" placeholder="选择结束日期"
            v-model="searchForm.endDate" :picker-options="pickerEndTime"></el-date-picker>
        </el-form-item>
        <el-form-item label="请求结果" prop="executeResult">
          <el-select v-model="searchForm.executeResult" clearable filterable>
            <el-option v-for="(item, index) in resultListInfo" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('searchForm')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="50"></el-table-column>
        <el-table-column label="用户名" prop="operatorName" width="100">
        </el-table-column>
        <el-table-column label="模块" prop="module" width="100"></el-table-column>
        <el-table-column label="操作类型" prop="type" width="100"></el-table-column>
        <el-table-column label="操作内容" prop="content" min-width="200"></el-table-column>
        <el-table-column label="请求URL" prop="requestUrl" min-width="150"></el-table-column>
        <el-table-column label="请求方式" prop="httpRequestMethod" width="100"></el-table-column>
        <el-table-column label="请求参数" prop="requestParam" min-width="100"></el-table-column>
        <el-table-column label="请求时长" prop="executeTime" width="120">
          <template slot-scope="{row}">
            <span v-if="row.executeTime != null && row.executeTime != ''">{{ row.executeTime }}ms</span>
          </template>
        </el-table-column>
        <el-table-column label="请求结果" prop="executeResult" width="100">
          <template slot-scope="{row}">
            <span v-if="row.executeResult == 0" class="errorColor">失败</span>
            <span v-if="row.executeResult == 1" class="successColor">成功</span>
            <span v-if="row.executeResult == 2" class="warningColor">异常</span>
          </template>
        </el-table-column>
        <el-table-column label="操作IP" prop="requestIp" width="120"></el-table-column>
        <el-table-column label="创建时间" prop="operateTime" width="200">
          <template slot-scope="{row}">
            <div>
              {{ row.operateTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户代理" prop="requestUa" min-width="100"></el-table-column>
        <el-table-column label="错误/异常信息" fixed="right" width="150">
          <template slot-scope="{row}">
            <div v-if="row.executeResult == 2">
              <el-button type="text" @click="showLogStackInfo(row)" icon="el-icon-info">查看异常信息</el-button>
            </div>
            <div v-else-if="row.executeResult == 0">
              <span>{{ row.errorMsg }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
    </div>
    <el-dialog v-dialogDrag lock-scroll title="异常信息" :visible.sync="showLogStackVisible">
      <div>
        <div style="white-space: pre-line;">{{ logStackInfo.errorStack }}</div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  requestLogList,
  requestLogModularList,
  requestLogStackInfo
} from '@/api/sysmgt.js'
export default {
  name: 'sysLogMgt_List',
  components: { Pagination },
  data() {
    return {
      showLogStackVisible: false,
      searchForm: {
        executeResult: '',
        operatorName: '',
        startDate: '',
        endDate: '',
        module: ''
      },
      logStackInfo: {},
      resultListInfo: [
        {
          "code": "1",
          "name": "成功",
        }, {
          "code": "0",
          "name": "失败",
        }, {
          "code": "2",
          "name": "异常",
        }
      ],
      moduleList: [
      ],
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      pickerBeginTime: {
        disabledDate: (time) => {
          return this.searchForm.endDate != null ? time.getTime() > new Date(this.searchForm.endDate) : false //只能选结束日期之前的日期
          //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
        }
      },
      pickerEndTime: {
        disabledDate: (time) => {
          return this.searchForm.startDate != null ? time.getTime() < new Date(this.searchForm.startDate) : false //只能选开始时间之后的日期
          //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
        }
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.dataList()
    },
    dataList() {
      var _this = this
      var params = new URLSearchParams()
      params.append('executeResult', this.searchForm.executeResult)
      params.append('operatorName', this.searchForm.operatorName)
      params.append('startDate', this.searchForm.startDate)
      params.append('endDate', this.searchForm.endDate)
      params.append('module', this.searchForm.module)
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      requestLogList(params).then(res => {
        if (res.data.code == 100) {
          _this.total = res.data.data.total    // 总条数
          _this.resultList = res.data.data.records   // 数据
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea()
      }).catch(error => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    logModularList() {
      var _this = this
      requestLogModularList().then(res => {
        if (res.data.code == 100) {
          _this.moduleList = res.data.data  // 数据
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(error => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    showLogStackInfo(row) {
      this.logStackInfo = ""
      var _this = this
      var params = new URLSearchParams()
      params.append("logId", row.id)
      requestLogStackInfo(params).then(res => {
        if (res.data.code == 100) {
          _this.logStackInfo = res.data.data
          this.showLogStackVisible = true
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(error => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    reset(name) {
      if (this[name] != undefined) {
        Object.keys(this[name]).forEach(key => {
          this[name][key] = ''
        })
      }
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.logModularList()
    this.dataList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
