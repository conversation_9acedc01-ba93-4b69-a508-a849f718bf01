<template>
  <div class="layoutContainer cacheContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="8" class="leftData">
          <div>
            <div class="topButton">
              <span>缓存列表</span>
              <el-button type="text" icon="el-icon-refresh" @click="refreshCacheNames()">刷新</el-button>
            </div>
            <div class="scrollClass">
              <el-table v-loading="loading" :data="cacheNames" :max-height="tableHeight" highlight-current-row
                @row-click="getCacheKeys" style="width: 100%" border stripe>
                <el-table-column label="序号" width="60" type="index"></el-table-column>
                <el-table-column label="缓存名称" prop="cacheName"></el-table-column>
                <el-table-column label="备注" prop="remark" />
                <el-table-column label="操作" width="100" fixed="right">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" class="deleteButton"
                      @click="handleClearCacheName(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-col>
        <el-col :span="8" class="leftData">
          <div>
            <div class="topButton">
              <span>键名列表</span>
              <el-button type="text" icon="el-icon-refresh" @click="refreshCacheKeys()">刷新</el-button>
            </div>
            <div class="scrollClass">
              <el-table v-loading="subLoading" :data="cacheKeys" :max-height="tableHeight" highlight-current-row
                @row-click="handleCacheValue" style="width: 100%" border stripe>
                <el-table-column label="序号" width="60" type="index"></el-table-column>
                <el-table-column label="缓存键名" :formatter="keyFormatter">
                </el-table-column>
                <el-table-column label="操作" width="100" fixed="right">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" class="deleteButton"
                      @click="handleClearCacheKey(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-col>
        <el-col :span="8" class="fromRight">
          <div>
            <div class="formTitle">
              <span>缓存内容</span>
              <el-button type="text" icon="el-icon-delete" @click="handleClearCacheAll()">清除全部缓存</el-button>
            </div>
            <el-form ref="cacheForm" :model="cacheForm" label-width="75px">
              <el-form-item label="缓存名称" prop="cacheName">
                <el-input v-model="cacheForm.cacheName" placeholder="请输入缓存名称" disabled />
              </el-form-item>
              <el-form-item label="缓存键名" prop="cacheKey">
                <el-input v-model="cacheForm.cacheKey" placeholder="请输入缓存键名" disabled />
              </el-form-item>
              <el-form-item label="缓存内容" prop="cacheValue">
                <el-input v-model="cacheForm.cacheValue" type="textarea" :rows="8" disabled />
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { contentSize } from '@/assets/js/common.js'
import { clearCacheAll, clearCacheKey, clearCacheName, getCacheValue, listCacheKey, listCacheName } from "@/api/sysmgt";

export default {
  name: "sysmgt_cache_list",
  data() {
    return {
      cacheNames: [],
      cacheKeys: [],
      cacheForm: {},
      loading: false,
      subLoading: false,
      nowCacheName: "",
      tableHeight: 100
    };
  },
  created() {
    this.getCacheNames();
  },
  methods: {
    /** 查询缓存名称列表 */
    getCacheNames() {
      this.loading = true;
      listCacheName().then(response => {
        this.cacheNames = response.data.data;
        this.loading = false;
      });
    },
    /** 刷新缓存名称列表 */
    refreshCacheNames() {
      this.getCacheNames();
      this.$DonMessage.success(this.$t('successTip.refreshTip'));
    },
    /** 清理指定名称缓存 */
    handleClearCacheName(row) {
      clearCacheName(row.cacheName).then(response => {
        this.$DonMessage.success("清理缓存名称[" + row.cacheName + "]成功")
        this.getCacheKeys();
      });
    },
    /** 查询缓存键名列表 */
    getCacheKeys(row) {
      const cacheName = row !== undefined ? row.cacheName : this.nowCacheName;
      if (cacheName === "") {
        return;
      }
      this.subLoading = true;
      listCacheKey(cacheName).then(response => {
        this.cacheKeys = response.data.data;
        this.subLoading = false;
        this.nowCacheName = cacheName;
      });
    },
    /** 刷新缓存键名列表 */
    refreshCacheKeys() {
      this.getCacheKeys();
    },
    /** 清理指定键名缓存 */
    handleClearCacheKey(cacheKey) {
      clearCacheKey(cacheKey).then(response => {
        this.$DonMessage.success("清理缓存键名[" + cacheKey + "]成功")
        this.getCacheKeys();
      });
    },
    /** 列表前缀去除 */
    nameFormatter(row) {
      return row.cacheName.replace(":", "");
    },
    /** 键名前缀去除 */
    keyFormatter(cacheKey) {
      return cacheKey.replace(this.nowCacheName, "");
    },
    /** 查询缓存内容详细 */
    handleCacheValue(cacheKey) {
      getCacheValue(this.nowCacheName, cacheKey).then(response => {
        this.cacheForm = response.data.data;
      });
    },
    /** 清理全部缓存 */
    handleClearCacheAll() {
      this.$confirm('确认清理全部缓存？', '清理全部缓存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        clearCacheAll().then(response => {
          this.$DonMessage.success('清理全部缓存成功')
        });
      })
    },
    tableArea() {
      setTimeout(() => {
        var allHeight = $(".scrollClass").height() - 5;
        this.tableHeight = allHeight;
      }, 120);
    },
    tableSize() {
      var _this = this;
      _this.tableArea();
      window.addEventListener("resize", function () {
        _this.tableArea();
      });
    },
  },
  mounted() {
    contentSize()
    this.tableSize();
  },
};
</script>
<style>
.cacheContainer .leftData>div .topButton {
  border-bottom: none;
}

.cacheContainer .infoDetail .fromRight .el-form-item {
  padding: 10px 30px 10px 20px;
}

.cacheContainer .scrollClass .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.5) !important;
}

.cacheContainer .infoDetail .leftData .topButton,
.cacheContainer .fromRight .formTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
