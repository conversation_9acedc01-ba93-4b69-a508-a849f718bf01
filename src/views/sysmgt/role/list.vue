<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelThree" :model="formInline" class="demo-form-inline">
        <el-form-item label="角色名" prop="rolename">
          <el-input v-model.trim="formInline.rolename" placeholder="请输入角色名"></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model.trim="formInline.code" placeholder="请输入编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss1A3B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addHandle()">新增</el-button>
      </div>
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="角色名" prop="name" min-width="150"></el-table-column>
        <el-table-column label="编码" prop="code" min-width="150"></el-table-column>
        <el-table-column label="层级" prop="level" width="100"></el-table-column>
        <el-table-column label="备注" prop="memo" min-width="150"></el-table-column>
        <el-table-column label="状态" prop="useFlag" width="100">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === '1'" class="successColor">生效</span>
            <span v-if="row.useFlag === '0'" class="errorColor">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_103')" size="small"
              @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_102')" class="deleteButton" size="small"
              @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false">
        <el-form ref='dataForm' :label-width="formLabelWidth" :model="dataForm" :rules="fromrules"
          label-position="center">
          <el-form-item label="角色名" prop="name">
            <el-input v-model.trim="dataForm.name" limit="limit" show-word-limit maxlength="30"
              placeholder="请输入角色名"></el-input>
          </el-form-item>
          <el-form-item label="编码" prop="code">
            <el-input v-model.trim="dataForm.code" limit="limit" show-word-limit maxlength="20"
              oninput="value=value.replace(/[^\w+$]/g,'')" placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="memo">
            <el-input v-model.trim="dataForm.memo" limit="limit" show-word-limit maxlength="100"
              placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="层级" prop="level">
            <el-input-number v-model.trim="dataForm.level" placeholder="请输入层级" controls-position="right" :min="1"
              :max="99" :precision="0" :step="1"></el-input-number>
            <span>数值越小，等级越高</span>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="whether"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick() : editClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { roleData, roleAdd, roleEdit, roleDel } from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_role_list',
  components: { Pagination },
  data() {
    return {
      formInline: {
        rolename: '',
        code: ''
      },
      dataForm: {
        id: '',
        name: '',
        code: '',
        level: 1,
        memo: '',
        useFlag: '1'
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      dialogStatus: '',
      textMap: {
        edit: '编辑角色',
        add: '新增角色'
      },
      id: '',
      whether: false,
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      fromrules: {
        name: [{ required: true, message: '角色名不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '编号不能为空', trigger: ['blur', 'change'] }]
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('name', this.formInline.rolename)
      params.append('code', this.formInline.code)
      roleData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea()
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    // 重置
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    resetTemp() {
      this.dataForm = {
        id: '',
        name: '',
        code: '',
        level: 1,
        memo: '',
        useFlag: 1
      }
      this.whetherState();
      this.$nextTick(function () {
        this.$refs.dataForm.clearValidate();
      })
    },
    // 新增
    addHandle() {
      this.dialogStatus = 'add'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    whetherState() {
      if (this.dataForm.useFlag == 0) {
        this.whether = false
      } else {
        this.whether = true
      }
    },
    useFlagState() {
      if (this.whether === false) {
        this.dataForm.useFlag = 0
      } else {
        this.dataForm.useFlag = 1
      }
    },
    addClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.useFlagState()
          let params = {
            'name': this.dataForm.name,
            'code': this.dataForm.code,
            'level': this.dataForm.level,
            'memo': this.dataForm.memo,
            'useFlag': this.dataForm.useFlag
          }
          roleAdd(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 编辑
    handleEdit(row) {
      this.dialogFormVisible = true
      this.resetTemp()
      this.dialogStatus = 'edit'
      this.dataForm = Object.assign({}, row)
      this.whetherState()
    },
    editClick() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.useFlagState()
          let params = {
            'name': this.dataForm.name,
            'code': this.dataForm.code,
            'level': this.dataForm.level,
            'memo': this.dataForm.memo,
            'useFlag': this.dataForm.useFlag
          }
          roleEdit(this.dataForm.id, params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList()
              this.dialogFormVisible = false
            } else {
              if (res.data.code === 404) {
                this.$DonMessage.error('系统出现异常，更新失败')
              } else {
                this.$DonMessage.error(res.data.msg)
              }
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 删除
    handleDelete(row) {
      var _this = this
      this.currentPage = 1
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('defaultRoleCode', row.code)
      this.$confirm('确定刪除【' + row.name + '】的相关信息?', '删除角色', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.roledelClick(row.id);
      })
    },
    roledelClick(id) {
      roleDel(id).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          if (this.resultList != null && this.resultList.length == 1) {
            this.currentPage = this.currentPage - 1
          }
          this.dataList()
        } else {
          this.$DonMessage.error('删除失败: ' + res.data.msg)
        }
      })
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.dataList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
