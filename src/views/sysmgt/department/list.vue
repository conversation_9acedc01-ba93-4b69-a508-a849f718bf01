<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="7" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss1A6B_101') || hasPerm('menuAsimss1A6B_102')">
              <el-button type="text" v-if="hasPerm('menuAsimss1A6B_101') && typeFlag" icon="el-icon-plus"
                @click="handelAddDept">{{ addTitle }}</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss1A6B_102')" icon="el-icon-delete"
                @click="handelDel">删除</el-button>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :default-expanded-keys="nodeKeyList" :data="listdata"
                  :props="defaultProps" @node-click="handleNodeClick"></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="17" class="fromRight">
          <div class="formTitle" v-html="editTitle"></div>
          <el-form ref="form" :label-width="formLabelWidth" :model="form" :rules="rules">
            <el-form-item label="名称" prop="name">
              <el-input placeholder="请输入名称" v-model.trim='form.name' limit="limit" show-word-limit
                maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <!--              <el-select v-model="form.type" :disabled="true">-->
              <!--                <el-option label="部门" value="dept"></el-option>-->
              <!--                <el-option label="小组" value="group"></el-option>-->
              <!--              </el-select>-->
              <el-select v-model="form.type" clearable filterable :disabled="true">
                <el-option v-for="(item, index) in typeList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model.trim="form.sort" placeholder="请输入排序" controls-position="right" :min="1"
                :max="9999" :precision="0" :step="1"></el-input-number>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input placeholder="请输入备注" v-model.trim='form.remark' limit="limit" show-word-limit
                maxlength="100"></el-input>
            </el-form-item>
            <el-form-item class="butArea">
              <el-button v-show="butType === ''" v-if="hasPerm('menuAsimss1A6B_101') || hasPerm('menuAsimss1A6B_103')"
                type="primary" @click="preserve()">{{ $t('button.submit') }}</el-button>
              <el-button v-show="butType === 'addNodeBut'" type="primary" @click="determine()">{{ $t('button.submit')
                }}</el-button>
              <!-- <el-button v-show="butType==='addNodeBut'" @click="resetForm()">重置</el-button> -->
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { contentSize } from '@/assets/js/common.js'
import {
  departmentDel,
  findDeptType,
  findDeptAll,
  addDeptApi,
  editDeptApi
} from '@/api/sysmgt.js'

export default {
  name: 'sysmgt_department_list',
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      parentId: "",
      nodeKeyList: [],
      listdata: [],
      addType: '',
      butType: '',
      formLabelWidth: '100px',
      editTitle: '当前信息',
      isDept: false,  // 2023-02-08 当点击部门时，出现 新增小组
      form: {
        id: "",
        pid: "",
        type: "",
        name: "",
        remark: "",
        sort: 1,
        children: [],
      },
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
      },

      // 类型
      typeList: [],
      // 类型索引
      typeIndex: 0,
      addTitle: '',
      addTitlePrefix: '新增',
      typeFlag: true,
    }
  },
  methods: {
    // 数据
    dataList() {
      findDeptAll().then(res => {
        this.listdata = res.data.data
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.form.id);
        });
      })
    },
    resetTemp() {
      this.form = {
        id: "",
        pid: "",
        type: "",
        name: "",
        remark: "",
        sort: 1,
        children: [],
      }
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      })
    },
    // 当前信息
    assignment(data) {
      this.butType = ''
      this.editTitle = "当前目录节点信息"
      this.form.id = data.id
      this.parentId = data.id
      this.form.path = data.path
      this.form.type = data.type
      this.form.name = data.name
      this.form.sort = data.sort
      this.form.remark = data.remark
      this.form.children = data.children
    },
    handleNodeClick(data) {
      this.resetTemp()
      this.assignment(data)

      // 修改 新增xx
      const len = this.typeList.length
      for (let i = 0; i < len; i++) {
        if (this.form.type === this.typeList[i].code) {
          this.typeIndex = i + 1;
          this.typeFlag = this.typeIndex < len
          break
        }
      }
      if (this.typeFlag) {
        this.addTitle = this.addTitlePrefix + this.typeList[this.typeIndex].name
      }
    },
    // 新增部门
    handelAddDept() {
      let path = this.form.path;
      this.resetTemp()
      this.form.path = path
      this.butType = 'addNodeBut'
      this.editTitle = this.addTitle
      this.form.type = this.typeList[this.typeIndex].code
    },

    //确定
    determine() {
      this.nodeKeyList = []
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = {
            "name": this.form.name,
            "path": this.form.path,
            "remark": this.form.remark,
            "sort": this.form.sort,
            "type": this.form.type
          }
          addDeptApi(params).then(res => {
            if (res.data.code == 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.nodeKeyList.push(this.parentId)
              this.resetTemp()
              this.butType = ''
              this.editTitle = "当前信息"
              this.dataList()
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          }).catch(function (error) {
            this.$DonMessage.error(this.$t('errorTip.systemTip'));
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 保存
    preserve() {
      this.nodeKeyList = []
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = {
            "name": this.form.name,
            "remark": this.form.remark,
            "sort": this.form.sort,
            "type": this.form.type
          }
          editDeptApi(this.form.id, params).then(res => {
            if (res.data.code == 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.nodeKeyList.push(this.form.id)
              this.butType = ''
              this.editTitle = "当前信息"
              this.dataList()
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          }).catch(function (error) {
            this.$DonMessage.error(this.$t('errorTip.systemTip'));
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 重置
    // resetForm(){
    //   this.resetTemp()
    // },
    // 删除
    handelDel() {
      if (this.form.id == null || this.form.id.length <= 0) {
        this.$DonMessage.warning('请先选中要删除的节点')
        return false
      }
      this.nodeKeyList = []
      if (this.form.children !== null && this.form.children.length > 0) {
        this.$DonMessage.warning('有子节点无法删除')
        return
      }
      var id = this.form.id
      this.$confirm('确定删除【' + this.form.name + '】的目录节点信息?', '删除目录节点', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        departmentDel(id).then(res => {
          if (res.data.code == 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.nodeKeyList.push(this.form.pid)
            this.resetTemp()
            this.butType = ''
            this.editTitle = "当前目录节点信息"
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },

    getDeptType() {
      findDeptType().then(res => {
        this.typeList = res.data.data;
        this.typeFlag = true;
        this.addTitle = this.addTitlePrefix + this.typeList[this.typeIndex].name
      }).catch(e => {
        this.typeList = []
      })
    }
  },
  mounted() {
    this.typeIndex = 0;
    this.getDeptType();
    this.dataList()
    contentSize()

  },
}
</script>
