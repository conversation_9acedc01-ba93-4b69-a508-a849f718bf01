<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFive" :model="formInline" class="demo-form-inline">
        <el-form-item label="服务店代码" prop="realCode">
          <el-input v-model.trim="formInline.realCode" placeholder="请输入服务店代码"></el-input>
        </el-form-item>
        <el-form-item label="服务店名称" prop="realName">
          <el-input v-model.trim="formInline.realName" placeholder="请输入服务店名称"></el-input>
        </el-form-item>
        <el-form-item label="登录账户" prop="loginName">
          <el-input v-model.trim="formInline.loginName" placeholder="请输入登录账号"></el-input>
        </el-form-item>
        <!-- <el-form-item label="所属国家" prop="userCountry">
          <el-select v-model="formInline.userCountry" clearable filterable>
            <el-option v-for="(item, index) in userCountryList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_101')" icon="el-icon-plus" @click="addClcik()">
          新增服务店
        </el-button>
        <!-- <el-button type="text" v-if="hasPerm('menuAsimss1A2B_102')" class="deleteButton"
          @click="batchDelClick()"><i class="el-icon-delete"></i>批量删除</el-button> -->
        <!-- <el-upload class="upload-demo inline-block" ref="elUpload" action="#"
          :show-file-list="false" :limit="1" :file-list="fileList" :before-upload="onBeforeUpload"
          :http-request="uploadFile" :on-change="onUploadChange">
          <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_107')" size="min" icon="bulkImport-icon">批量上传</el-button>
        </el-upload> -->
        <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_107')" size="min" icon="bulkImport-icon"
          @click="batchUpload()">
          批量上传
        </el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_108')" icon="bulkDown-icon"
          @click="batchExport()">批量下载
        </el-button>
        <!-- <el-button type="text"  icon="el-icon-download"
          @click="downTemplateClick()">下载模板</el-button> -->
      </div>
      <!-- :load="loadLazyData" -->
      <el-table ref="multipleTable" border row-key="id" style="width:100%" highlight-current-row :lazy="true"
        :load="loadLazyData" :max-height="maximumHeight" :data="resultList" :default-expand-all="false"
        @header-dragend="changeColWidth" @selection-change="handleSelectionChange" @select-all="selectAll"
        :row-style="rowStyle" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="服务店代码" prop="stationCode" min-width="200"></el-table-column>
        <el-table-column label="服务店名称" prop="realName" min-width="200"></el-table-column>
        <el-table-column label="登录账户" prop="username" min-width="120"></el-table-column>
        <el-table-column label="所属国家" prop="defaultCountryName" min-width="100"></el-table-column>
        <el-table-column label="联系人" prop="contacts" min-width="100"></el-table-column>
        <el-table-column label="性别" prop="sex" min-width="50">
          <template slot-scope="{row}">
            <span v-if="row.sex == 0">男</span>
            <span v-else>女</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号" prop="mobile" width="120"></el-table-column>
        <el-table-column label="邮箱" prop="email" width="150"></el-table-column>
        <el-table-column label="地址" prop="address" min-width="160"></el-table-column>
        <el-table-column label="状态" prop="useFlag" width="70">
          <template slot-scope="{row}">
            <span v-if="row.useFlag" class="successColor">生效</span>
            <span v-else class="errorColor">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="300">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_101') && row.useFlag == 1" size="small"
              @click="editUseFlag(row, 0)">
              停用
            </el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_101') && row.useFlag == 0" size="small"
              @click="editUseFlag(row, 1)">
              启用
            </el-button>
            <!-- 2023-12-22 隐藏 -->
            <el-button v-if="row.pid == 0 && hasPerm('menuAsimss1AdealerMgt_101')" type="text" size="small"
              @click="addClcik(row)">
              新增账户
            </el-button>
            <!-- <el-button v-if="row.pid == 0 && hasPerm('menuAsimss1AdealerMgt_101')" type="text" size="small"
            @click="addSubAccount(row)">批量新增二级</el-button> -->
            <!-- <el-button v-if="hasPerm('menuAsimss1AdealerMgt_113')" type="text" size="small"
              @click="distriTrain(row)">分配车型</el-button> -->
            <el-button v-if="hasPerm('menuAsimss1AdealerMgt_124')" type="text" size="small" @click="initPsw(row)">
              重置密码
            </el-button>
            <el-button v-if="hasPerm('menuAsimss1AdealerMgt_103')" type="text" size="small" @click="editopen(row)">
              编辑
            </el-button>
            <el-button type="text" class="deleteButton" v-if="hasPerm('menuAsimss1AdealerMgt_102')" size="small"
              @click="delectClick(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="pageLimitList" />

      <!-- 新增，编辑 -->
      <el-dialog v-dialogDrag lock-scroll
        :title="textMap[dialogStatus] + (temp.pid != null && temp.pid > 0 ? '账户' : '服务店')"
        :visible.sync="dialogFormVisible">
        <!-- 新增，编辑 -->
        <el-form v-if="dialogStatus === 'edit' || dialogStatus === 'add'" ref='temp' :rules="fromTemp" :model="temp"
          label-position="center" :validate-on-rule-change="false" :label-width="formLabelWidth">
          <el-form-item label="登录账户" prop="username" v-if="temp.pid != null && temp.pid > 0">
            <el-input v-if="dialogStatus === 'add'" v-model.trim="temp.username" placeholder="请输入账号" limit="limit"
              show-word-limit maxlength="20"></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username !== ''" v-model.trim="temp.username" limit="limit"
              show-word-limit maxlength="20" :disabled="true"></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username === ''" v-model.trim="temp.username" limit="limit"
              show-word-limit maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="服务店代码" prop="stationCode">
            <el-input v-model.trim="temp.stationCode" placeholder="请输入服务店代码" limit="limit" show-word-limit
              maxlength="50" :disabled="temp.pid != null && temp.pid > 0"></el-input>
          </el-form-item>
          <el-form-item label="服务店名称" prop="realName">
            <el-input v-model.trim="temp.realName" placeholder="请输入服务店名称" limit="limit" show-word-limit maxlength="50"
              :disabled="temp.pid != null && temp.pid > 0"></el-input>
          </el-form-item>

          <!-- 2023-08-01 添加默认国家 -->
          <el-form-item label="所属国家" prop="defaultCountryCode">
            <el-select v-model="temp.defaultCountryCode" clearable filterable disabled>
              <el-option v-for="(item, index) in userCountryList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="所属城市" prop="cityId">
            <select-tree ref="modelSelectTree"
              :options="cityList"
              v-model.trim="temp.cityId"
              :props="defaultProps"
              :show_checkbox="false"
              :expand_on_click_node="true"
              :check_on_click_node="false"
              placeholder="请选择城市" />

          </el-form-item> -->
          <el-form-item label="联系人" prop="contacts">
            <el-input v-model.trim="temp.contacts" limit="limit" show-word-limit maxlength="20"
              placeholder="请输入联系人"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="temp.sex">
              <el-radio :label="0">男</el-radio>
              <el-radio :label="1">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model.trim="temp.mobile" maxlength="11" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="temp.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input rows="2" v-model.trim="temp.address" limit="limit" show-word-limit maxlength="100"
              placeholder="请输入地址"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch :disabled="temp.useFlagDisable" v-model="temp.useFlag"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'edit' ? editClick() : addClick()">
              {{ $t('button.submit') }}
            </el-button>
            <el-button plain @click="dialogFormVisible = false">
              {{ $t('button.cancel') }}
            </el-button>
          </div>
        </el-form>
      </el-dialog>
      <!-- 分配车型 -->
      <el-dialog v-dialogDrag lock-scroll title="分配车型" :visible.sync="dialogTrainTreeVisible"
        v-if="dialogTrainTreeVisible">
        <div style="height: 300px; width: 100%; overflow-y:auto;">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <el-tree :data="showTrainTree" show-checkbox node-key="id" ref="trainTree" :default-expand-all="true"
            :default-checked-keys="checkedTrain" :filter-node-method="filterNode" :props="defaultTrainProps">
          </el-tree>
        </div>
        <div class="submitArea">
          <el-button type="primary" @click="getCheckedKeys()">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogTrainTreeVisible = false">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>

      <!-- 批量上传 -->
      <el-dialog v-dialogDrag title="批量上传" :visible.sync="dialogImportFormVisible" :close-on-click-modal="false">
        <div>
          <div>
            <div style="font-weight:bold">1.下载模板</div>
            <div style="margin: 8px 0 8px 15px">导入文件必须符合模板格式，请<a style="cursor:pointer;color:var(--theme-color)"
                @click="downTemplateClick()">下载模板</a>。</div>

          </div>
          <div style="margin: 10px 0">
            <div style="font-weight:bold">2.上传文件</div>
            <el-form ref="uploadForm" class="demo-form-inline">
              <el-form-item style="margin: 8px 0 8px 15px">
                <el-upload
                  class="upload-demo"
                  drag
                  ref="elUpload"
                  action="#"
                  :file-list="fileList"
                  :on-exceed="handleExceed"
                  :limit="1"
                  :before-upload="onBeforeUpload"
                  :before-remove="handleBeforeRemove"
                  :on-remove="handleOnRemove"
                >
                  <div class="el-upload_text">
                    <svg-icon icon-class="fileUpload"></svg-icon>
                    <p>{{ $t("button.uploadTip") }}</p>
                  </div>
                  <div slot="tip" class="el-upload__tip">
                    {{ $t("identifying.uploadTip", {format: 'xlsx, xls', count: 1, size: '100MB'}) }}
                  </div>
                </el-upload>
              </el-form-item>
              <div class="submitArea">
                <el-button type="primary" @click="uploadFile()">{{ $t('button.submit') }}</el-button>
                <el-button plain @click="dialogImportFormVisible = false">{{ $t('button.cancel') }}</el-button>
              </div>
            </el-form>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { tableHeight, uploadIcon } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import SelectTree from '@/components/TreeView/SelectTree.vue'
import {
  // 列表
  dealerData,
  // 添加
  dealerAdd,
  // 编辑
  dealerEdit,
  // 删除
  dealerDel,
  // 批量添加子账户
  dealerBatchSubAccount,
  // 查询个人信息
  getUserInfoId,
  // 国家
  userCountryData,
  //查询国家下的信息
  getCountryTree,
  // 重置密码
  userPasswordReset,
  dealerBatchImport,
  dealerBatchDown,
  downDealerTemplate,
  userBatchDel,
  getUserBrandTrainTree,
  getUserTrain,
  getUserTrainByUserId,
  updateUserTrain, dealerChildrenData, dealerUserInfo,
} from '@/api/sysmgt.js'

export default {
  name: 'sysmgt_dealer_list',
  components: { Pagination },
  // components: { Pagination, SelectTree },
  data() {
    return {
      checkAll: false,
      resolveMap: new Map,
      formInline: {
        realName: '',
        loginName: '',
        userrole: '',
        userCountry: '',
        realCode: '',
      },
      temp: {
        id: '',
        pid: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: 'generalUser',
        defaultCountryCode: 'cn',
        defaultCountryName: '',
        deptId: '',
        deptName: '',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 2,
        useFlag: true,
        grantPrice: true,
        placeOrder: true,
        useFlagDisable: false,
        cityName: '',
        stationCode: '',
      },
      perms: [],
      sltUserRole: {
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      },
      sltUserCountry: {
        userName: '',
        defaultCountry: '',
        countryArr: []
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      resultList: [],
      dialogStatus: '',
      textMap: {
        edit: '编辑',
        add: '新增',
        restPwd: '重置密码',
        assignRole: '分配角色',
        assignCountry: '分配国家',
      },
      businessEmpower: [
        {
          code: '1',
          name: '价格授权',
        },
        {
          code: '2',
          name: '下单授权',
        }
      ]
      ,
      businessInfo: {
        // 类型
        business: '1',
        // 国家
        country: '',
        userName: '',
        realName: '',
      },
      dialogBusinessVisible: false,
      continentList: [], // 国家集合
      dealerLists: [],  // 服务店集合
      dealers: [],
      determineList: [], // 选中的集合
      selectNum: 0,  // 选中的数量
      totalApply: 0,
      currentPageApply: 1,
      pagesizeApply: 10,
      accreditTableHeight: 0,

      lazyLoadMap: new Map(),
      defaultProps: {
        parent: 'pid',
        value: 'code',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          if (val.children == null) {
            return false
          } else {
            return true
          }
        }
      },
      deleteList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      userRoleList: [],
      userCountryList: [],

      fileList: [],
      dialogImportFormVisible: false,
      showUploadBtn: true,
      uploadFileList: [],

      fromTemp: {
        username: [
          { required: true, message: '登录账户不能为空', trigger: ['blur', 'change'] },
          { min: 4, max: 20, message: "登录账号为4-20位字符", }
        ],
        stationCode: [{ required: true, message: '服务店代码不能为空', trigger: ['blur', 'change'] }],
        realName: [{ required: true, message: '服务店名称不能为空', trigger: ['blur', 'change'] }],
        defaultCountryCode: [{ required: true, message: '所属国家不能为空', trigger: ['blur', 'change'] }],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: ['blur', 'change'] },
          { pattern: /^1\d{10}$/, message: "请输入正确的手机号格式", }
        ],
        // email: [
        //   {required: true, message: '邮箱不能为空', trigger: ['blur', 'change']},
        //   {type: "email", message: "请输入正确的邮箱格式",}
        // ],
        contacts: [{ required: true, message: '联系人不能为空', trigger: ['blur', 'change'] }],
      },
      cityList: [],

      // 车型和人员的关联
      filterText: '',
      trainUser: '',
      userTrainTree: [],
      showTrainTree: [],
      checkedTrain: [],
      parentTrainIds: [],
      dialogTrainTreeVisible: false,
      defaultTrainProps: {
        children: 'children',
        label: 'name'
      },
      maximumHeight: 0,

      mobileRegex: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
      emailRegex: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.trainTree.filter(val);
    },
    // $route(to) {
    //   if (to.name == 'sysmgtuserlist') {
    //     this.getUserCountryList()
    //   }
    // }
  },
  methods: {
    // 表格样式
    rowStyle({ row, rowIndex }) {
      let styleJson = {
        "background": "var(--striped-color)",
      }
      if (row.pid != "0") {
        if (row.hasChildren == false || row.hasChildren == undefined) {
          return styleJson;
        } else {
          return {};
        }
      } else {
        return {};
      }
    },
    loadLazyData(dataObj, treeNode, resolve) {
      let id = dataObj.id;
      this.lazyLoadMap.set(id, { dataObj, treeNode, resolve });
      let urlSearchParams = new URLSearchParams();
      urlSearchParams.append("pid", id)
      dealerChildrenData(urlSearchParams).then(res => {
        if (res.data.code == 100) {
          let dataList = res.data.data
          dataList.forEach(item => {
            // 根据是否存在children字段并且其长度大于0来决定是否有子节点
            item.hasChildren = item.children && item.children.length > 0;
          })
          resolve(dataList)
        } else {
          this.$DonMessage.error("加载数据失败")
        }
      })
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      })
    },
    pageLimitList() {
      this.dataList()
    },
    // 列表数据
    dataList: function (pid) {
      if (pid && pid != 0) {
        let { dataObj, treeNode, resolve } = this.lazyLoadMap.get(pid);
        this.loadLazyData(dataObj, treeNode, resolve);
        return;
      }
      this.resultList = [{}]
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('stationCode', this.formInline.realCode)
      params.append('realName', this.formInline.realName)
      params.append('username', this.formInline.loginName)
      params.append('defaultRoleCode', 'generalUser')
      params.append('defaultCountryCode', this.formInline.userCountry)
      params.append("userType", "2")
      dealerData(params).then(res => {
        if (res.data.code == 100) {
          setTimeout(() => {
            this.total = res.data.total
            let data = res.data.data ? res.data.data : []
            data.forEach(item => {
              // 根据后端返回的children数据判断是否有子数据
              item.hasChildren = item.children && item.children.length > 0;
            })
            this.resultList = data
          })
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },

    // 获取国家
    getUserCountryList() {
      userCountryData().then(res => {
        this.userCountryList = res.data.data
      })
    },
    // 所属城市
    resetTemp() {
      this.temp = {
        id: '',
        pid: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: 'generalUser',
        defaultCountryCode: 'cn',
        deptId: '',
        deptName: '',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 2,
        useFlag: true,
        grantPrice: true,
        placeOrder: true,
        useFlagDisable: false,
        cityName: '',
        stationCode: "",
      }
      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
      setTimeout(() => {
        if (this.$refs.modelSelectTree) {
          this.$refs.modelSelectTree.initSelected('', '')
        }

      });
    },
    changeUserType(val) {
      this.temp.userType = val
    },
    // 新增
    addClcik(row) {
      var _this = this
      _this.dialogStatus = 'add'
      _this.dialogFormVisible = true
      setTimeout(() => {
        if (_this.$refs.modelSelectTree) {
          _this.$refs.modelSelectTree.initSelected('', '')
        }
        _this.resetTemp()
        if (row) {
          this.temp.pid = row.id
          this.temp.stationCode = row.stationCode;
          this.temp.realName = row.realName
          if (row.useFlag == 0) {
            this.temp.useFlagDisable = true
          }
        }
      })
    },
    addClick() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          if (!this.temp.pid) {
            this.temp.username = this.temp.stationCode
          }
          var params = new URLSearchParams()
          params.append('realName', this.temp.realName)
          params.append('username', this.temp.username)
          params.append('defaultRoleCode', 'generalUser')
          params.append('defaultCountryCode', this.temp.defaultCountryCode)
          params.append('email', this.temp.email)
          params.append('address', this.temp.address)
          params.append('contacts', this.temp.contacts)
          params.append('mobile', this.temp.mobile)
          params.append('userType', 2)
          params.append('sex', this.temp.sex)
          params.append('stationCode', this.temp.stationCode)
          params.append('useFlag', this.temp.useFlag ? "1" : "0")
          if (this.temp.pid) {
            params.append("pid", this.temp.pid)
          }
          dealerAdd(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList(this.temp.pid)
              this.dialogFormVisible = false
              this.dataList()
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.temp.placeOrder = true
      this.temp.grantPrice = true
      this.temp.useFlag = true
      this.currentPage = 1
      this.dataList()
    },
    selectAll() {
      this.checkAll = !this.checkAll;
      this.handSelectAll(this.resultList, this.checkAll)
    },
    handSelectAll(tableData, checkAll) {
      tableData.forEach((row) => {
        this.$refs.multipleTable.toggleRowSelection(row, checkAll);
        if (row.children != undefined) {
          this.handSelectAll(row.children, checkAll)
        }
      })
    },
    // 批量删除
    handleSelectionChange(val) {
      this.deleteList = val
    },
    batchDelClick() {
      var list = []
      if (this.deleteList.length == 0) {
        this.$DonMessage.warning("未选中任何用户")
        return
      }
      this.deleteList.forEach(function (item) {
        list.push(item.id)
      })
      var params = new URLSearchParams()
      params.append('ids', JSON.stringify(list))
      this.$confirm('确定批量删除选中的行', '批量删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userBatchDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 100
      if (!docxExt && !docExt) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'xls, xlsx' }))
        return false;
      }
      if (!isLimit) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }))
        return false;
      }
      this.showUploadBtn = false
      uploadIcon();    
      setTimeout(() => {
        var fileObj = { "name": file.name, "filePath": "", "file": file }
        this.fileList.push(fileObj)
      }, 100)
      return true;
    },
    onUploadChange(file) {
      //this.files.push(file.raw)

    },
    // 批量上传
    uploadFile(param) {
      if (this.fileList.length <= 0) {
        this.$DonMessage.warning('请上传文件')
        return
      }
      var _this = this
      var formData = new FormData();
      formData.append('file', this.fileList[0].file);
      dealerBatchImport(formData).then(res => {
        if (res.data.code === 100) {
          _this.$DonMessage.success(this.$t('successTip.uploadTip'))
        } else {
          _this.showUploadBtn = true
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
        }
        _this.dataList()
        _this.fileList = []
      }).catch(function (error) {
        _this.fileList = []
        _this.showUploadBtn = true
        _this.$DonMessage.error(_this.$t('errorTip.systemTip'));
      })
    },
    handleExceed(files, fileList) {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 1}))
      return
    },
    uploadFileError(err, file, fileList) {
      this.$DonMessage.error('导入失败')
      this.$refs.upload.clearFiles();
    },
    handleOnRemove() {
      const _this = this;
      _this.fileList = [];
      _this.showUploadBtn = true
    },
    handleBeforeRemove() {
      if (this.fileList != null && this.fileList.length > 0) {
        return this.$confirm('确认删除文件？', '删除', { type: 'warning'})
      }
    },
    // 批量上传
    batchUpload() {
      // 打开上传弹窗
      this.fileList = [];
      this.showUploadBtn = true;
      this.dialogImportFormVisible = true;
    },
    // 批量下载
    batchExport() {
      var params = new URLSearchParams()
      params.append('stationCode', this.formInline.realCode)
      params.append('realName', this.formInline.realName)
      params.append('username', this.formInline.loginName)
      params.append('defaultRoleCode', 'generalUser')
      params.append('defaultCountryCode', this.formInline.userCountry)
      params.append("userType", 2)
      dealerBatchDown(params).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        var name = "服务店信息列表.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 下载模板
    downTemplateClick() {
      var params = '';
      downDealerTemplate(params).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        var name = "服务店导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },


    // 分配车型
    distriTrain(row) {
      this.filterText = ''
      // 获取用户已经关联的车型
      this.trainUser = row.username
      var params = new URLSearchParams()
      params.append('userId', row.id)
      getUserBrandTrainTree(params).then(res => {
        this.userTrainTree = res.data.data

        params = new URLSearchParams()
        params.append('username', row.username)
        this.checkedTrain = []
        this.parentTrainIds = []
        if (row.pid && row.pid != 0) {
          var parentTrainParam = new URLSearchParams()
          parentTrainParam.append('userId', row.pid)
          getUserTrainByUserId(parentTrainParam).then((res) => {
            if (res.data.code = 100) {
              let parentTrains = res.data.data
              for (let i = 0; i < parentTrains.length; i++) {
                this.parentTrainIds.push(parentTrains[i].trainId);
              }
              this.showTrainTree = JSON.parse(JSON.stringify(this.userTrainTree))
              this.handTreeDisable(this.showTrainTree)
              getUserTrain(params).then(res2 => {
                if (res2.data.code == 100) {
                  let list = res2.data.data
                  for (let i = 0; i < list.length; i++) {
                    this.checkedTrain.push(list[i].trainId);
                  }
                  this.dialogTrainTreeVisible = true
                }
              })
            }
          })
        } else {
          this.showTrainTree = this.userTrainTree
          getUserTrain(params).then(res => {
            if (res.data.code == 100) {
              let list = res.data.data
              for (let i = 0; i < list.length; i++) {
                this.checkedTrain.push(list[i].trainId);
              }
              this.dialogTrainTreeVisible = true
            }
          })
        }
      })

    },
    /**
     * 处理车型树禁用信息
     * @param {*} tree
     */
    handTreeDisable(tree) {
      tree.forEach(f => {
        if (f.children) {
          this.handTreeDisable(f.children)
        } else {
          f.disabled = !this.parentTrainIds.includes(f.id)
        }
      })
    },
    // 获取选中
    getCheckedKeys() {
      let list = this.$refs.trainTree.getCheckedKeys()
      if (!list || list.length <= 0) {
        this.$DonMessage.warning("请选择车型")
        return false
      }
      var params = new URLSearchParams()
      params.append('username', this.trainUser)
      params.append('trainIds', list.toString())
      updateUserTrain(params).then(res => {
        if (res.data.code == 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dialogTrainTreeVisible = false
        } else {
          this.$DonMessage.error("分配失败，" + res.data.msg)
        }
      }).catch(e => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 密码重置
    initPsw(row) {
      this.$confirm('确定重置【' + row.username + '】的密码吗?', '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = '?id=' + row.id
        userPasswordReset(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.resetTip'))
          } else if (res.data.code === 101) {
            this.$DonMessage.error(res.data.msg)
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    // 编辑
    editopen(row) {
      var _this = this
      var searchParam = new URLSearchParams()
      searchParam.append("id", row.id)
      this.temp.pid = row.pid
      dealerUserInfo(searchParam).then((res) => {
        if (res.data.code == 100) {
          _this.dialogStatus = 'edit'
          _this.dialogFormVisible = true
          _this.resetTemp()
          var data = res.data.data
          _this.temp = Object.assign({}, data)

          if (row.pid && row.pid != 0) {
            var tempData = _this.$refs.multipleTable.store.states.data

            for (let parent of tempData) {
              if (parent.id == row.pid) {
                if (parent.useFlag == 0) {
                  _this.temp.useFlagDisable = true
                }
                break
              }
            }
          }
          this.temp.useFlag = data.useFlag == 1
          if (data === null || data.sex === null) {
            _this.temp.sex = 0
          } else {
            _this.temp.sex = data.sex
          }
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },

    // 启用 停用
    editUseFlag(row, type) {
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('realName', row.realName)
      params.append('username', row.username)
      params.append('mobile', row.mobile)
      params.append('useFlag', type)
      params.append('userType', 2)
      params.append('defaultCountryCode', row.defaultCountryCode)
      params.append('defaultRoleCode', 'generalUser')
      params.append('stationCode', row.stationCode)
      dealerEdit(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.operateTip'))
          this.dataList(this.temp.pid)
          // this.dialogFormVisible = false
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },

    // 提交编辑
    editClick() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('realName', this.temp.realName)
          params.append('username', this.temp.username)
          params.append('defaultRoleCode', 'generalUser')
          params.append('contacts', this.temp.contacts)
          params.append('sex', this.temp.sex)
          params.append('mobile', this.temp.mobile)
          params.append('email', this.temp.email)
          params.append('address', this.temp.address)
          params.append('userType', 2)
          params.append('useFlag', this.temp.useFlag ? "1" : "0")
          params.append('defaultCountryCode', this.temp.defaultCountryCode)
          params.append('stationCode', this.temp.stationCode)
          dealerEdit(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              this.dataList(this.temp.pid)
              this.dialogFormVisible = false
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 删除
    delectClick(row) {
      this.$confirm('确定删除【' + row.realName + '】的相关信息?', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dealerDel(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList(row.pid)
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },

    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },


    addSubAccount(row) {
      this.$prompt('请输入添加的个数', '批量添加【' + row.username + '】的二级账户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: 4,
        inputPattern: /^[1-9]$/,
        inputErrorMessage: '请输入大于0小于10的正整数'
      }).then(({ value }) => {
        var params = new URLSearchParams()
        params.append('username', row.username)
        params.append('number', value)
        dealerBatchSubAccount(params).then(res => {
          if (res.data.code == 100) {
            this.$DonMessage.success(this.$t('successTip.operateTip'))
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        }).catch(e => {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        })
      })
    },

    // 权限选择
    cellMouseEnter(row) {
      $(".nationalList._" + row.id + " .el-icon-close").show()
    },
    cellLeaveEnter() {
      $(".nationalList .el-icon-close").hide()
    },
    // 筛选出的数据集
    selectData() {
      let country = this.businessInfo.country;
      let username = this.businessInfo.userName;
      let realname = this.businessInfo.realName;
      let list = []
      this.dealerLists = []
      for (let i = 0; i < this.dealers.length; i++) {
        let row = this.dealers[i]
        if (country && country.length > 0 && row.defaultCountryCode !== country) {
          continue;
        }
        if (username && username.length > 0 && row.username.indexOf(username) < 0) {
          continue;
        }
        if (realname && realname.length > 0 && row.realName.indexOf(realname) < 0) {
          continue;
        }
        list.push(row)
      }
      // 分页
      this.totalApply = list.length
      let i = (this.currentPageApply - 1) * this.pagesizeApply
      let j = i + this.pagesizeApply
      this.dealerLists = list.slice(i, j)
      this.nationalArea()
    },
    // 弹窗 表格高度
    nationalArea() {
      var _this = this;
      setTimeout(() => {
        var allTable = $(".nationalInfo").outerHeight(true);
        _this.accreditTableHeight = allTable;
      }, 80)
      window.addEventListener("resize", function () {
        setTimeout(() => {
          var tableArea = $(".nationalInfo").outerHeight(true);
          _this.accreditTableHeight = tableArea;
        }, 80)
      })
    },

  },
  mounted() {
    let tt = this.$store.state.perms.indexOf('menuAsimss1A2B_105')
    this.dataList()
    this.getUserCountryList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
