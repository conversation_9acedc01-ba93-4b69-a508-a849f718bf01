<template>
  <div class="layoutContainer">
    <div class="secondFloat" style="border:none" v-if="hasPerm('menuAsimss1A9B_106')">
      <el-form ref="from" :model="from" class="oauthFrom">
        <el-radio v-model="radio" label="1" @change="radioCheck($event)">用户</el-radio>
        <el-radio v-model="radio" label="2" @change="radioCheck($event)">角色</el-radio>
        <el-select v-model="from.region" v-if="radio === '1'" clearable filterable style="margin:0 10px;"
          @change="refresh">
          <el-option v-for="(item, index) in defaultUserList" :key="index" :label="getUserLabel(item)"
            :value="item.username"></el-option>
        </el-select>
        <el-select v-model="from.region" v-if="radio === '2'" clearable filterable style="margin:0 10px;"
          @change="refresh">
          <el-option v-for="(item, index) in defaulRoleList" :key="index" :label="item.name"
            :value="item.code"></el-option>
        </el-select>
        <el-button size="medium" type="primary" @click="submit">提交</el-button>
        <el-button size="medium" plain @click="refresh">刷新</el-button>
      </el-form>
    </div>
    <div class="tableDetail oauthContent">
      <el-table class="autotable" style="width:100%" border highlight-current-row :max-height="maximumHeight"
        :data="resultList" row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :row-style="rowStyle">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="菜单名称" prop="name" width="152">
          <template slot-scope="{row}">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="菜单编码" prop="code" width="150">
          <template slot-scope="{row}">
            <span>{{ row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分配权限" width="82">
          <template slot-scope="{row}">
            <el-checkbox-group v-model="distribute" v-if="row.type == 3">
              <el-checkbox :label="row.code">{{ }}</el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>
        <el-table-column label="操作按钮权限" min-width="360">
          <template slot-scope="{row}">
            <div v-if="row.permission !== '' && row.permission !== null" style="display:flex;">
              <el-checkbox @change="checkAllChilde($event, row, row.code)" v-model="checkAll[row.code]">全选</el-checkbox>
              <el-checkbox-group v-model="checkList[row.code]" @change="selectChildrenCheck($event, row, row.code)">
                <el-checkbox v-for="(menu, index) in row.permission" :key="index" :label="menu.code">
                  {{ menu.name + '[' + menu.code + ']' }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { tableHeight } from '@/assets/js/common.js'
import { oauthData, oauthRoleList, oauthUserList, oauthSubmit, userRefresh, roleRefresh } from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_oauth_list',
  data() {
    return {
      from: {
        region: ''
      },
      radio: '1',
      resultList: [],
      defaulRoleList: [],
      defaultUserList: [],
      checkAll: [],
      checkList: [],
      indeterminate: [],
      distribute: [],
      maximumHeight: 0,
    }
  },
  created() {
    oauthData().then(res => {
      this.resultList = res.data.data
      this.resetPermission()
      // 渲染完成后更新展开节点
      this.$nextTick(() => {
        this.expandAll()
      })
      this.tableHeightArea()
    })
  },
  methods: {
    rowStyle({ row, rowIndex }) {
      let styleJson = {
        "background": "var(--striped-color)",
      }
      if (row.pcode != "menuAsimss") {
        if (row.children == null || row.children.length == 0) {
          return styleJson;
        } else {
          return {};
        }
      } else {
        return {};
      }

    },
    submit() {
      var params = new URLSearchParams()
      var roleCode = ""
      var userCode = ""
      if (this.radio === '1') {
        userCode = this.from.region
        if (userCode == '') {
          this.$DonMessage.warning('请选择授权用户')
          return false;
        }
      } else if (this.radio === '2') {
        roleCode = this.from.region
        if (roleCode == '') {
          this.$DonMessage.warning('请选择授权角色')
          return false;
        }
      } else {
        this.$DonMessage.warning('系统登录异常请先勾选用户或角色，选择授权用户或角色之后再提交')
        return false;
      }
      params.append('role', roleCode)
      params.append('user', userCode)
      var menuPermission = "";//所有分配权限的菜单
      var menus = "";//存放选中的菜单数据
      for (let i = 0; i < this.distribute.length; i++) {
        //拼接菜单字符串
        menus += this.distribute[i] + ",";
        menuPermission += this.distribute[i] + "_";
        var perms = this.checkList[this.distribute[i]]
        if (perms != null && perms.length > 0) {
          for (let j = 0; j < perms.length; j++) {
            menuPermission += perms[j] + "*";
          }
        }
        menuPermission += ",";
      }
      params.append('menus', menus)
      params.append('menuPermission', menuPermission)
      oauthSubmit(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dataList()
          this.refresh()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(err => {
        if (err !== null && err !== '' && err.responseText !== null) {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        }
      })
    },
    resetPermission() {
      if (this.resultList != null) {
        for (var i = 0; i < this.resultList.length; i++) {
          var children = this.resultList[i].children
          if (children != null && children.length > 0) {
            for (var j = 0; j < children.length; j++) {
              this.$set(this.checkList, children[j].code, [])
              this.$set(this.checkAll, children[j].code, [])
            }
          }
        }
      }
    },
    refresh() {
      this.resetPermission()
      this.distribute = []
      var params = new URLSearchParams()
      var perList = []
      var menusList = []
      if (this.radio === '1' && this.from.region !== '') {
        params.append('user', this.from.region)
        userRefresh(params).then(res => {
          var ret = res.data.data
          if (ret.menus != null && ret.menus.length !== 0) {
            for (let i = 0; i < ret.menus.length; i++) {
              var menuCode = ret.menus[i].menuCode
              menusList.push(menuCode)
              perList = []
              if (ret.permissions != null && ret.permissions.length > 0) {
                ret.permissions.forEach(function (item) {
                  if (item.menuCode == menuCode) {
                    perList.push(item.permissionCode)
                  }
                })
                this.checkList[menuCode] = perList
              }
              this.distribute = menusList
            }
          }
        })
      } else if (this.radio === '2' && this.from.region !== '') {
        params.append('role', this.from.region)
        roleRefresh(params).then(res => {
          var ret = res.data.data
          if (ret.menus != null && ret.menus.length !== 0) {
            for (let i = 0; i < ret.menus.length; i++) {
              var menuCode = ret.menus[i].menuCode
              menusList.push(menuCode)
              perList = []
              if (ret.permissions != null && ret.permissions.length > 0) {
                ret.permissions.forEach(function (item) {
                  if (item.menuCode == menuCode) {
                    perList.push(item.permissionCode)
                  }
                })
                this.checkList[menuCode] = perList
              }
              this.distribute = menusList
            }
          }
        })
      }
    },
    // 数据
    dataList() {
      oauthData().then(res => {
        this.resultList = res.data.data
        this.tableHeightArea()
      })
    },
    // 角色列表
    getUserRoleList() {
      oauthRoleList().then(res => {
        if (res.data.code === 100) {
          this.defaulRoleList = res.data.data
        }
      })
    },
    // 用户列表
    getUserList() {
      oauthUserList().then(res => {
        if (res.data.code === 100) {
          this.defaultUserList = res.data.data
        }
      })
    },
    getUserLabel(item) {
      let realName = item.realName
      if (realName && realName.length > 20) {
        realName = realName.substring(0, 20) + '...'
      }
      return realName + ' ( ' + item.username + ' )'
    },
    // 单选
    radioCheck(event) {
      this.from.region = ''
      this.distribute = []
    },
    expandAll() {
      const els = this.$el.getElementsByClassName('el-table__expand-icon')
      if (els != null) {
        for (let i = 0; i < els.length; i++) {
          els[i].click()
        }
      }
    },
    // 全选
    checkAllChilde(event, row, code) {
      let list = []
      row.permission.forEach(function (item) {
        list.push(item.code)
      })
      this.checkList[code] = event ? list : []
    },
    selectChildrenCheck(event, row, code) {
      if (event.length == 1) {
        this.distribute.push(row.code)
      } else if (event.length == 0) {
        this.distribute = this.distribute.filter(item => item != row.code)
      }
      if (event.length === row.permission.length) {
        this.checkAll[code] = true
      } else {
        this.checkAll[code] = false
      }
      // this.indeterminate[code] = checkedCount > 0 && checkedCount < row.data.length
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.tableHeightArea()
    this.getUserRoleList()
    this.getUserList()
  },
}
</script>
<style>
.oauthContent .el-checkbox {
  margin: 5px;
}

.oauthContent .el-checkbox-group {
  display: block;
  white-space: normal;
}

.autotable th .el-checkbox__inner {
  width: 0;
  height: 0;
  border: 0
}

.el-checkbox-group {
  display: inline-block;
  margin-left: 10px;
}
</style>
