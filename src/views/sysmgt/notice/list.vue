<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFour" :model="formInline" class="demo-form-inline">
        <el-form-item label="标题" prop="title">
          <el-input v-model.trim="formInline.title" placeholder="请输入公告标题"></el-input>
        </el-form-item>
        <el-form-item label="公告类型" prop="type">
          <el-select v-model="formInline.type" clearable filterable>
            <el-option v-for="(item, index) of typeList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formInline.status" clearable filterable>
            <el-option label="草稿" value="1"></el-option>
            <el-option label="发布" value="2"></el-option>
            <el-option label="关闭" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss3A21B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addData(null)">新增</el-button>
      </div>
      <!-- 列表内容 -->
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="标题" prop="title" min-width="150"></el-table-column>
        <el-table-column label="适用国家" prop="sltCountry" min-width="120"></el-table-column>
        <el-table-column label="公告类型" prop="type" width="100">
          <template slot-scope="{row}">
            <div v-for="(item, index) in typeList" :key="index">
              <span v-if="item.code == row.type">{{ item.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="访问次数" prop="visitTimes" width="100"></el-table-column>
        <el-table-column label="置顶" prop="isTop" width="80">
          <template slot-scope="{row}">
            <span v-if="row.isTop == 1" style="color:#009933">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="推荐" prop="isRecmd" width="80">
          <template slot-scope="{row}">
            <span v-if="row.isRecmd == 1">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column> -->
        <el-table-column label="状态" prop="status" width="80">
          <template slot-scope="{row}">
            <span v-if="row.status === 1">草稿</span>
            <span v-if="row.status === 2" class="successColor">发布</span>
            <span v-if="row.status === 3" class="errorColor">关闭</span>
          </template>
        </el-table-column>
        <el-table-column label="更新人员" prop="username" width="100"></el-table-column>
        <el-table-column label="更新日期" prop="updatedTime" width="160">
          <template slot-scope="{row}">
            <div>
              {{ row.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="260">
          <template slot-scope="{row}">
            <el-button type="text" size="small" v-if="hasPerm('menuAsimss3A21B_104')"
              @click="headerDetail(row)">详情</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A21B_103')" type="text" size="small"
              @click="addData(row)">编辑</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss3A21B_119')" type="text" size="small"
              @click="statusEdit(row, 'close')">关闭</el-button>
            <el-button v-if="row.status == 3 && hasPerm('menuAsimss3A21B_119')" type="text" size="small"
              @click="statusEdit(row, 'open')">开启</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A21B_118')" type="text" size="small"
              @click="statusEdit(row, 'issue')">发布</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss3A21B_118')" type="text" size="small"
              @click="statusEdit(row, 'revocation')">撤回</el-button>
            <el-button v-if="row.isTop == 0 && hasPerm('menuAsimss3A21B_103')" type="text" size="small"
              @click="statusEdit(row, 'top')">置顶</el-button>
            <el-button v-if="row.isTop == 1 && hasPerm('menuAsimss3A21B_103')" type="text" size="small"
              @click="statusEdit(row, 'notop')">取消置顶</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A21B_102')" type="text" size="small"
              @click="del(row)" class="deleteButton">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
    </div>
  </div>
</template>
<script>
import { addTabs, tableHeight, DonMessageTip } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { noticeData, noticeTypeList, noticeStateEdit, noticeDel } from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_notice_list',
  components: { Pagination },
  data() {
    return {
      formInline: {
        title: '',
        status: '',
        type: '',
      },
      temp: {
        id: '',
        title: '',
        content: '',
        status: '',
        type: '',
        visitTimes: '',
        isTop: '',
        isRecmd: '',
      },
      statusList: [
        { name: '草稿', code: 1 },
        { name: '发布', code: 2 },
        { name: '关闭', code: 3 }
      ],
      topRecmd: [
        { name: '是', code: 1 },
        { name: '否', code: 0 },
      ],
      typeList: [],
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('title', this.formInline.title)
      params.append('status', this.formInline.status)
      params.append('type', this.formInline.type)
      noticeData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea();
      })
    },
    // 公告类型
    getTypeList() {
      noticeTypeList().then(res => {
        if (res !== null && res.data.code === 100) {
          this.typeList = res.data.data
        }
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    // 更改状态
    statusEdit(row, sign) {
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('sign', sign)
      noticeStateEdit(params).then((res) => {
        if (res.data.code === 100) {
          DonMessageTip(sign);
          this.dataList()
          window.noticeUnread();
          window.systemReminder();
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(err => {
        if (err !== null && err !== '' && err.responseText !== null) {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        }
      })
    },
    // 重置
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    // 新增
    addData(row) {
      var title = ""
      var id = ""
      sessionStorage.setItem("notice", JSON.stringify(row));
      if (row == undefined) {
        title = "新增系统公告"
        id = "add"
      } else {
        title = "编辑 " + row.title
        id = row.id
      }
      this.$router.push({ name: 'addNotice', params: { id: id, list: JSON.stringify(row) } });
      addTabs(this.$route.path, title);
    },
    // 删除
    del(row) {
      var _this = this
      this.$confirm('确定删除当前系统公告', '删除公告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        noticeDel(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },
    // 详情
    headerDetail(row) {
      this.$router.push({ name: 'noticeDetail', params: { id: row.id } })
      addTabs(this.$route.path, row.title);
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.dataList();
    this.getTypeList();
    window.addEventListener('keydown', this.keyDown);
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
