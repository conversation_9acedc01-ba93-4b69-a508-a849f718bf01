<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="8" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss1Aregion_101') || hasPerm('menuAsimss1Aregion_102')">
              <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_101') && typeFlag" icon="el-icon-plus"
                @click="addRoot">{{ addTitle }}</el-button>
              <el-dropdown v-if="hasPerm('menuAsimss1Aregion_107')">
                <el-button type="text" size="small" style="font-size: 14px;"
                  icon="el-icon-upload2">导入数据</el-button>&nbsp;&nbsp;&nbsp;
                <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                  <el-dropdown-item>
                    <el-upload class="upload-demo inline-block" ref="elUpload" action="#"
                      :show-file-list="false" :limit="1" :file-list="fileList" :before-upload="onBeforeUpload"
                      :http-request="(param) => uploadFile(param, 'continent')"
                      :on-change="onUploadChange">导入大洲数据</el-upload>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-upload class="upload-demo inline-block" ref="elUpload" action="#"
                      :show-file-list="false" :limit="1" :file-list="fileList" :before-upload="onBeforeUpload"
                      :http-request="(param) => uploadFile(param, 'country')" :on-change="onUploadChange">
                      导入国家数据
                    </el-upload>
                  </el-dropdown-item>

                  <el-dropdown-item>
                    <el-upload class="upload-demo inline-block" ref="elUpload" action="#"
                      :show-file-list="false" :limit="1" :file-list="fileList" :before-upload="onBeforeUpload"
                      :http-request="(param) => uploadFile(param, 'province')" :on-change="onUploadChange">
                      导入省份数据
                    </el-upload>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-upload class="upload-demo inline-block" ref="elUpload" action="#"
                      :show-file-list="false" :limit="1" :file-list="fileList" :before-upload="onBeforeUpload"
                      :http-request="(param) => uploadFile(param, 'city')" :on-change="onUploadChange">
                      导入城市数据
                    </el-upload>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <!-- <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_107')" icon="el-icon-download" @click="downTemplateClick()">下载模板</el-button> -->
              <el-dropdown v-if="hasPerm('menuAsimss1Aregion_107')" placement="bottom">
                <el-button type="text" size="small" style="font-size: 14px;"
                  icon="el-icon-download">下载模板</el-button>&nbsp;&nbsp;&nbsp;
                <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                  <el-dropdown-item @click.native="downTemplateClick('大洲导入模板.xlsx')">下载大洲模板</el-dropdown-item>
                  <el-dropdown-item @click.native="downTemplateClick('国家导入模板.xlsx')">下载国家模板</el-dropdown-item>
                  <el-dropdown-item @click.native="downTemplateClick('省份导入模板.xlsx')">下载省份模板</el-dropdown-item>
                  <el-dropdown-item @click.native="downTemplateClick('城市导入模板.xlsx')">下载城市模板</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_102')" icon="el-icon-delete"
                @click="del">删除</el-button>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :default-expanded-keys="nodeKeyList" :data="listdata"
                  :props="defaultProps" @node-click="handleNodeClick"></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="16" class="fromRight">
          <div class="formTitle" v-html="editTitle"></div>
          <el-form ref="form" :model="form" :rules="rules" :label-width="formLabelWidth"
            :validate-on-rule-change="false">
            <el-form-item label="节点类型" prop="typeName">
              <el-input class="formCode" disabled v-model.trim='form.typeName'></el-input>
            </el-form-item>

            <el-form-item label="代码" prop="code">
              <el-input class="formCode" :disabled="butType !== 'addNodeBut'" placeholder="请输入代码"
                oninput="value=value.replace(/[^\w_-]/ig,'')" v-model.trim='form.code' limit="limit" show-word-limit
                maxlength="20"></el-input>
            </el-form-item>

            <el-form-item label="名称" prop="name">
              <el-input v-model.trim="form.name" placeholder="请输入名称" limit="limit" show-word-limit
                maxlength="50"></el-input>
            </el-form-item>

            <!--            <el-form-item v-if="form.type == '1'" label="别名" prop="alias">-->
            <!--              <template slot="label">-->
            <!--                <span>别名-->
            <!--                  <el-tooltip class="item" effect="dark" placement="right">-->
            <!--                    <i class="question-icon"></i>-->
            <!--                    <div slot="content">-->
            <!--                      <p>填写时，名称之间用逗号分隔；如：新加坡共和国,星洲,星岛,狮城</p>-->
            <!--                    </div>-->
            <!--                  </el-tooltip>-->
            <!--                </span>-->
            <!--              </template>-->
            <!--              <el-input v-model.trim="form.alias" placeholder="请输入别名" ></el-input>-->
            <!--            </el-form-item>-->

            <el-form-item label="排序" prop="sort">
              <el-input-number v-model.trim="form.sort" placeholder="请输入排序" controls-position="right" :min="1"
                :max="9999" :precision="0" :step="1"></el-input-number>
            </el-form-item>
            <el-form-item class="butArea">
              <el-button type="primary" @click="preserve()">{{ $t('button.submit') }}</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>


    </div>
  </div>
</template>
<script>
import { contentSize } from '@/assets/js/common.js'
import {
  regionDel,
  regionImportInfo,
  regionTemplate,
  findRegionAll, findRegionType, editRegionApi, addRegionApi
} from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_region_list',
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      nodeKeyList: [],
      editTitle: '当前信息',
      rules: {
        code: [],
        name: []
      },

      formLabelWidth: '100px',
      butType: '',
      listdata: [],
      typeList: [],

      // 类型索引
      typeIndex: 0,
      addTitle: '',
      addTitlePrefix: '新增',
      typeFlag: true,

      fileList: [],

      form: {
        id: '',
        pid: '',
        typeName: '',
        type: '',
        code: '',
        name: '',
        sort: 1,
        whether: true,
        alias: '',   // 别名

      }
    }
  },
  methods: {
    // 获取结构树
    dataList() {
      findRegionAll().then(res => {
        this.listdata = res.data.data
      })
    },
    // 获取类型
    getTypeList() {
      findRegionType().then(res => {
        this.typeList = res.data.data

        this.typeFlag = true;
        this.addTitle = this.addTitlePrefix + this.typeList[this.typeIndex].name
      }).catch(e => {
        this.typeList = []
      })
    },


    // 显示详情，点击获取当前车系信息
    handleNodeClick(data) {
      let _this = this;
      _this.form.code = data.code;
      _this.form.name = data.name;
      _this.form.id = data.id;
      _this.form.whether = data.inUse == 1;
      _this.form.path = data.path;
      _this.form.sort = data.sort;
      _this.form.type = data.type;
      // _this.form.alias = data.aliasSlt;
      // for (let i = 0; i < _this.typeList.length; i++) {
      //   const item = _this.typeList[i];
      //   if (item.code == data.type) {
      //     _this.form.typeName = item.name;
      //     break
      //   }
      // }
      // 修改 新增xx
      const len = this.typeList.length
      for (let i = 0; i < len; i++) {
        if (this.form.type === this.typeList[i].code) {
          _this.form.typeName = this.typeList[i].name;
          this.typeIndex = i + 1;
          this.typeFlag = this.typeIndex < len
          break
        }
      }
      if (this.typeFlag) {
        this.addTitle = this.addTitlePrefix + this.typeList[this.typeIndex].name
      }
    },
    resetting() {
      this.form = {
        id: '',
        pid: '',
        typeName: '',
        type: '',
        code: '',
        name: '',
        sort: 1,
        whether: true,

      }
    },
    // 增加根节点
    addRoot() {
      this.butType = 'addNodeBut'

      this.form.typeName = this.typeList[this.typeIndex].name
      this.form.type = this.typeList[this.typeIndex].code
      this.form.id = ''
      this.form.code = ''
      this.form.name = ''
      this.form.sort = 1
      this.form.whether = true
      this.form.alias = ''
      this.editTitle = this.addTitle
      this.form.type = this.typeList[this.typeIndex].code
    },
    // 新增子节点
    addSub() {
      if (this.form.id == '') {
        this.$DonMessage.warning('请先选中父级节点')
        return false
      }
      if (this.form.type == 4) {
        this.$DonMessage.warning('选中的父级节点不能为 城市 节点')
        return false
      }
      let typeObj;
      for (let i = 0; i < this.typeList.length; i++) {
        const item = this.typeList[i];
        if (item.code == this.form.type) {
          typeObj = this.typeList[i + 1]
          break
        }
      }
      this.butType = 'addNodeBut'
      this.editTitle = '当前选中父级节点：' + this.form.name
      let pid = this.form.id
      this.form.whether = true;
      this.form.pid = pid;
      this.form.sort = 1;
      this.form.type = typeObj.code;
      this.form.typeName = typeObj.name;
      this.form.id = '';
      this.form.code = '';
      this.form.name = '';
      this.form.alias = '';
    },
    // 下载模板
    downTemplateClick(name) {
      var params = '';
      regionTemplate(name).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.downTip"));
          return
        }
        // var name = "区域导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 10
      if (!docxExt && !docExt) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'xls, xlsx' }))
        return false;
      }
      if (!isLimit) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '10MB' }))
        return false;
      }
      return true;
    },
    onUploadChange(file) {
      //this.files.push(file.raw)

    },
    // 批量上传
    uploadFile(param, type) {
      var _this = this
      var formData = new FormData();
      formData.append('file', param.file);
      formData.append('type', type);
      regionImportInfo(formData).then(res => {
        _this.fileList = [];
        if (res.data.code === 100) {
          if (res.data.data) {
            _this.$alert("警告：" + res.data.data, '导入成功', { dangerouslyUseHTMLString: true })
          } else {
            this.$DonMessage.success(this.$t('successTip.importTip'));
          }
          _this.dataList()
        } else {
          _this.$alert(res.data.msg, '导入失败', { dangerouslyUseHTMLString: true })
        }
      }).catch(function (error) {
        _this.fileList = [];
        _this.$DonMessage.error(_this.$t('errorTip.systemTip'));
      })
    },
    uploadFileError(err, file, fileList) {
      this.$DonMessage.error("导入失败");
      this.$refs.upload.clearFiles();
    },
    // 删除
    del() {
      var _this = this
      if (this.form.id == '') {
        this.$DonMessage.warning('请先选中要删除的节点')
        return false
      } else {
        _this.$confirm('确定删除【' + _this.form.name + '】区域?', '删除区域节点', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          //删除节点
          _this.deleteNode(_this.form.id)
          _this.resetting()
        })
      }
    },
    deleteNode(id) {
      regionDel(id).then(res => {
        if (res.data.code == 100) {
          this.$DonMessage.success(this.$t('successTip.deleteTip'))
          this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    // 保存
    preserve() {
      if (!this.form.code) {
        this.$DonMessage.warning('区域代码不能为空');
        return false;
      }
      if (!this.form.name) {
        this.$DonMessage.warning('区域名称不能为空');
        return false;
      }
      this.nodeKeyList = []

      const params = {
        'code': this.form.code,
        'name': this.form.name,
        'path': this.form.path,
        'type': this.form.type,
        'sort': this.form.sort

      }

      if (this.form.id != null && this.form.id != '' && this.form.id != 'null' && this.form.id != undefined && this.form.id != 'undefined') {
        this.updateNode(this.form.id, params)
        this.nodeKeyList.push(this.form.id)
      } else {
        this.addNode(params)
        this.nodeKeyList.push(this.form.pid)
      }
    },
    // 编辑
    updateNode(id, params) {
      editRegionApi(id, params).then(res => {
        if (res.data.code == 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },
    // 新增节点
    addNode(params) {
      addRegionApi(params).then(res => {
        if (res.data.code == 100) {
          this.$DonMessage.success(this.$t('successTip.submitTip'))
          this.dataList()
          this.butType = ''
          this.editTitle = '当前信息'
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(function (error) {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },



  },
  mounted() {
    this.typeIndex = 0
    this.dataList()
    this.getTypeList()
    contentSize()
  },
}
</script>
