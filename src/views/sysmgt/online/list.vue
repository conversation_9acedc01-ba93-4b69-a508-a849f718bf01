<template>
  <div class="layoutContainer">
    <div class="tableHandle">
      <el-button type="text" icon="el-icon-refresh" @click="refreshData()">刷新</el-button>
    </div>
    <div class="tableDetail">
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="list" @header-dragend="changeColWidth">
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="登录账户" prop="username" min-width="100"></el-table-column>
        <el-table-column label="上线时间" prop="connectTime" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.connectTime | conversion("yyyy-MM-dd HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column label="上线时长" prop="connectTime" min-width="100">
          <template slot-scope="scope">
            <span>{{ getOnlineTime(scope.row.connectTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template slot-scope="{row}">
            <el-button type="text" size="small" v-if="store.state.userName != row.username"
              @click="kickOut(row)">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { requestKicKOutOnLineUser, requestOnLineUserList } from "@/api/sysmgt";
import { tableHeight } from "@/assets/js/common";
import store from "@/store";
export default {
  name: 'sysmgt_online_list',
  computed: {
    store() {
      return store
    }
  },
  components: { Pagination },
  // components: { Pagination },
  data() {
    return {
      list: [],
      refreshTime: 0,
      pagesize: 10,
      currentPage: 1,
      total: 0,
      maximumHeight: 0,
    }
  },
  methods: {
    refreshData() {
      if ((Date.now() - this.refreshTime) / 1000 < 1) {
        this.$DonMessage.warning("刷新过于频繁！");
        return
      }
      this.dataList()
    },
    getOnlineTime(connectTime) {
      let startTime = new Date(connectTime);
      let nowDta = Date.now();
      let coastTime = nowDta - startTime;
      let hour = Math.floor(coastTime / (1000 * 60 * 60));
      let min = Math.floor((coastTime - hour * 1000 * 60 * 60) / (1000 * 60));
      let second = Math.floor((coastTime - hour * 1000 * 60 * 60 - min * 1000 * 60) / 1000);
      return hour + "时" + min + "分" + second + "秒"
    },
    kickOut: function (row) {
      let id = row.userId;
      this.$confirm('此操作将强制下线该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let urlSearchParams = new URLSearchParams();
        urlSearchParams.append('userId', id);
        requestKicKOutOnLineUser(urlSearchParams).then(res => {
          if (res.data.code === 200) {
            this.dataList()
            this.$DonMessage.success("下线成功")
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 列表数据
    dataList() {
      requestOnLineUserList(this.currentPage, this.pagesize).then(res => {
        if (res.data.code === 100) {
          this.list = res.data.data;
          this.total = Number.parseInt(res.data.total);
          this.refreshTime = Date.now()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },    // 用户信息报告高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
  },
  mounted() {
    this.dataList()
    setTimeout(() => {
      this.maximumArea();
    }, 10)
  }
}
</script>
