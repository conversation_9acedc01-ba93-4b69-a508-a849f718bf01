<template>
  <div class="logistics">
    <el-dialog title="物流发货" :visible.sync="dialogVisible" width="800px !important" @close="handleCancel" :close-on-click-modal="false">
      <div class="logistics-form">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <!-- 基本信息 -->
        <div class="section-title">基本信息</div>
        <el-row :gutter="0" justify="center">
          <el-col :span="12">
            <el-form-item label="源单据号">
              <el-input  v-model="form.sourceNo" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货类型" prop="bizType">
              <el-select v-model="form.bizType" placeholder="请选择">
                <el-option label="销售发货" :value="0"/>
                <el-option label="采购退货" :value="3"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 物流信息 -->
        <div class="section-title">物流信息</div>
        <el-row :gutter="5" justify="center">
          <el-col :span="12">
            <el-form-item label="物流单号" prop="logisticsNo">
              <el-input v-model="form.logisticsNo" placeholder="请输入物流单号"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物流公司" prop="logisticsCompanyId">
              <el-select
                v-model="form.logisticsCompanyId"
                placeholder="请选择"
                filterable
                style="width: 100%;"
                @change="handleLogisticsChange"
              >
                <el-option
                  v-for="item in logisticsList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 收货人信息 -->
        <div class="section-title">收货人信息</div>
        <el-row :gutter="5">
          <el-col :span="12">
            <el-form-item label="收货人" prop="consigneeName">
<!--              <select-consignee-info ref="consigneeName"-->
<!--                                     v-model="form.consigneeName"-->
<!--                                     :input-param="form.consigneeName"-->
<!--                                     :contactsId = "form.contactsId"-->
<!--                                     input-type="consignee"-->
<!--                                     placeholder="请输入收货人"-->
<!--                                     @select="handleConsignee"></select-consignee-info>-->
              <el-input v-model="form.consigneeName"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货人电话" prop="consigneeMobile">
              <el-input v-model="form.consigneeMobile"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="收货地址" prop="consigneeAddress">
          <el-input v-model="form.consigneeAddress"/>
        </el-form-item>

        <!-- 发货人信息 -->
        <div class="section-title">发货人信息</div>
        <el-row :gutter="5">
          <el-col :span="12">
            <el-form-item label="发货人" prop="senderName">
              <select-shipp-info
                v-model="form.senderName"
                :input-param="form.senderName"
                input-type="sender"
                placeholder="请输入发货人"
                @select="handleSender"></select-shipp-info>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货人电话" prop="senderMobile">
              <el-input v-model="form.senderMobile"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="发货地址" prop="senderAddress">
          <el-input v-model="form.senderAddress"/>
        </el-form-item>
      </el-form>
      </div>
      <div slot="footer" class="submitArea">
        <el-button type="primary" @click="submitForm">{{ $t('button.submit') }}</el-button>
        <el-button plain @click="handleCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getLogisticsAllList} from "@/api/basicmgt";
import {updateLogisticsOrder} from "@/api/logisticsmgt";
import SelectShippInfo from "@/views/logistics/selectShippInfo.vue";

export default {
  components: {SelectShippInfo},
  emits: ['close'],
  props: {
    selectRow: {
      type: Object
    }
  },
  data() {
    return {
      logisticsList:[],
      dialogVisible: true,
      form: {},
      rules: {
        bizType: [{required: true, message: '请选择发货类型', trigger: 'change'}],
        logisticsNo: [{required: true, message: '请输入物流单号', trigger: 'blur'}],
        logisticsCompany: [{required: true, message: '请选择物流公司', trigger: 'change'}],
        consigneeName: [{required: true, message: '请输入收货人', trigger: 'blur'}],
        consigneeMobile: [{required: true, message: '请输入收货人电话', trigger: 'blur'}],
        consigneeAddress: [{required: true, message: '请输入收货地址', trigger: 'blur'}],
        senderName: [{required: true, message: '请输入发货人', trigger: 'blur'}],
        senderMobile: [{required: true, message: '请输入发货人电话', trigger: 'blur'}],
        senderAddress: [{required: true, message: '请输入发货地址', trigger: 'blur'}],
      },
    };
  },
  methods: {
    //物流公司选择变化
    handleLogisticsChange(id) {
      const selected = this.logisticsList.find(item => item.id === id);
      this.form.logisticsCompany = selected ? selected.name : '';
    },
    //选择收货人信息
    handleConsignee(consignee){
      this.form.consignee = consignee.id
      this.form.consigneeName = consignee.name
      this.form.consigneeMobile = consignee.mobile
      this.form.consigneeAddress = consignee.address
    },
    //选择发货人信息
    handleSender(sender){
      this.form.sender = sender.id
      this.form.senderName = sender.contacts
      this.form.senderMobile = sender.mobile
      this.form.senderAddress = sender.address
    },
    //取消
    handleCancel() {
      this.$emit('close')
    },
    //提交
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          //请求后端，添加发货数据
          updateLogisticsOrder(this.form).then(res => {
            if (res.data.code === 100){
              this.$DonMessage.success("物流信息添加成功")
              this.handleCancel()
            }else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        }
      });
    },
    //获取物流公司信息
    getLogisticsListAll(){
      getLogisticsAllList().then(res => {
        if (res.data.code === 100) {
          this.logisticsList = res.data.data
        }
      })
    },
  },
  mounted() {
    this.form = this.selectRow
    this.getLogisticsListAll()
  }
};
</script>

<style scoped>
.section-title {
  font-size: 16px;
  margin: 5px 0 10px;
  padding-bottom: 5px;
  color: var(--secondary-text);
  border-bottom: 1px dashed var(--border-color);
}

.dialog-footer {
  text-align: right;
}

.logistics .el-input {
  width: 70% !important;
}

</style>
