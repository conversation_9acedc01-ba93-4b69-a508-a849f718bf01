<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>发货单详情</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="出库单号">
                <span>{{ detailInfo.sourceNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="出库日期">
                <span>{{ conversion(detailInfo.outboundTime, 'yyyy-MM-dd') }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="类型">
                <span>{{ getOutboundTypeText(detailInfo.outboundType)}}</span>
              </el-descriptions-item>
              <el-descriptions-item label="源单据号">
                <span>{{ detailInfo.outSourceNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="往来单位">
                <span>{{ detailInfo.counterpartyName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户订单号">
                <span>{{ detailInfo.counterpartyCode }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ detailInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="consignee">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                收货信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item v-if="detailType === 'shipped'" label="发货类型">
                 <span>{{formatBizType(detailInfo.bizType)}}</span>
              </el-descriptions-item>
              <el-descriptions-item v-if="detailType === 'shipped'" label="物流单号">
                <span>{{ detailInfo.logisticsNo}}</span>
              </el-descriptions-item>
              <el-descriptions-item v-if="detailType === 'shipped'" label="物流公司">
                <span>{{ detailInfo.logisticsCompany}}</span>
              </el-descriptions-item>

              <el-descriptions-item label="收货人">
                <span>{{ detailInfo.consigneeName}}</span>
              </el-descriptions-item>
              <el-descriptions-item label="收货人电话">
                <span>{{ detailInfo.consigneeMobile }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="收货地址">
                <span>{{ detailInfo.consigneeAddress}}</span>
              </el-descriptions-item>
              <el-descriptions-item v-if="detailType === 'shipped'" label="发货人">
                <span>{{ detailInfo.senderName}}</span>
              </el-descriptions-item>
              <el-descriptions-item v-if="detailType === 'shipped'" label="发货人电话">
                <span>{{ detailInfo.senderMobile}}</span>
              </el-descriptions-item>
              <el-descriptions-item v-if="detailType === 'shipped'" label="发货地址">
                <span>{{ detailInfo.senderAddress}}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="detailInfo.logisticsShipDetails"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="商品编码" prop="productCode" width="90"></el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="100"> </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="80">
                <template slot-scope="{ row }">
                  <img v-if="row.imageUrl !== ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" prop="brand" min-width="100"> </el-table-column>
              <el-table-column label="规格型号" prop="productModel" min-width="100"> </el-table-column>
              <el-table-column label="出库仓库" prop="warehouseName" min-width="100"> </el-table-column>
              <el-table-column label="库位" prop="locationName" min-width="100"> </el-table-column>
              <el-table-column label="单位" prop="unit" min-width="100"> </el-table-column>
              <el-table-column v-if="detailType === 'pending'" label="待发货数量" prop="quantity" min-width="100"> </el-table-column>
              <el-table-column v-if="detailType === 'shipped'" label="发货数量" prop="quantity" min-width="100"> </el-table-column>
<!--              <el-table-column label="单价(￥)" prop="unitPrice" min-width="100"> </el-table-column>-->
<!--              <el-table-column label="总金额(￥)" prop="amount" min-width="100"> </el-table-column>-->
              <el-table-column label="备注" prop="remark" min-width="100"> </el-table-column>
            </el-table>
          </el-collapse-item>

          <el-collapse-item name="status">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                状态信息
              </span>
            </template>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="制单人">
                {{ detailInfo.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import {conversion} from "../../store/filters";
import {sysServerUrl} from "@/assets/js/common";
import {logisticsOrderInfo} from "@/api/logisticsmgt";
import {dictTypeQueryData} from "@/api/sysmgt";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "logisticsDetail",
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      bizTypes: [],
      detailInfoId: '',
      detailType: '',
      activeNames: ["base","consignee", "product", "status", "more"], // 全部展开
      detailInfo: {
        logisticsShipDetails: [],
      },
    }
  },
  methods: {
    //格式化发货类型
    formatBizType(bizType){
      const findBizType = this.bizTypes.find(item => Number(item.code) === bizType);
      return findBizType ? findBizType.name : '';
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //根据发货单id获取详细数据
    getDetailInfo(){
      const params = {
        id: this.detailInfoId
      }
      logisticsOrderInfo(params).then(res => {
        console.log("jjjjjjjjjjjjj",res)
        if (res.data.code === 100){
          this.detailInfo = res.data.data;
        }
      })
    },
    sysServerUrl() {
      return sysServerUrl
    },
    conversion,
    getOutboundTypeText(status) {
      switch(status) {
        case 'sales':
          return '销售出库';
        case 1:
          return '部分入库';
        case 'other':
          return '其他出库';
        default:
          return '未知出库'; // 默认情况，可根据需要调整
      }
    },
    getLogisticsBizType(){
      dictTypeQueryData('logisticsBizType').then(res => {
        if (res.data.code === 100) {
          this.bizTypes = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    this.detailType = this.$route.params.type;
    this.detailInfoId = this.$route.params.id;
    this.getLogisticsBizType();
    this.getDetailInfo();
  },
}
</script>

<style scoped>

</style>
