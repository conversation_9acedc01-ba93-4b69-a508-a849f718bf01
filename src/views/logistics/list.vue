<template>
  <div class="layoutContainer">

    <div class="secondFloat">
      <!-- 添加代发货/已发货切换标签 -->
      <div class="tab-container">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="待发货" name="pending"></el-tab-pane>
          <el-tab-pane label="已发货" name="shipped"></el-tab-pane>
        </el-tabs>
      </div>
      <el-form :inline="true" :model="queryParams" :label-width="$labelFour">
        <!-- 申请日期 -->
        <el-form-item label="单据日期">
          <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
        </el-form-item>
        <el-form-item label="源单据号" prop="sourceNo">
          <el-input
            v-model="queryParams.sourceNo"
            placeholder="请输入源单据号"
            clearable
          />
        </el-form-item>
        <el-form-item label="往来单位">
          <el-input v-model="queryParams.contactsName"
                    placeholder="请输入往来单位"
                    clearable></el-input>
        </el-form-item>
        <!-- 业务类型 -->
        <!-- 收货人 -->
        <el-form-item label="收货人" prop="consigneeName">
          <el-input
            v-model="queryParams.consigneeName"
            placeholder="请输入收货人"
            clearable
          />
        </el-form-item>
        <!-- 状态 -->
<!--        <el-form-item label="状态" prop="status" v-if="isShow && activeTab ==='pending'">-->
<!--          <el-select-->
<!--            v-model="queryParams.status"-->
<!--            placeholder="请选择状态"-->
<!--            clearable-->
<!--          >-->
<!--            <el-option-->
<!--              v-for="item in logisticsStatus"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="发货类型" prop="bizType" v-if="isShow && activeTab ==='shipped'">
        <el-select
            v-model="queryParams.bizType"
            placeholder="发货类型"
            clearable
          >
            <el-option
              v-for="item in bizTypes"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain type="primary" @click="handleReset">{{ $t('button.reset') }}</el-button>
          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i
            class="el-icon-arrow-up el-icon--right"></i></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="el-icon-plus" @click="shipments()" v-if="activeTab === 'pending'">发货</el-button>
<!--          <el-button type="text" icon="bulkImport-icon" @click="batchImport()">批量导入</el-button>-->
<!--          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>-->
<!--          <el-button type="text" icon="deleteRed-icon" @click="delClick()">删除</el-button>-->
        </div>
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="sourceNo" label="源单据号" width="170">
          <template #default="{row}">
              <span @click="handleDetail(row)" class="linkStyle">{{row.sourceNo}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="单据日期" width="160">
          <template #default="{row}">
            {{conversion(row.createdTime,'yyyy-MM-dd HH:mm:ss')}}
          </template>
        </el-table-column>
        <el-table-column v-if="activeTab ==='shipped'" prop="bizType" label="发货类型" width="100">
          <template #default="{row}">
            {{formatBizType(row.bizType)}}
          </template>
        </el-table-column>
        <el-table-column prop="contactsName" label="往来单位" width="200"/>
        <el-table-column prop="consigneeName" label="收货人" width="100"/>
        <el-table-column prop="consigneeMobile" label="收货人电话" width="120"/>
        <el-table-column prop="consigneeAddress" label="收货地址" width="300"/>
        <el-table-column v-if="activeTab ==='shipped'" prop="senderName" label="发货人" width="100"/>
        <el-table-column v-if="activeTab ==='shipped'" prop="senderMobile" label="发货人电话" width="120"/>
        <el-table-column v-if="activeTab ==='shipped'" prop="senderAddress" label="发货地址" width="300"/>
        <el-table-column prop="remark" label="备注" width="auto"/>
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="{row}">
            <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
            <el-button v-if="activeTab === 'pending'" type="text" size="small" @click="handleShipments(row)">发货</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>

    </div>
    <logisticsDialog v-if="logisticsDialogVisible"
                     :selectRow="selectRow"
                     @close="handleDialogClose"></logisticsDialog>

  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {addTabs, tableHeight} from "@/assets/js/common";
import {logisticsOrderList, purchaseOrderList} from "@/api/logisticsmgt";
import LogisticsDialog from "@/views/logistics/logisticsDialog.vue";
import {dictTypeQueryData} from "@/api/sysmgt";

/**
 * 物流列表组件
 * 展示物流信息列表，支持查询、新增、编辑、删除等操作
 */
export default {
  name: "logisticsList",
  components: {LogisticsDialog, DateRangeSelector, Pagination},
  data() {
    return {
      logisticsDialogVisible: false,
      bizTypes: [],
      activeTab: 'pending', // 默认选中代发货标签
      selectList: [],
      selectRow: {},
      deleteList: [],
      maximumHeight: 0,
      total: 0,
      isShow: false,
      resultList: [],
      queryParams: {
        contactsName: '', // 往来单位
        logisticsCompany: '',
        logisticsNo: '',
        sourceNo: '',
        bizType: null, //业务类型
        status: null,
        consigneeName: '',
        consigneeMobile: '',
        shippingStatus: 0, // 0: 代发货, 2: 已发货
        page: 1,
        pageSize: 10
      },
    }
  },
  methods: {
    //选择发货
    shipments(){
      if (this.selectList.length === 0){
        this.$DonMessage.warning('请选择一条数据进行操作');
        return;
      }
      if (this.selectList.length > 1){
        this.$DonMessage.warning('只能选择一条数据进行操作');
        return;
      }
      this.selectRow = this.selectList[0];
      this.logisticsDialogVisible = true;
    },
    //处理标签页切换事件
    handleTabClick(tab) {
      // 根据标签页设置发货状态 切换代发货，已发货状态
      this.queryParams.shippingStatus = (tab.name === 'pending' ? 0 : 2);
      this.activeTab = tab.name;
      this.handleSearch();
    },
    // 表格高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 获取日期范围选择
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      this.handleSearch();
    },
    //时间格式化方法
    conversion,
    //处理发货信息弹窗关闭
    handleDialogClose(){
      this.logisticsDialogVisible = false;
      this.handleSearch();
    },
    //查询
    handleSearch() {
      // 发起请求查询接口
      this.dataList(this.queryParams)
    },
    //格式化发货类型
    formatBizType(bizType){
      const findBizType = this.bizTypes.find(item => Number(item.code) === bizType);
      return findBizType ? findBizType.name : '';
    },
    //跳转到源单据详情页
    handleSourceOrderDetail(row){
      console.log("跳转到源单据详情页", row)
    },
    // 选择的数据
    handleSelectionChange(val) {
      this.selectList = val
    },
    //重置查询条件
    handleReset() {
      this.queryParams = {
        page: 1,
        pageSize: 10
      }
      //重置日期选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      this.handleSearch()
    },
    //分页查询数据
    dataList(params) {
      // 调用API接口获取物流列表数据
      logisticsOrderList(params).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 新增
    addClick() {
      // 跳转到新增页面
      this.$router.push('/logistics/add')
    },
    // 导出
    exportClick() {
      // 导出功能实现
      this.$DonMessage.info('导出功能待实现')
    },
    // 批量导入
    batchImport() {
      // 批量导入功能实现
      this.$DonMessage.info('批量导入功能待实现')
    },
    // 删除
    delClick() {
      if (this.deleteList.length === 0) {
        this.$DonMessage.warning('请选择要删除的数据')
        return
      }
      this.$confirm('确定删除选中的数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除功能实现
        this.$DonMessage.success('删除成功')
        this.handleSearch()
      })
    },
    // 详情
    handleDetail(row) {
      // 跳转到详情页面
      const title = row.sourceNo;
      this.$router.push({
        name: 'logisticsDetail',
        params: {
          id: row.id,
          type: this.activeTab
        }
      })
      addTabs(this.$route.path, title)
    },
    // 操作发货
    handleShipments(row) {
      if (!row){
        this.$DonMessage.warning('请选择一条数据进行操作');
        return;
      }
      this.selectRow = row;
      this.logisticsDialogVisible = true;
    },
    getLogisticsBizType(){
      dictTypeQueryData('logisticsBizType').then(res => {
        if (res.data.code === 100) {
          this.bizTypes = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    this.getLogisticsBizType()
    this.handleSearch();
  },
  created() {
    console.log('物流列表组件已创建');
  }
}
</script>

<style scoped>
/* 标签页样式 */
.tab-container {
  margin-bottom: 15px;
  background-color: #fff;
  padding: 0 20px 0;
  border-bottom: 1px solid #e4e7ed;
  user-select: none;
}

/* 可以根据需要添加其他样式 */
</style>
