<template>
  <div class="elAutocomplete">
    <el-autocomplete
      ref="autocomplete"
      popper-class="my-autocomplete"
      :inputParam="inputParam"
      :inputType="inputType"
      v-model="inputValue"
      :fetch-suggestions="querySearch"
      :placeholder="placeholder"
      @select="handleSelect"
      :disabled="disabled"
      @blur="handleBlur"
    >
      <template slot-scope="{item}">
        {{ item.name }}
      </template>
    </el-autocomplete>
    <el-button :disabled="disabled" type="text" icon="selectIcon-icon" @click="handleIconClick"></el-button>
    <el-dialog width="1400px !important" :title="textMap[dialogStatus]" :visible.sync="dialogCategoryVisible"
               :append-to-body="true" v-if="dialogCategoryVisible">
      <!--        <div class="secondFloat">-->
      <!--          <el-form :inline="true" :model="searchForm" label-width="100px">-->
      <!--            <el-form-item label="销售订单号">-->
      <!--              <el-input v-model="searchForm.salesNo" placeholder="请输入销售订单号"></el-input>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="订单日期">-->
      <!--              <el-date-picker-->
      <!--                v-model="searchForm.salesTime"-->
      <!--                type="date"-->
      <!--                placeholder="选择日期"-->
      <!--              ></el-date-picker>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item>-->
      <!--              <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>-->
      <!--              <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>-->
      <!--            </el-form-item>-->
      <!--          </el-form>-->
      <!--        </div>-->
      <el-table
        style="width: 100%;"
        ref="table"
        :data="resultData"
        border
        stripe
        highlight-current-row
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="name" label="联系人" width="100"/>
        <el-table-column prop="mobile" label="联系电话" width="120"/>
        <el-table-column prop="telephone" label="座机" width="120"/>
        <el-table-column prop="sex" label="性别" width="60"/>
        <el-table-column prop="wechat" label="微信" width="150"/>
        <el-table-column prop="email" label="邮箱" width="150"/>
        <el-table-column prop="address" label="地址" width="350"/>
        <el-table-column prop="defaultFlag" label="默认联系人" width="100">
          <template #default="{row}">
            <el-switch v-model="row.defaultFlag"
                       :active-value="1"
                       :inactive-value="0"
                       disabled></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" width="auto">
        </el-table-column>
      </el-table>
      <div class="submitArea">
        <el-button type="primary" @click="onSubmit">{{ $t('button.submit') }}</el-button>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {renderTree} from "@/assets/js/common";
import {getStatusClass} from "@/assets/js/utils";
import {getContactsBySupplierId} from "@/api/basicmgt";

export default {
  name: "selectConsigneeInfo",
  props: {
    contactsId: String, //客户id
    modelValue: String,
    placeholder: {type: String, default: "请输入内容"},
    inputParam: {type: String, default: ""},
    inputType: {type: String, default: ""},
    warehouseId: {type: String, default: ""},
    disabled: Boolean,
  },
  emits: ['update:modelValue', 'search','select'],
  data() {
    return {
      textMap: {
        categoryName: '商品分类',
        brand: "品牌分类",
        brandInfo: '品牌',
        warehouseInfo: '默认仓库',
        locationInfo: '默认库位',
        salesOrder: '选择销售订单',
        consignee: '选择联系人'
      },
      resultData: [],
      auditStatusDicts: [],
      inputValue: "",
      categoryList: [],
      dialogCategoryVisible: false,
      dialogStatus: '',
      searchForm: {},
      searchVal: "", // 搜索值
      searchResult: [],
      selectedValue: [], // 记录是否选择
      pagesize: 10,
      currentPage: 1,
      total: 0,
      currentRow: [],
    }
  },
  watch: {
    inputValue(val) {
      this.inputValue = val;
    },
  },
  mounted() {
    if (this.inputParam !== null && this.inputParam !== undefined && this.inputParam !== '') {
      this.inputValue = this.inputParam;
    }
    this.dataList(); // 更有语义的函数名
  },
  methods: {
    getStatusClass,
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    handleSelectionChange(selection) {
      if (selection.length > 1) {
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(selection.pop());
      }
      this.treeCurrentNode = selection[0];
    },
    // 获取数据
    dataList() {
      //根据客户id查询客户联系人信息列表
      getContactsBySupplierId(this.contactsId).then(res => {
        if (res.data.code === 100) {
          this.resultData = res.data.data;
        }
      })
    },
    // 查询数据
    async querySearch(queryString, cb) {
      var restaurants = this.categoryList;
      var results = queryString ? restaurants.filter(this.createAssemblyFilter(queryString)) : restaurants;
      // this.searchResult = results;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createAssemblyFilter(queryString) {
      return (restaurant) => {
        return (restaurant.name.indexOf(queryString) !== -1);
      };
    },
    // 选中值
    handleSelect(item) {
      this.selectedValue = item;
      this.inputValue = item.name
      this.$emit('select', item);
    },
    handleBlur() {
      if (!this.selectedValue) {
        this.inputValue = ''; // 未选择则清空
      }
    },
    clear() {
      this.selectedValue = '';
      this.inputValue = '';
      this.$emit('select', '');
    },
    // 搜索值
    handleSearch() {
      this.dataList();
    },
    handleReset() {

    },

    onSearch() {
    },
    async handleIconClick() {
      if (this.disabled === true) {
        return
      }
      this.dataList();
      this.dialogStatus = this.inputType;
      this.dialogCategoryVisible = true;
    },
    // 树结构图标
    renderContent(h, {node, data}) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".categoryTreeManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 数选中值
    handleCategorySelect(node) {
      this.treeCurrentNode = node
    },
    // 提交
    onSubmit() {
      if (this.treeCurrentNode == null) {
        this.$DonMessage.warning("请选择联系人")
        return
      }
      this.inputValue = this.treeCurrentNode.name;
      this.$emit('select', this.treeCurrentNode);
      this.dialogCategoryVisible = false;
    },
    // 取消
    onCancel() {
      this.dialogCategoryVisible = false;
    },
  },
}
</script>

<style scoped>
.elAutocomplete .el-input__suffix {
  cursor: pointer;
}

.searchTreeArea {
  display: flex;
  margin-bottom: 10px;
}

.el-dialog .searchTreeArea .el-input {
  width: 300px !important;
  margin-right: 10px;
}

.el-dialog .searchTreeArea .el-input .el-input__inner {
  width: 100% !important;
}
</style>
