<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>入库单详情</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="入库单号">
                <span>{{ detailInfo.inboundNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="入库日期">
                <span>{{ detailInfo.inboundTime | conversion('yyyy-MM-dd') }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="类型">
                <span>{{ getoutboundTypeText(detailInfo.inboundType)}}</span>
              </el-descriptions-item>
              <el-descriptions-item label="源单据号">
                <span>{{ detailInfo.sourceNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="往来单位">
                <span>{{ detailInfo.counterpartyName }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ detailInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="detailInfo.stockInboundDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              show-summary
              :summary-method="getSummaries"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="商品编码" prop="productCode" min-width="120"></el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="120"></el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="100">
                <template slot-scope="{row}">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" prop="brand" width="110"></el-table-column>
              <el-table-column label="规格型号" prop="productModel" width="110"></el-table-column>
              <el-table-column label="入库仓库" prop="warehouseName" width="110"></el-table-column>
              <el-table-column label="库位" prop="locationName" width="110"></el-table-column>
              <el-table-column label="单位" prop="unit" width="100"></el-table-column>
              <el-table-column label="入库数量" prop="currentQty" width="100" align="right"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
            </el-table>
          </el-collapse-item>

          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="制单人">
                {{ detailInfo.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="附件">
                <div v-for="(item, index) of detailInfo.productAccessoryList" :key="index">
                  <img v-if="item.format == 'jpg' || item.format == 'png'" :src="$filePath + item.path" alt="" style="width: 100px; max-height: 100px;" />
                  <span v-else>{{item.fileName}}</span>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>
<script>
import { removeTabs, collapseArea } from "@/assets/js/common";
import {inboundInfo} from "@/api/stockmgt";

export default {
  name: "outStockDetail",
  data() {
    return {
      id: "",
      detailInfo: {},
      accessoryList: [], // 附件信息
      stockInboundDetailList:[],
      fileList: [], // 图片上传列表
      activeNames: ["base", "product", "status", "more"], // 全部展开
    }
  },
  methods:{
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    getStatusText(status) {
      switch(status) {
        case 0:
          return '未入库';
        case 1:
          return '部分入库';
        case 2:
          return '已入库';
        default:
          return '未知状态'; // 默认情况，可根据需要调整
      }
    },
    getoutboundTypeText(status) {
      switch(status) {
        case 'purchase':
          return '采购入库';
        case 'return':
          return '退料入库';
        case 'transfer':
          return '调拨入库';
        case 'other':
          return '其他入库';
        default:
          return '未知入库'; // 默认情况，可根据需要调整
      }
    },
    async dataList() {
      var _this = this;
      try {
        const res = await inboundInfo(_this.id)
        if (res.data.code == "100") {
          _this.detailInfo = res.data.data
          if (_this.detailInfo.productAccessoryList) {
            var imgList = _this.detailInfo.productAccessoryList.length;
            if (imgList > 0) {
              _this.accessoryList = _this.detailInfo.productAccessoryList;
            }
          }
          var productList = _this.detailInfo.stockInboundDetailList.length;
          if (productList > 0) {
            _this.stockInboundDetailList = _this.detailInfo.stockInboundDetailList;
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          removeTabs(this.$route);
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        removeTabs(this.$route);
      }
      collapseArea()
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        if (this.detailInfo.stockInboundDetailList) {
          switch (column.property) {
            case "currentQty":
              this.detailInfo.stockInboundDetailList.forEach((item) => {
                if (item.currentQty) {
                  count += item.currentQty;
                }
              });
              sums[index] = count;
              break;
          }
        }
      })
      return sums;
    },
  },
  mounted() {
    var _this = this;
    _this.id = _this.$route.params.id;
    _this.dataList();
  },
}
</script>
