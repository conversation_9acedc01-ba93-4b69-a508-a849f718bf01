<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>待入库单详情</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="生成日期">
                <span>{{ detailInfo.createdTime | conversion('yyyy-MM-dd') }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="类型">
                <span>{{ getoutboundTypeText(detailInfo.inboundType)}}</span>
              </el-descriptions-item>
              <el-descriptions-item label="源单据号">
                <span>{{ detailInfo.sourceNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="往来单位">
                <span>{{ detailInfo.counterpartyName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <span>{{getStatusText(detailInfo.status)}}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ detailInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="detailInfo.stockInboundDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              show-summary
              :summary-method="getSummaries"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="商品编码" prop="productCode" min-width="120"></el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="120"> </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="100">
                <template slot-scope="{row}">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" prop="brand" width="110"> </el-table-column>
              <el-table-column label="规格型号" prop="productModel" width="110"> </el-table-column>
              <el-table-column label="单位" prop="unit" width="100"> </el-table-column>
              <el-table-column label="数量" prop="quantity" width="110" align="right"> </el-table-column>
              <el-table-column label="待入库数量" prop="beQty" width="120" align="right"></el-table-column>
              <el-table-column label="已入库数量" prop="yetQty" width="120" align="right"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"> </el-table-column>
              <!-- 操作 -->
              <el-table-column label="操作" width="100" >
                <template slot-scope="{row}" fixed="right" v-if="row.yetQty !== 0">
                  <el-button type="text" size="small" @click="inStockDetailByProductId(row)">
                    入库明细
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="制单人">
                {{ detailInfo.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <el-dialog v-dialogDrag :width="'800px !important'" :title="'入库明细'" :visible.sync="dialogStatus">
      <el-descriptions>
      <el-descriptions-item label="商品编码">
        <span>{{this.productInfo.code}}</span>
      </el-descriptions-item>
      <el-descriptions-item label="商品名称">
        <span>{{ this.productInfo.name }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="单位">
        <span>{{ this.productInfo.unit }}</span>
      </el-descriptions-item>
        </el-descriptions>
        <el-table
          style="width: 100%;"
          ref="table"
          border
          stripe
          :data="contactsList"
          highlight-current-row
          @header-dragend="changeColWidth"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column label="序号" width="50" type="index"></el-table-column>
          <el-table-column prop="createdTime" label="入库日期" width="120">
            <template slot-scope="scope">
              {{ scope.row.createdTime | conversion('yyyy-MM-dd') }}
            </template>
          </el-table-column>
          <el-table-column label="入库单号" prop="inboundNo" min-width="100"></el-table-column>
          <el-table-column label="入库仓库" prop="warehouseName" min-width="100"></el-table-column>
          <el-table-column label="库位" prop="locationName" width="100"></el-table-column>
          <el-table-column label="入库数量" prop="currentQty" min-width="100" align="right"></el-table-column>
          <el-table-column label="状态"  prop="status" width="80">
            <template slot-scope="scope">
              <div :class="getStatusClass(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" min-width="120"></el-table-column>
        </el-table>
        <Pagination v-show="contactsPage.total > 0" :total="contactsPage.total" :page.sync="contactsPage.currentPage"
                    :limit.sync="contactsPage.pagesize" @pagination="inboundDetailInfo(row)" />

    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import {removeTabs, collapseArea} from "@/assets/js/common";
import {inboundDetailInfo, inboundInfo} from "@/api/stockmgt";
import inStockDetail from "@/views/stock/inStock/inStockDetail.vue";
export default {
  name: "beInStockDetail",
  components: { Pagination },
  computed: {
    inStockDetail() {
      return inStockDetail
    }
  },
  data() {
    return {
      productInfo:{
        name:'',
        code:'',
        unit:''
      },
      contactsPage: {
        pagesize: 10,
        currentPage: 1,
        total: 0,
      },
      contactsList: [],
      dialogStatus: false, // 控制弹窗显示/隐藏
      detailInfo: {},
      id: "",
      accessoryList: [], // 附件信息
      stockInboundDetailList:[],
      activeNames: ["base", "product", "status", "more"], // 全部展开
    }
  },
  methods:{
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    getStatusClass(status) {
      switch(status) {
        case 0:
          return 'errorColor'; // 未生成 - 红色或其他错误颜色
        case 1:
          return 'warningColor'; // 部分生成 - 黄色或其他警告颜色
        case 2:
          return 'successColor'; // 已生成 - 绿色或其他成功颜色
        case 3:
          return ''; // 未生成 - 红色或其他错误颜色
        case 4:
          return 'errorColor'; // 部分生成 - 黄色或其他警告颜色
        case 5:
          return 'successColor'; // 已生成 - 绿色或其他成功颜色
        case 6:
          return 'errorColor'; // 驳回
        default:
          return 'errorColor'; // 默认情况，可根据需要调整
      }
    },
    getStatusText(status) {
      switch(status) {
        case 0:
          return '未入库';
        case 1:
          return '部分入库';
        case 2:
          return '已入库';
        case 3:
          return '草稿';
        case 4:
          return '待审核';
        case 5:
          return '已审核';
        case 6:
          return '审核驳回';
        default:
          return '未知状态'; // 默认情况，可根据需要调整
      }
    },
    inboundDetailInfo(row){
      const params = {
        productId: row.productId,
        sourceId: this.detailInfo.sourceId,
        page: this.contactsPage.currentPage,
        limit: this.contactsPage.pagesize,
      }
      inboundDetailInfo(params).then((res) => {
        if (res.data.code == "100") {
          this.contactsList = res.data.data;
          this.contactsPage.total = res.data.total;
        }
      })
    },
    getoutboundTypeText(status) {
      switch(status) {
        case 'purchase':
          return '采购入库';
        case 'return':
          return '退料入库';
        case 'transfer':
          return '调拨入库';
        case 'production':
          return '成品入库';
        case 'other':
          return '其他入库';
        default:
          return '未知入库'; // 默认情况，可根据需要调整
      }
    },
    async dataList() {
      var _this = this;
      try {
        const res = await inboundInfo(_this.id)
        if (res.data.code == "100") {
          _this.detailInfo = res.data.data
          if(_this.detailInfo.productAccessoryList) {
            var imgList = _this.detailInfo.productAccessoryList.length;
            if (imgList > 0) {
              _this.accessoryList = _this.detailInfo.productAccessoryList;
            }
          }
          var productList = _this.detailInfo.stockInboundDetailList.length;
          if (productList > 0) {
            _this.stockInboundDetailList = _this.detailInfo.stockInboundDetailList;
          }
        }else {
          this.$DonMessage.warning("当前信息不存在");
          removeTabs(this.$route);
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        removeTabs(this.$route);
      }
      collapseArea()
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        if (this.detailInfo.stockInboundDetailList) {
          switch (column.property) {
            case "quantity":
              this.detailInfo.stockInboundDetailList.forEach((item) => {
                if (item.quantity) {
                  count += item.quantity;
                }
              });
              sums[index] = count;
              break;
            case "beQty":
              this.detailInfo.stockInboundDetailList.forEach((item) => {
                if (item.beQty) {
                  count += item.beQty;
                }
              });
              sums[index] = count;
              break;
            case "yetQty":
              this.detailInfo.stockInboundDetailList.forEach((item) => {
                if (item.yetQty) {
                  count += item.yetQty;
                }
              });
              sums[index] = count;
              break;
          }
        }
      })
      return sums;
    },
    // 获取入库明细
    inStockDetailByProductId(row){
      this.productInfo.name = row.productName;
      this.productInfo.code = row.productCode;
      this.productInfo.unit = row.unit;
      this.dialogStatus = true;
      const params = {
        productId: row.productId,
        sourceId: this.detailInfo.sourceId,
        page: this.contactsPage.currentPage,
        limit: this.contactsPage.pagesize,
      }
      inboundDetailInfo(params).then((res) => {
        if (res.data.code == "100") {
          this.contactsList = res.data.data;
          this.contactsPage.total = res.data.total;
        }
      })
    }
  },
  mounted() {
    var _this = this;
    _this.id = _this.$route.params.id;
    _this.detailType = _this.$route.params.detailType;
    _this.dataList();
  },
}
</script>
