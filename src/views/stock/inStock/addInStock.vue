<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="this.stateType == 'edit'">编辑入库单</div>
      <div v-else>新增入库单</div>
      <div>
        <el-button type="primary" @click="onSave()">保存</el-button>
        <el-button plain @click="onSubmit()">提交</el-button>
        <el-button plain @click="onSaveAndAudit()">提交并审核</el-button>
        <el-button plain @click="removeClick()">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFour">
                <el-form-item label="入库单号" prop="inboundNo">
                  <el-input v-model="form.inboundNo" placeholder="入库单号由系统生成" disabled />
                </el-form-item>
                <el-form-item label="入库日期" prop="inboundTime">
                  <el-input disabled v-model="form.inboundTime"></el-input>
                </el-form-item>
                <el-form-item label="类型" prop="inboundType">
                  <div v-if="disabledStatue">
                    <el-select v-model="form.inboundType" placeholder="请选择类型"  clearable :disabled="disabledStatue">
                      <el-option
                        v-for="item in salesTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div v-if="!disabledStatue">
                    <el-select v-model="form.inboundType" placeholder="请选择类型"  clearable :disabled="disabledStatue">
                      <el-option
                        v-for="item in addSalesTypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item label="源单据号" prop="sourceNo">
                  <SelectPurchaseOrder
                    v-if="form.inboundType == 'purchase'"
                    ref="selectInput"
                    v-model="form.sourceNo"
                    :input-param="form.sourceNo"
                    input-type="salesOrder"
                    placeholder="请选择采购订单"
                    @select="handleSelectSalesOrder"
                    :disabled="disabledStatue"
                  ></SelectPurchaseOrder>

                  <el-input
                    v-else-if="form.inboundType == 'return'"
                    ref="selectInput"
                    v-model="form.sourceNo"
                    :input-param="form.sourceNo"
                    input-type="salesOrder"
                    placeholder="请选择退料单"
                    :disabled="disabledStatue"
                  ></el-input>

                  <el-input
                    v-else-if="form.inboundType == 'transfer'"
                    ref="selectInput"
                    v-model="form.sourceNo"
                    :input-param="form.sourceNo"
                    input-type="salesOrder"
                    placeholder="请选择调拨单"
                    :disabled="disabledStatue"
                  ></el-input>

                  <el-input
                    v-else-if="form.inboundType == 'production'"
                    ref="selectInput"
                    v-model="form.sourceNo"
                    :input-param="form.sourceNo"
                    input-type="salesOrder"
                    placeholder="请选择生产订单"
                    :disabled="disabledStatue"
                  ></el-input>


                  <el-input v-else disabled></el-input>
                </el-form-item>
                <el-form-item label="往来单位" prop="counterpartyName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.counterpartyName"
                    :inputParam="form.counterpartyName"
                    :disabled="stateType == 'add' ? form.sourceNo ?  true : false: disabledStatue"
                    inputType="partnerSupplierInfo"
                    placeholder="请选择往来单位"
                    @select="onInputSearch('', 'partner', $event)"
                  ></selectInput>
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500" show-word-limit/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <div class="tableHandle spaceBbetwee" v-if="stateType == 'add' ? form.sourceNo ?  false: true: !disabledStatue">
              <el-button type="primary" icon="addProducts-icon" @click="addProduct">添加商品</el-button>
              <div>
                <el-button type="text" icon="import-icon" v-if="false">批量导入</el-button>
                <el-button type="text" icon="setIcon-icon" v-if="false">批量设置</el-button>
                <el-button type="text" icon="deleteRed-icon" @click="batchDelete">批量删除</el-button>
              </div>
            </div>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.stockInboundDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              @selection-change="handleSelectChange"
              show-summary
              :summary-method="getSummaries"
            >
<!--              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="商品编码" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productCode !== undefined">{{row.productCode}}</span>
                </template>
              </el-table-column>
              <el-table-column label="商品名称" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productName !== undefined">{{row.productName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="80">
                <template slot-scope="{row}">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" width="100">
                <template slot-scope="{row}">
                  <span v-if="row.brand !== undefined">{{row.brand}}</span>
                </template>
              </el-table-column>
              <el-table-column label="规格型号" width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productModel !== undefined">{{row.productModel}}</span>
                </template>
              </el-table-column>
              <el-table-column label="入库仓库" prop="warehouseName" min-width="120">
                <template #header>
                  <span class="required-field">入库仓库</span>
                </template>
                <template #default="{ row, $index }">
                  <el-form :model="row" :rules="infoRules" ref="warehouseName">
                    <el-form-item prop="warehouseName">
                      <div class="rowEditShow">
                        <selectInput
                          ref="selectInput"
                          v-model="row.warehouseName"
                          :inputParam="row.warehouseName"
                          inputType="warehouseInfo"
                          placeholder="请选择入库仓库"
                          @select="onInputSearch($index,'warehouse', $event)"
                        ></selectInput>
                      </div>
                    </el-form-item>
                  </el-form>
               </template>
              </el-table-column>
              <el-table-column label="库位" prop="locationName" min-width="120">
                <template #header>
                  <span class="required-field">库位</span>
                </template>
                <template #default="{ row, $index }">
                  <el-form :model="row" :rules="infoRules" ref="locationName">
                    <el-form-item prop="locationName">
                      <div class="rowEditShow">
                        <selectInput
                          ref="selectInput"
                          v-model="row.locationName"
                          :inputParam="row.locationName"
                          inputType="locationInfo"
                          :warehouseId= row.inWarehouseId
                          placeholder="请选择库位"
                          @select="onInputSearch($index,'location', $event)"
                        ></selectInput>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="单位" width="80" prop="unit" v-if="stateType == 'add' ? form.sourceNo ?  true: false : disabledStatue">
                <template slot-scope="{row}">
                  <span v-if="row.unit !== undefined">{{row.unit}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单位" width="80" prop="unit" v-if="stateType == 'add' && !form.sourceNo">
                <template #header >
                  <span class="required-field">单位</span>
                </template>
                <template #default="{row}" >
                  <el-form :model="row" :rules="infoRules" ref="unit">
                    <el-form-item prop="unit">
                      <div class="rowEditShow">
                        <el-select v-model="row.unit">
                          <el-option v-for="item of unitData" :key="item.id" :label="item.name" :value="item.name"></el-option>
                        </el-select>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="入库数量" min-width="80" prop="currentQty" v-if="this.stateType != 'addInfo'">
                <template #header>
                  <span class="required-field">入库数量</span>
                </template>
                <template #default="{row}">
                  <el-form :model="row" :rules="infoRules" ref="currentQty">
                    <el-form-item prop="currentQty">
                      <div class="rowEditShow">
                        <el-input v-model.number="row.currentQty"></el-input>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="数量" prop="quantity" min-width="80" align="right" v-if="this.stateType == 'addInfo'">
                <template slot-scope="{row}">
                  <span v-if="row.quantity !== undefined">{{row.quantity}}</span>
                </template>
              </el-table-column>
              <el-table-column label="待入库数量" prop="beQty" min-width="80" align="right" v-if="this.stateType == 'addInfo'">
                <template slot-scope="{row}">
                  <span v-if="row.beQty !== undefined">{{row.beQty}}</span>
                </template>
              </el-table-column>
              <el-table-column label="本次入库数量" min-width="80" prop="currentQty" v-if="this.stateType == 'addInfo'">
                <template #header>
                  <span class="required-field">本次入库数量</span>
                </template>
                <template #default="{row}">
                  <el-form :model="row" :rules="infoRules" ref="currentQty">
                    <el-form-item prop="currentQty">
                      <div class="rowEditShow">
                        <el-input v-model.number="row.currentQty"></el-input>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>

              <el-table-column label="备注" min-width="120">
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <el-input v-model.number="row.remark"></el-input>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" fixed="right" width="80">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton" @click="delClick(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="创建人">
                {{ form.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="this.stateType == 'edit'">
                {{ form.updatedUser }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="this.stateType == 'edit'">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="附件">
                <el-upload
                  style="width: 500px;"
                  class="upload-demo"
                  :action="uploadUrl"
                  :headers="importHeader"
                  :file-list="fileList"
                  :on-remove="handleOnRemove"
                  :before-remove="beforeOnRemove"
                  :before-upload="beforeAvatarUpload"
                  :on-exceed="handleOnExceed"
                  :on-success="handleOnSuccess"
                  :limit="1"
                >
                  <span class="linkStyle" v-if="this.count == 0">点击上传</span>
                </el-upload>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <dialogTable
      v-if="isReload"
      :isReload.sync="isReload"
      type="product"
      :formList.sync="form.stockInboundDetailList"
      :columns.sync="productColumns"
    >
      <template #imageUrl="scope">
        <img v-if="scope.row.imageUrl != ''" class="pictureShow" :src="$filePath + scope.row.imageUrl" alt="">
      </template>
    </dialogTable>
  </div>
</template>
<script>
import selectInput from "@/components/selectInput/selectInput.vue";
import SelectPurchaseOrder from "@/views/stock/inStock/SelectPurchaseOrder.vue";
import SelectSalesOrder from "@/views/purchase/purchaseApply/SelectSalesOrder.vue";
import dialogTable from "@/components/dialogTable/dialogTable.vue";
import {getSalesCompanyList} from "@/api/basicmgt";
import {findUserPage} from "@/api/sysmgt";
import {addTabs, removeTabs, sysServerUrl , collapseArea, beforeRouteInfo, getContentData} from "@/assets/js/common";
import {getSalesOrderEdit} from "@/api/salesmgt";
import { productColumns } from "@/assets/js/tableHeader";
import { getBatchDeleteInfo, getDeleteInfo, getUnitListData } from '@/views/basic/basicCommon'
import { addInbound, getInSourceOrderInfo, editInbound, inboundInfo } from "@/api/stockmgt";
export default {
  name: "addInStock",
  // eslint-disable-next-line vue/no-unused-components
  components: {SelectPurchaseOrder, SelectSalesOrder, selectInput, dialogTable},
  data() {
    return {
      stateType: "", //类型
      disabledStatue: false, //输入框状态
      id: "",
      count : 0,
      productColumns: productColumns,
      uploadUrl: sysServerUrl + 'sys/upload/attach?flag=inbound', // 文件上传地址
      fileList: [], // 文件列表
      form: {
        // 基本信息
        id: "",
        sourceNo:'',
        inboundType:'',
        inboundNo:'',
        purchaseNo:'',
        supplierName:'',
        customerNo:'',
        warehouseName:'',
        warehouseId:'',
        locationId:'',
        locationName:'',
        createdUser: "",
        createdUserName: "",
        counterpartyName: "",
        counterpartyCode: "",
        createdTime: "",
        inboundTime: "",
        updatedUser: "",
        updatedTime: "",
        stockInboundDetailList: [],
        productAccessoryList: [],
      },
      salesTypeOptions: [
        { value: 'purchase', label: '采购入库' },      // 采购入库
        { value: 'return', label: '退料入库' },      // 退料入库
        { value: 'transfer', label: '调拨入库' },      // 调拨入库
        { value: 'production', label: '成品入库' },      // 成品入库
        // { value: 'INBOUND', label: '退料入库' },       // 退料入库
      ],
      addSalesTypeOptions: [
        { value: 'purchase', label: '采购入库' },      // 采购入库
        // { value: 'return', label: '退料入库' },      // 退料入库
        // { value: 'transfer', label: '调拨入库' },       // 调拨入库
        // { value: 'production', label: '成品入库' },       // 成品入库
      ],
      rules: {
        inboundType: [{ required: true, message: '类型不能为空', trigger: ['blur', 'change'] }],
        sourceNo: [{ required: true, message: '源单据不能为空', trigger: ['blur', 'change'] }],
      },
      infoRules: {
        warehouseName: [{ required: true, message: '入库仓库不能为空', trigger: ['blur', 'change'] }],
        locationName:[{ required: true, message: '库位不能为空', trigger: ['blur', 'change'] }],
        currentQty:[{ required: true, message: '本次入库数量不能为空', trigger: ['blur', 'change'] }],
        unit: [{ required: true, message: '单位不能为空', trigger: ['blur', 'change'] }],
      },
      activeNames: ["base", "product", "more"], // 全部展开
      selectList: [],
      unitData: [],
      // 添加商品
      isReload: false,
    }
  },
  computed: {
    // 设置请求上传的头部
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    onInputSearch(index,type, $event) {
      if (type == "warehouse") {
        this.form.stockInboundDetailList[index].warehouseName = $event.name;
        this.form.stockInboundDetailList[index].warehouseId = $event.id;
      }
      if (type == "location") {
        this.form.stockInboundDetailList[index].locationName = $event.name;
        this.form.stockInboundDetailList[index].locationId = $event.id;
      }
      if (type == "partner") {
        this.form.supplierName = $event.name
      }
    },
    //选择销售订单
    handleSelectSalesOrder(purchaseOrder) {
      this.form.sourceNo = purchaseOrder.purchaseNo;
      this.form.purchaseNo = purchaseOrder.purchaseNo
      this.form.sourceId = purchaseOrder.id
      this.form.supplierName = purchaseOrder.supplierName
      this.form.stockInboundDetailList = []; // 清空之前选择的商品
      // 获取采购订单下的商品信息
      let param = {
        id: purchaseOrder.id,
        inboundType : 'purchase',
      }
      getInSourceOrderInfo(param).then(res => {
          if (res.data.code === 100) {
            this.form.stockInboundDetailList =res.data.data;
          }
      })
    },
    // 计量单位
    getUnitData() {
      const unitTree = async()=>{
        var result = await getUnitListData();
        this.unitData = result;
      }
      unitTree();
    },
    // 添加商品
    addProduct() {
      // if (this.form.customerId === '' || this.form.customerName === ''){
      //   this.$DonMessage.warning("请先选择客户！");
      //   return
      // }
      this.isReload = true;
    },
    // 批量删除
    handleSelectChange(val) {
      this.selectList = val;
    },
    batchDelete() {
      getBatchDeleteInfo(this.form.stockInboundDetailList, this.selectList, "删除商品信息")
    },
    // 删除
    delClick(index) {
      if (this.form.stockInboundDetailList.length == 1) {
        this.$DonMessage.warning("商品信息不能为空")
        return
      }
      getDeleteInfo(this.form.stockInboundDetailList, index, "删除商品信息");
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        if (this.form.stockInboundDetailList) {
          switch (column.property) {
            case "quantity":
              this.form.stockInboundDetailList.forEach((item) => {
                if (item.quantity) {
                  count += item.quantity;
                }
              });
              sums[index] = count;
              break;
            case "beQty":
              this.form.stockInboundDetailList.forEach((item) => {
                if (item.beQty) {
                  count += item.beQty;
                }
              });
              sums[index] = count;
              break;
            case "currentQty":
              this.form.stockInboundDetailList.forEach((item) => {
                if (item.currentQty) {
                  count += item.currentQty;
                }
              });
              sums[index] = count;
              break;
          }
        }
      })
      return sums;
    },
    // 取消
    removeClick() {
      removeTabs(this.$route);
    },
    // 保存
    onSubmit(){
      this.form.status = 4
      this.submit()
    },
    onSave(){
      this.form.status = 3
      this.submit()
    },
    onSaveAndAudit(){
      this.form.status = 5
      this.submit()
    },
    submit(){
      var isStatus = true;
      if (this.form.inboundType == 'purchase') {
        if (this.form.sourceNo == '') {
          this.$DonMessage.warning('请先选择源单据!');
          return
        }
      }
      if (this.form.inboundType == 'purchase') {
        if (this.form.sourceNo == undefined || this.form.sourceNo == '') {
          this.$DonMessage.warning("采购入库，源单据不可为空!");
          isStatus = false;
        }
      }
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
          isStatus = false
        }
      })
      if (this.form.stockInboundDetailList.length > 0) {
        if (this.$refs["warehouseName"] != undefined) {
          this.$refs["warehouseName"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("入库仓库不能为空");
              isStatus = false;
            }
          })
        }
        if (this.$refs["locationName"] != undefined) {
          this.$refs["locationName"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("库位不能为空");
              isStatus = false;
            }
          })
        }
        if(this.stateType != "addInfo") {
          if (this.$refs["quantity"] != undefined) {
            this.$refs["quantity"].validate(valid => {
              if (!valid) {
                this.$DonMessage.warning("入库数量不能为空");
                isStatus = false;
              }
            })
          }
          if (this.$refs["unit"] != undefined) {
            this.$refs["unit"].validate(valid => {
              if (!valid) {
                this.$DonMessage.warning("单位不能为空");
                isStatus = false;
              }
            })
          }
        }
        if (this.stateType == "addInfo") {
          if (this.$refs["currentQty"] != undefined) {
            this.$refs["currentQty"].validate(valid => {
              if (!valid) {
                this.$DonMessage.warning("本次入库数量不能为空");
                isStatus = false;
              }
            })
          }
        }
        this.form.stockInboundDetailList.forEach(item => {
          if (item.currentQty == undefined || item.currentQty <= 0 ) {
            this.$DonMessage.warning("本次入库数量不能为空且要大于0");
            isStatus = false;
          }
          if (this.stateType == "addInfo") {
            if (item.currentQty > item.beQty) {
              this.$DonMessage.warning("本次入库数量不能大于待入库数量");
              isStatus = false;
            }
          }
        })

      } else {
        this.$DonMessage.warning('商品信息不能为空');
        isStatus = false
      }
      if (isStatus == false) {
        return
      }
      this.form.productAccessoryList = this.fileList;
      var params = {
        id:this.form.id,
        sourceId:this.form.sourceId,
        sourceNo:this.form.sourceNo,
        inboundTime:this.form.inboundTime,
        status:this.form.status,
        quantity:this.form.quantity,
        inboundType:this.form.inboundType,
        counterpartyName:this.form.counterpartyName,
        counterpartyCode:this.form.counterpartyCode,
        remark:this.form.remark,
        stockInboundDetailList: this.form.stockInboundDetailList,
        productAccessoryList: this.form.productAccessoryList
      }
      if (this.stateType == "edit") {
        editInbound(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      } else {
        addInbound(params).then((res) => {
          console.log(res);
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        });
      }
    },
    formClear() {
      this.form = {
        id: "",
        sourceNo:'',
        inboundType:'',
        inboundNo:'',
        purchaseNo:'',
        supplierName:'',
        customerNo:'',
        warehouseName:'',
        warehouseId:'',
        locationId:'',
        locationName:'',
        createdUser: "",
        createdUserName: "",
        counterpartyName: "",
        counterpartyCode: "",
        createdTime: "",
        inboundTime: "",
        updatedUser: "",
        updatedTime: "",
        stockInboundDetailList: [],
        productAccessoryList: [],
      }
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      });
    },
    // 获取订单信息
    async salesOrderInfo() {
      try {
        const res = await inboundInfo(this.id)
        // inboundInfo(this.id).then((res) => {
        if (res.data.code == "100") {
          this.form = Object.assign({}, res.data.data);
          if (this.stateType == 'addInfo'){
            this.form.inboundTime = this.$options.filters.conversion(new Date().getTime(), "yyyy-MM-dd");
          } else {
            this.form.inboundTime = this.$options.filters.conversion(this.form.inboundTime ,"yyyy-MM-dd");
            // this.form.createdTime = new Date().toLocaleDateString();
          }
          this.form.createdUser = this.$store.state.realName;
          this.form.updatedUser = this.$store.state.realName;
          this.form.updatedTime = new Date().getTime();
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.removeClick()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.removeClick()
      }
    },
    async initialState() {
      var type = this.$route.params.type;
      this.id = this.$route.params.id;
      if (type == "add") {
        if (this.id == "add") { // 新增入库
          this.stateType = "add"
          await this.formClear();
          this.form.inboundTime = this.$options.filters.conversion(new Date().getTime(), "yyyy-MM-dd");
          this.disabledStatue = false
        } else {
          this.rules = {};
          this.stateType = "addInfo"  // 入库
          await this.salesOrderInfo();
          this.disabledStatue = true
        }
      } else { // 编辑
        this.stateType = "edit"
        await this.salesOrderInfo();
        this.disabledStatue = true
      }
      getContentData(this)
      collapseArea()
      this.getUnitData();
    },
    // 附件上传
    handleOnSuccess(res, obj) {
      this.fileList = []
      var file = {
        fileName: res.data.fileName,
        name: res.data.fileName,
        path: res.data.fileUrl,
      }
      this.fileList.push(file)
      this.count = 1
    },
    // 文件移除前的钩子
    beforeOnRemove() {
      if (this.fileList.length >　0) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    // 文件移除时的钩子
    handleOnRemove() {
      this.fileList = []
      this.count = 0
    },
    beforeAvatarUpload(file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const isLt2M = file.size / 1024 / 1024 < 100
      var suffix = [
        'jpg',
        'png',
        'mp4',
        'mp3',
        'xls',
        'xlsx',
        'doc',
        'docx',
        'zip',
      ];
      if (!suffix.includes(fileName)) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip' }));
        return false;
      }
      if (!isLt2M) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }))
        return false;
      }
    },
    // 超过文件数量限制时的钩子
    handleOnExceed() {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 1}))
      return
    },
  },
  mounted() {
    this.initialState();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name == "addInStock") {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  }
}
</script>
