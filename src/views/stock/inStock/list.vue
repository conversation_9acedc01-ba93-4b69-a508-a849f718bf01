<template>
  <div class="layoutContainer">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="待入库单" name="beInStock">
        <div class="secondFloat">
          <el-form :inline="true" ref="queryParams" :model="queryParams" :label-width="$labelFour">
            <el-form-item label="单据日期">
              <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
            </el-form-item>

            <el-form-item label="源单据号" prop="sourceNo">
              <el-input
                v-model="queryParams.sourceNo"
                placeholder="请输入源订单号"
                clearable
              />
            </el-form-item>
            <el-form-item label="往来单位" prop="counterpartyName" >
              <selectInput
                ref="selectInput"
                v-model="queryParams.counterpartyName"
                :inputParam="queryParams.counterpartyName"
                inputType="partnerSupplierInfo"
                placeholder="请选择往来单位"
                @select="onInputSearch('partner', $event)"
              ></selectInput>
            </el-form-item>

            <el-form-item label="商品" prop="productName">
              <selectInput
                ref="selectInput"
                v-model="queryParams.productName"
                :inputParam="queryParams.productName"
                inputType="productInfo"
                placeholder="请选择商品"
                @select="onInputSearch('product', $event)"
              ></selectInput>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item>
              <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
              <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
<!--              <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i-->
<!--                class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--              <el-button type="text" @click="isShow = false" v-if="isShow">收起<i-->
<!--                class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">

          <el-table style="width:100%"
                    border
                    stripe
                    ref="table"
                    highlight-current-row
                    :max-height="maximumHeight"
                    :data="resultList"
                    @header-dragend="changeColWidth"
                    @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" fixed="left" align="center"></el-table-column>
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>

            <el-table-column prop="createdTime" label="单据日期" width="150">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
              </template>
            </el-table-column>
            <el-table-column prop="sourceNo" label="源单据号" min-width="100">
              <template #default="{row}">
                <span class="linkStyle" @click="handleSourceNoDetail(row)">{{ row.sourceNo }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" min-width="80">
              <template slot-scope="scope">
                <div :class="getStatusClass(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="outboundType" label="类型" min-width="80">
              <template slot-scope="scope">
                <div >
                  {{ getoutboundTypeText(scope.row.inboundType) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="counterpartyName" label="往来单位" min-width="100"/>
            <el-table-column prop="quantity" label="数量" width="80"/>

            <el-table-column prop="createdUserName" label="制单人" width="100"/>
            <el-table-column prop="createdTime" label="制单时间" width="150">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="120"/>
            <el-table-column label="操作" fixed="right" width="120">
              <template slot-scope="{row}">
                <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
                <el-button type="text" size="small" v-if="row.status != 2" @click="handleEdit(row)">入库</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 底部分页组件 -->
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                      @pagination="handleSearch"/>
        </div>
      </el-tab-pane>

      <el-tab-pane label="入库单" name="inStock">
        <div class="secondFloat">
          <el-form :inline="true" ref="queryParams" :model="queryParams" :label-width="$labelFour">
            <el-form-item label="单据日期">
              <DateRangeSelector ref="dateInSelector" @change="handleDateRangeSelect"/>
            </el-form-item>
            <el-form-item label="往来单位" prop="counterpartyName" >
              <selectInput
                ref="selectInput"
                v-model="queryParams.counterpartyName"
                :inputParam="queryParams.counterpartyName"
                inputType="partnerSupplierInfo"
                placeholder="请选择往来单位"
                @select="onInputSearch('partner', $event)"
              ></selectInput>
            </el-form-item>


            <el-form-item label="商品" prop="productName">
              <selectInput
                ref="selectInput"
                v-model="queryParams.productName"
                :inputParam="queryParams.productName"
                inputType="productInfo"
                placeholder="请选择商品"
                @select="onInputSearch('product', $event)"
              ></selectInput>
            </el-form-item>
            <!-- 状态 -->
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
              >
                <el-option v-for="item in auditInStockStatus" :value="item.code" :key="item.code" :label="item.name"></el-option>
              </el-select>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item>
              <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
              <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
<!--              <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i-->
<!--                class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--              <el-button type="text" @click="isShow = false" v-if="isShow">收起<i-->
<!--                class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
              <el-button type="text" icon="bulkImport-icon" @click="batchImport()">批量导入</el-button>
              <el-dropdown>
            <span>
              <i class="process-icon"></i>
              审核
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
                <el-dropdown-menu slot="dropdown">
                  <div>
                    <el-dropdown-item @click.native ="auditClick('reviewPass')">审核通过</el-dropdown-item>
                    <el-dropdown-item @click.native ="auditClick('reviewReject')">审核驳回</el-dropdown-item>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>

              <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
              <el-button type="text" icon="deleteRed-icon" @click="delClick()">删除</el-button>
            </div>
          </div>
          <el-table style="width:100%"
                    border
                    stripe
                    ref="table"
                    highlight-current-row
                    :max-height="maximumHeight"
                    :data="resultList"
                    @header-dragend="changeColWidth"
                    @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" fixed="left" align="center"></el-table-column>
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column prop="createdTime" label="单据日期" width="150">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
              </template>
            </el-table-column>
<!--            <el-table-column prop="inboundNo" label="入库单号" min-width="150"/>-->
            <el-table-column prop="inboundNo" label="入库单号" min-width="100">
              <template #default="{row}">
                <span class="linkStyle" @click="inStockDetail(row)">{{ row.inboundNo }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" min-width="80">
              <template slot-scope="scope">
                <div :class="getStatusClass(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="sourceNo" label="源单据号" min-width="100">
              <template #default="{row}">
                <span class="linkStyle" @click="handleSourceNoDetail(row)">{{ row.sourceNo }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="inboundType" label="类型" width="120">
              <template slot-scope="scope">
                <div >
                  {{ getoutboundTypeText(scope.row.inboundType) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="counterpartyName" label="往来单位" min-width="120"/>
            <el-table-column prop="quantity" label="数量" width="100"/>


            <el-table-column prop="createdUserName" label="制单人" width="100">
              <template slot-scope="scope">
               <span>{{ scope.row.createdUserName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createdTime" label="制单时间" width="160">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="120"/>
            <el-table-column label="操作" fixed="right" width="120">
              <template slot-scope="{row}">
                <el-button type="text" size="small" @click="inStockDetail(row)">详情</el-button>
                <el-button type="text" size="small"  v-if="row.status == 3 || row.status == 6" @click="editOutStock(row)">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 底部分页组件 -->
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                      @pagination="handleSearch"/>
        </div>
      </el-tab-pane>

    </el-tabs>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="reviewRejectDialogVisible" v-if="reviewRejectDialogVisible">
      <el-form :model="reason"  ref="reason" >
        <el-form-item label="驳回理由" prop="reason">
          <el-input v-model="reason" />
        </el-form-item>

        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleAuditReject">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="reviewRejectDialogVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {salesList, salesOrderDel, salesOrderDiscard, salesOrderPass, salesOrderReject} from "@/api/salesmgt";
import {addTabs, tableHeight} from "@/assets/js/common";
import {dictTypeQueryData} from "@/api/sysmgt";
import {commentDel} from "@/api/material";
import {
  auditInbound,
  auditOutbound,
  auditRejectInbound,
  auditRejectOutbound,
  beInStockList,
  beOutStockList, delInbound, delOutbound
} from "@/api/stockmgt";
import selectInput from "@/components/selectInput/selectInput.vue";

export default {
  name: "list",
  components: { selectInput, DateRangeSelector, Pagination},
  data() {
    return {
      activeTab:'beInStock',
      reason:'',
      auditInStockStatus: [
        {code:3,name:'草稿'},
        {code:4,name:'待审核'},
        {code:5,name:'已审核'},
        {code:6,name:'审核驳回'}
      ],
      reviewRejectDialogVisible: false,
      auditStatusDicts: [],
      selectedRows: [],
      deleteList: [],
      maximumHeight: 0,
      total: 0,
      dialogStatus: '',
      textMap: {
        reviewReject: "审核驳回"
      },
      isShow: false,
      resultList: [],
      queryParams: {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        salesNo: '',
        sourceNo: '',
        supplierId: '',
        salesTime: '',
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        selectedSupplier: '',
        counterpartyName: '',
        productName: '',
        productId: '',
        page: 1,
        pageSize: 10
      },
    }
  },
  methods: {
    handleClick(tab, event) {
      this.activeTab = tab.name;
      this.queryParams = {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        salesNo: '',
        sourceNo: '',
        supplierId: '',
        salesTime: '',
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        selectedSupplier: '',
        counterpartyName: '',
        productName: '',
        productId: '',
        page: 1,
        pageSize: 10
      };
      this.handleSearch()
    },
    getStatusClass(status) {
      switch(status) {
        case 0:
          return 'errorColor'; // 未生成 - 红色或其他错误颜色
        case 1:
          return 'warningColor'; // 部分生成 - 黄色或其他警告颜色
        case 2:
          return 'successColor'; // 已生成 - 绿色或其他成功颜色
        case 3:
          return ''; // 未生成 - 红色或其他错误颜色
        case 4:
          return 'errorColor'; // 部分生成 - 黄色或其他警告颜色
        case 5:
          return 'successColor'; // 已生成 - 绿色或其他成功颜色
        case 6:
          return 'warningColor'; // 已生成 - 绿色或其他成功颜色
        default:
          return 'errorColor'; // 默认情况，可根据需要调整
      }
    },
    getStatusText(status) {
      switch(status) {
        case 0:
          return '未入库';
        case 1:
          return '部分入库';
        case 2:
          return '已入库';
        case 3:
          return '草稿';
        case 4:
          return '待审核';
        case 5:
          return '已审核';
        case 6:
          return '审核驳回';
        default:
          return '未知状态'; // 默认情况，可根据需要调整
      }
    },
    getoutboundTypeText(status) {
      switch(status) {
        case 'purchase':
          return '采购入库';
        case 'return':
          return '退料入库';
        case 'transfer':
          return '调拨入库';
        case 'production':
          return '成品入库';
        case 'other':
          return '其他入库';
        default:
          return '未知入库'; // 默认情况，可根据需要调整
      }
    },

    //新增
    addClick() {
      const title = "新增入库单";
      this.$router.push({ name: 'addInStock', params: { type:'add', id: 'add' }});
      addTabs(this.$route.path, title);
    },
    //批量导入
    batchImport() {

    },
    handleAuditReject(){
      let ids = ""
      this.selectedRows.forEach(item => {
        if (item.status != 4) {
          this.$DonMessage.warning("只能审核待审核的数据！");
          return false;
        }else {
          ids += item.id + ","
        }
      })
      var params = new URLSearchParams()
      params.append('ids', ids)
      params.append('reason', this.reason)
      auditRejectInbound(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success('审核驳回成功!')
          this.reviewRejectDialogVisible = false;
          this.activeTab = 'inStock';
          const params = {
            activeTab : 'inStock'
          }
          this.dataList(params)
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },
    //批量作废
    abolishClick() {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要作废的数据");
        return false;
      }
      this.$confirm('确定作废【' + this.selectedRows.length + '】条数据吗?', '作废订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        salesOrderDiscard(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.activeTab = 'inStock';
            const params = {
              activeTab : 'inStock'
            }
            this.dataList(params)
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },
    onInputSearch(type, $event) {
      if (type == "partner") {
        this.queryParams.counterpartyName = $event.name;
      }
      if(type=="product"){
        this.queryParams.productName = $event.name;
        this.queryParams.productId = $event.id;
      }
    },
    //审核
    auditClick(type) {
      this.reason='';
      if(type == 'reviewReject'){
        this.reviewRejectDialogVisible = true;
        return;
      }
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要审核的数据");
        return false;
      }
      this.$confirm('确定审核【' + this.selectedRows.length + '】条数据吗?', '审核单据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          if (item.status != 4) {
            this.$DonMessage.warning("只能审核待审核的数据！");
            return false;
          }else {
            ids += item.id + ","
          }
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        if (type == 'reviewPass'){
          auditInbound(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("审核通过,已入库!")
              const params = {
                activeTab : 'inStock'
              }
              this.dataList(params)
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          })
        }
      })
    },
    //批量删除
    delClick() {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要删除的数据");
        return false;
      }
      this.$confirm('确定删除【' + this.selectedRows.length + '】条数据吗?', '删除评价', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          if (item.status != 3 && item.status != 6) {
            this.$DonMessage.warning("只能删除草稿或审核驳回数据！");
            return false;
          }else {
            ids += item.id + ","
          }
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        delInbound(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.activeTab = 'inStock';
            const params = {
              activeTab : 'inStock'
            }
            this.dataList(params)
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },
    //导出
    exportClick() {

    },
    //待入库单详情
    handleDetail(row) {
      var title = row.sourceNo;
      this.$router.push({ name: 'beInStockDetail', params: { id: row.id }});
      addTabs(this.$route.path, title);
    },
    handleSourceNoDetail(row) {
      // 类型如果是采购入库跳到采购订单
      if(row.inboundType == 'purchase'){
        var title = row.sourceNo;
        this.$router.push({ name: 'purchaseOrderDetail', params: { id: row.sourceId,type: "detail" }});
        addTabs(this.$route.path, title);
      } else if (row.inboundType == 'return') {
        var title = row.sourceNo;
        this.$router.push({ name: 'ReturnDetail', params: { id: row.sourceId,type: "detail" }});
        addTabs(this.$route.path, title);
      }else if (row.inboundType == 'transfer') {
        var title = row.sourceNo;
        this.$router.push({ name: 'transferDetail', params: { id: row.sourceId,type: "detail" }});
        addTabs(this.$route.path, title);
      }

    },
    // 入库单详情
    inStockDetail(row) {
      var title = row.inboundNo;
      this.$router.push({ name: 'inStockDetail', params: { id: row.id }});
      addTabs(this.$route.path, title);
    },
    //入库
    handleEdit(row) {
      var title = "新增入库单";
      this.$router.push({ name: 'addInStock', params: { type:'add', id: row.id }});
      addTabs(this.$route.path, title);
    },
    // 编辑入库单
    editOutStock(row) {
      var title = "编辑入库单";
      this.$router.push({ name: 'addInStock', params: { type:'edit', id: row.id }});
      addTabs(this.$route.path, title);
    },


    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();

      window.addEventListener("resize", function () {

      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //时间格式化方法
    conversion,
    //查询
    handleSearch() {
      const params = new URLSearchParams();
      params.append('status', this.queryParams.status);
      params.append('startDate', this.queryParams.startDate);
      params.append('endDate', this.queryParams.endDate);
      params.append('productId', this.queryParams.productId);
      params.append('sourceNo', this.queryParams.sourceNo);
      params.append('counterpartyName', this.queryParams.counterpartyName);
      params.append('activeTab', this.activeTab);
      params.append('page', this.queryParams.page);
      params.append('limit', this.queryParams.pageSize);
        // status: this.queryParams.status,
        // startDate: this.queryParams.startDate,
        // endDate: this.queryParams.endDate,
        // productId: this.queryParams.productId,
        // sourceNo: this.queryParams.sourceNo,
        // counterpartyName : this.queryParams.counterpartyName,
        // activeTab : this.activeTab,
        // page: this.queryParams.page,
        // limit: this.queryParams.pageSize

      // 发起请求查询接口
      this.dataList(params)
    },
    //  处理选中的行
    handleSelectionChange(val) {
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },
    //重置查询条件
    handleReset() {
      this.queryParams = {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        salesNo: '',
        sourceNo: '',
        supplierId: '',
        salesTime: '',
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        selectedSupplier: '',
        counterpartyName: '',
        productName: '',
        productId: '',
        page: 1,
        pageSize: 10
      }
      if (this.$refs['queryParams'].resetFields() !== undefined) {
        this.$refs['queryParams'].resetFields()
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.queryParams.productId = '';
      //重置时间选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      if (this.$refs.dateInSelector){
        this.$refs.dateInSelector.reset()
      }
      this.handleSearch()
    },
    //分页查询数据
    dataList(params) {
      beInStockList(params).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 获取日期范围选择
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      this.handleSearch();
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    this.handleSearch();
    //获取审核状态数据字典
    this.getAuditStatusDict();
  },
}
</script>

<style scoped>
.warningColor {
  color: #fea927;
}
</style>
