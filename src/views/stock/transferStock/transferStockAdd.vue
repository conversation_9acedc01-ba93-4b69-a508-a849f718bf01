<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="this.stateType == 'add'">新增调拨单</div>
      <div v-if="this.stateType == 'edit'">编辑调拨单</div>
      <div>
        <el-button type="primary" @click="onCancel()">保存</el-button>
        <el-button plain @click="onSubmit()">提交</el-button>
        <el-button plain @click="onCancelAndAudit()">提交并审核</el-button>
        <el-button plain @click="removeClick()">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFive">
                <el-form-item label="调拨单号" prop="transferNo">
                  <el-input v-model="form.transferNo" placeholder="调拨单号由系统生成" disabled />
                </el-form-item>
                <el-form-item label="调拨日期" prop="transferTime">
                  <el-date-picker
                    v-model="form.transferTime"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="请选择日期"
                    :disabled="form.readonly"
                    :picker-options="pickerOptions"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="调出仓库" prop="fromWarehouseName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.fromWarehouseName"
                    :inputParam="form.fromWarehouseName"
                    inputType="warehouseInfo"
                    placeholder="请选择调出仓库"
                    @select="onInputSearch('fromWarehouse', $event)"
                  ></selectInput>
                </el-form-item>
                <el-form-item label="调入仓库" prop="inWarehouseName">
                  <selectInput
                    ref="selectInput"
                    v-model="form.inWarehouseName"
                    :inputParam="form.inWarehouseName"
                    inputType="warehouseInfo"
                    placeholder="请选择调入仓库"
                    @select="onInputSearch('inWarehouse', $event)"
                  ></selectInput>
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500" show-word-limit/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <div class="tableHandle spaceBbetwee">
              <el-button type="primary" icon="addProducts-icon" @click="addProduct">添加商品</el-button>
              <div>
                <el-button type="text" icon="import-icon">批量导入</el-button>
                <el-button type="text" icon="setIcon-icon">批量设置</el-button>
                <el-button type="text" icon="deleteRed-icon">批量删除</el-button>
              </div>
            </div>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.stockTransferDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>

              <el-table-column label="商品编码" min-width="100">
                <template #header>
                  <span>商品编码</span>
                </template>
                <template slot-scope="{row}">
                  <span v-if="row.productCode !== undefined">{{row.productCode}}</span>
                </template>
              </el-table-column>
              <el-table-column label="商品名称" min-width="100">
                <template #header>
                  <span>商品名称</span>
                </template>
                <template slot-scope="{row}">
                  <span v-if="row.productName !== undefined">{{row.productName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="80">
                <template slot-scope="{ row }">
                  <img v-if="row.imageUrl !== ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" min-width="100">
                <template #header>
                  <span>商品品牌</span>
                </template>
                <template slot-scope="{row}">
                  <span v-if="row.brand !== undefined">{{row.brand}}</span>
                </template>
              </el-table-column>
              <el-table-column label="规格型号" min-width="100">
                <template #header>
                  <span>规格型号</span>
                </template>
                <template slot-scope="{row}">
                  <span v-if="row.model !== undefined">{{row.model}}</span>
                </template>
              </el-table-column>

              <el-table-column label="调出仓库" min-width="100">
                <template #header>
                  <span class="required-field">调出仓库</span>
                </template>
                <template>
                  {{form.fromWarehouseName}}
                </template>
              </el-table-column>

              <el-table-column label="调出库位">
                <template #header>
                  <span class="required-field">调出库位</span>
                </template>
                <template slot-scope="{ row, $index }">
                  <div class="rowEditShow fromLocationName">
                  <selectInput
                    ref="selectInputFrom"
                    v-model="row.fromLocationName"
                    :inputParam="row.fromLocationName"
                    inputType="locationInfo"
                    :warehouseId= form.fromWarehouseId
                    placeholder="请选择调出库位"
                    @select="onInputSearchIndex($index,'fromLocation', $event)"
                  ></selectInput>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="调入仓库" min-width="100">
                <template #header>
                  <span class="required-field">调入仓库</span>
                </template>
                <template>
                  {{form.inWarehouseName}}
                </template>
              </el-table-column>

              <el-table-column label="调入库位">
                <template #header>
                  <span class="required-field">调入库位</span>
                </template>
                <template slot-scope="{ row, $index }">
                  <div class="rowEditShow inLocationName">
                  <selectInput
                    ref="selectInput"
                    v-model="row.inLocationName"
                    :inputParam="row.inLocationName"
                    inputType="locationInfo"
                    :warehouseId= form.inWarehouseId
                    placeholder="请选择调入库位"
                    @select="onInputSearchIndex($index,'inLocation', $event)"
                  ></selectInput>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="单位" min-width="100">
                <template #header>
                  <span>单位</span>
                </template>
                <template slot-scope="{row}">
                  <span v-if="row.unit !== undefined">{{row.unit}}</span>
                </template>
              </el-table-column>
              <el-table-column label="调拨数量" min-width="100">
                <template #header>
                  <span class="required-field">调拨数量</span>
                </template>
                <template slot-scope="{row}">
                  <el-input
                    v-model.number="row.quantity"
                    size="mini"
                    placeholder="请输入调拨数量"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="100">
                <template #header>
                  <span >备注</span>
                </template>
                <template slot-scope="{row}">
                  <el-input
                    v-model.number="row.remark"
                    size="mini"
                    placeholder="请输入备注"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="80">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton" @click="delClick(scope.$index, form.stockTransferDetailList)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="创建人">
                {{ form.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="this.stateType == 'edit'">
                {{ form.updatedUser }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="this.stateType == 'edit'">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="附件">
                <el-upload
                  style="width: 500px;"
                  class="upload-demo"
                  :action="uploadUrl"
                  :headers="importHeader"
                  :file-list="fileList"
                  :on-remove="handleOnRemove"
                  :before-remove="beforeOnRemove"
                  :before-upload="beforeAvatarUpload"
                  :on-exceed="handleOnExceed"
                  :on-success="handleOnSuccess"
                  :limit="1"
                >
                  <span class="linkStyle">点击上传</span>
                </el-upload>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <dialogTable
      v-if="isReload"
      :isReload.sync="isReload"
      type="product"
      :formList.sync="form.stockTransferDetailList"
      :columns="productColumns"
    >
      <template #imageUrl="scope">
        <img v-if="scope.row.imageUrl != ''" class="pictureShow" :src="$filePath + scope.row.imageUrl" alt="">
      </template>
    </dialogTable>
  </div>
</template>

<script>
import selectInput from "@/components/selectInput/selectInput.vue";
import { getSalesCompanyList} from "@/api/basicmgt";
import {findUserPage, } from "@/api/sysmgt";
import {addTabs, removeTabs, sysServerUrl} from "@/assets/js/common";
import {editTransferBound, transferBoundInfo} from "@/api/stockmgt";
import {addTransferBound} from "@/api/stockmgt";
import dialogTable from "@/components/dialogTable/dialogTable.vue";
import { productColumns } from "@/assets/js/tableHeader";
export default {
  name: "transferStockAdd",
  components: {dialogTable, selectInput},
  data() {
    return {
      count: 0,
      productColumns: productColumns,
      id:'',
      selectedCustomer: '', // 客户列表选中数据
      productId: "", //id
      stateType: "", //类型
      // 基本信息
      form: {
        id: "",
        fromWarehouseName: "",
        fromWarehouseId: "",
        inWarehouseName: "",
        inWarehouseId: "",
        fromLocationName: "",
        inLocationName: "",
        fromLocationId: "",
        inLocationId: "",
        status: 1,
        remark: "",
        transferNo: "",
        transferTime: "",
        stockTransferDetailList: [],
        productAccessoryList: [],
      },
      rules: {
        code: [{ required: true }],
        name: [{ required: true, message: '商品名称不能为空', trigger: ['blur', 'change'] }],
        fromWarehouseName: [{ required: true, message: '调出仓库不能为空', trigger: ['blur', 'change'] }],
        inWarehouseName: [{ required: true, message: '调入仓库不能为空', trigger: ['blur', 'change'] }],
        categoryName: [{ required: true, message: '商品类别不能为空', trigger: ['blur', 'change'] }],
        unit: [{ required: true, message: '计量单位不能为空', trigger: ['blur', 'change'] }],
      },
      pagesize: 999,
      currentPage: 1,
      salesCompanyList: [], // 销售公司
      userDataList: [], // 销售人员
      unitData: [], //计量单位
      detailInfo: {}, // 商品信息
      tableData: [], // 商品特性 供应商
      rowEditIndex: "",
      colimnEditIndex: "",
      accessoryList: [], // 图片信息
      fileList: [], // 图片上传列表
      uploadUrl: sysServerUrl + 'sys/upload/attach?flag=transfer', // 文件上传地址
      activeNames: ["base", "product", "more"], // 全部展开
      // 添加商品
      isReload: false,
      // 进度条
      progressFlag: false,
      percentage: 0,
      pickerOptions: {
        disabledDate(time) {
          // 禁用今天之前的日期，只允许选择今天及以后的日期
          return time.getTime() <= Date.now() - 8.64e7; // 8.64e7 是一天的毫秒数（24 * 60 * 60 * 1000）
          // return time.getTime() < Date.now(); // 禁用今天及之前的日期（包括今天）
        }
      }
    }
  },
  computed: {
    // 设置请求上传的头部
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods:{
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    addProduct() {
      if (this.form.fromWarehouseId === '' || this.form.inWarehouseId === ''){
        this.$DonMessage.warning("请先选择调入和调出仓库！");
        return
      }
      this.isReload = true;
    },
    getSalesCompanyList(){
      getSalesCompanyList({}).then((res) => {
        if (res.data.code == '100') {
          this.salesCompanyList = res.data.data
        }
      });
    },
    onInputSearchIndex(index,type, $event) {
      if (type == "fromLocation") {
        this.form.stockTransferDetailList[index].fromLocationName = $event.name;
        this.form.stockTransferDetailList[index].fromLocationId = $event.id;
      } else if (type == "inLocation"){
        this.form.stockTransferDetailList[index].inLocationName = $event.name;
        this.form.stockTransferDetailList[index].inLocationId = $event.id;
      }
    },
    onInputSearch(type, $event) {
      if (type == "name") {
        this.form.categoryName = $event.name;
        this.form.categoryId = $event.id;
      } else if (type == "fromWarehouse") {
        document.querySelectorAll('.rowEditShow.fromLocationName .el-autocomplete input').forEach((item, index) => {
          document.querySelectorAll('.rowEditShow.fromLocationName .el-autocomplete input')[index].value = ""
        });
        this.form.fromWarehouseName = $event.name;
        this.form.fromWarehouseId = $event.id;
      } else if (type == "inWarehouse") {
        // 清空调入库位
        document.querySelectorAll('.rowEditShow.inLocationName .el-autocomplete input').forEach((item, index) => {
          document.querySelectorAll('.rowEditShow.inLocationName .el-autocomplete input')[index].value = ""
        });
        this.form.inWarehouseName = $event.name;
        this.form.inWarehouseId = $event.id;
      }else if (type == "location") {
        this.form.locationName = $event.name;
        this.form.locationId = $event.id;
      } else if (type == "brand") {
        this.form.brand = $event.name
      }
    },
    sysServerUrl() {
      return sysServerUrl
    },
    getUserList(){
      findUserPage(this.currentPage + "/" + this.pagesize,{}).then(res => {
        if (res.data.code == '100') {
          this.userDataList = res.data.data
        }
      });
    },
    removeClick() {
      removeTabs(this.$route);
      this.$router.push('/stock/transferStock/list')
      setTimeout(() => {
        addTabs(this.$route.path, "调拨管理");
      });
    },
    // 删除
    delClick(index, rows) {
      this.$confirm('确定删除相关信息?', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rows.splice(index, 1);
        this.$DonMessage.success(this.$t('successTip.deleteTip'))
      })
    },
    // 保存
    onSubmit(){
      this.form.status = 1
      this.submit()
    },
    onCancel(){
      this.form.status = 0
      this.submit()
    },
    onCancelAndAudit(){
      this.form.status = 2
      this.submit()
    },
    submit(){
      this.form.productAccessoryList = this.fileList;
      var isStatus = true;
      if (this.form.stockTransferDetailList.length > 0) {
        this.form.stockTransferDetailList.forEach(item => {
          if (item.fromWarehouseName == undefined || item.fromWarehouseName == '') {
            this.$DonMessage.warning('调出仓库不能为空!');
            isStatus = false
          }
          if (item.fromLocationName == undefined || item.fromLocationName == '') {
            this.$DonMessage.warning('调出库位不能为空!');
            isStatus = false
          }
          if (item.inWarehouseName == undefined || item.inWarehouseName == '') {
            this.$DonMessage.warning('调入仓库不能为空!');
            isStatus = false
          }
          if (item.inLocationName == undefined || item.inLocationName == '') {
            this.$DonMessage.warning('调入库位不能为空!');
            isStatus = false
          }
          item.fromWarehouseId= this.form.fromWarehouseId
          item.fromWarehouseName= this.form.fromWarehouseName
          item.inWarehouseId= this.form.inWarehouseId
          item.inWarehouseName= this.form.inWarehouseName
        })
      } else {
        this.$DonMessage.warning('商品信息不能为空');
        isStatus = false
      }
      if (isStatus == false) {
        return;
      }
      var params = {
        id: this.form.id,
        transferTime:this.form.transferTime,
        fromWarehouseId:this.form.fromWarehouseId,
        inWarehouseId:this.form.inWarehouseId,
        status: this.form.status,
        remark: this.form.remark,
        stockTransferDetailList: this.form.stockTransferDetailList,
        productAccessoryList: this.form.productAccessoryList
      }
      // 验证
      var flag = false;
      if (params.stockTransferDetailList.length > 0) {
        params.stockTransferDetailList.forEach(item => {
          if (item.quantity == null || item.quantity == '') {
            flag = true;
          }
        })
      }else {
        this.$DonMessage.error("调拨库存不能为空！");
        return;
      }
      if (flag){
        this.$DonMessage.error("调拨库存数量不能为空！");
        return;
      }
      // 验证商品的id是否唯一
      let sameProduct = false;
      const map = new Map();
      for (const item of this.form.stockTransferDetailList) {
        if (!item.productId) continue;
        const key = `${item.productId}`;
        if (map.has(key)) {
          sameProduct = true; // 存在重复
          this.$DonMessage.error("商品："+ item.name + " 存在相同的调拨商品信息，请检查")
          return;
        }
        map.set(key, true);
      }


      if(this.stateType === "add") {
        addTransferBound(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        });
      } else {
        editTransferBound(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      }
    },
    // 获取订单信息
    async transferBoundInfo() {
      // this.resetForm();
      transferBoundInfo(this.id).then((res) => {
        if (res.data.code == "100") {
          this.form = Object.assign({}, res.data.data);
          this.form.updatedUser = this.$store.state.realName;
          this.form.updatedTime = new Date().getTime();
          var imgList = this.form.productAccessoryList.length;
          if (imgList > 0) {
            this.accessoryList = this.form.productAccessoryList;
          }
        }
      });
    },
    initialState() {
      this.id = this.$route.params.id;
      if (this.id == "add") {
        this.stateType = "add";
        this.form.createdTime = new Date().getTime();
        this.form.createdUser = this.$store.state.realName;
        this.form.createdUserName = this.$store.state.realName;
      } else {
        this.stateType = "edit";
        this.form.updatedUser = this.$store.state.realName;
        this.transferBoundInfo();
      }
    },
    // 附件上传
    handleOnSuccess(res, obj) {
      this.fileList = []
      var file = {
        fileName: res.data.fileName,
        name: res.data.fileName,
        path: res.data.fileUrl,
      }
      this.fileList.push(file)
      this.count = 1
    },
    // 文件移除前的钩子
    beforeOnRemove() {
      if (this.fileList.length >　0) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    // 文件移除时的钩子
    handleOnRemove() {
      this.fileList = []
      this.count = 0
    },
    beforeAvatarUpload(file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const isLt2M = file.size / 1024 / 1024 < 100
      var suffix = [
        'jpg',
        'png',
        'mp4',
        'mp3',
        'xls',
        'xlsx',
        'doc',
        'docx',
        'zip',
      ];
      if (!suffix.includes(fileName)) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip' }));
        return false;
      }
      if (!isLt2M) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }))
        return false;
      }
    },
    // 超过文件数量限制时的钩子
    handleOnExceed() {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 1}))
      return
    },
  },
  mounted() {
    this.initialState();
      },
  watch: {
    $route(to) {
      if (to.name == "transferStockAdd") {
        this.initialState();
      }
    }
  }
}
</script>

<style scoped>

</style>
