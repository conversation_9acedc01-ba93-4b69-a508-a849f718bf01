<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" :label-width="$labelFour">
        <el-form-item label="单据日期">
          <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
        </el-form-item>
        <el-form-item label="调拨单号" prop="salesNo">
          <el-input
            v-model="queryParams.salesNo"
            placeholder="请输入调拨单号"
            clearable
          />
        </el-form-item>
        <el-form-item label="商品" prop="productName">
          <selectInput
            ref="selectInput"
            v-model="queryParams.productName"
            :inputParam="queryParams.productName"
            inputType="productInfo"
            placeholder="请选择商品"
            @select="onInputSearch('product', $event)"
          ></selectInput>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option v-for="item in auditStatusDicts" :value="item.code" :key="item.code" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
<!--          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i-->
<!--            class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i-->
<!--            class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
<!--          <el-button type="text" icon="el-icon-plus" @click="addClick()">复制</el-button>-->
          <el-dropdown>
            <span>
              <i class="process-icon"></i>
              审核
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
                <el-dropdown-item @click.native ="auditClick('reviewPass')">审核通过</el-dropdown-item>
                <el-dropdown-item @click.native ="auditClick('reviewReject')">审核驳回</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
          <el-button type="text" icon="deleteRed-icon" @click="delClick()">删除</el-button>
        </div>
        <!--        <div>-->
        <!--          <el-button type="text" icon="el-icon-setting">自定义列</el-button>-->
        <!--          <el-button type="text" icon="el-icon-refresh">刷新</el-button>-->
        <!--        </div>-->
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="transferTime" label="单据日期" width="150">
          <template #default="scope">
            {{ conversion(scope.row.transferTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="transferNo" label="调拨单号" min-width="100">
          <template #default="{row}">
            <span class="linkStyle" @click="handleDetail(row)">{{ row.transferNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="80">
          <template slot-scope="scope">
            <div :class="getStatusClass(scope.row.status)">
              {{ formatAuditStatus(scope.row.status) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="fromWarehouseName" label="调出仓库" min-width="100"/>
        <el-table-column prop="inWarehouseName" label="调入仓库" min-width="100"/>
        <el-table-column prop="transferQty" label="调拨数量" width="150"/>

        <el-table-column prop="auditUser" label="审核人" width="100"/>
        <el-table-column prop="auditTime" label="审核时间" width="150">
          <template slot-scope="scope">
            {{ conversion(scope.row.auditTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>

        <el-table-column prop="createdUserName" label="制单人" width="100"/>
        <el-table-column prop="createdTime" label="制单时间" width="150">
          <template slot-scope="scope">
            {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注"/>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
            <el-button type="text" size="small" v-if="row.status == 0 || row.status == 3" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>
    </div>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="reviewRejectDialogVisible" v-if="reviewRejectDialogVisible">
      <el-form :model="reason"  ref="reason" >
        <el-form-item label="驳回理由" prop="reason">
          <el-input v-model="reason" />
        </el-form-item>

        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleAuditReject">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="reviewRejectDialogVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {salesList, salesOrderDel, salesOrderDiscard, salesOrderPass, salesOrderReject} from "@/api/salesmgt";
import {addTabs, tableHeight} from "@/assets/js/common";
import {dictTypeQueryData, findDeptAll, findUserPage} from "@/api/sysmgt";
import {commentDel} from "@/api/material";
import customerInfo from "@/views/sales/salesOrder/customerInfo.vue";
import {getStatusClass} from "@/assets/js/utils";
import selectInput from "@/components/selectInput/selectInput.vue";
import {
  auditOutbound,
  auditRejectTransferBound,
  auditTransferBound,
  delTransferBound,
  transferList
} from "@/api/stockmgt";
import transferDetail from "@/views/stock/transferStock/transferDetail.vue";

export default {
  name: "list",
  components: {selectInput, DateRangeSelector, Pagination},
  data() {
    return {

      pagesize: 999,
      currentPage: 1,
      userDataList: [],
      reason:'',
      customerDialogVisible: false,
      reviewRejectDialogVisible: false,
      auditStatusDicts: [],
      selectedRows: [],
      deleteList: [],
      maximumHeight: 0,
      total: 0,
      dialogStatus: '',
      textMap: {
        reviewReject: "审核驳回"
      },
      isShow: false,
      resultList: [],

      form:{
        customerName: "",
        customerId: "",
      },
      queryParams: {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        applyNo: '',
        endDate: '',
        salesNo: '',
        applyOrderStatus: '',
        taskOrderStatus: '',
        shippingStatus: '',
        collectionStatus: '',
        salesUser: '',
        customerName: '',
        salesTime: '',
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        productName: '',
        productId: '',
        page: 1,
        pageSize: 10
      },
    }
  },
  methods: {
    getStatusClass,

    getAuditStatusText(status) {
      switch(status) {
        case 0:
          return '草稿';
        case 1:
          return '待审核';
        case 2:
          return '已审核';
        case 3:
          return '审核驳回';
        default:
          return '未知状态'; // 默认情况，可根据需要调整
      }
    },
    onInputSearch(type, $event) {
      if (type == "name") {
        this.queryParams.productName = '';
        this.queryParams.productId = '';
        this.queryParams.categoryName = $event.name;
        this.queryParams.categoryId = $event.id;
      } else if (type == "product") {
        this.queryParams.productName = $event.name;
        this.queryParams.productId = $event.id;
      } else if (type == "warehouse") {
        this.queryParams.warehouseName = $event.name;
        this.queryParams.warehouseId = $event.id;
      } else if (type == "location") {
        this.queryParams.locationName = $event.name;
        this.queryParams.locationId = $event.id;
      } else if (type == "brand") {
        this.queryParams.brand = $event.name
      }
    },
    //转换状态显示
    formatAuditStatus(status){
      const match = this.auditStatusDicts.find(item => item.code == status);
      return match ? match.name : '';
    },


    //新增
    addClick() {
      const title = "新增调拨单";
      this.$router.push({
        name: 'transferStockAdd',
        params: {id: 'add'}
      });
      addTabs(this.$route.path, title);
    },
    //批量导入
    batchImport() {

    },
    handleAuditReject(){
      let ids = ""
      this.selectedRows.forEach(item => {
        ids += item.id + ","
      })
      var params = new URLSearchParams()
      params.append('ids', ids)
      params.append('reason', this.reason)
      auditRejectTransferBound(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success('驳回成功')
          this.reviewRejectDialogVisible = false;
          this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },

    //审核
    auditClick(type) {
      this.reason='';
      if(type == 'reviewReject'){
        this.reviewRejectDialogVisible = true;
        return;
      }
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要审核的数据");
        return false;
      }
      this.$confirm('确定审核【' + this.selectedRows.length + '】条数据吗?', '审核单据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        if (type == 'reviewPass'){
          auditTransferBound(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(res.data.data)
              this.dataList()
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          })
        }
      })
    },
    //批量删除
    delClick() {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要删除的数据");
        return false;
      }
      this.$confirm('确定删除【' + this.selectedRows.length + '】条数据吗?', '删除单据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        delTransferBound(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },
    //导出
    exportClick() {

    },
    //详情
    handleDetail(row) {
      var title = row.transferNo;
      this.$router.push({ name: 'transferDetail', params: { id: row.id }});
      addTabs(this.$route.path, title);
    },
    //编辑
    handleEdit(row) {

      var title = "编辑 " + row.transferNo;
      this.$router.push({ name: 'transferStockAdd', params: {id: row.id}});
      addTabs(this.$route.path, title);
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //时间格式化方法
    conversion,
    //查询
    handleSearch() {
      const params = new URLSearchParams();
      params.append('applyNo', this.queryParams.applyNo);
      params.append('applyOrderStatus', this.queryParams.applyOrderStatus);
      params.append('taskOrderStatus', this.queryParams.taskOrderStatus);
      params.append('shippingStatus', this.queryParams.shippingStatus);
      params.append('collectionStatus', this.queryParams.collectionStatus);
      params.append('salesUser', this.queryParams.salesUser);
      params.append('salesOrderNo', this.queryParams.salesOrderNo);
      params.append('salesNo', this.queryParams.salesNo);
      params.append('status', this.queryParams.status);
      params.append('startDate', this.queryParams.startDate);
      params.append('endDate', this.queryParams.endDate);
      params.append('customerOrderNo', this.queryParams.customerOrderNo);
      params.append('page', this.queryParams.page);
      params.append('limit', this.queryParams.pageSize);
      params.append('productId', this.queryParams.productId);
      // const params = {
      //   applyNo: this.queryParams.applyNo,
      //   applyOrderStatus: this.queryParams.applyOrderStatus,
      //   taskOrderStatus: this.queryParams.taskOrderStatus,
      //   shippingStatus: this.queryParams.shippingStatus,
      //   collectionStatus: this.queryParams.collectionStatus,
      //   salesUser: this.queryParams.salesUser,
      //   salesOrderNo: this.queryParams.salesOrderNo,
      //   salesNo: this.queryParams.salesNo,
      //   status: this.queryParams.status,
      //   startDate: this.queryParams.startDate,
      //   endDate: this.queryParams.endDate,
      //   customerOrderNo: this.queryParams.customerOrderNo,
      //   page: this.queryParams.page,
      //   limit: this.queryParams.pageSize,
      //   productName:this.queryParams.productName
      // }
      // 发起请求查询接口
      this.dataList(params)
    },
    //  处理选中的行
    handleSelectionChange(val) {
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },
    //重置查询条件
    handleReset() {
      this.queryParams = {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        applyNo: '',
        endDate: '',
        salesNo: '',
        applyOrderStatus: '',
        taskOrderStatus: '',
        shippingStatus: '',
        collectionStatus: '',
        salesUser: '',
        customerName: '',
        salesTime: '',
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        productName: '',
        productId: '',
        page: 1,
        pageSize: 10
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.queryParams.productId = '';
      //重置日期选择组件
      if(this.$refs.dateRangeSelector){
        this.$refs.dateRangeSelector.reset();
      }
      this.handleSearch()
    },
    //分页查询数据
    dataList(params) {
      transferList(params).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },


    getCurrentNode(node) {
      if (node != null) {
        this.$refs['form'].validateField('applyDept')
      }
    },
    // 获取日期范围选择
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      this.handleSearch();
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    this.handleSearch();
    //获取审核状态数据字典
    this.getAuditStatusDict();
    // this.getUserList();
  },
  created() {

  },
}
</script>

<style scoped>

</style>
