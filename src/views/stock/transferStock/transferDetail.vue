<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>调拨单详情</div>
      <div>
        <el-button plain>导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="调拨单号">
                <span>{{ detailInfo.transferNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="调拨时间">
                <span>{{ detailInfo.transferTime | conversion('yyyy-MM-dd HH:mm:ss') }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="调出仓库">
                <span>{{ detailInfo.fromWarehouseName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="调入仓库">
                <span>{{ detailInfo.inWarehouseName }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ detailInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="detailInfo.stockTransferDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="商品编码" prop="productCode" min-width="120"></el-table-column>
              <el-table-column label="商品名称" prop="productName" min-width="120"> </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="80">
                <template slot-scope="{ row }">
                  <img v-if="row.imageUrl !== ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" prop="brand" min-width="100"> </el-table-column>
              <el-table-column label="规格型号" prop="model" min-width="100"> </el-table-column>
              <el-table-column label="调出仓库" prop="fromWarehouseName" min-width="100"> </el-table-column>
              <el-table-column label="调出库位" prop="fromLocationName" min-width="100"> </el-table-column>
              <el-table-column label="调入仓库" prop="inWarehouseName" min-width="100"> </el-table-column>
              <el-table-column label="调入库位" prop="inLocationName" min-width="100"> </el-table-column>
              <el-table-column label="调拨数量" prop="quantity" min-width="100"> </el-table-column>
              <el-table-column label="单位" prop="unit" min-width="100"> </el-table-column>
              <el-table-column label="备注" prop="remark" min-width="100"> </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="制单人">
                {{ detailInfo.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ detailInfo.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions label="附件">
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

  </div>
</template>

<script>
import { removeTabs, collapseArea } from "@/assets/js/common";
import {transferBoundInfo} from "@/api/stockmgt";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "transferDetail",
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      id: "",
      detailInfo: {},
      accessoryList: [], // 附件信息
      salesOrderDetailList:[],
      fileList: [], // 图片上传列表
      activeNames: ["base", "product", "status", "more"], // 全部展开
    }
  },
  methods:{
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    async dataList() {
      var _this = this;
      try {
        const res = await transferBoundInfo(_this.id);
        if (res.data.code == 100) {
          _this.detailInfo = res.data.data
          if (_this.detailInfo.productAccessoryList) {
            var imgList = _this.detailInfo.productAccessoryList.length;
            if (imgList > 0) {
              _this.accessoryList = _this.detailInfo.productAccessoryList;
            }
          }
          var productList = _this.detailInfo.stockTransferDetailList.length;
          if (productList > 0) {
            _this.stockTransferDetailList = _this.detailInfo.stockTransferDetailList;
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          removeTabs(this.$route);
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        removeTabs(this.$route);
      }
    }

  },
  mounted() {
    var _this = this;
    _this.id = _this.$route.params.id;
    _this.dataList();
  },
}
</script>
