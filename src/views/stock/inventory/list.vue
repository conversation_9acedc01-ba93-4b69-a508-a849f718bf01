<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" :label-width="$labelFour">

        <el-form-item label="仓库" prop="warehouseName">
          <selectInput
            ref="selectInput"
            v-model="queryParams.warehouseName"
            :inputParam="queryParams.warehouseName"
            inputType="warehouseInfo"
            placeholder="请选择仓库"
            @select="onInputSearch('warehouse', $event)"
          ></selectInput>
        </el-form-item>

<!--        <el-form-item label="商品类别" prop="categoryName">-->
<!--          <selectInput-->
<!--            ref="selectInput"-->
<!--            v-model="queryParams.categoryName"-->
<!--            :inputParam="queryParams.categoryName"-->
<!--            inputType="categoryName"-->
<!--            placeholder="请选择商品类别"-->
<!--            @select="onInputSearch('name', $event)"-->
<!--          ></selectInput>-->
<!--        </el-form-item>-->

        <el-form-item label="商品" prop="productName">
          <selectInput
            ref="selectInput"
            v-model="queryParams.productName"
            :inputParam="queryParams.productName"
            inputType="productInfo"
            placeholder="请选择商品"
            @select="onInputSearch('product', $event)"
          ></selectInput>
        </el-form-item>



        <el-form-item  prop="status" label="库存预警" >
            <el-select  v-model="queryParams.status" placeholder="请选择预警方式" clearable>
              <el-option label="预警上限" value="1" />
              <el-option label="预警下限" value="2" />
            </el-select>
        </el-form-item >


        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="el-icon-plus">生成单据</el-button>
          <el-button type="text" icon="bulkDown-icon" @click="lockClick()">锁定库存</el-button>
          <el-button type="text" icon="bulkDown-icon" @click="unlockClick()">解锁库存</el-button>
          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导入</el-button>
          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
        </div>
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange"
                :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="50" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="warehouseCategoryName" label="仓库类型" width="150"/>
        <el-table-column prop="warehouseName" label="仓库名称" width="150"/>
        <el-table-column prop="locationName" label="库位" width="150"/>
        <el-table-column prop="productCategoryName" label="商品类别" width="150"/>
        <el-table-column prop="productCode" label="商品编码" width="150"/>
        <el-table-column prop="productName" label="商品名称" width="150"/>
        <el-table-column prop="brand" label="品牌" width="150"/>
        <el-table-column label="图片" prop="imageUrl" width="60">
          <template slot-scope="{ row }">
            <img v-if="row.imageUrl !== ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
          </template>
        </el-table-column>
        <el-table-column prop="model" label="规格类型" width="150"/>
        <el-table-column prop="currentInventory" label="当前库存" width="150"/>
        <el-table-column prop="lockQty" label="锁定库存" width="150"/>
        <el-table-column prop="waitProcessQty" label="待加工数" width="150"/>
        <el-table-column prop="waitShipmentsQty" label="待发货数" width="150"/>
        <el-table-column prop="warningLower" label="预警下限" width="150"/>
        <el-table-column prop="warningUpper" label="预警上限" width="150"/>
        <el-table-column prop="unit" label="单位" width="150"/>
        <el-table-column prop="unitPrice" label="成本单价(￥)" width="150"/>
        <el-table-column prop="unitPrice" label="成本金额(￥)" width="150"/>
        <el-table-column prop="remark" label="备注" width="150"/>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>

    </div>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="addDialogVisible" v-if="addDialogVisible">
      <el-form :model="selectedRow" :rules="lockRules" ref="selectedRow" >
        <el-form-item label="商品编码" prop="productCode">
          <el-input v-model="selectedRow.productCode" disabled  />
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="selectedRow.productName"  disabled/>
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="selectedRow.unit" disabled />
        </el-form-item>
        <el-form-item label="数量" prop="beLockQty" v-if="dialogStatus == 'lock'">
          <el-input v-model="selectedRow.beLockQty" ></el-input>
        </el-form-item>

        <el-form-item label="锁定数量" prop="lockQty" v-if="dialogStatus == 'unlock'">
          <el-input v-model="selectedRow.lockQty" disabled></el-input>
        </el-form-item>

        <el-form-item label="解锁数量" prop="unlockQty" v-if="dialogStatus == 'unlock'">
          <el-input v-model="selectedRow.unlockQty" ></el-input>
        </el-form-item>

        <el-form-item class="submitArea">
          <el-button type="primary" @click="handlelockStock">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="addDialogVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
// import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {salesList} from "@/api/salesmgt";
import {addTabs, sysServerUrl, tableHeight} from "@/assets/js/common";
import {dictTypeQueryData} from "@/api/sysmgt";
// import warehouseInfo from "@/views/stock/inventory/warehouseInfo.vue";
// import categoryInfo from "@/views/stock/inventory/categoryInfo.vue";
// import productInfo from "@/views/stock/inventory/productInfo.vue";
import {inventoryList, lockInventory, unLockInventory} from "@/api/stockmgt";
import selectInput from "@/components/selectInput/selectInput.vue";

export default {
  name: "list",
  components: {selectInput,  Pagination},
  data() {
    return {
      // warehouseDialogVisible: false,
      // categoryDialogVisible: false,
      // productDialogVisible: false,
      auditStatusDicts: [],
      selectedRows: [],
      selectedRow: {
        quantity : 0,
      },
      deleteList: [],
      maximumHeight: 0,
      total: 0,
      isShow: false,
      addDialogVisible: false,
      resultList: [],
      dialogStatus: "",
      queryParams: {
        warehouseName:'',
        warehouseId:'',
        categoryName:'',
        categoryId:'',
        productName:'',
        productId:'',
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        salesNo: '',
        salesTime: '',
        salesOrderNo: '',
        status: null,
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10
      },
      textMap: {
        lock: "锁定库存",
        unlock: "解锁库存",
      },
       lockRules: {
      beLockQty: [
        { required: true, message: '数量不能为空', trigger: ['blur', 'change'] },
      ],
      unlockQty: [
        { required: true, message: '解锁数量不能为空', trigger: ['blur', 'change'] },
      ],
    },
    }
  },
  methods: {
    tableRowClassName({ row }){
      const current = Number(row.currentInventory);
      const lower = Number(row.warningLower);
      const upper = Number(row.warningUpper);

      // 如果当前库存小于预警下限 或 大于预警上限，则返回自定义类名
      if (current < lower || current > upper) {
        return 'warning-row'; // 这个类名会在 CSS 中定义样式
      }
      return ''; // 否则不添加特殊类
    },
    sysServerUrl() {
      return sysServerUrl
    },

    //转换状态显示
    formatAuditStatus(status){
      const match = this.auditStatusDicts.find(item => item.code == status);
      return match ? match.name : '';
    },
    //新增
    addClick() {
      const title = "新增销售订单";
      this.$router.push({
        name: 'salesAdd',
        params: {id: 'add'}
      });
      addTabs(this.$route.path, title);
    },
    //锁定库存
    lockClick() {
      this.selectedRow = this.selectedRows[0];
      if(this.selectedRow == undefined){
        this.$DonMessage.warning('请选择要锁定的库存');
        return;
      }
      // if(this.selectedRows.length >= 1){
      //   this.$message({
      //     message: '请选择要锁定的库存',
      //     type: 'warning'
      //   });
      //   return;
      // }
      this.selectedRow.beLockQty = '';
      this.dialogStatus = "lock"
      this.addDialogVisible = true;
    },
    //解锁库存
    unlockClick() {
      this.selectedRow = this.selectedRows[0];
      if(this.selectedRow == undefined){
        this.$DonMessage.warning('请选择要解锁的库存');
        return;
      }
      this.selectedRow.unlockQty = '';
      this.dialogStatus = "unlock"
      this.addDialogVisible = true;
    },
    handlelockStock(){
      let params = {
        id: this.selectedRow.id,
        lockQty: this.selectedRow.lockQty,
        beLockQty: this.selectedRow.beLockQty,
        unlockQty: this.selectedRow.unlockQty,
      }
      if (this.dialogStatus == "lock") {
        lockInventory(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success("锁定库存成功");
            this.addDialogVisible = false;
            this.dataList(params);
          } else {
            this.$DonMessage.error(res.data.msg)
            this.addDialogVisible = false;
          }
        })
      } else {
        unLockInventory(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success("解锁库存成功");
            this.addDialogVisible = false;
            this.dataList(params);
          } else {
            this.$DonMessage.error(res.data.msg)
            this.addDialogVisible = false;
          }
        })
      }

    },
    //生成采购订单
    generateOrderClick() {

    },
    //删除
    delClick() {

    },
    //导出
    exportClick() {

    },
    onInputSearch(type, $event) {
      if (type == "name") {
        this.queryParams.productName = '';
        this.queryParams.productId = '';
        this.queryParams.categoryName = $event.name;
        this.queryParams.categoryId = $event.id;
      } else if (type == "product") {
        this.queryParams.productName = $event.name;
        this.queryParams.productId = $event.id;
      } else if (type == "warehouse") {
        this.queryParams.warehouseName = $event.name;
        this.queryParams.warehouseId = $event.id;
      } else if (type == "location") {
        this.queryParams.locationName = $event.name;
        this.queryParams.locationId = $event.id;
      } else if (type == "brand") {
        this.queryParams.brand = $event.name
      }
    },

    // 采购订单表格高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //时间格式化方法
    conversion,
    //查询
    handleSearch() {
      const params = {
        status: this.queryParams.status,
        warehouseName: this.queryParams.warehouseName,
        warehouseId: this.queryParams.warehouseId,
        categoryName: this.queryParams.categoryName,
        categoryId: this.queryParams.categoryId,
        productId: this.queryParams.productId,
      }
      // 发起请求查询接口
      this.dataList(params)
    },
    //  处理选中的行
    handleSelectionChange(val) {
      if (val.length > 1) {
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(val.pop());
      }
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },
    //重置查询条件
    handleReset() {
      this.queryParams = {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        applyNo: '',
        salesOrderNo: '',
        status: null,
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear();
      }
      this.handleSearch()
    },
    //分页查询数据
    dataList(params) {
      inventoryList(params).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    console.log("1211111111111111111111111111")
    this.handleSearch();
    //获取审核状态数据字典
    this.getAuditStatusDict();
  },
  created() {
    console.log('Component created');
  },
}
</script>

<style>
.warning-row {
  background-color: #ffcccc; /* 浅红色背景，你可以根据需要调整颜色 */
}
</style>
