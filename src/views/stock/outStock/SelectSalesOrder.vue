<template>
  <div class="elAutocomplete">
    <el-autocomplete
      ref="autocomplete"
      popper-class="my-autocomplete"
      :inputParam="inputParam"
      :inputType="inputType"
      v-model="inputValue"
      :fetch-suggestions="querySearch"
      :placeholder="placeholder"
      @select="handleSelect"
      :disabled="disabled"
      @blur="handleBlur"
    >
      <template slot-scope="{item}">
        {{ item.name }}
      </template>
    </el-autocomplete>
    <el-button :disabled="disabled" type="text" icon="selectIcon-icon" @click="handleIconClick"></el-button>
    <el-dialog width="1400px !important" :title="textMap[dialogStatus]" :visible.sync="dialogCategoryVisible" :append-to-body="true" v-if="dialogCategoryVisible">
      <div v-if="inputType === 'salesOrder'">
        <div class="secondFloat">
          <el-form :inline="true" :model="searchForm" label-width="100px">
            <el-form-item label="销售订单号">
              <el-input v-model="searchForm.salesNo" placeholder="请输入销售订单号"></el-input>
            </el-form-item>
            <el-form-item label="订单日期">
              <el-date-picker
                v-model="searchForm.salesTime"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
              <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table
          style="width: 100%;"
          ref="table"
          :data="resultData"
          border
          stripe
          highlight-current-row
          @header-dragend="changeColWidth"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
          <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column prop="salesNo" label="销售订单号" min-width="110" />
          <el-table-column prop="salesTime" label="订单日期" min-width="110" />
          <el-table-column prop="customerName" label="客户名称" min-width="110" />
          <el-table-column prop="customerNo" label="客户订单号" min-width="110" />
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column prop="collectionStatus" label="收款状态" width="80" />
          <el-table-column prop="salesUser" label="销售员" width="100" />
          <el-table-column prop="status" label="状态" width="100" />

          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <div :class="getStatusClass(scope.row.status)">
                <!--              {{ getAuditStatusText(scope.row.status) }}-->
                {{ formatAuditStatus(scope.row.status) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="auditUser" label="审核人" width="100" />
          <el-table-column prop="auditTime" label="审核时间" width="140" />
          <el-table-column prop="remark" label="备注" min-width="110" />
          <el-table-column prop="createdUser" label="制单人" width="100" />
          <el-table-column prop="createdTime" label="制单时间" width="140" />
        </el-table>
        <Pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
      </div>
      <div class="submitArea">
        <el-button type="primary" @click="onSubmit">{{ $t('button.submit') }}</el-button>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {
  getBrandData,
  getCategoryTreeData,
  getLocationData,
  getWarehouseData,
  productCategoryTree
} from "@/views/basic/basicCommon";
import {renderTree} from "@/assets/js/common";
import {salesList} from "@/api/salesmgt";
import {getStatusClass} from "@/assets/js/utils";
import {dictTypeQueryData} from "@/api/sysmgt";

export default {
  name: "SelectSalesOrder",
  props: {
    modelValue: String,
    placeholder: { type: String, default: "请输入内容" },
    inputParam: { type: String, default: "" },
    inputType: { type: String, default: "" },
    warehouseId: { type: String, default: ""  },
    disabled: Boolean,
  },
  emits: ['update:modelValue', 'search'],
  components: {Pagination},
  data() {
    return {
      auditStatusDicts: [],
      inputValue: "",
      categoryList: [],
      resultData: [], // 商品类别树结构
      dialogCategoryVisible: false,
      dialogStatus: '',
      textMap: {
        categoryName: '商品分类',
        brand: "品牌分类",
        brandInfo: '品牌',
        warehouseInfo: '默认仓库',
        locationInfo: '默认库位',
        salesOrder: '选择销售订单'
      },
      searchForm: {
        salesNo: "",
        salesTime: "",
      },
      searchVal: "", // 搜索值
      treeCurrentNode: null,
      searchResult: [],
      selectedValue: [], // 记录是否选择
      pagesize: 10,
      currentPage: 1,
      total: 0,
      currentRow: [],
    }
  },
  watch: {
    inputValue(val) {
      this.inputValue = val;
    },
  },
  mounted() {
    var _this = this;
    if(_this.inputParam != ""){
      _this.inputValue = _this.inputParam
    }
    this.dataList()

    //获取审核状态数据字典
    this.getAuditStatusDict();
  },
  methods: {
    getStatusClass,
    //转换状态显示
    formatAuditStatus(status){
      const match = this.auditStatusDicts.find(item => item.code == status);
      return match ? match.name : '';
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    handleSelectionChange(selection) {
      if (selection.length > 1) {
        this.$refs.table.clearSelection();
        this.$refs.table.toggleRowSelection(selection.pop());
      }
      this.treeCurrentNode = selection[0];
    },
    // 获取数据
    dataList() {
      this.categoryList = [];
      this.resultData = [];
      const params = {
        page: this.currentPage,
        limit: this.pagesize,
        salesNo: this.searchForm.salesNo === ''? null : this.searchForm.salesNo,
        salesTime: this.searchForm.salesTime ==='' ? null : this.searchForm.salesTime,
        status: 2, // 审核通过
      }
      salesList(params).then(res => {
        if (res.data.code === 100){
          this.resultData = res.data.data;
          this.total = res.data.total;
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 查询数据
    async querySearch(queryString, cb) {
      var restaurants = this.categoryList;
      var results = queryString ? restaurants.filter(this.createAssemblyFilter(queryString)) : restaurants;
      // this.searchResult = results;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createAssemblyFilter(queryString) {
      return (restaurant) => {
        return (restaurant.name.indexOf(queryString) !== -1);
      };
    },
    // 选中值
    handleSelect(item) {
      this.selectedValue = item;
      this.inputValue = item.name
      this.$emit('select', item);
    },
    handleBlur() {
      if (!this.selectedValue) {
        this.inputValue = ''; // 未选择则清空
      }
    },
    clear() {
      this.selectedValue = '';
      this.inputValue = '';
      this.$emit('select', '');
    },
    // 搜索值
    handleSearch() {
      this.dataList();
    },
    handleReset(){

    },
    // 弹框
    // 搜索
    onSearch() {},
    async handleIconClick() {
      if (this.disabled == true) {
        return
      }
      this.dataList();
      this.dialogStatus = this.inputType;
      this.dialogCategoryVisible = true;
    },
    // 树结构图标
    renderContent(h, { node, data }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".categoryTreeManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 数选中值
    handleCategorySelect(node) {
      this.treeCurrentNode = node
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        console.error('审核状态数据字典', res)
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 提交
    onSubmit() {
      if (this.treeCurrentNode == null) {
        this.$DonMessage.warning("请选择销售订单")
        return
      }
      this.inputValue = this.treeCurrentNode.salesNo;
      this.$emit('select', this.treeCurrentNode);
      this.dialogCategoryVisible = false;
    },
    // 取消
    onCancel() {
      this.dialogCategoryVisible = false;
    },
  },
}
</script>

<style scoped>
.elAutocomplete .el-input__suffix{
  cursor: pointer;
}
.searchTreeArea {
  display: flex;
  margin-bottom: 10px;
}
.el-dialog .searchTreeArea .el-input {
  width: 300px !important;
  margin-right: 10px;
}
.el-dialog .searchTreeArea .el-input .el-input__inner{
  width: 100% !important;
}
</style>
