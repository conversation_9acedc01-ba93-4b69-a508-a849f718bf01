<template>
  <div class="supplierContainer">
    <div class="dialogSearch">
      <el-input placeholder="输入编码/名称搜索"></el-input>
      <el-checkbox v-model="enableState">仅显示启用</el-checkbox>
      <el-button type="primary" >搜索</el-button>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span>客户分类</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <!-- <el-scrollbar> -->
                <el-tree :data="productTreeList" node-key="id" :render-content="renderContent"
                  :default-expand-all="true" @node-click="handleCategorySelect" :props="{
                    label: 'name',
                    children: 'children'
                  }" ref="categoryTree"></el-tree>
              <!-- </el-scrollbar> -->
            </div>
          </div>
        </el-col>
        <el-col :span="19">
          <el-row>
            <el-col :span="19" class="nationalInfo">
              <div>
                <el-table
                  :data="productList"
                  :height="tableHeight"
                  style="width: 100%;"
                  ref="table"
                  highlight-current-row
                border
                stripe
                @header-dragend="changeColWidth"
                @row-click="handleRowClick"
                >
                <!-- 移除 type="selection" 列 -->
                <el-table-column label="客户编码" prop="code"></el-table-column>
                <el-table-column label="客户名称" prop="name"></el-table-column>
                <el-table-column label="分类"></el-table-column>
                <el-table-column label="状态">
                  <template slot-scope="{row}">
                    <span class="successColor" v-if="row.status === 1">生效</span>
                    <span class="errorColor" v-if="row.status === 0">失效</span>
                  </template>
                </el-table-column>
                <el-table-column label="备注" prop="remark"></el-table-column>
                </el-table>
                <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="submitArea">
      <div style="float: left;">
        <el-button plain>新增</el-button>
      </div>
      <el-button type="primary" @click="supplierSubmit()">{{ $t('button.submit') }}</el-button>
      <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { productCategoryTree, getCategoryTreeData } from "@/views/basic/basicCommon";
import {getCategoryTree, getCustomerList, getProductList, getSupplierList} from '@/api/basicmgt'
import { addTabs, renderTree, tableHeight, contextmenuSeat } from "@/assets/js/common";
export default {
  name: "addProduct",
  components: { Pagination },
  data() {
    return {
      enableState: false,
      productTreeList: [],
      productList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      tableHeight:0,
      maxHeight:0,
      // 选中的对象
      determineModelList: [],
      // 选中的数量
      selectNum: 0,
      selectedCustomer : ''
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 单选逻辑：点击行时更新选中项
    handleRowClick(row) {
      this.selectedCustomer = row;  // 存储当前选中的客户
      this.$refs.table.setCurrentRow(row);  // 高亮当前行
    },
    renderContent(h, { node, data }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".commodityManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 客户分类
    getProductTree() {
      const tree = async () => {
        var result = await getCategoryTreeData("customer");
        this.productTreeList = result;
      }
      tree();
    },
    // 树结构点击事件
    handleCategorySelect(row) {
      this.selectedCategoryId = row.id;
      this.currentPage = 1;
      this.dataList();
    },
    // 客户列表
    dataList() {
      const params = {
        categoryId: this.selectedCategoryId,
        page: this.currentPage,
        size: this.pagesize,
      }
      getCustomerList(params).then((res) => {
        if (res.data.code == '100') {
          this.productList = res.data.data
          this.total = res.data.total;
          this.sizeArea()
        }
      });
    },
    // 提交
    supplierSubmit() {
      this.$parent.$parent.queryParams.selectedCustomer = this.selectedCustomer;
      this.$parent.$parent.queryParams.counterpartyName = this.selectedCustomer.name;
      this.$parent.$parent.queryParams.customerId = this.selectedCustomer.id;
      this.$parent.$parent.customerDialogVisible = false;
    },
    cancelClick() {
      this.$parent.$parent.customerDialogVisible = false;
    },
    heightArea() {
      var allHeight = $(".supplierContainer .infoDetail").height();
      var topHeight = $(".infoDetail .topButton").outerHeight(true);
      var leftVal = allHeight - topHeight;
      $(".supplierContainer .infoDetail .scrollClass").css("height", leftVal);
      var pageHeight = $(".nationalInfo .pagination-container").outerHeight(true);
      this.tableHeight = allHeight - pageHeight;
      this.maxHeight = allHeight;
    },
    sizeArea() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function() {
        _this.heightArea();
      })
    },
  },
  mounted() {
    this.getProductTree();
    this.dataList()
  },
}
</script>
<style>
.el-dialog .dialogSearch .el-input {
  width: 350px !important;
  margin-right: 10px;
}

.dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}


.dialogSearch .el-input .el-input__inner {
  width: 100% !important;
}
.dialogSearch .el-checkbox {
  margin-right: 10px !important;
}
.supplierContainer .infoDetail {
  height: 550px;
}
.supplierContainer .infoDetail .scrollClass {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
.supplierContainer .pagination-container {
  border-bottom: 1px solid var(--table-border);
  border-left: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border);
}
</style>
