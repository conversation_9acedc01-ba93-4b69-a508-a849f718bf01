<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="this.stateType == 'edit'">编辑出库单</div>
      <div v-else>新增出库单</div>
      <div>
        <el-button type="primary" @click="onSave()">保存</el-button>
        <el-button plain @click="onSubmit()">提交</el-button>
        <el-button plain @click="onSaveAndAudit()">提交并审核</el-button>
        <el-button plain @click="removeClick()">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFour">
                <el-form-item label="出库单号" prop="outboundNo">
                  <el-input v-model="form.outboundNo" placeholder="出库单号由系统生成" disabled />
                </el-form-item>
                <el-form-item label="出库日期" prop="outboundTime">
                  <el-input disabled v-model="form.outboundTime"></el-input>
                  <!-- <el-date-picker
                    v-model="form.outboundTime"
                    type="date"
                    clearable
                    value-format="yyyy-MM-dd"
                    placeholder="请选择日期"
                    @change="handleDateChange"
                  ></el-date-picker> -->
                </el-form-item>
                <el-form-item label="类型" prop="outboundType">
                  <el-select v-model="form.outboundType" placeholder="请选择类型" :disabled="disabledStatue">
                    <el-option
                      v-for="item in salesTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="源单据号" prop="sourceNo">

                  <SelectSalesOrder
                    v-if="form.outboundType == 'sales'"
                    ref="selectInput"
                    v-model="form.sourceNo"
                    :input-param="form.sourceNo"
                    input-type="salesOrder"
                    placeholder="请选择销售订单"
                    @select="handleSelectSalesOrder"
                    :disabled="disabledStatue"
                  ></SelectSalesOrder>

                  <el-input
                    v-else-if="form.outboundType == 'issue'"
                    ref="selectInput"
                    v-model="form.sourceNo"
                    :input-param="form.sourceNo"
                    input-type="salesOrder"
                    placeholder="请选择领料单"
                    :disabled="disabledStatue"
                  ></el-input>

                  <transferOrder
                    v-else-if="form.outboundType == 'transfer'"
                    ref="selectInput"
                    v-model="form.sourceNo"
                    :input-param="form.sourceNo"
                    input-type="salesOrder"
                    placeholder="请选择调拨单"
                    @select="handleSelectTransferOrder"
                    :disabled="disabledStatue"
                  ></transferOrder>
                  <el-input v-else disabled></el-input>
                </el-form-item>
                <el-form-item label="往来单位" prop="counterpartyName" v-if="form.outboundType != 'transfer'">
                  <selectInput
                    ref="selectInput"
                    v-model="form.counterpartyName"
                    :inputParam="form.counterpartyName"
                    :disabled="stateType == 'add' ? form.sourceNo ?  true : false: disabledStatue"
                    inputType="partnerCustomerInfo"
                    placeholder="请选择往来单位"
                    @select="onInputSearch('', 'partner', $event)"
                  ></selectInput>
                </el-form-item>
                <el-form-item label="客户订单号" prop="counterpartyCode" >
                  <el-input v-model="form.counterpartyCode" placeholder="请输入客户订单号" disabled/>
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500" show-word-limit/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <div class="tableHandle spaceBbetwee" v-if="stateType == 'add' ? form.sourceNo ?  false: true: !disabledStatue">
              <el-button type="primary" icon="addProducts-icon" @click="addProduct">添加商品</el-button>
              <div>
                <el-button type="text" icon="import-icon">批量导入</el-button>
                <el-button type="text" icon="setIcon-icon">批量设置</el-button>
                <el-button type="text" icon="deleteRed-icon">批量删除</el-button>
              </div>
            </div>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.stockOutboundDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              @selection-change="handleSelectChange"
              show-summary
              :summary-method="getSummaries"
            >
<!--              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column label="商品编码" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productCode !== undefined">{{row.productCode}}</span>
                </template>
              </el-table-column>
              <el-table-column label="商品名称" min-width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productName !== undefined">{{row.productName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="图片" prop="imageUrl" width="80">
                <template slot-scope="{row}">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="$filePath + row.imageUrl" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="品牌" width="100">
                <template slot-scope="{row}">
                  <span v-if="row.brand !== undefined">{{row.brand}}</span>
                </template>
              </el-table-column>
              <el-table-column label="规格型号" width="100">
                <template slot-scope="{row}">
                  <span v-if="row.productModel !== undefined">{{row.productModel}}</span>
                </template>
              </el-table-column>

              <el-table-column label="出库仓库" prop="warehouseName" min-width="120">
                <template #header>
                  <span class="required-field">出库仓库</span>
                </template>
                <template #default="{ row, $index }">
                  <el-form :model="row" :rules="infoRules" ref="warehouseName">
                    <el-form-item prop="warehouseName">
                      <div class="rowEditShow">
                        <selectInput
                          ref="selectInput"
                          v-model="row.warehouseName"
                          :inputParam="row.warehouseName"
                          inputType="warehouseInfo"
                          placeholder="请选择出库仓库"
                          @select="onInputSearch($index,'warehouse', $event)"
                        ></selectInput>
                      </div>
                    </el-form-item>
                  </el-form>
               </template>
              </el-table-column>
              <el-table-column label="库位" prop="locationName" min-width="120">
                <template #header>
                  <span class="required-field">库位</span>
                </template>
                <template #default="{ row, $index }">
                  <el-form :model="row" :rules="infoRules" ref="locationName">
                    <el-form-item prop="locationName">
                      <div class="rowEditShow">
                        <selectInput
                          ref="selectInput"
                          v-model="row.locationName"
                          :inputParam="row.locationName"
                          inputType="locationInfo"
                          :warehouseId= row.fromWarehouseId
                          placeholder="请选择库位"
                          @select="onInputSearch($index,'location', $event)"
                        ></selectInput>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="单位" width="80" prop="unit" v-if="stateType == 'add' ? form.sourceNo ?  true: false : disabledStatue">
                <template slot-scope="{row}">
                  <span v-if="row.unit !== undefined">{{row.unit}}</span>
                </template>
              </el-table-column>
              <el-table-column label="单位" width="80" prop="unit" v-if="stateType == 'add' && !form.sourceNo">
                <template #header >
                  <span class="required-field">单位</span>
                </template>
                <template #default="{row}" >
                  <el-form :model="row" :rules="infoRules" ref="unit">
                    <el-form-item prop="unit">
                      <div class="rowEditShow">
                        <el-select v-model="row.unit">
                          <el-option v-for="item of unitData" :key="item.id" :label="item.name" :value="item.name"></el-option>
                        </el-select>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="出库数量" min-width="80" prop="currentQty" v-if="this.stateType != 'addInfo'">
                <template #header>
                  <span class="required-field">出库数量</span>
                </template>
                <template #default="{row}">
                  <el-form :model="row" :rules="infoRules" ref="currentQty">
                    <el-form-item prop="currentQty">
                      <div class="rowEditShow">
                        <el-input v-model.number="row.currentQty"></el-input>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="数量" min-width="80" prop="quantity" align="right" v-if="this.stateType == 'addInfo'">
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <span>{{row.quantity}}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="待出库数量"  prop="beQty" min-width="80" align="right" v-if="this.stateType == 'addInfo'">
                <template slot-scope="{row}">
                  <span v-if="row.beQty !== undefined">{{row.beQty}}</span>
                </template>
              </el-table-column>
              <el-table-column label="本次出库数量" min-width="80" prop="currentQty" v-if="this.stateType == 'addInfo'">
                <template #header>
                  <span class="required-field">本次出库数量</span>
                </template>
                <template #default="{row}">
                  <el-form :model="row" :rules="infoRules" ref="currentQty">
                    <el-form-item prop="currentQty">
                      <div class="rowEditShow">
                        <el-input v-model.number="row.currentQty"></el-input>
                      </div>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="100">
                <template slot-scope="{row}">
                  <div class="rowEditShow">
                    <el-input v-model.number="row.remark"></el-input>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" fixed="right" width="80">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton" @click="delClick(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="创建人">
                {{ form.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="创建日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人">
                {{ form.updatedUser }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="附件">
                <el-upload
                  style="width: 500px;"
                  class="upload-demo"
                  :action="uploadUrl"
                  :headers="importHeader"
                  :file-list="fileList"
                  :on-remove="handleOnRemove"
                  :before-remove="beforeOnRemove"
                  :before-upload="beforeAvatarUpload"
                  :on-exceed="handleOnExceed"
                  :on-success="handleOnSuccess"
                  :limit="1"
                >
                  <span class="linkStyle" v-if="this.count == 0">点击上传</span>
                </el-upload>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <dialogTable
      v-if="isReload"
      :isReload.sync="isReload"
      type="product"
      :formList.sync="form.stockOutboundDetailList"
      :columns.sync="productColumns"
    >
      <template #imageUrl="scope">
        <img v-if="scope.row.imageUrl != ''" class="pictureShow" :src="$filePath + scope.row.imageUrl" alt="">
      </template>
    </dialogTable>
  </div>
</template>
<script>
import selectInput from "@/components/selectInput/selectInput.vue";
import {getSalesCompanyList} from "@/api/basicmgt";
import {findUserPage} from "@/api/sysmgt";
import {addTabs, removeTabs, sysServerUrl, collapseArea, beforeRouteInfo, getContentData} from "@/assets/js/common";
import {getSalesOrderEdit} from "@/api/salesmgt";
import {addOutbound, getSourceOrderInfo, outboundInfo, editOutbound} from "@/api/stockmgt";
import {conversion} from "@/store/filters";
import index from "vuex";
import { getBatchDeleteInfo, getDeleteInfo, getUnitListData } from '@/views/basic/basicCommon'
import SelectSalesOrder from "@/views/stock/outStock/SelectSalesOrder.vue";
import transferOrder from "@/views/stock/outStock/transferOrder.vue";
import dialogTable from "@/components/dialogTable/dialogTable.vue";
import {productColumns} from "@/assets/js/tableHeader";

export default {
  name: 'addOutStock',
  components: {SelectSalesOrder, transferOrder,dialogTable, selectInput},
  data() {
    return {
      stateType: "", //类型
      count:0,
      disabledStatue: false, //输入框状态
      id: "",
      productColumns: productColumns,
      uploadUrl: sysServerUrl + 'sys/upload/attach?flag=outbound', // 文件上传地址
      fileList: [], // 文件列表
      // 基本信息
      form: {
        id: "",
        sourceNo:'',
        outboundType:'',
        outboundNo:'',
        counterpartyName:'',
        counterpartyCode:'',
        customerNo:'',
        warehouseName:'',
        warehouseId:'',
        locationId:'',
        locationName:'',
        createdUser: "",
        createdUserName: "",
        createdTime: "",
        outboundTime: "",
        updatedUser: "",
        updatedTime: "",
        stockOutboundDetailList: [],
        productAccessoryList: [],
      },
      salesTypeOptions: [
        { value: 'sales', label: '销售出库' },      // 销售出库
        { value: 'issue', label: '领料出库' },      // 领料出库
        { value: 'transfer', label: '调拨出库' },      // 调拨出库

      ],
      rules: {
        outboundType: [{ required: true, message: '类型不能为空', trigger: ['blur', 'change'] }],
        sourceNo: [{ required: true, message: '源单据不能为空', trigger: ['blur', 'change'] }],
      },
      infoRules: {
        warehouseName: [{ required: true, message: '入库仓库不能为空', trigger: ['blur', 'change'] }],
        locationName:[{ required: true, message: '库位不能为空', trigger: ['blur', 'change'] }],
        yetQty:[{ required: true, message: '本次出库数量不能为空', trigger: ['blur', 'change'] }],
        currentQty: [{ required: true, message: '出库数量不能为空', trigger: ['blur', 'change'] }],
        unit: [{ required: true, message: '单位不能为空', trigger: ['blur', 'change'] }],
      },
      activeNames: ["base", "product", "more"], // 全部展开
      selectList: [],
      unitData: [],
      // 添加商品
      isReload: false,
    }
  },
  computed: {
    // 设置请求上传的头部
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    onInputSearch(index,type, $event) {
     if (type == "warehouse") {
        this.form.stockOutboundDetailList[index].warehouseName = $event.name;
        this.form.stockOutboundDetailList[index].warehouseId = $event.id;
      }
      if (type == "location") {
        this.form.stockOutboundDetailList[index].locationName = $event.name;
        this.form.stockOutboundDetailList[index].locationId = $event.id;
      }
      if (type == "partner") {
        this.form.counterpartyName = $event.name;
      }
    },
    //选择销售订单
    handleSelectSalesOrder(salesOrder) {
      this.form.sourceNo = salesOrder.salesNo
      this.form.sourceId = salesOrder.id
      this.form.counterpartyName = salesOrder.customerName
      this.form.counterpartyCode = salesOrder.customerNo
      this.form.stockOutboundDetailList = []; // 清空之前选择的商品
      // 获取销售订单下的商品信息
      let param = {
        id: salesOrder.id,
        outboundType : 'sales',
      }
      getSourceOrderInfo(param).then(res => {
        if (res.data.code === 100) {
          this.form.stockOutboundDetailList =res.data.data;
           console.log(res.data)
        }
      })
    },
    handleSelectTransferOrder(transferOrder) {
      this.form.sourceNo = transferOrder.transferNo
      this.form.sourceId = transferOrder.id
      this.form.stockOutboundDetailList = []; // 清空之前选择的商品
      // 获取销售订单下的商品信息
      let param = {
        id: transferOrder.id,
        outboundType : 'transfer',
      }
      getSourceOrderInfo(param).then(res => {
        if (res.data.code === 100) {
          this.form.stockOutboundDetailList =res.data.data;
          console.log(res.data)
        }
      })
    },
    // 计量单位
    getUnitData() {
      const unitTree = async()=>{
        var result = await getUnitListData();
        this.unitData = result;
      }
      unitTree();
    },
    addProduct() {
      // if (this.form.customerId === '' || this.form.customerName === ''){
      //   this.$DonMessage.warning("请先选择客户！");
      //   return
      // }
      this.isReload = true;
    },
    // 批量删除
    handleSelectChange(val) {
      this.selectList = val;
    },
    batchDelete() {
      getBatchDeleteInfo(this.form.stockOutboundDetailList, this.selectList, "删除商品信息")
    },
    // 删除
    delClick(index) {
      if (this.form.stockOutboundDetailList.length == 0) {
        this.$DonMessage.warning("商品信息不能为空")
        return
      }
      getDeleteInfo(this.form.stockOutboundDetailList, index, "删除商品信息");
    },
    // 取消
    removeClick() {
      removeTabs(this.$route);
    },
    // 提交
    onSubmit(){
      this.form.status = 4
      this.submit()
    },
    // 保存
    onSave(){
      this.form.status = 3
      this.submit()
    },
    // 提交并审核
    onSaveAndAudit(){
      this.form.status = 5
      this.submit()
    },
    submit(){
      var isStatus = true;
      if (this.form.outboundType == 'sales' || this.form.outboundType == 'transfer') {
        if (this.form.sourceNo == '') {
          this.$DonMessage.warning('请先选择源单据!');
          return
        }
      }
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
          isStatus = false
        }
      })
      if (this.form.stockOutboundDetailList.length > 0) {
        if (this.$refs["warehouseName"] != undefined) {
          this.$refs["warehouseName"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("入库仓库不能为空");
              isStatus = false;
            }
          })
        }
        if (this.$refs["locationName"] != undefined) {
          this.$refs["locationName"].validate(valid => {
            if (!valid) {
              this.$DonMessage.warning("库位不能为空");
              isStatus = false;
            }
          })
        }
        if(this.stateType != "addInfo") {
          if (this.$refs["quantity"] != undefined) {
            this.$refs["quantity"].validate(valid => {
              if (!valid) {
                this.$DonMessage.warning("出库数量不能为空");
                isStatus = false;
              }
            })
          }
          if (this.$refs["unit"] != undefined) {
            this.$refs["unit"].validate(valid => {
              if (!valid) {
                this.$DonMessage.warning("单位不能为空");
                isStatus = false;
              }
            })
          }
        }
        if (this.stateType == "addInfo") {
          if (this.$refs["currentQty"] != undefined) {
            this.$refs["currentQty"].validate(valid => {
              if (!valid) {
                this.$DonMessage.warning("本次出库库数量不能为空");
                isStatus = false;
              }
            })
          }
        }
        this.form.stockOutboundDetailList.forEach(item => {
          if (item.currentQty == undefined || item.currentQty <= 0 ) {
            this.$DonMessage.warning("本次出库库数量不能为空且要大于0");
            isStatus = false;
          }
          if (this.stateType == "addInfo") {
            if (item.currentQty > item.beQty) {
              this.$DonMessage.warning("本次出库数量不能大于待出库数量");
              isStatus = false;
            }
          }
        })
      } else {
        this.$DonMessage.warning('商品信息不能为空');
        isStatus = false
      }
      if (isStatus == false) {
        return
      }
      this.form.productAccessoryList = this.fileList;
      var params = {
        id:this.form.id,
        sourceId:this.form.sourceId,
        sourceNo:this.form.sourceNo,
        outboundTime:this.form.outboundTime,
        status:this.form.status,
        quantity:this.form.quantity,
        outboundType:this.form.outboundType,
        counterpartyName:this.form.counterpartyName,
        counterpartyCode:this.form.counterpartyCode,
        counterpartyId: this.form.counterpartyId,
        remark:this.form.remark,
        stockOutboundDetailList: this.form.stockOutboundDetailList,
        productAccessoryList: this.form.productAccessoryList
      }
      if(this.stateType === "edit") {
        editOutbound(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      } else {
        addOutbound(params).then((res) => {
          if (res.data.code == '100') {
            this.$DonMessage.success(this.$t('successTip.submitTip'));
            this.removeClick();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        });

      }
    },
    formClear() {
      this.form = {
        id: "",
        sourceNo:'',
        outboundType:'',
        outboundNo:'',
        counterpartyName:'',
        counterpartyCode:'',
        customerNo:'',
        warehouseName:'',
        warehouseId:'',
        locationId:'',
        locationName:'',
        createdUser: "",
        createdUserName: "",
        createdTime: "",
        outboundTime: "",
        updatedUser: "",
        updatedTime: "",
        stockOutboundDetailList: [],
        productAccessoryList: [],
      }
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      });
    },
    // 获取订单信息
    async salesOrderInfo() {
      try {
        const res = await outboundInfo(this.id)
        if (res.data.code == "100") {
          this.form = Object.assign({}, res.data.data);
          if (this.stateType == 'addInfo'){
            this.form.outboundTime =  this.$options.filters.conversion(new Date().getTime(), "yyyy-MM-dd");
          }else {
            this.form.outboundTime = this.$options.filters.conversion(this.form.outboundTime ,"yyyy-MM-dd");
          }

          this.form.createdUser = this.$store.state.realName;
          this.form.updatedUser = this.$store.state.realName;
          this.form.updatedTime = new Date().getTime();
          if (this.form.productAccessoryList != null && this.form.productAccessoryList.length > 0) {
            this.form.productAccessoryList.forEach((item) => {
              item.name = item.fileName
            })
            this.fileList = this.form.productAccessoryList;
          }
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.removeClick()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.removeClick()
      }
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        if (this.form.stockOutboundDetailList) {
          switch (column.property) {
            case "quantity":
              this.form.stockOutboundDetailList.forEach((item) => {
                if (item.quantity) {
                  count += item.quantity;
                }
              });
              sums[index] = count;
              break;
            case "beQty":
              this.form.stockOutboundDetailList.forEach((item) => {
                if (item.beQty) {
                  count += item.beQty;
                }
              });
              sums[index] = count;
              break;
            case "currentQty":
              this.form.stockOutboundDetailList.forEach((item) => {
                if (item.currentQty) {
                  count += item.currentQty;
                }
              });
              sums[index] = count;
              break;
          }
        }
      })
      return sums;
    },
    async initialState() {
      var type = this.$route.params.type;
      this.id = this.$route.params.id;
      if (type == "add") {
        if (this.id == "add") {
          this.stateType = "add"
          await this.formClear();
          this.form.outboundTime =  this.$options.filters.conversion(new Date().getTime(), "yyyy-MM-dd");
          this.disabledStatue = false
        } else {
          this.rules = {};
          this.stateType = "addInfo"
          await this.salesOrderInfo();
          this.disabledStatue = true
        }
      } else {
        this.stateType = "edit"
        await this.salesOrderInfo();
        this.disabledStatue = true
      }
      getContentData(this)
      collapseArea()
      this.getUnitData()
    },
    // 附件上传
    handleOnSuccess(res, obj) {
      this.fileList = []
      var file = {
        fileName: res.data.fileName,
        name: res.data.fileName,
        path: res.data.fileUrl,
      }
      this.fileList.push(file)
      this.count = 1
    },
    // 文件移除前的钩子
    beforeOnRemove() {
      if (this.fileList.length >　0) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    // 文件移除时的钩子
    handleOnRemove() {
      this.fileList = []
      this.count = 0
    },
    beforeAvatarUpload(file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const isLt2M = file.size / 1024 / 1024 < 100
      var suffix = [
        'jpg',
        'png',
        'mp4',
        'mp3',
        'xls',
        'xlsx',
        'doc',
        'docx',
        'zip',
      ];
      if (!suffix.includes(fileName)) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip' }));
        return false;
      }
      if (!isLt2M) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }))
        return false;
      }
    },
    // 超过文件数量限制时的钩子
    handleOnExceed() {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 1}))
      return
    },
  },
  mounted() {
    this.initialState();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name == "addOutStock") {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  }
}
</script>
