<template>
  <div class="layoutContainer">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="待出库单" name="beOutStock">
        <div class="secondFloat">
          <el-form :inline="true" :model="queryParams" :label-width="$labelFour">
            <el-form-item label="单据日期">
              <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
            </el-form-item>

            <el-form-item label="源单据号" prop="salesNo">
              <el-input
                v-model="queryParams.sourceNo"
                placeholder="请输入销售订单号"
                clearable
              />
            </el-form-item>

            <el-form-item label="往来单位" prop="counterpartyName" >
              <selectInput
                ref="selectInput"
                v-model="queryParams.counterpartyName"
                :inputParam="queryParams.counterpartyName"
                inputType="partnerCustomerInfo"
                placeholder="请选择往来单位"
                @select="onInputSearch('partner', $event)"
              ></selectInput>
            </el-form-item>

            <el-form-item label="商品" prop="productName">
              <selectInput
                ref="selectInput"
                v-model="queryParams.productName"
                :inputParam="queryParams.productName"
                inputType="productInfo"
                placeholder="请选择商品"
                @select="onInputSearch('product', $event)"
              ></selectInput>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item>
              <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
              <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
<!--              <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i-->
<!--                class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--              <el-button type="text" @click="isShow = false" v-if="isShow">收起<i-->
<!--                class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">

          <el-table style="width:100%"
                    border
                    stripe
                    ref="table"
                    highlight-current-row
                    :max-height="maximumHeight"
                    :data="resultList"
                    @header-dragend="changeColWidth"
                    @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" fixed="left" align="center"></el-table-column>
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>

            <el-table-column prop="createdTime" label="单据日期" width="150">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
              </template>
            </el-table-column>
            <el-table-column prop="sourceNo" label="源单据号" min-width="100">
              <template #default="{row}">
                <span class="linkStyle" @click="handleSourceNoDetail(row)">{{ row.sourceNo }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" min-width="80">
              <template slot-scope="scope">
                <div :class="getStatusClass(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="outboundType" label="类型" min-width="100">
              <template slot-scope="scope">
                <div >
                  {{ getoutboundTypeText(scope.row.outboundType) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="counterpartyName" label="往来单位" min-width="120"/>
            <el-table-column prop="quantity" label="数量" width="150"/>


            <el-table-column prop="createdUserName" label="制单人" width="100"/>
            <el-table-column prop="createdTime" label="制单时间" width="150">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd hh:mm:ss') }}
              </template>
            </el-table-column>
            <el-table-column prop="counterpartyCode" label="客户订单号" width="150"/>
            <el-table-column prop="remark" label="备注"/>
            <el-table-column label="操作" fixed="right" width="130">
              <template slot-scope="{row}">
                <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
                <el-button type="text" size="small" v-if="row.status != 2" @click="handleEdit(row)">出库</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 底部分页组件 -->
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                      @pagination="handleSearch"/>
        </div>
      </el-tab-pane>

      <el-tab-pane label="出库单" name="outStock">
        <div class="secondFloat">
          <el-form :inline="true" :model="queryParams" :label-width="$labelFour">
            <el-form-item label="单据日期">
              <DateRangeSelector ref="dateOutRangeSelector" @change="handleDateRangeSelect"/>
            </el-form-item>
            <el-form-item label="往来单位" prop="counterpartyName" >
              <selectInput
                ref="selectInput"
                v-model="queryParams.counterpartyName"
                :inputParam="queryParams.counterpartyName"
                inputType="partnerCustomerInfo"
                placeholder="请选择往来单位"
                @select="onInputSearch('partner', $event)"
              ></selectInput>
            </el-form-item>

            <el-form-item label="商品" prop="productName">
              <selectInput
                ref="selectInput"
                v-model="queryParams.productName"
                :inputParam="queryParams.productName"
                inputType="productInfo"
                placeholder="请选择商品"
                @select="onInputSearch('product', $event)"
              ></selectInput>
            </el-form-item>
            <!-- 状态 -->
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
              >
                <el-option v-for="item in auditOutStockStatus" :value="item.code" :key="item.code" :label="item.name"></el-option>
              </el-select>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item>
              <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
              <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
<!--              <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i-->
<!--                class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
<!--              <el-button type="text" @click="isShow = false" v-if="isShow">收起<i-->
<!--                class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
              <el-button type="text" icon="bulkImport-icon" @click="batchImport()">批量导入</el-button>
              <el-dropdown>
            <span>
              <i class="process-icon"></i>
              审核
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
                <el-dropdown-menu slot="dropdown">
                  <div>
                    <el-dropdown-item @click.native ="auditClick('reviewPass')">审核通过</el-dropdown-item>
                    <el-dropdown-item @click.native ="auditClick('reviewReject')">审核驳回</el-dropdown-item>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>

              <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
              <el-button type="text" icon="deleteRed-icon" @click="delClick()">删除</el-button>
            </div>
            <!--        <div>-->
            <!--          <el-button type="text" icon="el-icon-setting">自定义列</el-button>-->
            <!--          <el-button type="text" icon="el-icon-refresh">刷新</el-button>-->
            <!--        </div>-->
          </div>
          <el-table style="width:100%"
                    border
                    stripe
                    ref="table"
                    highlight-current-row
                    :max-height="maximumHeight"
                    :data="resultList"
                    @header-dragend="changeColWidth"
                    @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" fixed="left" align="center"></el-table-column>
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column prop="createdTime" label="单据日期" width="150">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
              </template>
            </el-table-column>
<!--            <el-table-column prop="outboundNo" label="出库单号" width="150"/>-->
            <el-table-column prop="outboundNo" label="出库单号" min-width="100">
              <template #default="{row}">
                <span class="linkStyle" @click="outStockDetail(row)">{{ row.outboundNo }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" min-width="80">
              <template slot-scope="scope">
                <div :class="getStatusClass(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="sourceNo" label="源单据号" min-width="100">
              <template #default="{row}">
                <span class="linkStyle" @click="handleSourceNoDetail(row)">{{ row.sourceNo }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="outboundType" label="类型" width="140">
              <template slot-scope="scope">
                <div >
                  {{ getoutboundTypeText(scope.row.outboundType) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="counterpartyName" label="往来单位" width="150"/>
            <el-table-column prop="quantity" label="数量" width="150"/>


            <el-table-column prop="createdUserName" label="制单人" width="100"/>
            <el-table-column prop="createdTime" label="制单时间" width="150">
              <template slot-scope="scope">
                {{ conversion(scope.row.createdTime, 'yyyy-MM-dd hh:mm:ss') }}
              </template>
            </el-table-column>
            <el-table-column prop="counterpartyCode" label="客户订单号" width="150"/>
            <el-table-column prop="remark" label="备注"/>
            <el-table-column label="操作" fixed="right" width="130">
              <template slot-scope="{row}">
                <el-button type="text" size="small" @click="outStockDetail(row)">详情</el-button>
                <el-button type="text" size="small" v-if="row.status == 3 || row.status == 6" @click="editOutStock(row)">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 底部分页组件 -->
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"  @pagination="handleSearch"/>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="reviewRejectDialogVisible" v-if="reviewRejectDialogVisible">
      <el-form :model="reason"  ref="reason" >
        <el-form-item label="驳回理由" prop="reason">
          <el-input v-model="reason" />
        </el-form-item>
        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleAuditReject">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="reviewRejectDialogVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {salesList, salesOrderDel, salesOrderDiscard, salesOrderPass, salesOrderReject} from "@/api/salesmgt";
import {addTabs, tableHeight} from "@/assets/js/common";
import {dictTypeQueryData} from "@/api/sysmgt";
import {commentDel} from "@/api/material";
import {auditOutbound, auditRejectOutbound, beOutStockList, delOutbound} from "@/api/stockmgt";
import selectInput from "@/components/selectInput/selectInput.vue";

export default {
  name: "list",
  components: {selectInput, DateRangeSelector, Pagination},
  data() {
    return {
      activeTab:'beOutStock',
      customerDialogVisible:false,
      reason:'',
      reviewRejectDialogVisible: false,
      auditStatusDicts: [],
      auditBeOutStockStatus: [
        {code:0,name:'未出库'},
        {code:1,name:'部分出库'},
        {code:2,name:'已出库'}
      ],
      auditOutStockStatus: [
        {code:3,name:'草稿'},
        {code:4,name:'待审核'},
        {code:5,name:'已审核'},
        {code:6,name:'审核驳回'}
      ],
      selectedRows: [],
      deleteList: [],
      maximumHeight: 0,
      total: 0,
      dialogStatus: '',
      textMap: {
        reviewReject: "审核驳回"
      },
      isShow: false,
      resultList: [],
      queryParams: {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        salesNo: '',
        sourceNo: '',
        salesTime: '',
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10,
        counterpartyName: '',
        customerId: '',
        productName: '',
        productId: ''
      },
    }
  },
  methods: {
    handleClick(tab, event) {
      this.activeTab = tab.name;
      this.handleSearch()
    },
    getStatusClass(status) {
      switch(status) {
        case 0:
          return 'errorColor'; // 未生成 - 红色或其他错误颜色
        case 1:
          return 'warningColor'; // 部分生成 - 黄色或其他警告颜色
        case 2:
          return 'successColor'; // 已生成 - 绿色或其他成功颜色
        case 3:
          return ''; // 草稿 - 红色或其他错误颜色
        case 4:
          return 'warningColor'; // 待审核 - 黄色或其他警告颜色
        case 5:
          return 'successColor'; // 审核通过 - 绿色或其他成功颜色
        case 6:
          return 'errorColor'; // 审核驳回 - 红色或其他错误颜色
        default:
          return 'errorColor'; // 默认情况，可根据需要调整
      }
    },
    getStatusText(status) {
      switch(status) {
        case 0:
          return '未出库';
        case 1:
          return '部分出库';
        case 2:
          return '已出库';
        case 3:
          return '草稿';
        case 4:
          return '待审核';
        case 5:
          return '已审核';
        case 6:
          return '审核驳回';
        default:
          return '未知状态'; // 默认情况，可根据需要调整
      }
    },
    getoutboundTypeText(status) {
      switch(status) {
        case 'sales':
          return '销售出库';
        case 'transfer':
          return '调拨出库';
        case 'issue':
          return '领料出库';
        case 'other':
          return '其他出库';
        default:
          return '未知出库'; // 默认情况，可根据需要调整
      }
    },
    onInputSearch(type, $event) {
      if (type == "partner") {
        this.queryParams.counterpartyName = $event.name;
      }
      if (type == "product") {
        this.queryParams.productName = $event.name;
        this.queryParams.productId = $event.id;
      }
    },

    //转换状态显示
    formatAuditStatus(status){
      const match = this.auditStatusDicts.find(item => item.code == status);
      return match ? match.name : '';
    },
    //新增
    addClick() {
      const title = "新增出库单";
      this.$router.push({ name: 'addOutStock', params: {id: 'add', type:'add'} });
      addTabs(this.$route.path, title);
    },
    //批量导入
    batchImport() {
    },
    handleAuditReject(){
      let ids = ""
      this.selectedRows.forEach(item => {
        if (item.status != 4) {
          this.$DonMessage.warning("只能审核待审核的数据！");
          return false;
        }else {
          ids += item.id + ","
        }
      })
      var params = new URLSearchParams()
      params.append('ids', ids)
      params.append('reason', this.reason)
      auditRejectOutbound(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success("已审核驳回")
          this.reviewRejectDialogVisible = false;
          const params = {
            activeTab : 'outStock'
          }
          this.dataList(params)
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },
    //批量作废
    abolishClick() {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要作废的数据");
        return false;
      }
      this.$confirm('确定作废【' + this.selectedRows.length + '】条数据吗?', '作废订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        salesOrderDiscard(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },
    //审核
    auditClick(type) {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要审核的数据");
        return false;
      }
      this.reason='';
      if(type == 'reviewReject'){
        this.reviewRejectDialogVisible = true;
        return;
      }
      this.$confirm('确定审核【' + this.selectedRows.length + '】条数据吗?', '审核订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          if (item.status != 4) {
            this.$DonMessage.warning("只能审核待审核的数据！");
            return false;
          }else {
            ids += item.id + ","
          }
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        if (type == 'reviewPass'){
          auditOutbound(params).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(this.$t('successTip.submitTip'))
              const params = {
                activeTab : 'outStock'
              }
              this.dataList(params)
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          })
        }
      })
    },
    //批量删除
    delClick() {
      if (this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请选择需要删除的数据");
        return false;
      }
      this.$confirm('确定删除【' + this.selectedRows.length + '】条数据吗?', '删除出库单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.selectedRows.forEach(item => {
          if (item.status != 3 && item.status != 6) {
            this.$DonMessage.warning("只能删除草稿或审核驳回数据！");
            return false;
          }else {
            ids += item.id + ","
          }
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        delOutbound(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.activeTab = 'outStock';
            const params = {
              activeTab : 'outStock'
            }
            this.dataList(params)
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },
    //导出
    exportClick() {

    },
    //待出库单详情
    handleDetail(row) {
      var title = row.sourceNo;
      this.$router.push({ name: 'beOutStockDetail', params: { id: row.id }});
      addTabs(this.$route.path, title);
    },

    handleSourceNoDetail(row) {
      if (row.outboundType == 'sales') {
        var title = row.sourceNo;
        this.$router.push({ name: 'salesOrderDetail', params: { id: row.sourceId }});
        addTabs(this.$route.path, title);
      } else if (row.outboundType == 'transfer') {
        var title = row.sourceNo;
        this.$router.push({ name: 'transferDetail', params: { id: row.sourceId }});
        addTabs(this.$route.path, title);
      } else if (row.outboundType == 'issue') {
        var title = row.sourceNo;
        this.$router.push({ name: 'IssueDetail', params: { id: row.sourceId }});
        addTabs(this.$route.path, title);
      }
    },
    // 出库单详情
    outStockDetail(row) {
      var title = row.outboundNo;
      this.$router.push({ name: 'outStockDetail', params: { id: row.id }});
      addTabs(this.$route.path, title);
    },
    //出库
    handleEdit(row) {
      var title = "新增出库单";
      this.$router.push({ name: 'addOutStock', params: {id: row.id, type:'add'} });
      addTabs(this.$route.path, title);
    },
    // 编辑出库单
    editOutStock(row) {
      var title = "编辑出库单";
      this.$router.push({ name: 'addOutStock', params: {id: row.id, type:'edit'} });
      addTabs(this.$route.path, title);
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //时间格式化方法
    conversion,
    //查询
    handleSearch() {
      // const params = {
      //   status: this.queryParams.status,
      //   startDate: this.queryParams.startDate,
      //   endDate: this.queryParams.endDate,
      //   productId: this.queryParams.productId,
      //   sourceNo: this.queryParams.sourceNo,
      //   counterpartyName : this.queryParams.counterpartyName,
      //   activeTab : this.activeTab,
      //   page: this.queryParams.page,
      //   limit: this.queryParams.pageSize
      // }
      const params = new URLSearchParams();
      params.append('status', this.queryParams.status);
      params.append('startDate', this.queryParams.startDate);
      params.append('endDate', this.queryParams.endDate);
      params.append('productId', this.queryParams.productId);
      params.append('sourceNo', this.queryParams.sourceNo);
      params.append('counterpartyName', this.queryParams.counterpartyName);
      params.append('activeTab', this.activeTab);
      params.append('page', this.queryParams.page);
      params.append('limit', this.queryParams.pageSize);

      // 发起请求查询接口
      this.dataList(params)
    },
    //  处理选中的行
    handleSelectionChange(val) {
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },
    //重置查询条件
    handleReset() {
      this.queryParams = {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        salesNo: '',
        sourceNo: '',
        salesTime: '',
        salesOrderNo: '',
        status: '',
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10,
        counterpartyName: '',
        customerId: '',
        productName: '',
        productId: ''
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.queryParams.productId = '';
      //重置时间选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      if (this.$refs.dateOutRangeSelector){
        this.$refs.dateOutRangeSelector.reset()
      }
      this.handleSearch()
    },
    //分页查询数据
    dataList(params) {
      beOutStockList(params).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 获取日期范围选择
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      this.handleSearch();
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    this.handleSearch();
    //获取审核状态数据字典
    this.getAuditStatusDict();
  },
  created() {
    console.log('Component created');
  },
}
</script>

<style scoped>
.warningColor {
  color: #fea927;
}
</style>
