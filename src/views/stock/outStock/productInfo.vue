<template>
  <div class="supplierContainer">
    <div class="dialogSearch">
      <el-form :inline="true" :label-width="$labelTwo" class="demo-form-inline">
        <el-form-item label="编码" prop="searchCode">
          <el-input v-model.trim="searchCode" placeholder="请输入编码"></el-input>
        </el-form-item>
        <el-form-item label="名称" prop="searchName">
          <el-input v-model.trim="searchName" placeholder="请输入名称"></el-input>
        </el-form-item>
        <!-- <el-input placeholder="输入编码/名称搜索"></el-input> -->
        <el-checkbox v-model="enableState">仅显示启用</el-checkbox>
        <el-button type="primary" @click="searchClick()">{{ $t('button.search') }}</el-button>
        <el-button plain @click="resetClick()">{{ $t('button.reset') }}</el-button>
      </el-form>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="4" class="leftData">
          <div>
            <div class="topButton">
              <span>商品类别</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <el-scrollbar>
                <el-tree :data="productTreeList" node-key="id" :render-content="renderContent"
                  :default-expand-all="true" @node-click="handleCategorySelect" :props="{
                    label: 'name',
                    children: 'children'
                  }" ref="categoryTree"></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="20">
          <el-row>
            <el-col :span="18" class="nationalInfo">
              <div>
                <el-table
                  :data="productList"
                  :height="tableHeight"
                  style="width: 100%;"
                  ref="table"
                  highlight-current-row
                  border
                  stripe
                  row-key="id"
                  reserve-selection
                  @header-dragend="changeColWidth"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
                  <el-table-column label="序号" width="50" type="index"></el-table-column>
                  <el-table-column label="商品编码" prop="code" min-width="120" sortable></el-table-column>
                  <el-table-column label="商品名称" prop="name" min-width="120"></el-table-column>
                  <el-table-column label="图片" prop="imageUrl" width="60">
                    <template slot-scope="{row}">
                      <img class="pictureShow" :src="filePath + row.imageUrl" alt="">
                    </template>
                  </el-table-column>
                  <el-table-column label="类别" prop="categoryName" width="100" sortable></el-table-column>
                  <el-table-column label="条形码" prop="barCode" min-width="100"></el-table-column>
                  <el-table-column label="品牌" prop="brand" width="100"></el-table-column>
                  <el-table-column label="规格型号" prop="model" width="100"></el-table-column>
                  <el-table-column label="计量单位" prop="unit" width="80"></el-table-column>
                  <el-table-column label="状态" width="60" prop="status">
                    <template slot-scope="{row}">
                      <span class="successColor" v-if="row.status === 1">启用</span>
                      <span class="errorColor" v-if="row.status === 0">禁用</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="默认仓库" prop="warehouseName" width="110"></el-table-column>
                  <el-table-column label="默认库位" prop="locationName" width="110"></el-table-column>
                  <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
                </el-table>
                <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
              </div>
            </el-col>
            <el-col :span="6" class="nationalSelect">
              <el-table
                style="width:100%"
                :height="maxHeight"
                border
                stripe
                highlight-current-row
                :data="determineModelList"
                ref="applytable"
                :header-cell-style="{}"
              >
            <el-table-column prop="title">
              <template slot="header">
                <div class="authorityTitle">
                  <div>
                    <span>已选(<b> {{ selectNum }} </b>)</span>
                  </div>
                  <div>
                    <span class="deleteRed-icon clearAction" @click="emptyCountry">清空</span>
                  </div>
                </div>
              </template>
              <template slot-scope="scope">
                <div :class="'nationalList ' + '_' + scope.row.id">
                  <span>{{ scope.row.code }} {{  scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="submitArea">
      <div style="float: left;">
        <el-button plain @click="productAdd">新增</el-button>
      </div>
      <el-button type="primary" @click="productSubmit()">{{ $t('button.submit') }}</el-button>
      <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import selectInput from "@/components/selectInput/selectInput.vue";
import customerInfo from "@/views/sales/salesOrder/customerInfo.vue";
import {getProductAdd, getProductEdit, getProductInfo, getCategoryTree, getProductList, getSupplierList, getSalesCompanyList} from "@/api/basicmgt";
import {findUserPage, findUserPageTenant, userDataList} from "@/api/sysmgt";
import {sysServerUrl,addTabs, removeTabs, renderTree, tableHeight, contextmenuSeat} from "@/assets/js/common";
import {getSalesOrderAdd, getSalesOrderEdit, getSalesOrderInfo} from "@/api/salesmgt";
import { productCategoryTree, addProductInfo } from "@/views/basic/basicCommon"

export default {
  name: "outStockDetail",
  // eslint-disable-next-line vue/no-unused-components
  components: { selectInput, customerInfo, Pagination},
  data() {
    return {
      filePath:  sysServerUrl + 'sys/upload/display?filePath=',
      searchCode:"",
      searchName:"",
      selectedCategoryId: "",
      enableState: false,
      productTreeList: [],
      productList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      tableHeight:0,
      maxHeight:0,
      // 选中的对象
      determineModelList: [],
      // 选中的数量
      selectNum: 0,
      selectAllList: [],
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    renderContent(h, { node, data }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".commodityManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 搜索
    searchClick() {
      if (this.enableState == true) {
        this.searchStatus = 1;
      } else {
        this.searchStatus = ""
      }
      this.currentPage = 1;
      this.dataList();
    },
    // 重置
    resetClick() {
      this.searchCode = "";
      this.searchName = "";
      this.enableState = false;
      this.searchStatus = ""
      this.currentPage = 1;
      this.dataList();
    },
    // 商品列表
    getProductTree() {
      const treeData = async () => {
        var result = await productCategoryTree();
        this.productTreeList = result;
      }
      treeData();
    },
    // 树结构点击事件
    handleCategorySelect(row) {
      this.selectedCategoryId = row.id;
      this.currentPage = 1;
      this.dataList();
    },
    // 商品列表
    dataList() {
      const params = {
        code: this.searchCode,
        name: this.searchName,
        status:this.enableState ? 1 : '',
        categoryId: this.selectedCategoryId,
        page: this.currentPage,
        limit: this.pagesize,
      }
      getProductList(params).then((res) => {
        if (res.data.code == '100') {
          this.productList = res.data.data
          this.total = res.data.total;
          this.sizeArea()
          setTimeout(() => {
            this.selectAllList.forEach(row => {
              this.$refs.table.toggleRowSelection(row);
              const target = this.productList.find(item => item.id == row.id)
              if (target) {
                this.$refs.table.toggleRowSelection(target);
              }
            })
          });
        }
      });
    },
    // 表格多选
    handleSelectionChange(selection) {
      console.log(selection);
      this.selectAllList = this.determineModelList
      this.determineModelList = [...new Map([...this.selectAllList, ...selection].map(item => [item.id, item])).values()];
      this.selectNum = this.determineModelList.length
    },
    // 清空
    emptyCountry() {
      this.determineModelList = [];
      this.handleSelectionChange([])
      this.$refs.table.clearSelection();
      this.$refs.applytable.clearSelection();
    },
    // 新增
    productAdd() {
      addProductInfo(null)
    },
    // 提交
    productSubmit() {
      var list = this.$parent.$parent.form.stockOutboundDetailList;
      this.determineModelList.forEach(row => {
        row.productId = row.id;
        row.productCode = row.code;
        row.productName = row.name;
        row.fromWarehouseId = row.warehouseId;
        row.fromWarehouseId = row.locationId;
      })
      var newList = [...new Map([...list, ...this.determineModelList].map(item => [item.id, item])).values()];
      this.$parent.$parent.form.stockOutboundDetailList = newList;
      this.$parent.$parent.dialogFormVisible = false;
    },
    cancelClick() {
      this.$parent.$parent.dialogFormVisible = false;
    },
    heightArea() {
      var allHeight = $(".supplierContainer .infoDetail").height();
      var topHeight = $(".infoDetail .topButton").outerHeight(true);
      var leftVal = allHeight - topHeight;
      $(".supplierContainer .infoDetail .scrollClass").css("height", leftVal);
      var pageHeight = $(".nationalInfo .pagination-container").outerHeight(true);
      this.tableHeight = allHeight - pageHeight;
      this.maxHeight = allHeight;
    },
    sizeArea() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function() {
        _this.heightArea();
      })
    },
  },
  mounted() {
    this.getProductTree();
    this.dataList()
  },
}
</script>
<style>
.supplierContainer .dialogSearch .el-input {
  width: 350px !important;
  margin-right: 10px;
}
.supplierContainer .dialogSearch .el-form .el-input {
  width: 200px !important;
}
.dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

/* .dialogSearch .el-input {
  width: 200px !important;
  margin-right: 10px;
} */

.dialogSearch .el-input .el-input__inner {
  width: 100% !important;
}
.dialogSearch .el-checkbox {
  margin-right: 10px !important;
}
/* .el-dialog */
.supplierContainer .infoDetail {
  height: 550px;
}
.supplierContainer .infoDetail .scrollClass {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
.supplierContainer .pagination-container {
  border-bottom: 1px solid var(--table-border);
  border-left: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border);
}
</style>
