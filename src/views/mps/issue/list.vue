<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" label-width="100px">
        <!-- 单据编号 -->
        <el-form-item label="单据编号" prop="issueNo">
          <el-input
            v-model="queryParams.issueNo"
            placeholder="请输入单据编号"
            clearable
          />
        </el-form-item>
        <!-- 领料时间 -->
        <el-form-item label="领料时间">
          <DateRangeSelector ref="dateRangeSelector" @change="handleIssueDateRange"/>
        </el-form-item>
        <!-- 领料仓库 -->
        <el-form-item label="领料仓库" prop="warehouseName">
          <el-input
            v-model="queryParams.warehouseName"
            placeholder="请输入领料仓库"
            clearable
          />
        </el-form-item>
        <!-- 物料编码 -->
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
          />
        </el-form-item>
        <!-- 物料名称 -->
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
          />
        </el-form-item>
        <!-- 审核状态 -->
        <el-form-item label="审核状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择审核状态"
            clearable
          >
            <el-option v-for="item in statusOptions" :value="item" :key="item" :label="item"></el-option>
          </el-select>
        </el-form-item>

        <!-- 更多查询条件 -->
        <template v-if="isShow">
          <el-form-item label="单据日期">
            <DateRangeSelector ref="dateDocumentSelector" @change="handleDocumentDateRange"/>
          </el-form-item>
          <el-form-item label="单据来源" prop="documentSource">
            <el-input
              v-model="queryParams.documentSource"
              placeholder="请输入单据来源"
              clearable
            />
          </el-form-item>
          <el-form-item label="领料人" prop="issueUserName">
            <el-input
              v-model="queryParams.issueUserName"
              placeholder="请输入领料人"
              clearable
            />
          </el-form-item>
        </template>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i
            class="el-icon-arrow-up el-icon--right"></i></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
          <el-button type="text" icon="bulkImport-icon" @click="batchImport()">批量导入</el-button>
          <el-dropdown @command="auditClick">
            <span>
              <i class="process-icon"></i>
              审核
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
                <el-dropdown-item command="audit">审核通过</el-dropdown-item>
<!--                <el-dropdown-item command="revert">反审核</el-dropdown-item>-->
                <el-dropdown-item command="reject">审核驳回</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
          <el-button type="text" icon="disable-icon" @click="auditClick('abrogate')">作废</el-button>
          <el-button type="text" icon="deleteRed-icon" @click="handleBatchDelete()">删除</el-button>
<!--          <el-button type="text" icon="el-icon-refresh" @click="handleReturn()">退料</el-button>-->
        </div>
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange"
                @sort-change="handleSortChange"
      >
        <el-table-column type="selection" min-width="40" align="center"></el-table-column>
        <el-table-column label="序号" type="index" min-width="60" align="center"></el-table-column>
        <!-- 领料时间 -->
        <el-table-column prop="issueTime" label="单据日期" min-width="120" sortable="custom">
        </el-table-column>
        <!-- 单据编号 -->
        <el-table-column prop="issueNo" label="领料单号" min-width="150" sortable="custom">
          <template #default="{row}">
            <span class="linkStyle" @click="handleDetail(row)">{{ row.issueNo }}</span>
          </template>
        </el-table-column>
        <!-- 审核状态 -->
        <el-table-column prop="status" label="状态" min-width="100" sortable="custom">
          <template slot-scope="scope">
            <div :class="getStatusClass(scope.row.status)">
              {{ scope.row.status }}
            </div>
          </template>
        </el-table-column>
        <!-- 领料人 -->
        <el-table-column prop="issueUserName" label="领料人" min-width="100"/>
        <!-- 源单编号 -->
        <el-table-column prop="sourceNo" label="源单编号" min-width="150" sortable="custom">
          <template #default="{row}">
            <span class="linkStyle" @click="handleSourceDetail(row)">{{ row.sourceNo }}</span>
          </template>
        </el-table-column>


        <!-- 出库状态 -->
        <el-table-column prop="outboundStatus" label="出库状态" min-width="100">
          <template slot-scope="scope">
            <div :class="getStatusClass(scope.row.outboundStatus)">
              <!-- 部分出库状态添加链接 -->
              <span
                v-if="scope.row.outboundStatus === '部分出库'"
                class="linkStyle"
                @click="handleDetail(scope.row)"
              >
                {{ scope.row.outboundStatus }}
              </span>
              <!-- 其他状态正常显示 -->
              <span v-else>
                {{ scope.row.outboundStatus }}
              </span>
            </div>
          </template>
        </el-table-column>
        <!-- 审核人 -->
        <el-table-column prop="auditUserName" label="审核人" min-width="100"/>
        <!-- 审核时间 -->
        <el-table-column prop="auditTime" label="审核时间" min-width="140" sortable="custom">

        </el-table-column>
        <!-- 制单人 -->
        <el-table-column prop="createdUserName" label="制单人" min-width="100"/>
        <!-- 制单时间 -->
        <el-table-column prop="createdTime" label="制单时间" min-width="140" sortable="custom">

        </el-table-column>
        <!-- 备注 -->
        <el-table-column prop="remark" label="备注" min-width="150"/>
        <!-- 操作 -->
        <el-table-column label="操作" fixed="right" min-width="200">
          <template slot-scope="{row}">
            <div class="operation-container">
              <!-- 编辑按钮 - 草稿和驳回状态都可以编辑 -->
              <el-button
                v-if="row.status === '草稿' || row.status === '审核驳回'"
                type="text"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>

              <!-- 详情按钮 - 所有状态都显示 -->
              <el-button
                type="text"
                size="small"
                @click="handleDetail(row)"
              >
                详情
              </el-button>

              <!-- 退料按钮 - 只有已审核状态显示 -->
<!--              <el-button
                v-if="row.status === '已审核'"
                type="text"
                size="small"
                @click="handleReturn(row)"
              >
                退料
              </el-button>-->

              <!-- 删除按钮 - 只有作废状态显示 -->
              <el-button
                v-if="row.status === '作废'"
                type="text"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>

    </div>

    <!-- 审核驳回组件 -->
    <AuditRejectDialog
      :visible.sync="rejectDialogVisible"
      :document-id="rejectDocumentId"
      :document-no="rejectDocumentNo"
      document-label="领料单号"
      title="审核驳回"
      file-flag="issue"
      :reject-api="handleRejectIssue"
      @success="handleSearch"
      @close="handleSearch"
    />
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import AuditRejectDialog from "@/components/AuditRejectDialog/index.vue";
import {
  getIssueList,
  deleteIssue,
  batchDeleteIssue,
  auditIssue,
  revertIssue,
  rejectIssue,
  abrogateIssue,
  exportIssue,
  materialReturn
} from "@/api/mpsmgt";
import {addTabs, tableHeight} from "@/assets/js/common";

export default {
  name: "MaterialIssueList",
  components: {DateRangeSelector, Pagination, AuditRejectDialog},
  data() {
    return {
      // 审核驳回对话框相关
      rejectDialogVisible: false,
      rejectDocumentId: '',
      rejectDocumentNo: '',
      selectedRows: [],
      maximumHeight: 0,
      total: 0,
      isShow: false,
      resultList: [],
      queryParams: {
        issueNo: null,
        issueDateStart: null,
        issueDateEnd: null,
        warehouseId: null,
        warehouseName: null,
        materialCode: null,
        materialName: null,
        status: null,
        // 更多查询条件
        documentDateStart: null,
        documentDateEnd: null,
        documentSource: null,
        issueUserId: null,
        issueUserName: null,
        page: 1,
        pageSize: 10,
        // 排序参数
        sortField: null,
        asc: false
      },
      statusOptions: [
        '草稿',
        '待审核',
        '已审核',
        '审核驳回',
        '作废'
      ]
    }
  },
  async mounted() {
    this.maximumHeight = await tableHeight();
    this.handleSearch();
  },
  methods: {
    conversion,

    /**
     * 处理表格排序变化
     * @param {Object} column - 列信息
     * @param {String} prop - 列属性名
     * @param {String} order - 排序方向 'ascending' | 'descending' | null
     */
    handleSortChange({ column, prop, order }) {
      console.log('排序变化:', { column, prop, order });

      if (order) {
        // 设置排序字段和方向
        this.queryParams.sortField = prop;
        this.queryParams.asc = order === 'ascending';
      } else {
        // 清除排序
        this.queryParams.sortField = null;
        this.queryParams.asc = false;
      }

      // 重新查询数据
      this.handleSearch();
    },

    /**
     * 获取状态样式类名
     */
    getStatusClass(status) {
      switch (status) {
        case '草稿':
          return '';
        case '待审核':
        case '未出库':
          return 'errorColor';
        case '已审核':
        case '已出库':
          return 'successColor';
        case '审核驳回':
          return 'abnormalColor';
        case '作废':
          return 'abrogateColor';
        default:
          return '';
      }
    },



    /**
     * 领料时间范围选择
     */
    handleIssueDateRange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.queryParams.issueDateStart = dateRange[0];
        this.queryParams.issueDateEnd = dateRange[1];
      } else {
        this.queryParams.issueDateStart = null;
        this.queryParams.issueDateEnd = null;
      }
    },

    /**
     * 单据日期范围选择
     */
    handleDocumentDateRange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.queryParams.documentDateStart = dateRange[0];
        this.queryParams.documentDateEnd = dateRange[1];
      } else {
        this.queryParams.documentDateStart = null;
        this.queryParams.documentDateEnd = null;
      }
    },

    /**
     * 搜索
     */
    handleSearch() {
      // 构建符合PageQuery<IssueQueryDTO>结构的请求参数
      const requestParams = {
        current: this.queryParams.page,
        size: this.queryParams.pageSize,
        query: {
          issueNo: this.queryParams.issueNo,
          issueTimeRange: this.queryParams.issueDateStart && this.queryParams.issueDateEnd ? {
            startDate: this.queryParams.issueDateStart,
            endDate: this.queryParams.issueDateEnd
          } : null,
          issueDateRange: this.queryParams.documentDateStart && this.queryParams.documentDateEnd ? {
            startDate: this.queryParams.documentDateStart,
            endDate: this.queryParams.documentDateEnd
          } : null,
          warehouseId: this.queryParams.warehouseId,
          materialCode: this.queryParams.materialCode,
          materialName: this.queryParams.materialName,
          status: this.queryParams.status,
          sourceNo: this.queryParams.documentSource,
          issueUser: this.queryParams.issueUserId
        },
        // 排序参数
        sortField: this.queryParams.sortField,
        asc: this.queryParams.asc
      };

      getIssueList(requestParams).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data || [];
          this.total = res.data.total || 0;
        } else {
          this.$DonMessage.error(res.data.msg || '获取数据失败');
        }
      }).catch(error => {
        this.$DonMessage.error('获取数据失败：' + error.message);
      });
    },

    /**
     * 重置
     */
    handleReset() {
      this.queryParams = {
        issueNo: null,
        issueDateStart: null,
        issueDateEnd: null,
        warehouseId: null,
        warehouseName: null,
        materialCode: null,
        materialName: null,
        status: null,
        documentDateStart: null,
        documentDateEnd: null,
        documentSource: null,
        issueUserId: null,
        issueUserName: null,
        page: 1,
        pageSize: 10,
        // 排序参数
        sortField: null,
        asc: false
      };
      // 清除表格排序状态
      this.$nextTick(() => {
        if (this.$refs.table) {
          this.$refs.table.clearSort();
        }
      });
      //重置时间选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      if (this.$refs.dateDocumentSelector){
        this.$refs.dateDocumentSelector.reset()
      }
      this.handleSearch();
    },

    /**
     * 新增生产领料单
     */
    addClick() {
      var title = "新增生产领料单";
      this.$router.push({
        name: 'AddIssue',
        params: {id: 'add', type: 'add'}
      }).then(() => {

        addTabs(this.$route.path, title);
      });
    },

    /**
     * 多类型审核操作
     */
    auditClick(type) {
      if (!this.selectedRows.length) {
        this.$DonMessage.warning('请选择要操作的记录');
        return;
      }

      let message = '';
      let apiCall = null;

      switch (type) {
        case 'audit':
          message = '审核';
          apiCall = auditIssue;
          break;
        case 'revert':
          message = '反审核';
          apiCall = revertIssue;
          break;
        case 'abrogate':
          message = '作废';
          apiCall = abrogateIssue;
          break;
        case 'reject':
          message = '驳回';
          // 驳回只能单个操作
          if (this.selectedRows.length > 1) {
            this.$DonMessage.warning('一次只能选择一个领料单进行驳回操作');
            return;
          }
          this.rejectDocumentId = this.selectedRows[0].id;
          this.rejectDocumentNo = this.selectedRows[0].issueNo;
          this.rejectDialogVisible = true;
          return;
      }

      this.$confirm(`确定要${message}选中的记录吗？`, `${message}确认`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectedRows.map(item => item.id);

        if (!apiCall) return;
        apiCall(ids).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(`${message}成功`);
            this.handleSearch();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        }).catch(error => {
          this.$DonMessage.error(`${message}失败：` + error.message);
        });
      });
    },

    /**
     * 批量导入
     */
    batchImport() {
      this.$DonMessage.info('批量导入功能开发中，敬请期待');
    },

    /**
     * 导出
     */
    exportClick() {
      // 构建符合PageQuery<IssueQueryDTO>结构的导出参数
      const exportParams = {
        current: 1,
        size: 999999, // 导出时设置较大的分页大小
        query: {
          issueNo: this.queryParams.issueNo,
          issueTimeRange: this.queryParams.issueDateStart && this.queryParams.issueDateEnd ? {
            startDate: this.queryParams.issueDateStart,
            endDate: this.queryParams.issueDateEnd
          } : null,
          issueDateRange: this.queryParams.documentDateStart && this.queryParams.documentDateEnd ? {
            startDate: this.queryParams.documentDateStart,
            endDate: this.queryParams.documentDateEnd
          } : null,
          warehouseId: this.queryParams.warehouseId,
          materialCode: this.queryParams.materialCode,
          materialName: this.queryParams.materialName,
          status: this.queryParams.status,
          sourceNo: this.queryParams.documentSource,
          issueUser: this.queryParams.issueUserId
        }
      };

      exportIssue(exportParams).then(res => {
        // 处理文件下载
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'});
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = '生产领料单导出.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$DonMessage.success('导出成功');
      }).catch(() => {
        this.$DonMessage.error('导出失败');
      });
    },

    /**
     * 退料操作
     */
    handleReturn(row) {
      if (row) {
        this.$DonMessage.info(`退料功能开发中，敬请期待，领料单：${row.issueNo}`);
      } else {
        if (!this.selectedRows.length) {
          this.$DonMessage.warning('请选择要退料的记录');
          return;
        }
        this.$DonMessage.info('批量退料功能开发中，敬请期待');
      }
    },

    /**
     * 处理领料单驳回
     */
    handleRejectIssue(id, rejectReason, accessoryList) {
      return rejectIssue(id, rejectReason, accessoryList);
    },

    /**
     * 编辑领料单
     */
    handleEdit(row) {
      const title = `编辑-${row.issueNo}`;
      this.$router.push({
        name: 'AddIssue',
        params: {
          id: row.id,
          type:'edit',
        }
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },

    /**
     * 查看详情
     */
    handleDetail(row) {
      const title = row.issueNo;
      this.$router.push({
        name: 'IssueDetail',
        params: {
          id: row.id
        }
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },

    /**
     * 源单详情（生产任务单详情）
     */
    handleSourceDetail(row) {
      if (!row.sourceId) {
        this.$DonMessage.warning('该记录没有关联的源单据');
        return;
      }

      const title = `生产任务单详情-${row.sourceNo}`;
      this.$router.push({
        name: 'TaskOrderDetail',
        params: {
          id: row.sourceId
        }
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },

    /**
     * 批量删除方法
     */
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$DonMessage.warning('请选择要删除的数据');
        return;
      }

      // 前端状态校验 - 只允许删除草稿和作废状态的领料单
      const allowedStatuses = ['草稿', '作废'];
      const invalidRows = this.selectedRows.filter(row => !allowedStatuses.includes(row.status));

      if (invalidRows.length > 0) {
        this.$DonMessage.error('只能删除草稿和作废状态的领料单');
        return;
      }

      this.$confirm('确定删除选中的领料单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const ids = this.selectedRows.map(row => row.id);
          const res = await batchDeleteIssue(ids);

          if (res.data.code === 100) {
            this.$DonMessage.success('批量删除成功');
            this.handleSearch();
          } else {
            this.$DonMessage.error(res.data.msg || '批量删除失败');
          }
        } catch (error) {
          this.$DonMessage.error('删除失败：' + error.message);
        }
      });
    },

    /**
     * 删除操作（单条）
     */
    handleDelete(row) {
      this.$confirm(`确定删除领料单【${row.issueNo}】吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteIssue(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success('删除成功');
            this.handleSearch();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        });
      });
    },

    /**
     * 表格选择变化
     */
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    /**
     * 改变列宽
     */
    changeColWidth() {
      // 表格列宽变化处理
    }
  }
}
</script>

<style scoped>
.operation-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.linkStyle {
  color: #409EFF;
  cursor: pointer;
}

.linkStyle:hover {
  text-decoration: underline;
}

.successColor {
  color: #67C23A;
}

.errorColor {
  color: #F56C6C;
}

.abnormalColor {
  color: #E6A23C;
}

.abrogateColor {
  color: #909399;
}
</style>
