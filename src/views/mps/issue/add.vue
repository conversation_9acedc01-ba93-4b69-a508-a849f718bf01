<template>
  <div id="addIssue" class="layoutContainer">
    <!-- 标题栏 -->
    <div class="elTabtitle">
      <div class="elTabtitleLeft">
        <span class="elTabtitleLeftText">{{ stateType === 'add' ? '新增' : '编辑' }}生产领料单</span>
      </div>
      <div class="elTabtitleRight">
        <el-button type="primary" size="small" @click="onSubmit('save')" v-if="stateType === 'add'">保存</el-button>
        <el-button plain size="small" @click="onSubmit('submit')" v-if="stateType === 'add'">提交
        </el-button>
        <el-button type="primary" size="small" @click="onEdit('save')" v-if="stateType === 'edit'">保存</el-button>
        <el-button plain size="small" @click="onEdit('submit')" v-if="stateType === 'edit'">提交
        </el-button>
        <el-button plain size="small" @click="onSubmitAndAudit">提交并审核</el-button>
        <el-button plain size="small" @click="onCancel">取消</el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="elTabsContent">
      <div class="elTabsContentBox">
        <el-collapse v-model="activeNames">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-form :model="form" :rules="rules" ref="form" label-width="120px">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="领料单号" prop="issueNo">
                    <el-input v-model="form.issueNo" placeholder="系统自动生成" disabled></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="单据日期" prop="issueTime">
                    <el-date-picker
                      disabled
                      v-model="form.issueTime"
                      type="date"
                      placeholder="选择日期"
                      value-format="yyyy-MM-dd"
                      style="width: 100%">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="领料人" prop="issueUser">
                    <el-select v-model="form.issueUser" placeholder="请选择领料人" style="width: 100%">
                      <el-option
                        v-for="user in userList"
                        :key="user.id"
                        :label="user.realName"
                        :value="user.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <!-- 明细信息 -->
          <el-collapse-item name="detail">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                明细信息
              </span>
            </template>
            <div style="margin-bottom: 10px;">
              <el-button type="primary" size="small" @click="selectTaskOrder">选择生产任务单</el-button>
<!--              <el-button type="warning" size="small" @click="applyForMaterial" style="margin-left: 10px;">
                <i class="el-icon-shopping-cart-2"></i>申请采购物料
              </el-button>-->
            </div>

            <el-table
              :data="form.details"
              border
              stripe
              size="small"
              style="width: 100%"
              ref="table">

              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>

              <el-table-column prop="sourceNo" label="源单据号" width="150" align="center"></el-table-column>

              <el-table-column prop="productCode" label="商品编码" width="120" align="center"></el-table-column>

              <el-table-column prop="productName" label="商品名称" width="150" align="center"></el-table-column>

              <el-table-column prop="productUnit" label="单位" width="80" align="center"></el-table-column>

              <el-table-column prop="plannedProdQty" label="生产数量" width="100" align="center"></el-table-column>

              <el-table-column prop="materialCode" label="物料编码" width="120" align="center"></el-table-column>

              <el-table-column prop="materialName" label="物料名称" width="150" align="center"></el-table-column>

              <el-table-column prop="materialModel" label="规格型号" width="120" align="center"></el-table-column>

              <el-table-column prop="warehouseName" label="仓库" width="100" align="center"></el-table-column>

              <el-table-column prop="locationName" label="库位" width="100" align="center"></el-table-column>

              <el-table-column prop="currentInventory" label="库存" width="100" align="center">
                <template slot-scope="scope">
                  <span :class="{'stock-insufficient': scope.row.currentInventory < scope.row.requiredQty}">
                    {{ scope.row.currentInventory || 0 }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column prop="lockQty" label="锁定库存" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.lockQty || 0 }}
                </template>
              </el-table-column>

              <el-table-column prop="materialUnit" label="单位" width="80" align="center"></el-table-column>

              <el-table-column prop="requiredQty" label="应领数量" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.requiredQty || 0 }}
                </template>
              </el-table-column>

              <el-table-column prop="issuedQty" label="已领数量" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.issuedQty || 0 }}
                </template>
              </el-table-column>

              <el-table-column prop="currentQty" label="本次领料" width="100" align="center">
                <template slot-scope="scope">
                  <div
                    @mouseenter="onRowMouseEnter(scope.$index)"
                    @mouseleave="onRowMouseLeave(scope.$index)"
                    style="min-height: 32px; display: flex; align-items: center; justify-content: center;">
                    <el-input
                      v-if="editingRowIndex === scope.$index"
                      v-model="scope.row.currentQty"
                      ref="currentQtyInputRef"
                      @blur="onFieldBlur(scope, 'currentQty')"
                      @change="onCurrentQtyChange(scope.row)"
                      size="mini"
                      style="width: 80px;">
                    </el-input>
                    <span v-else>
                      {{ scope.row.currentQty || 0 }}
                    </span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="mini"
                    @click="delClick(scope.$index, form.details)"
                    style="color: #f56c6c;">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <!-- 更多信息 -->
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="制单人">
                {{ form.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="制单日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="this.stateType == 'edit'">
                {{ form.updatedUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="this.stateType == 'edit'">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="审核人" v-if="form.auditorName">
                {{ form.auditorName }}
              </el-descriptions-item>
              <el-descriptions-item label="审核时间" v-if="form.auditTime">
                {{ form.auditTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 选择生产任务单弹窗 -->
    <el-dialog
      v-if="dialogVisibleSelectTask"
      :visible.sync="dialogVisibleSelectTask"
      title="选择生产任务单"
      width="1200px !important"
      :destroy-on-close="true"
      @close="onSelectTaskDialogClose">
      <SelectTaskOrder @select="handleSelectTaskOrder"></SelectTaskOrder>
    </el-dialog>

  </div>
</template>

<script>
import {conversion} from "@/store/filters";
import {oauthUserList} from "@/api/sysmgt";
import {addIssue, editIssue, getIssueDetail} from '@/api/mpsmgt'
import {removeTabs} from "@/assets/js/common";
import SelectTaskOrder from "@/views/mps/issue/SelectTaskOrder.vue";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "AddIssue",
  components: {SelectTaskOrder},
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      dialogVisibleSelectTask: false, // 选择生产任务单弹窗
      issueId: "", // 领料单ID
      stateType: "", // 类型（add/edit）
      // 基本信息
      form: {
        issueNo: '',                    // 领料单号
        sourceNo: '',                   // 源单据号
        sourceId: null,                 // 源单据ID（生产任务单ID）
        issueTime: '',                  // 单据日期
        issueUser: null,                // 领料人
        remark: '',                     // 备注
        status: null,                   // 状态
        details: [],             // 领料单详情列表
        needAudit: false,
      },
      rules: {
        issueDate: [{required: true, message: '单据日期不能为空', trigger: ['blur', 'change']}],
        issueUser: [{required: true, message: '领料人不能为空', trigger: ['blur', 'change']}]
      },
      userList: [], // 用户列表
      editingRowIndex: -1, // 当前编辑行索引
      activeNames: ["base", "detail", "more"], // 全部展开
    }
  },
  methods: {
    /**
     * 删除明细
     */
    delClick(index, list) {
      console.log("delClick", index, list);
      if (!Array.isArray(list)) return;
      list.splice(index, 1);
    },

    /**
     * 处理选择的生产任务单
     */
    handleSelectTaskOrder(taskOrder) {
      console.log("选择的生产任务单", taskOrder);
      if (taskOrder && taskOrder.taskDetailList) {
        // 自动填充单据日期为当前日期
        if (!this.form.issueTime) {
          this.form.issueTime = new Date().toISOString().slice(0, 10);
        }

        // 将任务单明细转换为与接口返回字段一致的格式
        const issueDetails = taskOrder.taskDetailList.map((item, index) => {
          return {
            sourceNo: taskOrder.taskNo, // 源单据号
            productName: taskOrder.productName, // 产品名称
            productCode: taskOrder.productCode, // 产品编码
            productModel: taskOrder.productModel || '', // 产品规格型号
            productUnit: taskOrder.productUnit, // 产品单位
            plannedProdQty: taskOrder.plannedProdQty, // 计划生产数量
            issueId: null, // 领料单id（新记录为空）
            materialId: item.materialId, // 物料id
            materialName: item.materialName || '', // 物料名称
            materialCode: item.materialCode || '', // 物料编码
            materialModel: item.materialModel || '', // 物料规格型号
            materialUnit: item.materialUnit || '', // 物料单位
            warehouseId: item.warehouseId || '', // 仓库id
            warehouseName: item.warehouseName || '', // 仓库名称
            locationId: item.locationId || '', // 库位id
            locationName: item.locationName || '', // 库位名称
            currentInventory: item.currentInventory || 0, // 当前库存
            lockQty: item.lockQty || 0, // 锁定库存
            requiredQty: item.quantity || 0, // 应领数量
            issuedQty: 0, // 已领数量（初始为0）
            currentQty: null, // 本次领料（初始为null）
            remark: item.remark || '', // 备注
            createdUser: null,
            createdTime: null,
            updatedUser: null,
            updatedTime: null
          };
        });

        this.form.sourceNo = taskOrder.taskNo; // 源单据号
        this.form.sourceId = taskOrder.id; // 源单据ID（生产任务单ID）
        // 追加到现有明细列表
        this.form.details = [];
        this.form.details.push(...issueDetails);
        this.$DonMessage.success(`已添加${issueDetails.length}条明细记录`);

        // 关闭弹窗
        this.dialogVisibleSelectTask = false;
      }
    },

    /**
     * 编辑保存
     */
    onEdit(type) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (type === "save") {
            // 修改，保持原状态
          } else if (type === "submit") {
            // 修改并提交审核
            this.form.status = '待审核';
          }

          // 准备提交数据，只包含 IssueDetailVO 核心字段
          const submitData = {
            ...this.form,
            details: this.form.details.map(item => ({
              issueId: item.issueId,
              materialId: item.materialId,
              materialName: item.materialName,
              materialCode: item.materialCode,
              materialModel: item.materialModel,
              materialUnit: item.materialUnit,
              warehouseId: item.warehouseId,
              locationId: item.locationId,
              requiredQty: item.requiredQty,
              issuedQty: item.issuedQty,
              currentQty: item.currentQty,
              remark: item.remark
            }))
          };

          editIssue(submitData).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("修改成功");
              // 编辑成功后返回上一页
              removeTabs(this.$route);
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          });
        } else {
          this.$message.error("请先完善表单信息");
          return false;
        }
      });
    },

    /**
     * 保存
     */
    onSubmit(type) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 所有校验通过，允许提交
          if (type === "save") {
            // 保存状态设置为草稿
            this.form.status = '草稿';
          } else if (type === "submit") {
            // 保存状态设置为待审核
            this.form.status = '待审核';
          }

          // 准备提交数据，只包含 IssueDetailVO 核心字段
          const submitData = {
            ...this.form,
            details: this.form.details.map(item => ({
              issueId: item.issueId,
              materialId: item.materialId,
              materialName: item.materialName,
              materialCode: item.materialCode,
              materialModel: item.materialModel,
              materialUnit: item.materialUnit,
              warehouseId: item.warehouseId,
              locationId: item.locationId,
              requiredQty: item.requiredQty,
              issuedQty: item.issuedQty,
              currentQty: item.currentQty,
              remark: item.remark
            }))
          };

          addIssue(submitData).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("保存成功");
              removeTabs(this.$route);

            } else {
              this.$DonMessage.error(res.data.msg);
            }
          });
        } else {
          // 有校验不通过，阻止提交
          this.$message.error("请先完善表单信息");
          return false;
        }
      });
    },

    /**
     * 提交并审核
     */
    onSubmitAndAudit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 设置为待审核状态
          this.form.status = '待审核';
          this.form.needAudit = true;

          // 准备提交数据，只包含 IssueDetailVO 核心字段
          const submitData = {
            ...this.form,
            details: this.form.details.map(item => ({
              issueId: item.issueId,
              materialId: item.materialId,
              materialName: item.materialName,
              materialCode: item.materialCode,
              materialModel: item.materialModel,
              materialUnit: item.materialUnit,
              warehouseId: item.warehouseId,
              locationId: item.locationId,
              requiredQty: item.requiredQty,
              issuedQty: item.issuedQty,
              currentQty: item.currentQty,
              remark: item.remark
            }))
          };

          // 根据是新增还是编辑模式调用不同的API
          const apiCall = this.stateType === 'add' ? addIssue : editIssue;

          apiCall(submitData).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("提交并审核成功");
              removeTabs(this.$route);
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          });
        } else {
          this.$message.error("请先完善表单信息");
          return false;
        }
      });
    },

    /**
     * 本次领料数量变化时的处理
     */
    onCurrentQtyChange(row) {
      console.log('本次领料数量变化:', row);
      // 这里可以添加数量变化的业务逻辑
    },

    /**
     * 申请采购物料
     */
    applyForMaterial() {
      this.$DonMessage.info('申请采购物料功能开发中，敬请期待');
    },

    /**
     * 鼠标进入表格行
     */
    onRowMouseEnter(index) {
      this.editingRowIndex = index;
      this.$nextTick(() => {
        // 自动聚焦到本次领料输入框
        if (this.$refs.currentQtyInputRef && this.$refs.currentQtyInputRef[0]) {
          this.$refs.currentQtyInputRef[0].focus();
        }
      });
    },

    /**
     * 鼠标离开表格行
     */
    onRowMouseLeave(index) {
      // 鼠标离开时不立即隐藏输入框，等待失去焦点事件
    },

    /**
     * 字段输入框失去焦点
     */
    onFieldBlur(scope, fieldName) {
      this.editingRowIndex = -1;
      // 这里可以添加保存逻辑
      console.log(`${fieldName}保存:`, scope.row);
    },

    /**
     * 取消
     */
    onCancel() {
      // 关闭当前tab并回到上一页
      removeTabs(this.$route);

    },

    /**
     * 监听表格列宽变化
     */
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    /**
     * 时间格式化
     */
    conversion,


    /**
     * 选择生产任务单
     */
    selectTaskOrder() {
      this.dialogVisibleSelectTask = true;
    },

    /**
     * 关闭选择生产任务单弹窗
     */
    onSelectTaskDialogClose() {
      this.dialogVisibleSelectTask = false;
    },

    /**
     * 获取用户列表
     */
    getUserData() {
      var param = {};
      oauthUserList(param).then((res) => {
        if (res.data.code === 100) {
          this.userList = res.data.data;
        }
      });
    },

    /**
     * 格式化用户显示标签
     */
    getUserLabel(item) {
      let realName = item.realName;
      if (realName && realName.length > 20) {
        realName = realName.substring(0, 20) + '...';
      }
      return realName + ' ( ' + item.username + ' )';
    },

    /**
     * 根据领料单ID，获取领料单详情
     */
    selectIssueInfo(issueId) {
      const params = {id: issueId};
      getIssueDetail(params).then((res) => {
        if (res.data.code === 100) {
          // 直接使用接口返回的数据，无需字段转换
          this.form = res.data.data;
          console.log('加载领料单详情:', this.form);
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      }).catch(err => {
        console.error('获取领料单详情失败:', err);
        this.$DonMessage.error('获取领料单详情失败');
      });
    },

    /**
     * 计算区域高度
     */
    areaHeight() {
      setTimeout(() => {
        var allHeight = $(".layoutContainer").height();
        var titleHeight = $(".elTabtitle").height();
        var marginVal = 2 * Number($(".elTabsContent .el-collapse-item").css("marginTop").split("px")[0]);
        var val = allHeight - titleHeight - marginVal - 5;
        $(".elTabsContent").css("height", val);
      }, 60);
    },

    /**
     * 内容尺寸调整
     */
    contentSize() {
      var _this = this;
      _this.areaHeight();
      window.addEventListener("resize", function () {
        _this.areaHeight();
      });
    },

    /**
     * 初始化新增模式
     */
    initAddMode() {
      this.form.createdTime = new Date().getTime();
      this.form.createdUserName = this.$store.state.realName;
      this.form.issueTime = new Date().toISOString().slice(0, 10); // 设置默认日期为今天

      // 检查是否有任务单信息，如果有则自动填充
      this.loadTaskOrderInfo();
    },

    /**
     * 加载任务单信息
     */
    loadTaskOrderInfo() {
      try {
        const taskOrderInfo = sessionStorage.getItem('taskOrderInfo');
        if (taskOrderInfo) {
          const taskOrder = JSON.parse(taskOrderInfo);
          console.log('加载任务单信息:', taskOrder);

          // 自动填充任务单相关信息
          this.form.sourceNo = taskOrder.taskNo; // 源单据号
          this.form.sourceId = taskOrder.id; // 源单据ID（生产任务单ID）

          // 如果有任务单明细，自动填充到领料单明细
          if (taskOrder.taskDetailList && taskOrder.taskDetailList.length > 0) {
            const issueDetails = taskOrder.taskDetailList.map((item, index) => {
              return {
                sourceNo: taskOrder.taskNo, // 源单据号
                productName: taskOrder.productName, // 产品名称
                productCode: taskOrder.productCode, // 产品编码
                productModel: taskOrder.productModel || '', // 产品规格型号
                productUnit: taskOrder.productUnit, // 产品单位
                plannedProdQty: taskOrder.plannedProdQty, // 计划生产数量
                issueId: null, // 领料单id（新记录为空）
                materialId: item.materialId, // 物料id
                materialName: item.materialName || '', // 物料名称
                materialCode: item.materialCode || '', // 物料编码
                materialModel: item.materialModel || '', // 物料规格型号
                materialUnit: item.materialUnit || '', // 物料单位
                warehouseId: item.warehouseId || '', // 仓库id
                warehouseName: item.warehouseName || '', // 仓库名称
                locationId: item.locationId || '', // 库位id
                locationName: item.locationName || '', // 库位名称
                currentInventory: item.currentInventory || 0, // 当前库存
                lockQty: item.lockQty || 0, // 锁定库存
                requiredQty: item.quantity || 0, // 应领数量
                issuedQty: 0, // 已领数量（初始为0）
                currentQty: null, // 本次领料（初始为null）
                remark: item.remark || '', // 备注
                createdUser: null,
                createdTime: null,
                updatedUser: null,
                updatedTime: null
              };
            });

            this.form.details = issueDetails;
            this.$DonMessage.success(`已自动加载${issueDetails.length}条物料明细`);
          }

          // 清除sessionStorage中的任务单信息
          sessionStorage.removeItem('taskOrderInfo');
        }
      } catch (error) {
        console.error('加载任务单信息失败:', error);
      }
    },

    /**
     * 初始化编辑模式
     */
    initEditMode() {
      // 加载领料单详情数据
      this.selectIssueInfo(this.issueId);
    },

    initPage() {
      console.log('路由参数:', this.$route.params);
      const {id} = this.$route.params;

      if (id === 'add') {
        // 新增模式
        this.stateType = "add";
        this.initAddMode();
      } else {
        // 编辑模式
        this.stateType = "edit";
        this.issueId = id;
        console.log(this.issueId, 'issueID')
        this.initEditMode();
      }

      this.contentSize();
      this.getUserData(); // 获取用户列表
    },
  },
  mounted() {
    this.initPage();
  },
  watch: {
    $route(to, from) {
      if (to.name === 'AddIssue') {
        this.initPage();
      }
    }
  }
}
</script>

<style scoped>
#addIssue {
  /* 状态颜色 */
  --success-color: #1c903b;
  --error-color: #F44336;
  --warning-color: #fea927;
  --info-color: #0a8fe2;
  --required-color: #F56C6C;
}

/* 库存不足时的红色样式 */
.stock-insufficient {
  color: #F56C6C !important;
  font-weight: bold;
}
</style>
