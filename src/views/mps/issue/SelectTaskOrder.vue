<template>
  <div class="select-task-order">
    <!-- 搜索条件 -->
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="任务单号">
          <el-input v-model="searchForm.taskNo" placeholder="请输入任务单号" clearable></el-input>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.productName" placeholder="请输入商品名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 任务单列表 -->
    <el-table
      ref="taskOrderTable"
      :data="taskOrderList"
      border
      stripe
      size="small"
      style="width: 100%; margin-top: 10px;"
      max-height="400px"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
      highlight-current-row>

      <el-table-column type="selection" width="55" align="center" :selectable="() => true" :reserve-selection="false"></el-table-column>

      <el-table-column prop="taskNo" label="任务单号" width="150" align="center"></el-table-column>

      <el-table-column prop="taskDate" label="单据日期" width="120" align="center">
        <template slot-scope="scope">
          {{ scope.row.taskDate | dateFormat }}
        </template>
      </el-table-column>

      <el-table-column prop="productCode" label="商品编码" width="120" align="center"></el-table-column>

      <el-table-column prop="productName" label="商品名称" width="150" align="center"></el-table-column>

      <el-table-column prop="productModel" label="规格型号" width="120" align="center"></el-table-column>

      <el-table-column prop="plannedProdQty" label="计划生产数量" width="120" align="center"></el-table-column>

      <el-table-column prop="orderQty" label="订购数量" width="120" align="center"></el-table-column>

      <el-table-column prop="productUnit" label="单位" width="80" align="center"></el-table-column>

      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <span :class="getStatusClass(scope.row.status)">{{ scope.row.status }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="plannedStartTime" label="计划开始时间" width="150" align="center">
        <template slot-scope="scope">
          {{ scope.row.plannedStartTime | dateTimeFormat }}
        </template>
      </el-table-column>

      <el-table-column prop="plannedEndTime" label="计划结束时间" width="150" align="center">
        <template slot-scope="scope">
          {{ scope.row.plannedEndTime | dateTimeFormat }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 底部按钮 -->
    <div class="dialog-footer" style="text-align: right; margin-top: 20px;">
      <el-button type="primary" @click="handleConfirm">确定</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import {getOrderDetail, getTaskOrderList} from "@/api/mpsmgt";

export default {
  name: "SelectTaskOrder",
  data() {
    return {
      searchForm: {
        taskNo: '',
        productName: '',
        status: '已审核' // 默认只显示已审核的任务单
      },
      taskOrderList: [],
      selectedTaskOrder: null, // 选中的任务单
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  filters: {
    dateFormat(value) {
      if (!value) return '';
      const date = new Date(value);
      return date.toISOString().slice(0, 10);
    },
    dateTimeFormat(value) {
      if (!value) return '';
      const date = new Date(value);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  },
  methods: {
    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
      const statusMap = {
        '草稿': 'status-draft',
        '待审核': 'status-pending',
        '已审核': 'status-approved',
        '审核驳回': 'status-rejected',
        '作废': 'status-cancelled'
      };
      return statusMap[status] || '';
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.pagination.currentPage = 1;
      this.loadTaskOrderList();
    },

    /**
     * 重置
     */
    handleReset() {
      this.searchForm = {
        taskNo: '',
        productName: '',
        status: '已审核'
      };
      this.pagination.currentPage = 1;
      this.loadTaskOrderList();
    },

    /**
     * 行点击事件
     */
    handleRowClick(row) {
      this.selectedTaskOrder = row;
      // 清除所有选择，然后选择当前行
      this.$refs.taskOrderTable.clearSelection();
      this.$refs.taskOrderTable.toggleRowSelection(row, true);
    },

    /**
     * 选择变化事件（实现单选）
     */
    handleSelectionChange(selection) {
      if (selection.length > 1) {
        // 如果选择了多个，只保留最后一个
        const lastSelected = selection[selection.length - 1];
        this.$refs.taskOrderTable.clearSelection();
        this.$refs.taskOrderTable.toggleRowSelection(lastSelected, true);
        this.selectedTaskOrder = lastSelected;
      } else if (selection.length === 1) {
        this.selectedTaskOrder = selection[0];
      } else {
        this.selectedTaskOrder = null;
      }
    },

    /**
     * 分页大小变化
     */
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadTaskOrderList();
    },

    /**
     * 当前页变化
     */
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadTaskOrderList();
    },

    /**
     * 取消
     */
    handleCancel() {
      this.$parent.$parent.dialogVisibleSelectTask = false;
    },

    /**
     * 确定选择
     */
    handleConfirm() {
      if (!this.selectedTaskOrder) {
        this.$message.warning('请选择一个生产任务单');
        return;
      }

      // 获取任务单详情（包含明细信息）
      this.getTaskOrderDetail(this.selectedTaskOrder.id);
    },

    /**
     * 获取任务单详情
     */
    getTaskOrderDetail(taskOrderId) {
      const params = {id: taskOrderId};
      getOrderDetail(params).then(res => {
        if (res.data.code === 100) {
          const taskOrderDetail = res.data.data;
          console.log('任务单详情:', taskOrderDetail);
          this.$emit('select', taskOrderDetail);
        } else {
          this.$message.error(res.data.msg || '获取任务单详情失败');
        }
      }).catch(err => {
        console.error('获取任务单详情失败:', err);
        this.$message.error('获取任务单详情失败');
      });
    },

    /**
     * 加载任务单列表
     */
    loadTaskOrderList() {
      const params = {
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        query: {
          taskNo: this.searchForm.taskNo,
          productName: this.searchForm.productName,
          status: '已审核'
        }
      };

      getTaskOrderList(params).then(res => {
        if (res.data.code === 100) {
          this.taskOrderList = res.data.data|| [];
          this.pagination.total = res.data.total || 0;
        } else {
          this.$message.error(res.data.msg || '获取任务单列表失败');
        }
      }).catch(err => {
        console.error('获取任务单列表失败:', err);
        this.$message.error('获取任务单列表失败');
      });
    }
  },
  mounted() {
    this.loadTaskOrderList();
  }
}
</script>

<style scoped>
.select-task-order {
  padding: 10px;
}

.search-form {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.pagination-wrapper {
  text-align: right;
  margin-top: 15px;
}

/* 状态样式 */
.status-draft {
  color: #909399;
}

.status-pending {
  color: #E6A23C;
}

.status-approved {
  color: #67C23A;
}

.status-rejected {
  color: #F56C6C;
}

.status-cancelled {
  color: #909399;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover {
  cursor: pointer;
}
</style>
