<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" label-width="100px">
        <!-- 单据日期 -->
        <el-form-item label="单据日期">
          <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
        </el-form-item>
        <!-- 客户名称 -->
        <el-form-item label="客户名称" prop="customerName">
          <selectInput
            ref="selectInput"
            v-model="queryParams.customerName"
            :inputParam="queryParams.customerName"
            inputType="customerInfo"
            placeholder="请选择客户名称"
            @select="onInputSearch($event)"
          ></selectInput>

        </el-form-item>
        <!-- 销售订单号 -->
        <el-form-item label="销售订单号" prop="salesOrderNo">
          <el-input
            v-model="queryParams.salesOrderNo"
            placeholder="请输入销售订单号"
            clearable
          />
        </el-form-item>
        <!-- 状态 -->
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option v-for="item in statusOptions" :value="item" :key="item" :label="item"></el-option>
          </el-select>
        </el-form-item>

        <!-- 更多查询条件 -->
        <template v-if="isShow">
          <el-form-item label="计划开工时间">
            <DateRangeSelector ref="datePlannedStartSelector" @change="handlePlannedStartDateRange"/>
          </el-form-item>
          <el-form-item label="计划完工时间">
            <DateRangeSelector ref="datePlannedEndSelector" @change="handlePlannedEndDateRange"/>
          </el-form-item>
          <el-form-item label="任务编号" prop="taskNo">
            <el-input
              v-model="queryParams.taskNo"
              placeholder="请输入任务编号"
              clearable
            />
          </el-form-item>
          <el-form-item label="商品编码" prop="code">
            <el-input
              v-model="queryParams.code"
              placeholder="请输入商品编码"
              clearable
            />
          </el-form-item>
          <el-form-item label="商品名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入商品名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="规格型号" prop="model">
            <el-input
              v-model="queryParams.model"
              placeholder="请输入规格型号"
              clearable
            />
          </el-form-item>
        </template>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i
            class="el-icon-arrow-up el-icon--right"></i></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
          <el-button type="text" icon="bulkImport-icon" @click="batchImport()">批量导入</el-button>
          <el-dropdown @command="auditClick">
            <span>
              <i class="process-icon"></i>
              审核
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
                <el-dropdown-item command="audit">审核通过</el-dropdown-item>
                <!--                <el-dropdown-item command="revert">反审核</el-dropdown-item>-->
                <el-dropdown-item command="reject">审核驳回</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown @command="moreClick">
            <span>
              <i class="receipt-icon"></i>
              生成单据
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
                <el-dropdown-item command="materialPicking">生产领料</el-dropdown-item>
                <el-dropdown-item command="materialReturn">生产退料</el-dropdown-item>
                <el-dropdown-item command="productInbound">成品入库</el-dropdown-item>
                <!--                <el-dropdown-item command="purchaseApply">提交采购申请单</el-dropdown-item>-->
              </div>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown @command="exportClick">
            <el-button type="text" icon="bulkDown-icon">
              导出<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="exportBasic">下载生产订单</el-dropdown-item>
              <el-dropdown-item command="exportDetail">下载生产订单（包含物料明细）</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="text" icon="disable-icon" @click="auditClick('abrogate')">作废</el-button>
          <el-button type="text" icon="deleteRed-icon" @click="handleBatchDelete()">删除</el-button>

        </div>
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange"
                @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>

        <!-- 单据日期 -->
        <el-table-column prop="taskDate" label="单据日期" min-width="120" sortable="custom">
          <template #default="scope">
            {{ conversion(scope.row.taskDate, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <!-- 任务编号 -->
        <el-table-column prop="taskNo" label="任务编号" min-width="160" sortable="custom">
          <template #default="{row}">
            <span class="linkStyle" @click="handleDetail(row)">{{ row.taskNo }}</span>
          </template>
        </el-table-column>
        <!-- 状态 -->
        <el-table-column prop="status" label="状态" width="80" sortable="custom">
          <template slot-scope="scope">
            <div :class="getStatusClass(scope.row.status)">
              {{ scope.row.status }}
            </div>
          </template>
        </el-table-column>

        <!-- 商品图片 -->
        <el-table-column label="商品图片" min-width="80">
          <template #default="{row}">
            <img v-if="row.imageUrl != ''" class="pictureShow" :src="row.imageUrl" alt=""/>
          </template>
        </el-table-column>
        <!-- 商品编码 -->
        <el-table-column prop="productCode" label="商品编码" min-width="120" sortable="custom">
          <template #default="{row}">
            <span class="linkStyle" @click="handleProductDetail(row)">{{ row.productCode }}</span>
          </template>
        </el-table-column>
        <!-- 商品名称 -->
        <el-table-column prop="productName" label="商品名称" min-width="150"/>
        <!-- 规格型号 -->
        <el-table-column prop="productModel" label="规格型号" min-width="120"/>

        <!-- 销售订单号 -->
        <el-table-column prop="salesOrderNo" label="销售订单号" min-width="160">
          <template #default="{row}">
            <span class="linkStyle" @click="handleSalesDetail(row)">{{ row.salesOrderNo }}</span>
          </template>
        </el-table-column>
        <!-- 客户名称 -->
        <el-table-column prop="customerName" label="客户名称" min-width="120"/>
        <!-- 客户订单号 -->
        <el-table-column prop="customerNo" label="客户订单号" min-width="120"/>
        <el-table-column prop="productUnit" label="单位" min-width="60"/>
        <!-- 订购数量 -->
        <el-table-column prop="orderQty" label="订购数量" min-width="100" sortable="custom"/>
        <!-- 计划生产数 -->
        <el-table-column prop="plannedProdQty" label="计划生产数" min-width="110" sortable="custom"/>
        <!-- 计划开工时间 -->
        <el-table-column prop="plannedStartTime" label="计划开工时间" min-width="140" sortable="custom">
          <template #default="scope">
            {{ conversion(scope.row.plannedStartTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <!-- 计划完工时间 -->
        <el-table-column prop="plannedEndTime" label="计划完工时间" width="140" sortable="custom">
          <template #default="scope">
            {{ conversion(scope.row.plannedEndTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>

        <!-- 审核人 -->
        <el-table-column prop="auditorName" label="审核人" width="100"/>
        <!-- 审核时间 -->
        <el-table-column prop="auditTime" label="审核时间" width="140">
          <template slot-scope="scope">
            {{ conversion(scope.row.auditTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <!-- 实际完工时间 -->
        <el-table-column prop="actualFinishTime" label="实际完工时间" width="140">
          <template slot-scope="scope">
            {{ conversion(scope.row.actualFinishTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <!-- 制单人 -->
        <el-table-column prop="createdUserName" label="制单人" width="100"/>
        <!-- 制单时间 -->
        <el-table-column prop="createdTime" label="制单时间" width="140">
          <template slot-scope="scope">
            {{ conversion(scope.row.createdTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column label="操作" fixed="right" width="280">
          <template slot-scope="{row}">
            <div class="operation-container">
              <!-- 编辑按钮 - 草稿和驳回状态都可以编辑 -->
              <el-button
                v-if="row.status === '草稿' || row.status === '审核驳回'"
                type="text"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>

              <!-- 加工按钮 - 已审核状态显示 -->
              <!--              <el-button
                              v-if="row.status === '已审核'"
                              type="text"
                              size="small"
                              @click="handleProcessing(row)"
                            >
                              加工
                            </el-button>-->

              <!-- 详情按钮 - 所有状态都显示 -->
              <el-button
                type="text"
                size="small"
                @click="handleDetail(row)"
              >
                详情
              </el-button>

              <!-- 物料操作下拉菜单 - 只有已审核状态显示 -->
              <el-dropdown
                v-if="row.status === '已审核'"
              >
                <el-button type="text" size="small">
                  物料<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="handleMaterialRegister(row)">登记</el-dropdown-item>
                  <el-dropdown-item @click.native="handleMaterialTrack(row)">追踪</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <!-- 删除按钮 - 只有作废状态显示 -->
              <el-button
                v-if="row.status === '作废'"
                type="text"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>

    </div>

    <!-- 审核驳回组件 -->
    <AuditRejectDialog
      :visible.sync="rejectDialogVisible"
      :document-id="rejectDocumentId"
      :document-no="rejectDocumentNo"
      document-label="任务单号"
      title="审核驳回"
      file-flag="taskOrder"
      :reject-api="handleRejectTaskOrder"
      @success="handleSearch"
      @close="handleSearch"
    />
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import selectInput from "@/components/selectInput/selectInput.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import AuditRejectDialog from "@/components/AuditRejectDialog/index.vue";
import {
  abrogateTaskOrder,
  auditTaskOrder,
  batchDeleteTaskOrder,
  deleteTaskOrder,
  exportTaskOrder, generateBeInStock,
  getOrderDetail,
  getTaskOrderList,
  rejectTaskOrder,
  revertTaskOrder
} from "@/api/mpsmgt";
import {addTabs, tableHeight} from "@/assets/js/common";

export default {
  name: "TaskOrderList",
  components: {selectInput, DateRangeSelector, Pagination, AuditRejectDialog},
  data() {
    return {
      // 审核驳回对话框相关
      rejectDialogVisible: false,
      rejectDocumentId: '',
      rejectDocumentNo: '',
      selectedRows: [],
      maximumHeight: 0,
      total: 0,
      isShow: false,
      resultList: [],
      queryParams: {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        customerName: null,
        salesOrderNo: null,
        status: null,
        // 更多查询条件
        plannedStartDateStart: null,
        plannedStartDateEnd: null,
        plannedEndDateStart: null,
        plannedEndDateEnd: null,
        taskNo: null,
        code: null,
        name: null,
        model: null,
        page: 1,
        pageSize: 10,
        // 排序参数
        sortField: null,
        asc: false
      },
      statusOptions: [
        '草稿',
        '待审核',
        '已审核',
        '审核驳回',
        '作废'
      ]
    }
  },
  methods: {
    onInputSearch($event) {
      this.queryParams.customerName = $event.name;
    },

    /**
     * 处理表格排序变化
     * @param {Object} column - 列信息
     * @param {String} prop - 列属性名
     * @param {String} order - 排序方向 'ascending' | 'descending' | null
     */
    handleSortChange({column, prop, order}) {
      console.log('排序变化:', {column, prop, order});

      if (order) {
        // 设置排序字段和方向
        this.queryParams.sortField = prop;
        this.queryParams.asc = order === 'ascending';
      } else {
        // 清除排序
        this.queryParams.sortField = null;
        this.queryParams.asc = false;
      }

      // 重新查询数据
      this.handleSearch();
    },
    /**
     * 获取状态样式类名
     */
    getStatusClass(status) {
      switch (status) {
        case '草稿':
          return '';
        case '待审核':
          return 'errorColor';
        case '已审核':
          return 'successColor';
        case '审核驳回':
          return 'abnormalColor';
        case '作废':
          return 'abrogateColor';
        default:
          return '';
      }
    },


    /**
     * 新增生产任务单
     */
    addClick() {
      var title = "新增生产任务单";
      this.$router.push({
        name: 'AddTaskOrder',
        params: {id: 'add', type: 'add'}
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },

    /**
     * 多类型审核操作
     */
    auditClick(type) {
      if (!this.selectedRows.length) {
        this.$DonMessage.warning('请选择要操作的记录');
        return;
      }

      let message = '';
      let apiCall = null;

      switch (type) {
        case 'audit':
          message = '审核';
          apiCall = auditTaskOrder;
          break;
        case 'revert':
          message = '反审核';
          apiCall = revertTaskOrder;
          break;
        case 'abrogate':
          message = '作废';
          apiCall = abrogateTaskOrder;
          break;
        case 'reject':
          message = '驳回';
          // 驳回只能单个操作
          if (this.selectedRows.length > 1) {
            this.$DonMessage.warning('一次只能选择一个任务单进行驳回操作');
            return;
          }
          this.rejectDocumentId = this.selectedRows[0].id;
          this.rejectDocumentNo = this.selectedRows[0].taskNo;
          this.rejectDialogVisible = true;
          return;
      }
      this.$confirm(`确定要${message}选中的记录吗？`, `${message}确认`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 合并所有选中行的ID
        const ids = this.selectedRows.map(item => item.id);

        // 统一调用API
        if (!apiCall) return;
        apiCall(ids).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(`${message}成功`);
            this.handleSearch();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        }).catch(error => {
          this.$DonMessage.error(`${message}失败：` + error.message);
        });
      });
    },

    /**
     * 更多操作
     */
    moreClick(command) {
      if (!this.selectedRows.length) {
        this.$DonMessage.warning('请选择要操作的记录');
        return;
      }

      // 检查选中记录的状态，只有已审核状态才能进行物料操作
      const invalidRows = this.selectedRows.filter(row => row.status !== '已审核');
      if (invalidRows.length > 0) {
        this.$DonMessage.warning('只有已审核状态的任务单才能进行物料操作');
        return;
      }

      // 一次只能选择一个任务单进行操作
      if (this.selectedRows.length > 1) {
        this.$DonMessage.warning('一次只能选择一个任务单进行操作');
        return;
      }

      const selectedItem = this.selectedRows[0];

      switch (command) {
        case 'materialPicking':
          // 生产领料
          this.handleMaterialPicking(selectedItem);
          break;
        case 'materialReturn':
          // 生产退料
          this.handleMaterialReturn(selectedItem);
          break;
        case 'productInbound':
          // 产品入库
          this.handleProductInbound(selectedItem);
          break;
        case 'purchaseApply':
          // 提交采购申请单
          this.$DonMessage.info('提交采购申请单功能开发中，敬请期待');
          break;
      }
    },

    /**
     * 生产领料操作
     */
    handleMaterialPicking(row) {
      // 先查询任务单详情，获取完整的任务单信息
      const params = {id: row.id};
      getOrderDetail(params).then((res) => {
        if (res.data.code === 100) {
          const taskOrderDetail = res.data.data;
          console.log('获取任务单详情:', taskOrderDetail);

          const title = `新增领料单-${row.taskNo}`;

          // 将完整的任务单信息存储到sessionStorage中，供新增页面使用
          sessionStorage.setItem('taskOrderInfo', JSON.stringify({
            id: taskOrderDetail.id,
            taskNo: taskOrderDetail.taskNo,
            productName: taskOrderDetail.productName,
            productCode: taskOrderDetail.productCode,
            productModel: taskOrderDetail.productModel,
            productUnit: taskOrderDetail.productUnit,
            plannedProdQty: taskOrderDetail.plannedProdQty,
            customerName: taskOrderDetail.customerName,
            salesOrderNo: taskOrderDetail.salesOrderNo,
            customerNo: taskOrderDetail.customerNo,
            orderQty: taskOrderDetail.orderQty,
            status: taskOrderDetail.status,
            taskDetailList: taskOrderDetail.taskDetailList || taskOrderDetail.taskOrderDetailList || []
          }));

          this.$router.push({
            name: 'AddIssue',
            params: {
              id: 'add'
            }
          }).then(() => {
            addTabs(this.$route.path, title);
          });
        } else {
          this.$DonMessage.error(res.data.msg || '获取任务单详情失败');
        }
      }).catch(err => {
        console.error('获取任务单详情失败:', err);
        this.$DonMessage.error('获取任务单详情失败，请稍后重试');
      });
    },

    /**
     * 生产退料操作
     */
    handleMaterialReturn(row) {
      // 先查询任务单详情，获取完整的任务单信息
      const params = {id: row.id};
      getOrderDetail(params).then((res) => {
        if (res.data.code === 100) {
          const taskOrderDetail = res.data.data;
          console.log('获取任务单详情:', taskOrderDetail);

          const title = `新增退料单-${row.taskNo}`;

          // 将完整的任务单信息存储到sessionStorage中，供新增页面使用
          sessionStorage.setItem('taskOrderInfo', JSON.stringify({
            id: taskOrderDetail.id,
            taskNo: taskOrderDetail.taskNo,
            productName: taskOrderDetail.productName,
            productCode: taskOrderDetail.productCode,
            productModel: taskOrderDetail.productModel,
            productUnit: taskOrderDetail.productUnit,
            plannedProdQty: taskOrderDetail.plannedProdQty,
            customerName: taskOrderDetail.customerName,
            salesOrderNo: taskOrderDetail.salesOrderNo,
            customerNo: taskOrderDetail.customerNo,
            orderQty: taskOrderDetail.orderQty,
            status: taskOrderDetail.status,
            taskDetailList: taskOrderDetail.taskDetailList || taskOrderDetail.taskOrderDetailList || []
          }));

          this.$router.push({
            name: 'AddReturn',
            params: {
              id: 'add'
            }
          }).then(() => {
            addTabs(this.$route.path, title);
          });
        } else {
          this.$DonMessage.error(res.data.msg || '获取任务单详情失败');
        }
      }).catch(err => {
        console.error('获取任务单详情失败:', err);
        this.$DonMessage.error('获取任务单详情失败，请稍后重试');
      });
    },

    handleProductInbound(row){
      generateBeInStock(row.id).then(res => {
        if (res.data.code === 100){
          this.$DonMessage.success("成品待入库单生成成功!")
          this.handleSearch()
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },




    /**
     * 批量导入
     */
    batchImport() {
      this.$DonMessage.info('批量导入功能开发中，敬请期待');
    },

    /**
     * 处理任务单驳回
     */
    handleRejectTaskOrder(id, rejectReason, accessoryList) {
      return rejectTaskOrder(id, rejectReason, accessoryList);
    },

    /**
     * 导出
     */
    exportClick(command) {
      // 构建导出参数，包含查询条件
      const exportParams = {
        includeDetail: command === 'exportDetail',
        customerName: this.queryParams.customerName,
        salesOrderNo: this.queryParams.salesOrderNo,
        status: this.queryParams.status,
        startTaskDate: this.queryParams.startDate,
        endTaskDate: this.queryParams.endDate,
        plannedStartDateStart: this.queryParams.plannedStartDateStart,
        plannedStartDateEnd: this.queryParams.plannedStartDateEnd,
        plannedEndDateStart: this.queryParams.plannedEndDateStart,
        plannedEndDateEnd: this.queryParams.plannedEndDateEnd,
        taskNo: this.queryParams.taskNo,
        code: this.queryParams.code,
        name: this.queryParams.name,
        model: this.queryParams.model,
      };

      exportTaskOrder(exportParams).then(res => {
        // 处理文件下载
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'});
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        const fileName = command === 'exportDetail' ? '生产任务单导出（包含物料明细）.xlsx' : '生产任务单导出.xlsx';
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$DonMessage.success('导出成功');
      }).catch(() => {
        this.$DonMessage.error('导出失败');
      });
    },

    /**
     * 加工操作
     */
    handleProcessing(row) {
      this.$DonMessage.info('加工功能待定，敬请期待');
    },

    /**
     * 编辑任务单
     */
    handleEdit(row) {
      const title = `编辑-${row.taskNo}`;
      this.$router.push({
        name: 'AddTaskOrder',
        params: {
          id: row.id
        }
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },

    /**
     * 查看详情
     */
    handleDetail(row) {
      const title = row.taskNo;
      this.$router.push({
        name: 'TaskOrderDetail',
        params: {
          id: row.id
        }
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },
// 批量删除方法
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$DonMessage.warning('请选择要删除的数据');
        return;
      }

      // 前端状态校验 - 只允许删除草稿和作废状态的任务单
      const allowedStatuses = ['草稿', '作废']; // 使用描述值进行状态校验
      const invalidRows = this.selectedRows.filter(row => !allowedStatuses.includes(row.status));

      if (invalidRows.length > 0) {
        this.$DonMessage.error('只能删除草稿和作废状态的任务单');
        return;
      }

      this.$confirm('确定删除选中的任务单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const ids = this.selectedRows.map(row => row.id);
          const res = await batchDeleteTaskOrder(ids);

          if (res.data.code === 100) {
            this.$DonMessage.success('批量删除成功');
            this.handleSearch(); // 刷新列表
          } else {
            this.$DonMessage.error(res.data.msg || '批量删除失败');
          }
        } catch (error) {
          this.$DonMessage.error('删除失败：' + error.message);
        }
      });
    },
    /**
     * 删除操作（单条）
     */
    handleDelete(row) {
      this.$confirm(`确定删除任务单【${row.taskNo}】吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTaskOrder(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success('删除成功');
            this.handleSearch();
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        });
      });
    },

    /**
     * 销售订单详情
     */
    handleSalesDetail(row) {
      if (!row.salesOrderId) {
        this.$DonMessage.warning('该记录没有关联的销售订单');
        return;
      }

      const title = `销售订单详情-${row.salesOrderNo}`;
      this.$router.push({
        name: 'salesOrderDetail',
        params: {
          id: row.salesOrderId
        }
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },

    /**
     * 商品详情
     */
    handleProductDetail(row) {
      if (!row.productId) {
        this.$DonMessage.warning('该记录没有关联的商品信息');
        return;
      }

      const title = `商品详情-${row.productCode}`;
      this.$router.push({
        name: 'productDetail',
        params: {
          id: row.productId
        }
      }).then(() => {
        addTabs(this.$route.path, title);
      });
    },

    /**
     * 物料登记
     */
    handleMaterialRegister(row) {
      const title = `物料登记-${row.taskNo}`;

      // 将行数据存储到sessionStorage中，避免路由参数传递复杂对象
      sessionStorage.setItem('materialRegisterRowData', JSON.stringify(row));

      this.$router.push({
        name: 'MaterialRegister',
        params: {
          id: row.id
        }
      });
      addTabs(this.$route.path, title);
    },

    /**
     * 物料追踪
     */
    handleMaterialTrack(row) {
      const title = `物料追踪-${row.taskNo}`;
      this.$router.push({
        name: 'MaterialTrack',
        params: {
          id: row.id
        }
      });
      addTabs(this.$route.path, title);
    },

    /**
     * 表格高度计算
     */
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },

    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },

    /**
     * 监听表格列宽变化
     */
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },

    /**
     * 时间格式化方法
     */
    conversion,

    /**
     * 查询
     */
    handleSearch() {
      const params = {
        query: {
          customerName: this.queryParams.customerName,
          salesOrderNo: this.queryParams.salesOrderNo,
          status: this.queryParams.status,
          startTaskDate: this.queryParams.startDate,
          endTaskDate: this.queryParams.endDate,
          plannedStartDateStart: this.queryParams.plannedStartDateStart,
          plannedStartDateEnd: this.queryParams.plannedStartDateEnd,
          plannedEndDateStart: this.queryParams.plannedEndDateStart,
          plannedEndDateEnd: this.queryParams.plannedEndDateEnd,
          taskNo: this.queryParams.taskNo,
          code: this.queryParams.code,
          name: this.queryParams.name,
          model: this.queryParams.model,
        },
        current: this.queryParams.page,
        size: this.queryParams.pageSize,
        // 排序参数
        sortField: this.queryParams.sortField,
        asc: this.queryParams.asc
      }
      console.log('查询参数（包含排序）:', params);
      // 发起请求查询接口
      this.dataList(params)
    },

    /**
     * 处理选中的行
     */
    handleSelectionChange(val) {
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },

    /**
     * 重置查询条件
     */
    handleReset() {
      this.queryParams = {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        customerName: '',
        salesOrderNo: '',
        status: null,
        // 更多查询条件
        plannedStartDateStart: '',
        plannedStartDateEnd: '',
        plannedEndDateStart: '',
        plannedEndDateEnd: '',
        taskNo: '',
        code: '',
        name: '',
        model: '',
        page: 1,
        pageSize: 10,
        // 排序参数
        sortField: null,
        asc: false
      }
      // 清除表格排序状态
      this.$nextTick(() => {
        if (this.$refs.table) {
          this.$refs.table.clearSort();
        }
      });
      //重置时间选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      if (this.$refs.datePlannedStartSelector) {
        this.$refs.datePlannedStartSelector.reset()
      }
      if (this.$refs.datePlannedEndSelector) {
        this.$refs.datePlannedEndSelector.reset()
      }
      this.handleSearch()
    },

    /**
     * 分页查询数据
     */
    dataList(params) {
      console.log("生产任务单分页查询参数：", params);
      getTaskOrderList(params).then(res => {
        console.log("生产任务单数据", res);
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea();
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      })
    },

    /**
     * 获取单据日期范围选择
     */
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      console.log('选择单据日期范围startDate：', this.queryParams.startDate);
      console.log('选择单据日期范围endDate：', this.queryParams.endDate);
      this.handleSearch();
    },

    /**
     * 获取计划开工时间范围选择
     */
    handlePlannedStartDateRange(range) {
      this.queryParams.plannedStartDateStart = range.startDate;
      this.queryParams.plannedStartDateEnd = range.endDate;
      this.handleSearch();
    },

    /**
     * 获取计划完工时间范围选择
     */
    handlePlannedEndDateRange(range) {
      this.queryParams.plannedEndDateStart = range.startDate;
      this.queryParams.plannedEndDateEnd = range.endDate;
      this.handleSearch();
    }
  },
  mounted() {
    this.handleSearch();
  },
  created() {
    console.log('TaskOrder Component created');
  },
}
</script>

<style scoped>
.linkStyle {
  color: #409EFF;
  cursor: pointer;
}

.linkStyle:hover {
  text-decoration: underline;
}

.abrogateColor {
  color: #777474;
}

/* 操作列布局样式 */
.operation-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.operation-container .el-button {
  margin: 0;
  padding: 0 5px;
  height: 28px;
  line-height: 28px;
}

.operation-container .el-dropdown {
  display: inline-block;
}
</style>
