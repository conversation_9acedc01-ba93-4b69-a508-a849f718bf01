<template>
  <div class="layoutContainer actionFlowDetail" id="taskOrderDetail">
    <div class="elTabtitle">
      <div>生产任务单详情</div>
      <div>
<!--        <el-button plain @click="handleIssue" :disabled="form.status !== '已审核'">领料</el-button>-->
        <!--        <el-button plain @click="handleQualityCheck">质检</el-button>-->
<!--        <el-button plain @click="handleInStock" :disabled="form.status !== '已审核'">入库</el-button>-->
<!--        <el-button plain @click="handleComplete" :disabled="!canComplete">完工
        </el-button>
        <el-dropdown @command="handleMaterial" :disabled="form.status !== '已审核'">
          <el-button plain style="margin-left: 8px;" :disabled="form.status !== '已审核'">
            物料<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="materialRegister">物料登记</el-dropdown-item>
            <el-dropdown-item command="materialTrack">物料追踪</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>-->
        <el-dropdown @command="handleExport">
          <el-button plain style="margin-left: 8px;">
            导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="exportBasic">下载生产订单</el-dropdown-item>
            <el-dropdown-item command="exportDetail">下载生产订单（包含物料明细）</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="任务单号">
                <span>{{ form.taskNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="单据日期">
                <span>{{ form.taskDate }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="商品编码">
                <span>{{ form.productCode }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="商品名称">
                <span>{{ form.productName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="规格型号">
                <span>{{ form.productModel }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="单位">
                <span>{{ form.productUnit }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="销售订单号">
                <span>{{ form.salesOrderNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户名称">
                <span>{{ form.customerName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户订单号">
                <span>{{ form.customerNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="订购数量">
                <span>{{ form.orderQty }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计划生产数量">
                <span>{{ form.plannedProdQty }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计划开始时间">
                <span>{{ form.plannedStartTime }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计划结束时间">
                <span>{{ form.plannedEndTime }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ form.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>

          <!-- 明细信息 -->
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                明细信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.taskDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="materialCode" label="物料编码" width="120"/>
              <el-table-column prop="materialName" label="物料名称" width="150"/>
              <el-table-column prop="imageUrl" label="图片" width="100">
                <template #default="{ row }">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="row.imageUrl" alt=""/>
                </template>
              </el-table-column>
              <el-table-column prop="materialModel" label="规格型号" width="120"/>
              <el-table-column prop="unitQty" label="单件用量" width="120"/>
              <el-table-column prop="quantity" label="所需数量" width="100"/>
              <el-table-column prop="currentInventory" label="库存" width="100">
                <template #default="scope">
                  <span :class="{ 'stock-insufficient': scope.row.currentInventory < scope.row.quantity }">
                    {{ scope.row.currentInventory }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="materialUnit" label="单位" width="80"/>
              <el-table-column prop="unitPrice" label="单价(¥)" width="80"/>
              <el-table-column prop="amount" label="金额(¥)" width="80"/>
              <el-table-column prop="attritionRate" label="损耗率(%)" width="100"/>
              <el-table-column prop="warehouseName" label="仓库" width="100"/>
              <el-table-column prop="locationName" label="库位" width="100"/>
              <el-table-column label="备注" prop="remark" min-width="110">
                <template slot-scope="scope">
                  <span>{{ scope.row.remark }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <!-- 更多信息 -->
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="制单人">
                <span>{{ form.createdUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="制单日期">
                <span>{{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="form.updatedUserName">
                <span>{{ form.updatedUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="form.updatedTime">
                <span>{{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="审核人" v-if="form.auditorName">
                <span>{{ form.auditorName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="审核时间" v-if="form.auditTime">
                <span>{{ form.auditTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="实际完工时间" v-if="form.actualFinishTime">
                <span>{{ form.actualFinishTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <span :class="getStatusClass(form.status)">
                  {{ formatStatus(form.status) }}
                </span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import {conversion} from "@/store/filters";
import {exportTaskOrder, finishTaskOrder, getOrderDetail} from "@/api/mpsmgt";
import {addTabs, collapseArea, removeTabs} from "@/assets/js/common";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "TaskOrderDetail",
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      taskOrderId: "", // 任务单ID
      // 基本信息
      form: {
        taskNo: '',                     // 任务单号
        taskDate: '',                   // 单据日期
        productId: null,                // 生产商品ID
        productCode: null,              // 生产商品编码
        productName: null,
        productModel: null,
        productUnit: '',                   // 计量单位
        salesOrderNo: '',               // 销售订单编号
        customerName: '',               // 客户名称
        customerNo: '',                 // 客户订单号
        orderQty: null,                 // 订购数量
        plannedProdQty: null,           // 计划生产数量
        plannedStartTime: '',           // 计划开始时间
        plannedEndTime: '',             // 计划结束时间
        actualFinishTime: '',           // 实际完工时间
        remark: '',                     // 备注
        status: null,                   // 状态
        taskDetailList: [],             // 任务单详情列表
        issueId: null,                  // 领料单ID
        createdUserName: '',            // 制单人
        createdTime: '',                // 制单时间
        updatedUserName: '',            // 修改人
        updatedTime: '',                // 修改时间
        auditorName: '',                // 审核人
        auditTime: ''                   // 审核时间
      },
      activeNames: ["base", "product", "more"], // 全部展开
      statusOptions: [
        {label: '草稿', value: 0},
        {label: '待审核', value: 1},
        {label: '已审核', value: 2},
        {label: '已作废', value: 3},
        {label: '已完工', value: 4}
      ]
    }
  },
  computed: {

    /**
     * 判断是否可以完工
     * 只有已审核状态且未完工的任务单才能进行完工操作
     */
    canComplete() {
      return this.form.status === '已审核' && !this.form.actualFinishTime;
    }
  },

  methods: {
    /**
     * 获取状态样式类名
     */
    getStatusClass(status) {
      switch (status) {
        case 0:
          return 'abnormalColor'; // 草稿
        case 1:
          return 'warningColor'; // 待审核
        case 2:
          return 'successColor'; // 已审核
        case 3:
          return 'errorColor'; // 已作废
        case 4:
          return 'blueColor'; // 已完工
        default:
          return '';
      }
    },

    /**
     * 格式化状态显示
     */
    formatStatus(status) {
      const match = this.statusOptions.find(item => item.value === status);
      return match ? match.label : '未知状态';
    },


    /**
     * 监听表格列宽变化
     */
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    /**
     * 时间格式化
     */
    conversion,

    /**
     * 领料操作
     */
    handleIssue() {
      // 检查任务单状态，只有已审核状态才能进行领料操作
      if (this.form.status !== '已审核') {
        this.$DonMessage.warning('只有已审核状态的任务单才能进行领料操作');
        return;
      }
        // 打开新增页面
        this.openIssueAdd();
    },

    /**
     * 打开领料单详情页面
     */
    openIssueDetail(issueId) {
      const title = `领料单详情-${this.form.taskNo}`;
      this.$router.push({
        name: 'IssueDetail',
        params: {
          id: issueId
        }
      });
      addTabs(this.$route.path, title);
    },

    /**
     * 打开领料单新增页面
     */
    openIssueAdd() {
      const title = `新增领料单-${this.form.taskNo}`;

      // 将任务单信息存储到sessionStorage中，供新增页面使用
      sessionStorage.setItem('taskOrderInfo', JSON.stringify({
        id: this.taskOrderId,
        taskNo: this.form.taskNo,
        productName: this.form.productName,
        productCode: this.form.productCode,
        productModel: this.form.productModel,
        productUnit: this.form.productUnit,
        plannedProdQty: this.form.plannedProdQty,
        taskDetailList: this.form.taskDetailList
      }));

      this.$router.push({
        name: 'AddIssue',
        params: {
          id: 'add'
        }
      });
      addTabs(this.$route.path, title);
    },

    /**
     * 质检操作
     */
    handleQualityCheck() {
      this.$DonMessage.info('质检功能开发中，敬请期待');
    },

    /**
     * 入库操作
     */
    handleInStock() {
      this.$DonMessage.info('入库功能开发中，敬请期待');
    },

    /**
     * 完工操作
     */
    handleComplete() {
      // 检查状态和实际完成时间
      if (this.form.status !== '已审核') {
        this.$DonMessage.warning('只有已审核状态的任务单才能进行完工操作');
        return;
      }

      if (this.form.actualFinishTime) {
        this.$DonMessage.warning('该任务单已完成，不能重复完工');
        return;
      }

      // 确认完工操作
      this.$confirm('确认要完成该生产任务单吗？', '完工确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用完工接口
        finishTaskOrder(this.taskOrderId).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success('完工成功');
            // 重新加载任务单详情
            this.selectTaskOrderInfo(this.taskOrderId);
          } else {
            this.$DonMessage.error(res.data.msg || '完工失败');
          }
        }).catch(err => {
          console.error('完工操作失败:', err);
          this.$DonMessage.error('完工操作失败，请稍后重试');
        });
      }).catch(() => {
        // 用户取消操作
        this.$DonMessage.info('已取消完工操作');
      });
    },

    /**
     * 物料操作
     */
    handleMaterial(command) {
      // 检查状态，只有已审核状态才能操作
      if (this.form.status !== '已审核') {
        this.$DonMessage.warning('只有已审核状态的任务单才能进行物料操作');
        return;
      }

      switch (command) {
        case 'materialRegister':
          // 物料登记
          this.handleMaterialRegister();
          break;
        case 'materialTrack':
          // 物料追踪
          this.handleMaterialTrack();
          break;
      }
    },

    /**
     * 物料登记
     */
    handleMaterialRegister() {
      const title = `物料登记-${this.form.taskNo}`;

      // 将行数据存储到sessionStorage中，避免路由参数传递复杂对象
      sessionStorage.setItem('materialRegisterRowData', JSON.stringify({
        id: this.taskOrderId,
        taskNo: this.form.taskNo,
        productName: this.form.productName,
        productCode: this.form.productCode,
        productModel: this.form.productModel,
        productUnit: this.form.productUnit,
        plannedProdQty: this.form.plannedProdQty,
        customerName: this.form.customerName,
        salesOrderNo: this.form.salesOrderNo,
        customerNo: this.form.customerNo,
        orderQty: this.form.orderQty,
        status: this.form.status
      }));

      this.$router.push({
        name: 'MaterialRegister',
        params: {
          id: this.taskOrderId
        }
      });
      addTabs(this.$route.path, title);
    },

    /**
     * 物料追踪
     */
    handleMaterialTrack() {
      const title = `物料追踪-${this.form.taskNo}`;
      this.$router.push({
        name: 'MaterialTrack',
        params: {
          id: this.taskOrderId
        }
      });
      addTabs(this.$route.path, title);
    },

    /**
     * 导出操作
     */
    handleExport(command) {
      // 构建导出参数，包含当前任务单信息
      const exportParams = {
        includeDetail: command === 'exportDetail',
        id: this.taskOrderId,
      };
      // 调用导出API
      exportTaskOrder(exportParams).then(res => {
        // 处理文件下载
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'});
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        const fileName = command === 'exportDetail' ?
          `生产任务单-${this.form.taskNo}（包含物料明细）.xlsx` :
          `生产任务单-${this.form.taskNo}.xlsx`;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$DonMessage.success('导出成功');
      }).catch(() => {
        this.$DonMessage.error('导出失败');
      });
    },

    /**
     * 根据任务单ID，获取任务单详情
     */
    selectTaskOrderInfo(taskOrderId) {
      const params = {id: taskOrderId};
      getOrderDetail(params).then((res) => {
        if (res.data.code === 100) {
          const data = res.data.data;
          console.log('加载任务单详情:', data);

          // 确保数据正确映射到表单
          this.form = {
            ...this.form, // 保持默认值
            ...data, // 覆盖后端数据
            // 确保销售订单号字段正确映射
            salesOrderNo: data.salesOrderNo || data.salesNo || '',
            customerName: data.customerName || '',
            customerNo: data.customerNo || '',
            orderQty: data.orderQty || null,
            // 确保领料单ID字段正确映射
            issueId: data.issueId || null,
            // 确保任务单明细正确映射
            taskDetailList: data.taskDetailList || data.taskOrderDetailList || []
          };

          console.log('映射后的表单数据:', this.form);
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      }).catch(err => {
        console.error('获取任务单详情失败:', err);
        this.$DonMessage.error('获取任务单详情失败');
      });
    },

    /**
     * 初始化页面
     */
    initPage() {
      // 获取路由参数中的任务单ID
      const {id} = this.$route.params;
      console.log('详情页面 - 路由参数:', this.$route.params);

      if (id && id !== 'detail') {
        this.taskOrderId = id;
        this.selectTaskOrderInfo(this.taskOrderId);
      } else {
        this.$DonMessage.error('任务单ID不能为空');
        removeTabs(this.$route);
      }
      collapseArea
      // this.contentSize();
    }
  },
  mounted() {
    this.initPage();
  },
  watch: {
    // 监听路由变化，解决切换tab后不初始化的问题
    '$route'(to, from) {
      if (to.name === 'TaskOrderDetail') {
        this.initPage();
      }
    }
  }
}
</script>

<style scoped>
#taskOrderDetail {
  /* 状态颜色 */
  --success-color: #1c903b;
  --error-color: #F44336;
  --warning-color: #fea927;
  --info-color: #0a8fe2;
  --required-color: #F56C6C;
}

/* 状态颜色样式 */
.abnormalColor {
  color: #E6A23C;
}

.warningColor {
  color: #fea927;
}

.successColor {
  color: #67C23A;
}

.errorColor {
  color: #F56C6C;
}

.blueColor {
  color: #409EFF;
}

/* 库存不足时的红色样式 */
.stock-insufficient {
  color: #F56C6C !important;
  font-weight: bold;
}
</style>
