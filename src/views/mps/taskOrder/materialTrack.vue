<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" label-width="100px" size="small">
        <!-- 物料编码 -->
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
          />
        </el-form-item>
        <!-- 物料名称 -->
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
        </div>
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                v-loading="loading"
                element-loading-text="加载中..."
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <!-- 任务单号 -->
        <el-table-column prop="taskOrderNo" label="任务单号" width="150">
          <template #default="{row}">
            <span class="linkStyle" @click="handleTaskOrderDetail(row)">{{ row.taskOrderNo }}</span>
          </template>
        </el-table-column>
        <!-- 物料编码 -->
        <el-table-column prop="materialCode" label="物料编码" width="120"/>
        <!-- 物料名称 -->
        <el-table-column prop="materialName" label="物料名称" width="150"/>
        <!-- 物料图片 -->
        <el-table-column label="物料图片" width="80">
          <template #default="{row}">
            <img v-if="row.imgUrl" class="pictureShow" :src="row.imgUrl" alt=""/>
          </template>
        </el-table-column>
        <!-- 规格型号 -->
        <el-table-column prop="materialModel" label="规格型号" width="120"/>
        <!-- 单位 -->
        <el-table-column prop="materialUnit" label="单位" width="80"/>
        <!-- 所需数量 -->
        <el-table-column prop="requiredQty" label="所需数量" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.requiredQty) }}
          </template>
        </el-table-column>
        <!-- 领料数量 -->
        <el-table-column prop="issuedQty" label="领料数量" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.issuedQty) }}
          </template>
        </el-table-column>
        <!-- 已用数量 -->
        <el-table-column prop="usedQty" label="已用数量" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.usedQty) }}
          </template>
        </el-table-column>
        <!-- 废料数量 -->
        <el-table-column prop="wasteQty" label="废料数量" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.wasteQty) }}
          </template>
        </el-table-column>
        <!-- 退料数量 -->
        <el-table-column prop="returnedQty" label="退料数量" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.returnedQty) }}
          </template>
        </el-table-column>
        <!-- 剩余数量 -->
        <el-table-column prop="remainingQty" label="剩余数量" width="100">
          <template #default="{ row }">
            <span :class="{ 'warning-text': row.remainingQty < 0 }">
              {{ formatNumber(row.remainingQty) }}
            </span>
          </template>
        </el-table-column>
        <!-- 备注 -->
        <el-table-column prop="remark" label="备注" min-width="150"/>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>


    </div>
    <!-- 业务规则说明 -->
    <div class="business-rules">
      <h4>业务规则说明：</h4>
      <ul>
        <li>已用数量：排除【废料数量】后的正常使用数</li>
        <li>废料数量：生产报损物料数</li>
        <li>剩余数量：剩余数量=领料数量-已用数量-废料数量-退料数量</li>
      </ul>
    </div>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import {exportMaterialTrack, getMaterialTrackPage} from "@/api/mpsmgt";
import {addTabs, tableHeight} from "@/assets/js/common";

export default {
  name: "MaterialTrack",
  components: {Pagination},
  data() {
    return {

      maximumHeight: 0,
      total: 0,
      loading: false,
      resultList: [],
      queryParams: {
        taskOrderId: null,
        materialCode: null,
        materialName: null,
        page: 1,
        pageSize: 10
      }
    }
  },
  methods: {
    /**
     * 格式化数字显示
     */
    formatNumber(value) {
      if (value === null || value === undefined) return '0';
      return Number(value).toFixed(2);
    },

    /**
     * 导出
     */
    exportClick() {
      const params = {
        query: {
          materialCode: this.queryParams.materialCode,
          materialName: this.queryParams.materialName
        }
      };

      exportMaterialTrack(params).then(res => {
        // 处理文件下载
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'});
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = '物料追踪导出.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$DonMessage.success('导出成功');
      }).catch(() => {
        this.$DonMessage.error('导出失败');
      });
    },


    /**
     * 查看任务单详情
     */
    handleTaskOrderDetail(row) {
      const title = `任务单详情-${row.taskOrderNo}`;
      this.$router.push({
        name: 'TaskOrderDetail',
        params: {
          id: row.taskOrderId
        }
      });
      addTabs(this.$route.path, title);
    },

    /**
     * 表格高度计算
     */
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },

    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },

    /**
     * 监听表格列宽变化
     */
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },

    /**
     * 时间格式化方法
     */
    conversion,

    /**
     * 查询
     */
    handleSearch() {
      const params = {
        query: {
          taskOrderId: this.queryParams.taskOrderId,
          materialCode: this.queryParams.materialCode,
          materialName: this.queryParams.materialName,
        },
        current: this.queryParams.page,
        size: this.queryParams.pageSize
      }
      // 发起请求查询接口
      this.dataList(params)
    },

    /**
     * 重置查询条件
     */
    handleReset() {
      this.queryParams = {
        taskOrderId: this.queryParams.taskOrderId,
        materialCode: '',
        materialName: '',
        page: 1,
        pageSize: 10
      }
      this.handleSearch()
    },


    /**
     * 分页查询数据
     */
    dataList(params) {
      this.loading = true;
      console.log("物料追踪分页查询参数：", params);
      getMaterialTrackPage(params).then(res => {
        this.loading = false;
        console.log("物料追踪数据", res);
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea();
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      }).catch(err => {
        this.loading = false;
        console.error('获取物料追踪数据失败:', err);
        this.$DonMessage.error('获取物料追踪数据失败');
      })
    }
  },
  mounted() {

    // 获取路由参数中的任务单ID
    const {id} = this.$route.params;
    console.log('物料登记页面 - 路由参数:', this.$route.params);

    if (id && id !== 'materialRegister') {
      this.queryParams.taskOrderId = id;
      this.handleSearch();
    } else {
      this.$DonMessage.error('任务单ID不能为空');
      this.onCancel();
    }
  },
  created() {
    console.log('MaterialTrack Component created');
  },
}
</script>

<style scoped>
.linkStyle {
  color: #409EFF;
  cursor: pointer;
}

.linkStyle:hover {
  text-decoration: underline;
}

/* 图片样式 */
.pictureShow {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

/* 警告文本样式 */
.warning-text {
  color: #F56C6C;
  font-weight: bold;
}

/* 业务规则说明样式 */
.business-rules {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
}

.business-rules h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.business-rules ul {
  margin: 0;
}

.business-rules li {
  margin-bottom: 5px;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.business-rules li:last-child {
  margin-bottom: 0;
}
</style>
