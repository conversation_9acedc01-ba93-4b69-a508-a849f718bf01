<template>
  <div class="layoutContainer addProduct" id="materialRegister">
    <div class="elTabtitle">
      <div>物料登记</div>
      <div>
        <el-button type="primary" @click="handleSubmit">{{ $t('button.submit') }}</el-button>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="任务单号">
                <span>{{ form.taskNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="单据日期">
                <span>{{ form.taskDate }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="商品编码">
                <span>{{ form.productCode }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="商品名称">
                <span>{{ form.productName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="规格型号">
                <span>{{ form.productModel }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="单位">
                <span>{{ form.productUnit }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="销售订单号">
                <span>{{ form.salesOrderNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户名称">
                <span>{{ form.customerName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户订单号">
                <span>{{ form.customerNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="订购数量">
                <span>{{ form.orderQty }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计划生产数量">
                <span>{{ form.plannedProdQty }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计划开始时间">
                <span>{{ form.plannedStartTime }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计划结束时间">
                <span>{{ form.plannedEndTime }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ form.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>

          <!-- 物料登记 -->
          <el-collapse-item name="material">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                物料登记
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="materialTable"
              :data="materialList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              show-summary
              :summary-method="getSummaries"
              v-loading="loading"
              element-loading-text="加载中..."
            >
              <el-table-column label="序号" width="60" type="index"></el-table-column>
              <el-table-column prop="materialCode" label="物料编码" min-width="100"/>
              <el-table-column prop="materialName" label="物料名称" min-width="120"/>
              <el-table-column prop="materialImage" label="图片" min-width="80">
                <template #default="{ row }">
                  <img v-if="row.imgUrl" class="pictureShow" :src="row.imgUrl" alt=""/>
                </template>
              </el-table-column>
              <el-table-column prop="materialModel" label="规格型号" min-width="80"/>
              <el-table-column prop="issuedQty" label="已领料数" :precision="2" min-width="80">
                <template #default="{ row }">
                  {{ formatNumber(row.issuedQty) }}
                </template>
              </el-table-column>
              <el-table-column prop="usedQty" label="本次用料" :precision="2" min-width="80">
                <template #default="{ row }">
                  <el-input
                    v-model="row.usedQty"
                    type="number"
                    size="small"
                    placeholder="请输入"
                    @change="handleUsedQtyChange(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="wasteQty" label="本次废料" :precision="2" min-width="80">
                <template #default="{ row }">
                  <el-input
                    v-model="row.wasteQty"
                    type="number"
                    size="small"
                    placeholder="请输入"
                    @change="handleWasteQtyChange(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="remainingQty" label="剩余数量" :precision="2" min-width="80">
                <template #default="{ row }">
                  <span :class="{ 'warning-text': row.remainingQty < 0 }">
                    {{ formatNumber(row.remainingQty) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="materialUnit" label="单位" min-width="80"/>
              <el-table-column prop="remark" label="本次废料备注" min-width="150">
                <template #default="{ row }">
                  <el-input
                    v-model="row.remark"
                    type="text"
                    size="small"
                    placeholder="请输入备注"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import {conversion} from "@/store/filters";
import {getMaterialTrackList, updateMaterialTrack} from "@/api/mpsmgt";
import {removeTabs} from "@/assets/js/common";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "MaterialRegister",
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      taskOrderId: "", // 任务单ID
      // 基本信息
      form: {
        taskNo: '',                     // 任务单号
        taskDate: '',                   // 单据日期
        productId: null,                // 生产商品ID
        productCode: null,              // 生产商品编码
        productName: null,
        productModel: null,
        productUnit: '',                   // 计量单位
        salesOrderNo: '',               // 销售订单编号
        customerName: '',               // 客户名称
        customerNo: '',                 // 客户订单号
        orderQty: null,                 // 订购数量
        plannedProdQty: null,           // 计划生产数量
        plannedStartTime: '',           // 计划开始时间
        plannedEndTime: '',             // 计划结束时间
        actualFinishTime: '',           // 实际完工时间
        remark: '',                     // 备注
        status: null,                   // 状态
        createdUserName: '',            // 制单人
        createdTime: '',                // 制单时间
        updatedUserName: '',            // 修改人
        updatedTime: '',                // 修改时间
        auditorName: '',                // 审核人
        auditTime: ''                   // 审核时间
      },
      // 物料登记数据
      materialList: [],
      // 加载状态
      loading: false,
      activeNames: ["base", "material"], // 全部展开
      statusOptions: [
        {label: '草稿', value: 0},
        {label: '待审核', value: 1},
        {label: '已审核', value: 2},
        {label: '已作废', value: 3},
        {label: '已完工', value: 4}
      ]
    }
  },
  methods: {
    /**
     * 获取状态样式类名
     */
    getStatusClass(status) {
      switch (status) {
        case 0:
          return 'abnormalColor'; // 草稿
        case 1:
          return 'warningColor'; // 待审核
        case 2:
          return 'successColor'; // 已审核
        case 3:
          return 'errorColor'; // 已作废
        case 4:
          return 'blueColor'; // 已完工
        default:
          return '';
      }
    },

    /**
     * 格式化状态显示
     */
    formatStatus(status) {
      const match = this.statusOptions.find(item => item.value === status);
      return match ? match.label : '未知状态';
    },

    /**
     * 格式化数字显示
     */
    formatNumber(value) {
      if (value === null || value === undefined) return '0';
      return Number(value).toFixed(2);
    },

    /**
     * 取消
     */
    onCancel() {
      // 关闭当前tab并回到上一页
      removeTabs(this.$route);
    },

    /**
     * 监听表格列宽变化
     */
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.materialTable.doLayout();
      });
    },

    /**
     * 时间格式化
     */
    conversion,

    /**
     * 设置基本信息（通过路由参数传递）
     */
    setBasicInfo(rowData) {
      if (rowData) {
        console.log('接收到的行数据:', rowData);

        // 直接使用传递过来的行数据设置基本信息
        this.form = {
          ...this.form, // 保持默认值
          taskNo: rowData.taskNo || '',
          taskDate: rowData.taskDate || '',
          productId: rowData.productId || null,
          productCode: rowData.productCode || '',
          productName: rowData.productName || '',
          productModel: rowData.productModel || '',
          productUnit: rowData.productUnit || '',
          salesOrderNo: rowData.salesOrderNo || '',
          customerName: rowData.customerName || '',
          customerNo: rowData.customerNo || '',
          orderQty: rowData.orderQty || null,
          plannedProdQty: rowData.plannedProdQty || null,
          plannedStartTime: rowData.plannedStartTime || '',
          plannedEndTime: rowData.plannedEndTime || '',
          actualFinishTime: rowData.actualFinishTime || '',
          remark: rowData.remark || '',
          status: rowData.status || null,
          createdUserName: rowData.createdUserName || '',
          createdTime: rowData.createdTime || '',
          updatedUserName: rowData.updatedUserName || '',
          updatedTime: rowData.updatedTime || '',
          auditorName: rowData.auditorName || '',
          auditTime: rowData.auditTime || ''
        };

        console.log('设置后的表单数据:', this.form);
      }
    },

    /**
     * 获取物料追踪数据
     */
    getMaterialTrackData(taskOrderId) {
      this.loading = true;
      const params = {
        taskOrderId: taskOrderId
      };

      getMaterialTrackList(params).then((res) => {
        this.loading = false;
        if (res.data.code === 100) {
          this.materialList = res.data.data || [];

          // 初始化数值字段，确保为数字类型
          this.materialList.forEach(item => {
            item.usedQty = item.usedQty || 0;
            item.wasteQty = item.wasteQty || 0;
            item.issuedQty = item.issuedQty || 0;
            item.remainingQty = item.remainingQty || 0;
            item.remark = item.remark || '';
          });

          console.log('物料追踪数据:', this.materialList);
        } else {
          this.$DonMessage.error(res.data.msg || '获取物料数据失败');
        }
      }).catch(err => {
        this.loading = false;
        console.error('获取物料追踪数据失败:', err);
        this.$DonMessage.error('获取物料追踪数据失败');
      });
    },

    /**
     * 处理本次用料变化
     */
    handleUsedQtyChange(row) {
      // 验证输入值
      const usedQty = parseFloat(row.usedQty) || 0;
      const issuedQty = parseFloat(row.issuedQty) || 0;

      if (usedQty > issuedQty) {
        this.$DonMessage.warning('本次用料不能大于已领料数');
        row.usedQty = issuedQty;
        return;
      }

      // 计算剩余数量
      row.remainingQty = issuedQty - usedQty - (parseFloat(row.wasteQty) || 0);
    },

    /**
     * 处理本次废料变化
     */
    handleWasteQtyChange(row) {
      // 验证输入值
      const wasteQty = parseFloat(row.wasteQty) || 0;
      const issuedQty = parseFloat(row.issuedQty) || 0;
      const usedQty = parseFloat(row.usedQty) || 0;

      if (wasteQty + usedQty > issuedQty) {
        this.$DonMessage.warning('用料和废料总数不能大于已领料数');
        row.wasteQty = Math.max(0, issuedQty - usedQty);
        return;
      }

      // 计算剩余数量
      row.remainingQty = issuedQty - usedQty - wasteQty;
    },

    /**
     * 表格汇总行
     */
    getSummaries(param) {
      const {columns, data} = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }

        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          if (column.property === 'issuedQty' || column.property === 'usedQty' || column.property === 'wasteQty') {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0).toFixed(2);
          } else if (column.property === 'remainingQty') {
            // 剩余数量显示总计
            const totalRemaining = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] = totalRemaining.toFixed(2);
          } else {
            sums[index] = '';
          }
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },

    /**
     * 提交物料登记数据
     */
    handleSubmit() {
      // 验证数据
      const invalidRows = this.materialList.filter(row => {
        const usedQty = parseFloat(row.usedQty) || 0;
        const wasteQty = parseFloat(row.wasteQty) || 0;
        const issuedQty = parseFloat(row.issuedQty) || 0;

        return usedQty + wasteQty > issuedQty;
      });

      if (invalidRows.length > 0) {
        this.$DonMessage.error('存在用料和废料总数大于已领料数的记录，请检查');
        return;
      }

      this.$confirm('确定要提交物料登记数据吗？', '提交确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        // 提交的数据
        updateMaterialTrack(this.materialList)
          .then(() => {
            this.loading = false;
            this.$DonMessage.success('提交成功');
            // 提交成功后关闭页面
            this.onCancel();
          })
          .catch(err => {
            this.loading = false;
            console.error('提交失败:', err);
            this.$DonMessage.error('提交失败');
          });
      });
    },

    /**
     * 初始化页面
     */
    initPage() {
      // 获取路由参数中的任务单ID
      const {id} = this.$route.params;
      console.log('物料登记页面 - 路由参数:', this.$route.params);

      if (id && id !== 'materialRegister') {
        this.taskOrderId = id;

        // 从sessionStorage中获取行数据
        const rowDataStr = sessionStorage.getItem('materialRegisterRowData');
        if (rowDataStr) {
          try {
            const rowData = JSON.parse(rowDataStr);
            console.log('物料登记页面 - 从sessionStorage获取的行数据:', rowData);
            this.setBasicInfo(rowData);
          } catch (error) {
            console.error('解析sessionStorage中的行数据失败:', error);
            this.$DonMessage.error('获取基本信息失败');
          }
        }

        // 获取物料追踪数据
        this.getMaterialTrackData(this.taskOrderId);
      } else {
        this.$DonMessage.error('任务单ID不能为空');
        this.onCancel();
      }
    }
  },
  mounted() {
    this.initPage();
  },
  watch: {
    // 监听路由变化，解决切换tab后不初始化的问题
    '$route'(to, from) {
      if (to.name === 'MaterialRegister') {
        this.initPage();
      }
    }
  }
}
</script>

<style scoped>
#materialRegister {
  /* 状态颜色 */
  --success-color: #1c903b;
  --error-color: #F44336;
  --warning-color: #fea927;
  --info-color: #0a8fe2;
  --required-color: #F56C6C;
}

/* 状态颜色样式 */
.abnormalColor {
  color: #E6A23C;
}

.warningColor {
  color: #fea927;
}

.successColor {
  color: #67C23A;
}

.errorColor {
  color: #F56C6C;
}

.blueColor {
  color: #409EFF;
}

/* 图片样式 */
.pictureShow {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

/* 警告文本样式 */
.warning-text {
  color: #F56C6C;
  font-weight: bold;
}
</style>
