<template>
  <div class="layoutContainer actionFlowDetail" id="addTaskOrder">
    <div class="elTabtitle">
      <div v-if="this.stateType === 'add'">新增生产任务单</div>
      <div v-if="this.stateType === 'edit'">编辑生产任务单</div>
      <div>
        <el-button type="primary" @click="onSubmit('save')" v-if="this.stateType === 'add'">保存</el-button>
        <el-button plain @click="onSubmit('submit')" v-if="this.stateType === 'add'">提交</el-button>
        <el-button type="primary" @click="onEdit('save')" v-if="this.stateType === 'edit'">保存</el-button>
        <el-button plain @click="onEdit('submit')" v-if="this.stateType === 'edit'">提交</el-button>
        <el-button plain @click="onSubmitAndAudit">提交并审核</el-button>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelSix">
                <el-form-item label="任务单号" prop="taskNo">
                  <el-input v-model="form.taskNo" placeholder="任务单号由系统生成" disabled/>
                </el-form-item>
                <el-form-item label="单据日期" prop="taskTime">
                  <el-date-picker v-model="form.taskDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd" disabled></el-date-picker>
                </el-form-item>
                <el-form-item label="商品编码" prop="productId">
                  <selectInput
                    ref="selectInput"
                    v-model="form.productId"
                    :inputType="'productInfo'"
                    :inputParam="form.productCode"
                    @select="onProductSelect"
                    placeholder="请选择商品"
                  ></selectInput>
                </el-form-item>

                <el-form-item label="商品名称" prop="productName">
                  <el-input v-model="form.productName" placeholder="" disabled/>
                </el-form-item>
                <el-form-item label="规格型号" prop="productModel">
                  <el-input v-model="form.productModel" placeholder="" disabled/>
                </el-form-item>
                <el-form-item label="单位" prop="productUnit">
                  <el-input v-model="form.productUnit" placeholder="" disabled/>
                </el-form-item>
                <el-form-item label="销售订单号" prop="salesOrderNo">
                  <SelectSalesOrder
                    ref="selectSalesOrderInput"
                    v-model="form.salesOrderNo"
                    :input-param="form.salesOrderNo"
                    input-type="salesOrder"
                    placeholder="请选择销售订单"
                    @select="onSalesOrderSelect"
                  ></SelectSalesOrder>
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                  <el-input v-model="form.customerName" placeholder="" disabled/>
                </el-form-item>
                <el-form-item label="客户订单号" prop="customerNo">
                  <el-input v-model="form.customerNo" placeholder="" disabled/>
                </el-form-item>
                <el-form-item label="订购数量" prop="orderQty">
                  <el-input v-model="form.orderQty" placeholder="" disabled/>
                </el-form-item>
                <el-form-item label="计划生产数量" prop="plannedProdQty">
                  <el-input-number v-model="form.plannedProdQty" :precision="2" :step="1" :min="1" size="mini" controls-position="right" placeholder="请输入计划生产数量" @change="onPlannedProdQtyChange"/>
                </el-form-item>
                <el-form-item label="计划开始时间" prop="plannedStartTime">
                  <el-date-picker v-model="form.plannedStartTime" type="date" placeholder="选择开始时间" value-format="yyyy-MM-dd"></el-date-picker>
                </el-form-item>
                <el-form-item label="计划结束时间" prop="plannedEndTime">
                  <el-date-picker v-model="form.plannedEndTime" type="date" placeholder="选择结束时间" value-format="yyyy-MM-dd"></el-date-picker>
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500"
                                show-word-limit/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>

          <!-- 明细信息 -->
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                明细信息
              </span>
            </template>
            <div class="tableHandle spaceBetween">
              <div>
                <el-button @click="addMaterial">
                  <i class="addProducts-icon"></i>添加物料
                </el-button>
<!--                <el-button @click="applyForMaterial">
                  <i class="el-icon-shopping-cart-2"></i>申请采购物料
                </el-button>-->
                <span class="errorColor" style="margin-left: 10px;font-size: 14px">(库存列内容，标红代表库存不足)</span>
              </div>
              <div>
                <el-button type="text" icon="setIcon-icon">批量设置</el-button>
                <el-button type="text" icon="deleteRed-icon">批量删除</el-button>
              </div>
            </div>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.taskDetailList"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="materialCode" label="物料编码" width="120"/>
              <el-table-column prop="materialName" label="物料名称" width="150"/>
              <el-table-column prop="imageUrl" label="图片" width="100">
                <template #default="{ row }">
                  <img v-if="row.imageUrl != ''" class="pictureShow" :src="row.imageUrl" alt=""/>
                </template>
              </el-table-column>
              <el-table-column prop="materialModel" label="规格型号" width="120"/>
              <el-table-column prop="unitQty" label="单件用量" width="120"/>

              <el-table-column prop="quantity" label="所需数量" width="100">
                <template #header>
                  <span class="required-field">所需数量</span>
                </template>
                <template #default="scope">
                  <el-input-number v-model="scope.row.quantity" :precision="2" :step="1" :min="0" size="mini" controls-position="right" style="width: 100%;" @change="onQuantityChange(scope.row)"/>
                </template>
              </el-table-column>


              <el-table-column prop="currentInventory" label="库存" width="100">
                <template #default="scope">
                  <span :class="{ 'stock-insufficient': scope.row.currentInventory < scope.row.quantity }">
                    {{ scope.row.currentInventory }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="productUnit" label="单位" width="80"/>
              <el-table-column prop="unitPrice" label="单价(¥)" width="80"/>
              <el-table-column prop="amount" label="金额(¥)" width="80"/>
              <el-table-column prop="attritionRate" label="损耗率(%)" width="100"/>
              <el-table-column prop="warehouseName" label="仓库" width="100"/>
              <el-table-column prop="locationName" label="库位" width="100"/>

              <el-table-column label="备注" prop="remark" min-width="110">
                <template slot-scope="scope">
                  <div class="rowEditShow">
                    <el-input
                      v-if="
                      rowEditIndex === scope.$index &&
                      columnEditIndex === scope.column.id"
                      ref="tableRowInputRef"
                      v-model="scope.row.remark"
                      @blur="onInputTableBlur(scope)"
                    >
                    </el-input>
                    <div
                      v-else
                      :class="'textShow ' + scope.$index + '_remark ' + scope.row.id + ' remark'"
                      @dblclick="dbClickCell(scope)"
                    >
                      <span>{{ scope.row.remark }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton"
                             @click="delClick(scope.$index, form.taskDetailList)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <!-- 更多信息 -->
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="制单人">
                {{ form.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="制单日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="this.stateType == 'edit'">
                {{ form.updatedUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="this.stateType == 'edit'">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="审核人" v-if="form.auditorName">
                {{ form.auditorName }}
              </el-descriptions-item>
              <el-descriptions-item label="审核时间" v-if="form.auditTime">
                {{ form.auditTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="实际完工时间" v-if="form.actualFinishTime">
                {{ form.actualFinishTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 添加物料弹窗 -->
    <el-dialog v-if="dialogVisibleAddMaterial"
               :visible.sync="dialogVisibleAddMaterial"
               title="添加物料"
               width="1400px !important"
               :destroy-on-close="true"
               @close="onAddMaterialDialogClose">
      <AddMaterialInfo @select="handleSelectMaterial"></AddMaterialInfo>
    </el-dialog>

  </div>
</template>

<script>
import {conversion} from "@/store/filters";
import {getUnitList, listProductByIds} from "@/api/basicmgt";
import AddMaterialInfo from "@/views/purchase/purchaseApply/AddMaterialInfo.vue";
import {addTaskOrder, editTaskOrder, getBomList, getOrderDetail} from "@/api/mpsmgt";
import {beforeRouteInfo, collapseArea, getContentData, removeTabs} from "@/assets/js/common";
import selectInput from "@/components/selectInput/selectInput.vue";
import SelectSalesOrder from "@/views/purchase/purchaseApply/SelectSalesOrder.vue";
import Decimal from 'decimal.js'
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "AddTaskOrder",
  components: {AddMaterialInfo, selectInput, SelectSalesOrder},
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      dialogVisibleAddMaterial: false, // 添加商品弹窗
      taskOrderId: "", // 任务单ID
      stateType: "", // 类型（add/edit）
      // 基本信息
      form: {
        taskNo: '',                     // 任务单号
        taskDate: '',                   // 单据日期

        productId: null,                // 生产商品ID
        productCode: null,              // 生产商品编码
        productName: null,
        productModel: null,
        productUnit: '',                   // 计量单位
        salesOrderNo: '',               // 销售订单编号
        salesOrderId: null,             // 销售订单ID
        customerName: '',               // 客户名称
        customerNo: '',                 // 客户订单号
        orderQty: null,                 // 订购数量

        plannedProdQty: 1,           // 计划生产数量
        plannedStartTime: '',           // 计划开始时间
        plannedEndTime: '',             // 计划结束时间
        actualFinishTime: '',           // 实际完工时间
        remark: '',                     // 备注
        status: null,                   // 状态

        needAudit: false,
        taskDetailList: []         // 任务单详情列表
      },
      rules: {
        taskDate: [{required: true, message: '单据日期不能为空', trigger: ['blur', 'change']}],
        productId: [{required: true, message: '商品编码不能为空', trigger: ['blur', 'change']}],
        plannedProdQty: [{required: true, message: '计划生产数量不能为空', trigger: ['blur', 'change']}],
        plannedStartTime: [{required: true, message: '计划开始时间不能为空', trigger: ['blur', 'change']}],
        plannedEndTime: [{required: true, message: '计划结束时间不能为空', trigger: ['blur', 'change']}]
      },
      unitData: [], // 计量单位
      rowEditIndex: "",
      columnEditIndex: "",
      activeNames: ["base", "product", "more"], // 全部展开
    }
  },
  methods: {
    /**
     * 删除商品
     */
    delClick(index, list) {
      console.log("delClick", index, list);
      if (!Array.isArray(list)) return;
      list.splice(index, 1);
    },

    /**
     * 处理选择的商品，根据productId查询信息
     */
    handleSelectMaterial(productIds) {
      console.log("selectProductIds", productIds);
      this.selectProductsInfo(productIds);
    },

    /**
     * 根据productId查询商品详细信息
     */
    selectProductsInfo(productIds) {
      const params = {
        ids: productIds
      };
      listProductByIds(params).then(res => {
        if (res.data.code === 100) {
          // 为每个商品添加任务单所需的字段
          let quantity = new Decimal(this.form.plannedProdQty).mul(new Decimal(1)).toFixed(2);
          const uniqueCodes = new Set(this.form.taskDetailList.map(item => item.code));
          const products = res.data.data.filter(item => !uniqueCodes.has(item.code)).map(item => {
            let amount = new Decimal(quantity).mul(new Decimal(item.purchasePrice || 0)).toFixed(2);
            return ({
              ...item,
              materialId: item.id,
              materialCode: item.code,
              materialName: item.name,
              materialModel: item.model,
              materialUnit: item.unit,
              warehouseId: item.warehouseId,
              locationId: item.locationId,
              unitQty: 1,
              quantity: quantity,
              unitPrice: item.purchasePrice,
              amount: amount,
              attritionRate: null,
              remark: item.remark || ''
            });
          });
          console.log("products", products);
          this.form.taskDetailList.push(...products);
          console.log("this.form.taskDetailList", this.form.taskDetailList);
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      });
    },

    /**
     * 编辑保存
     */
    onEdit(type) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (type === "save") {
            // 修改，保持原状态
          } else if (type === "submit") {
            // 修改并提交审核
            this.form.status = '待审核';
          }

          editTaskOrder(this.form).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("修改成功");
              // 编辑成功后返回上一页
              removeTabs(this.$route);

            } else {
              this.$DonMessage.error(res.data.msg);
            }
          });
        } else {
          this.$DonMessage.warning("请先完善表单信息");
          return false;
        }
      });
    },

    /**
     * 保存
     */
    onSubmit(type) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 所有校验通过，允许提交
          if (type === "save") {
            // 保存状态设置为草稿
            this.form.status = '草稿';
          } else if (type === "submit") {
            // 保存状态设置为待审核
            this.form.status = '待审核';
          }
          addTaskOrder(this.form).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("保存成功");
              removeTabs(this.$route);
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          });
        } else {
          // 有校验不通过，阻止提交
          this.$DonMessage.warning("请先完善表单信息");
          return false;
        }
      });
    },

    /**
     * 提交并审核
     */
    onSubmitAndAudit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 设置为待审核状态
          this.form.status = '待审核';
          this.form.needAudit = true;

          // 根据是新增还是编辑模式调用不同的API
          const apiCall = this.stateType === 'add' ? addTaskOrder : editTaskOrder;

          apiCall(this.form).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("提交并审核成功");
              removeTabs(this.$route);
            } else {
              this.$DonMessage.error(res.data.msg);
            }
          });
        } else {
          this.$DonMessage.warning("请先完善表单信息");
          return false;
        }
      });
    },

    /**
     * 计划生产数量变化时，重新计算物料所需数量
     */
    onPlannedProdQtyChange(newValue) {
      console.log('计划生产数量变化:', newValue);
      if (newValue && newValue > 0 && this.form.taskDetailList.length > 0) {
        // 重新计算每个物料的所需数量
        this.form.taskDetailList.forEach(item => {
          if (item.unitQty) {
            // 所需数量 = 单件用量 × 计划生产数量
            item.quantity = new Decimal(item.unitQty).mul(new Decimal(newValue)).toFixed(2);
            // 同时更新金额
            if (item.unitPrice) {
              item.amount = new Decimal(item.quantity).mul(new Decimal(item.unitPrice)).toFixed(2);
            }
          }
        });
      }
    },

    /**
     * 所需数量变化时，重新计算对应的金额
     */
    onQuantityChange(row) {
      console.log('所需数量变化:', row);
      if (row.quantity && row.unitPrice) {
        // 金额 = 所需数量 × 单价
        row.amount = new Decimal(row.quantity).mul(new Decimal(row.unitPrice)).toFixed(2);
        console.log('更新后的金额:', row.amount);
      } else if (!row.quantity || row.quantity <= 0) {
        // 如果所需数量为空或小于等于0，金额设为0
        row.amount = 0;
      }
    },

    /**
     * 取消
     */
    onCancel() {
      // 关闭当前tab并回到上一页
      removeTabs(this.$route);
    },

    /**
     * 监听表格列宽变化
     */
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    /**
     * 时间格式化
     */
    conversion,

    /**
     * 双击单元格
     */
    dbClickCell(scope) {
      var _this = this;
      _this.rowEditIndex = scope.$index;
      _this.columnEditIndex = scope.column.id;
      _this.$nextTick(() => {
        _this.$refs.tableRowInputRef.focus();
      });
    },

    /**
     * 输入框失去焦点
     */
    onInputTableBlur(scope) {
      const _this = this;
      _this.rowEditIndex = "";
      _this.columnEditIndex = "";
    },

    /**
     * 添加商品
     */
    addMaterial() {
      this.dialogVisibleAddMaterial = true;
    },
    /**
     * 申请采购物料
     */
    applyForMaterial() {
      this.$DonMessage.info('申请采购物料功能开发中，敬请期待');
    },

    /**
     * 关闭添加商品弹窗
     */
    onAddMaterialDialogClose() {
      this.dialogVisibleAddMaterial = false;
    },

    /**
     * 商品选择回调（来自selectInput组件）
     */
    onProductSelect(product) {
      console.log('选择商品:', product);
      if (product && product.id) {
        // 填充基本信息中的商品字段
        this.form.productId = product.id;
        this.form.productCode = product.code;
        this.form.productName = product.name;
        this.form.productModel = product.model;
        this.form.productUnit = product.unit;

        // 根据商品ID获取BOM物料列表
        this.loadBomMaterials(product.id);

        this.$DonMessage.success('商品选择成功');
        console.log("完整表单", this.form)
      }
    },

    /**
     * 销售订单选择回调（来自SelectSalesOrder组件）
     */
    onSalesOrderSelect(salesOrder) {
      console.log('选择销售订单:', salesOrder);
      if (salesOrder && salesOrder.id) {
        this.form.salesOrderNo = salesOrder.salesNo;
        this.form.salesOrderId = salesOrder.id; // 添加销售订单ID
        this.form.customerName = salesOrder.customerName;
        this.form.customerNo = salesOrder.customerNo;
        this.form.orderQty = salesOrder.quantity;
        this.$DonMessage.success('销售订单选择成功');
        console.log('表单数据更新后:', this.form);
      }
    },

    /**
     * 获取计量单位
     */
    getUnitData() {
      var param = {};
      getUnitList(param).then((res) => {
        if (res.data.code === 100) {
          this.unitData = res.data.data;
        }
      });
    },

    /**
     * 根据商品ID加载BOM物料列表
     */
    loadBomMaterials(productId) {
      console.log("产品ID", productId)
      const param = new URLSearchParams();
      param.append('productId', productId);
      param.append('withStockInfo', true);
      getBomList(param).then(res => {
        if (res.data.code === 100) {
          const bomData = res.data.data || [];
          if (bomData.length === 0) {
            this.form.taskDetailList = [];
            this.$DonMessage.info('该商品暂无BOM信息，请先维护物料或者手动添加物料');
            return;
          }

          console.log("bomData", bomData)
          // 将BOM物料转换为任务单明细格式
          const plannedQty = this.form.plannedProdQty;
          const materials = bomData.map(item => {
            const unitQty = item.quantity || 1;
            const quantity = new Decimal(unitQty).mul(new Decimal(plannedQty)).toFixed(2);
            const unitPrice = item.unitPrice || 0;
            const amount = new Decimal(quantity).mul(new Decimal(unitPrice)).toFixed(2);
            return {
              ...item,
              id: null, // 重置ID，因为这是新的任务单明细记录
              materialId: item.materialId,
              materialCode: item.code,
              materialName: item.name,
              materialModel: item.model,
              materialUnit: item.unit,
              unitQty: unitQty,
              quantity: quantity,
              unitPrice: unitPrice,
              amount: amount,
              attritionRate: item.attritionRate || 0,
              warehouseName: item.warehouseName || '',
              locationName: item.locationName || '',
              remark: item.remark || '',
              currentInventory: item.currentInventory || 0,
              lockQty: item.lockQty || 0,
            };
          });
          console.log('materials', materials);
          this.form.taskDetailList = materials;
          this.$DonMessage.success(`已自动加载${materials.length}种物料`);
        } else {
          this.form.taskDetailList = [];
          this.$DonMessage.info('该商品暂无BOM信息，请先维护物料或者手动添加物料');
        }
      }).catch(() => {
        this.form.taskDetailList = [];
        this.$DonMessage.info('获取BOM信息失败，请先维护物料或者手动添加物料');
      });
    },

    /**
     * 根据任务单ID，获取任务单详情
     */
    async selectTaskOrderInfo(taskOrderId) {
      try {
        const params = {id: taskOrderId};
        const res = await getOrderDetail(params)
        if (res.data.code === 100) {
          this.form = res.data.data;
          console.log('加载任务单详情:', this.form);
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.onCancel()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.onCancel()
      }
      // }).catch(err => {
      //   console.error('获取任务单详情失败:', err);
      //   this.$DonMessage.error('获取任务单详情失败');
      // });
    },

    /**
     * 计算区域高度
     */
    // areaHeight() {
    //   setTimeout(() => {
    //     var allHeight = $(".layoutContainer").height();
    //     var titleHeight = $(".elTabtitle").height();
    //     var marginVal = 2 * Number($(".elTabsContent .el-collapse-item").css("marginTop").split("px")[0]);
    //     var val = allHeight - titleHeight - marginVal - 5;
    //     $(".elTabsContent").css("height", val);
    //   }, 60);
    // },

    /**
     * 内容尺寸调整
     */
    // contentSize() {
    //   var _this = this;
    //   _this.areaHeight();
    //   window.addEventListener("resize", function () {
    //     _this.areaHeight();
    //   });
    // },

    /**
     * 初始化新增模式
     */
    initAddMode() {
      this.form = {
        taskNo: '',                     // 任务单号
        taskDate: '',                   // 单据日期

        productId: null,                // 生产商品ID
        productCode: null,              // 生产商品编码
        productName: null,
        productModel: null,
        productUnit: '',                   // 计量单位
        salesOrderNo: '',               // 销售订单编号
        salesOrderId: null,             // 销售订单ID
        customerName: '',               // 客户名称
        customerNo: '',                 // 客户订单号
        orderQty: null,                 // 订购数量

        plannedProdQty: 1,           // 计划生产数量
        plannedStartTime: '',           // 计划开始时间
        plannedEndTime: '',             // 计划结束时间
        actualFinishTime: '',           // 实际完工时间
        remark: '',                     // 备注
        status: null,                   // 状态

        taskDetailList: []         // 任务单详情列表
      };
      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      })
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.form.createdTime = new Date().getTime();
      this.form.createdUserName = this.$store.state.realName;
      this.form.taskDate = new Date().toISOString().slice(0, 10); // 设置默认日期为今天
    },

    /**
     * 初始化编辑模式
     */
    initEditMode() {
      // 加载任务单详情数据
      this.selectTaskOrderInfo(this.taskOrderId);
    },

    async initPage() {
      console.log('路由参数:', this.$route.params);
      const {id} = this.$route.params;

      if (id === 'add') {
        // 新增模式
        this.stateType = "add";
        await this.initAddMode();
      } else {
        // 编辑模式
        this.stateType = "edit";
        this.taskOrderId = id;
        await this.initEditMode();
      }
      getContentData(this);
      collapseArea();
      // this.contentSize();
    },
  },
  mounted() {
    this.initPage();
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      if (to.name === 'AddTaskOrder') {
        beforeRouteInfo(from.path, this.form);
        this.initPage();
      }
    }
  }
}
</script>

<style scoped>
#addTaskOrder {
  /* 状态颜色 */
  --success-color: #1c903b;
  --error-color: #F44336;
  --warning-color: #fea927;
  --info-color: #0a8fe2;
  --required-color: #F56C6C;
}

/* 库存不足时的红色样式 */
.stock-insufficient {
  color: #F56C6C !important;
  font-weight: bold;
}

</style>
