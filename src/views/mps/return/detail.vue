<template>
  <div class="layoutContainer actionFlowDetail" id="returnDetail">
    <div class="elTabtitle">
      <div>生产退料单详情</div>
      <div>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <!-- 基本信息 -->
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="退料单号">
                <span>{{ form.returnNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="单据日期">
                <span>{{ form.returnTime }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="源单据号">
                <span>{{ form.sourceNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="退料人">
                <span>{{ form.returnUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <span :class="getStatusClass(form.status)">
                  {{ formatStatus(form.status) }}
                </span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ form.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>

          <!-- 明细信息 -->
          <el-collapse-item name="detail">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                明细信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.details"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="sourceNo" label="源单据号" width="150"/>
              <el-table-column prop="materialCode" label="物料编码" width="120"/>
              <el-table-column prop="materialName" label="物料名称" width="150"/>
              <el-table-column prop="materialModel" label="规格型号" width="120"/>
              <el-table-column prop="materialUnit" label="单位" width="80"/>
              <el-table-column prop="warehouseName" label="仓库" width="100"/>
              <el-table-column prop="locationName" label="库位" width="100"/>
              <el-table-column prop="issuedQty" label="已领数量" width="100">
                <template #default="scope">
                  {{ scope.row.issuedQty || 0 }}
                </template>
              </el-table-column>
              <el-table-column prop="returnedQty" label="已退数量" width="100">
                <template #default="scope">
                  {{ scope.row.returnedQty || 0 }}
                </template>
              </el-table-column>
              <el-table-column prop="returnQty" label="本次退料" width="100">
                <template #default="scope">
                  {{ scope.row.returnQty || 0 }}
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" min-width="110">
                <template slot-scope="scope">
                  <span>{{ scope.row.remark }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <!-- 更多信息 -->
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="制单人">
                <span>{{ form.createdUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="制单日期">
                <span>{{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="form.updatedUserName">
                <span>{{ form.updatedUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="form.updatedTime">
                <span>{{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="审核人" v-if="form.auditorName">
                <span>{{ form.auditorName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="审核时间" v-if="form.auditTime">
                <span>{{ form.auditTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import {conversion} from "@/store/filters";
import {getReturnDetail} from '@/api/mpsmgt'
import {removeTabs, collapseArea} from "@/assets/js/common";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "ReturnDetail",
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      returnId: "", // 退料单ID
      // 基本信息
      form: {
        returnNo: '',                   // 退料单号
        sourceNo: '',                   // 源单据号
        returnTime: '',                 // 单据日期
        returnUser: null,               // 退料人ID
        returnUserName: '',             // 退料人姓名
        remark: '',                     // 备注
        status: null,                   // 状态
        details: [],                    // 退料单详情列表
        createdUserName: '',            // 制单人
        createdTime: '',                // 制单时间
        updatedUserName: '',            // 修改人
        updatedTime: '',                // 修改时间
        auditorName: '',                // 审核人
        auditTime: ''                   // 审核时间
      },
      activeNames: ["base", "detail", "more"], // 全部展开
      statusOptions: [
        {label: '草稿', value: '草稿'},
        {label: '待审核', value: '待审核'},
        {label: '已审核', value: '已审核'},
        {label: '已作废', value: '已作废'},
        {label: '已完成', value: '已完成'}
      ]
    }
  },
  methods: {
    /**
     * 获取状态样式类名
     */
    getStatusClass(status) {
      switch (status) {
        case '草稿':
          return 'abnormalColor'; // 草稿
        case '待审核':
          return 'warningColor'; // 待审核
        case '已审核':
          return 'successColor'; // 已审核
        case '已作废':
          return 'errorColor'; // 已作废
        case '已完成':
          return 'blueColor'; // 已完成
        default:
          return '';
      }
    },

    /**
     * 格式化状态显示
     */
    formatStatus(status) {
      const match = this.statusOptions.find(item => item.value === status);
      return match ? match.label : status || '未知状态';
    },

    /**
     * 取消
     */
    onCancel() {
      // 关闭当前tab并回到上一页
      removeTabs(this.$route);
    },

    /**
     * 监听表格列宽变化
     */
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },

    /**
     * 时间格式化
     */
    conversion,

    /**
     * 根据退料单ID，获取退料单详情
     */
    selectReturnInfo(returnId) {
      const params = {id: returnId};
      getReturnDetail(params).then((res) => {
        if (res.data.code === 100) {
          const data = res.data.data;
          console.log('加载退料单详情:', data);

          // 确保数据正确映射到表单
          this.form = {
            ...this.form, // 保持默认值
            ...data, // 覆盖后端数据
            // 确保详情列表正确映射
            details: data.details || data.returnDetailList || []
          };

          console.log('映射后的表单数据:', this.form);
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      }).catch(err => {
        console.error('获取退料单详情失败:', err);
        this.$DonMessage.error('获取退料单详情失败');
      });
    },

    /**
     * 初始化页面
     */
    initPage() {
      // 获取路由参数中的退料单ID
      const { id } = this.$route.params;
      console.log('详情页面 - 路由参数:', this.$route.params);

      if (id) {
        this.returnId = id;
        this.selectReturnInfo(this.returnId);
      } else {
        this.$DonMessage.error('退料单ID不能为空');
        this.onCancel();
      }
    }
  },
  mounted() {
    this.initPage();
  },
  watch: {
    // 监听路由变化，解决切换tab后不初始化的问题
    '$route'(to, from) {
      if (to.name === 'ReturnDetail') {
        this.initPage();
      }
    }
  }
}
</script>

<style scoped>
#returnDetail {
  /* 状态颜色 */
  --success-color: #1c903b;
  --error-color: #F44336;
  --warning-color: #fea927;
  --info-color: #0a8fe2;
  --required-color: #F56C6C;
}

/* 状态颜色样式 */
.abnormalColor {
  color: #E6A23C;
}

.warningColor {
  color: #fea927;
}

.successColor {
  color: #67C23A;
}

.errorColor {
  color: #F56C6C;
}

.blueColor {
  color: #409EFF;
}
</style>
