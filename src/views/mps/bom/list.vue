<template>
  <div id="bomList" class="layoutContainer commodityManage">

    <div class="infoDetail">
      <el-row>
        <el-col :span="9" class="leftData">
          <div>
            <div class="topButton">
              <span>选择商品</span>
            </div>

            <div class="secondFloat">
              <el-form :inline="true" :label-width="$labelFour" ref="from" :model="from" class="demo-form-inline">
                <!--                <el-form-item label="商品类别" prop="categoryName">
                                  <selectInput ref="selectInput" v-model="from.categoryName" :inputType="'categoryName'"
                                               @select="onInputSearch('from', $event)" placeholder="请选择商品类别"></selectInput>
                                </el-form-item>-->
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model.trim="from.name" placeholder="请选择商品名称"></el-input>
                </el-form-item>
                <el-form-item label="商品编码" prop="code">
                  <el-input v-model.trim="from.code" placeholder="请选择商品编码"></el-input>
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="onSearch">{{ $t('button.search') }}</el-button>
                  <el-button plain @click="reset()">{{ $t('button.reset') }}</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div>
              <el-table style="width: 100%;" ref="table" highlight-current-row :data="commodityList" border stripe
                        :max-height="maximumHeight" @header-dragend="changeColWidth" @row-click="handleRowClick">
                <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
                <el-table-column label="序号" width="50" type="index"></el-table-column>

                <el-table-column label="商品类别" prop="categoryName" width="95">
                  <template slot="header" slot-scope="scope">
                    <FilterHeader :column="scope.column" field-name="商品类别" field-prop="categoryName" filter-type="text"
                                  @tableFilter="tableFilter" @resetFilter="tableFilterReset"/>
                  </template>
                  <template slot-scope="{row}">
                    <span>{{ row.categoryName }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="商品编码" prop="code" min-width="130" sortable>
                  <template slot="header" slot-scope="scope">
                    <FilterHeader sort="sort" :column="scope.column" field-name="商品编码" field-prop="code"
                                  filter-type="text" filter-label="商品编码" @tableFilter="tableFilter"
                                  @resetFilter="tableFilterReset"/>
                  </template>
                  <template slot-scope="{row}">
                    <span>{{ row.code }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="name" min-width="120">
                  <template slot="header" slot-scope="scope">
                    <FilterHeader :column="scope.column" field-name="商品名称" field-prop="name" filter-type="text"
                                  filter-label="商品名称" @tableFilter="tableFilter" @resetFilter="tableFilterReset"/>
                  </template>
                  <template slot-scope="{row}">
                    <span>{{ row.name }}</span>
                  </template>
                </el-table-column>


                <!--                <el-table-column label="品牌" porp="brand" width="80"></el-table-column>-->
                <el-table-column label="规格型号" prop="model" width="100"></el-table-column>
                <el-table-column label="物料清单" prop="bomConfigured" width="90">
                  <template slot="header" slot-scope="scope">
                    <FilterHeader :column="scope.column" field-name="物料清单" field-prop="bomConfigured" filter-type="radio"
                                  :custom-arr-list="statusList" @tableFilter="tableFilter" @resetFilter="tableFilterReset"/>
                  </template>
                  <template slot-scope="{row}">
                    <span v-if="!row.bomConfigured" class="errorColor">未设置</span>
                    <span v-if="row.bomConfigured" class="successColor">已设置</span>
                  </template>
                </el-table-column>
                <!--                <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>-->
              </el-table>
              <Pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize"
                          @pagination="dataList"/>
            </div>
          </div>
        </el-col>
        <el-col :span="15" class="fromRight">

          <div class="rightTitle">
            <span>设置物料工序</span>
          </div>

          <!-- 添加商品信息显示区域 -->
          <div class="productInfo">
            <div class="productInfoItem">
              <el-col :span="8">
                <span class="label">商品编码：</span>
                <span class="value">{{ selectedProduct.code || '' }}</span>
              </el-col>
              <el-col :span="16">
                <span class="label">商品名称：</span>
                <span class="value">{{ selectedProduct.name || '' }}</span>
              </el-col>
            </div>
          </div>

          <!-- 添加Tab页签 -->
          <el-tabs v-model="activeTab" class="bomTabs">
            <!-- 所需物料 tab -->
            <el-tab-pane label="所需物料" name="materials">
              <div class="rightTitle none">
                <div>
                  <el-button type="text" icon="el-icon-plus" @click="addClick()">添加物料</el-button>
                  <el-tooltip content="复制给其他商品" placement="top">
                    <el-button type="text" icon="el-icon-document-copy" @click="openCopyDialog">复制</el-button>
                  </el-tooltip>
                  <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
                </div>
                <div>
                  <el-dropdown @command="handleImportCommand" trigger="click">
                    <el-button type="text" icon="bulkImport-icon">
                      批量导入<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="downloadTemplate">
                        <i class="el-icon-download"></i> 下载模板
                      </el-dropdown-item>
                      <el-dropdown-item command="uploadFile">
                        <i class="el-icon-upload2"></i> 上传表格
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-button type="text" icon="el-icon-setting" @click="batchUpdateQuantity()">批量填充用量</el-button>
                  <el-button type="text" icon="deleteRed-icon" @click="batchDelete()">批量删除</el-button>
                </div>
              </div>
              <div class="detailInfo rightTableArea">
                <el-table style="width: 100%;" ref="rightTable" highlight-current-row :data="bomList" border stripe
                          :max-height="maximumHeight" @header-dragend="changeColWidth">
                  <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
                  <el-table-column label="序号" width="50" type="index"></el-table-column>
                  <el-table-column label="物料编码" prop="materialCode" min-width="130" sortable>
                    <template slot-scope="{row}">
                      <span>{{ row.code }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="物料名称" prop="materialName" min-width="140">
                    <template slot-scope="{row}">
                      <span>{{ row.name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="规格" prop="specification" min-width="100">
                    <template slot-scope="{row}">
                      <span>{{ row.model }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="用量" prop="quantity" min-width="82">
                    <template slot-scope="{row}">
                      <div v-if="editingCell[row.id] === 'quantity'" class="edit-cell">
                        <el-input-number v-model="editingValues[row.id].quantity" :precision="2" :step="0.01"
                                         :min="0.01" :max="999999.99" size="mini" @change="handleNumberChange(row, 'quantity')"
                                         @blur="saveEdit(row, 'quantity')" @keyup.enter.native="saveEdit(row, 'quantity')"
                                         @keyup.esc.native="cancelEdit(row.id, 'quantity')" ref="quantityInput"
                                         controls-position="right" class="compact-number-input" style="width: 100%;">
                        </el-input-number>
                      </div>
                      <div v-else class="view-cell" @click="startEdit(row, 'quantity')">
                        <span>{{ row.quantity }}</span>
                        <i class="el-icon-edit edit-icon blueColor"></i>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="单位" prop="unit" width="50">
                    <template slot-scope="{row}">
                      <span>{{ row.unit }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="品牌" prop="brand" width="80">
                    <template slot-scope="{row}">
                      <span>{{ row.brand }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="损耗率(%)" prop="attritionRate" min-width="90">
                    <template slot-scope="{row}">
                      <div v-if="editingCell[row.id] === 'attritionRate'" class="edit-cell">
                        <el-input-number v-model="editingValues[row.id].attritionRate" :precision="2" :step="0.01"
                                         :min="0.01" :max="99.99" size="mini" @change="handleNumberChange(row, 'attritionRate')"
                                         @blur="saveEdit(row, 'attritionRate')" @keyup.enter.native="saveEdit(row, 'attritionRate')"
                                         @keyup.esc.native="cancelEdit(row.id, 'attritionRate')" ref="attritionRateInput"
                                         controls-position="right" class="compact-number-input" style="width: 100%;">
                        </el-input-number>
                      </div>
                      <div v-else class="view-cell" @click="startEdit(row, 'attritionRate')">
                        <span>{{ row.attritionRate }}</span>
                        <i class="el-icon-edit edit-icon blueColor"></i>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="仓库" prop="warehouseName" width="80">
                    <template slot-scope="{row}">
                      <span>{{ row.warehouseName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="库位" prop="location" width="80">
                    <template slot-scope="{row}">
                      <span>{{ row.locationName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="备注" prop="remark" min-width="120">
                    <template slot-scope="{row}">
                      <div v-if="editingCell[row.id] === 'remark'" class="edit-cell">
                        <el-input v-model="editingValues[row.id].remark" size="mini" maxlength="200"
                                  @blur="saveEdit(row, 'remark')" @keyup.enter.native="saveEdit(row, 'remark')"
                                  @keyup.esc.native="cancelEdit(row.id, 'remark')" ref="remarkInput" placeholder="请输入备注"
                                  style="width: 100%;">
                        </el-input>
                      </div>
                      <div v-else class="view-cell" @click="startEdit(row, 'remark')">
                        <span class="remark-text" :class="{ 'remark-placeholder': !row.remark }">{{
                            row.remark || '点击添加备注'
                          }}</span>
                        <i class="el-icon-edit edit-icon blueColor"></i>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="60" fixed="right">
                    <template slot-scope="{row}">
                      <el-button type="text" size="small" class="deleteButton errorColor" @click="deleteBomItem(row)">删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <Pagination v-show="rightTotal > 0" :total="rightTotal" :page.sync="rightCurrentPage"
                            :limit.sync="rightPageSize" @pagination="getBomList"/>
              </div>
            </el-tab-pane>

            <!-- 生产工序 tab -->
            <el-tab-pane label="生产工序" name="process">
              <div class="emptyContent">
                <div class="emptyIcon">
                  <i class="el-icon-warning-outline"></i>
                </div>
                <div class="emptyText">
                  <p>生产工序功能暂未开发</p>
                  <p class="subText">敬请期待后续版本更新</p>
                </div>
              </div>
            </el-tab-pane>

            <!-- 加工费用 tab -->
            <el-tab-pane label="加工费用" name="cost">
              <div class="emptyContent">
                <div class="emptyIcon">
                  <i class="el-icon-warning-outline"></i>
                </div>
                <div class="emptyText">
                  <p>加工费用功能暂未开发</p>
                  <p class="subText">敬请期待后续版本更新</p>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>

    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" v-if="dialogFormVisible">
      <el-form :label-width="formLabelWidth" :model="temp" ref="temp" :rules="tempRules">
        <el-form-item label="上级分类" prop="categoryName" v-if="dialogStatus != 'categoryRename'">
          <selectInput
            ref="selectInput"
            v-model="temp.categoryName"
            :inputParam="temp.categoryName"
            inputType="categoryName"
            @select="onInputSearch('temp', $event)"
            placeholder="请选择上级分类"
            :disabled="dialogStatus == 'categoryAdd' ? true : false"
          ></selectInput>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model.trim="temp.name" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort" v-if="dialogStatus != 'categoryRename'">
          <el-input-number v-model.trim="temp.sort" placeholder="请输入排序" controls-position="right" :min="1" :max="9999"
                           :precision="0" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item class="submitArea">
          <el-button type="primary" @click="handleCategorySubmit">{{ $t('button.submit') }}</el-button>
          <el-button plain @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 添加物料弹窗 -->
    <el-dialog v-dialogDrag title="添加物料" :visible.sync="materialDialogVisible" width="1200px !important"
               :before-close="cancelAddMaterials" v-if="materialDialogVisible">
      <div class="materialDialog">
        <el-row :gutter="20">
          <el-col :span="5" class="leftCategory">
            <div class="categoryTitle">
              <span>商品类别</span>
            </div>
            <div class="categoryTree">
              <el-scrollbar>
                <el-tree :data="materialCategoryTree" :render-content="renderContent" :default-expand-all="true"
                         @node-click="handleMaterialCategorySelect" :props="{
                    label: 'name',
                    children: 'children'
                  }" node-key="id"></el-tree>
              </el-scrollbar>
            </div>
          </el-col>
          <el-col :span="12" class="middleMaterial">
            <div class="materialTitle">
              <span>可选物料</span>
            </div>
            <div class="materialSearch">
              <el-form :inline="true" :label-width="$labelFour" :model="materialSearch" class="demo-form-inline">
                <el-form-item label="类别名称" prop="categoryName">
                  <selectInput
                    ref="selectInput"
                    v-model="materialSearch.categoryName"
                    :inputType="'categoryName'"
                    @select="onMaterialInputSearch('category', $event)"
                    placeholder="请选择类别名称"
                    style="width: 160px;"
                  >
                  </selectInput>
                </el-form-item>
                <el-form-item label="商品编码" prop="code">
                  <el-input v-model.trim="materialSearch.code" placeholder="请输入商品编码" style="width: 160px;"></el-input>
                </el-form-item>
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model.trim="materialSearch.name" placeholder="请输入商品名称" style="width: 160px;"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="onMaterialSearch">搜索</el-button>
                  <el-button plain @click="resetMaterialForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="materialTable">
              <el-table ref="materialTable" style="width: 100%;" :data="materialList" border stripe height="380"
                        @selection-change="handleMaterialSelectionChange">
                <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
                <el-table-column label="序号" width="50" type="index"></el-table-column>
                <el-table-column label="商品编码" prop="code" min-width="100">
                  <template slot-scope="{row}">
                    <span>{{ row.code }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="name" min-width="120">
                  <template slot-scope="{row}">
                    <span>{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="图片" prop="imageUrl" width="50">
                  <template slot-scope="{row}">
                    <img v-if="row.imageUrl != ''" class="pictureShow" :src="row.imageUrl" alt=""/>
                  </template>
                </el-table-column>
                <el-table-column label="类别" prop="categoryName" width="80">
                  <template slot-scope="{row}">
                    <span>{{ row.categoryName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="单位" prop="unit" width="60">
                  <template slot-scope="{row}">
                    <span>{{ row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="status" width="60">
                  <template slot-scope="{row}">
                    <span v-if="row.status === 0" class="errorColor">禁用</span>
                    <span v-if="row.status === 1" class="successColor">启用</span>
                  </template>
                </el-table-column>
              </el-table>
              <Pagination v-show="materialTotal > 0" :total="materialTotal" :page.sync="materialCurrentPage"
                          :limit.sync="materialPageSize" @pagination="getMaterialList"/>
            </div>
          </el-col>
          <el-col :span="7" class="rightSelected">
            <div class="selectedTitle">
              <span>已选物料 ({{ selectedMaterials.length }})</span>
              <el-button type="text" size="small" @click="clearSelectedMaterials" v-if="selectedMaterials.length > 0">
                清空
              </el-button>
            </div>
            <div class="selectedList">
              <el-scrollbar style="height: 450px;">
                <div v-if="selectedMaterials.length === 0" class="emptySelected">
                  <i class="el-icon-warning-outline"></i>
                  <p>暂未选择物料</p>
                </div>
                <div v-else>
                  <div v-for="(item, index) in selectedMaterials" :key="item.id" class="selectedItem">
                    <div class="itemContent">
                      <img v-if="item.imageUrl" :src="item.imageUrl" alt="" class="itemImage"/>
                      <div v-else class="itemImage noImage">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                      <div class="itemInfo">
                        <div class="itemName" :title="item.name">{{ item.name }}</div>
                        <div class="itemCode">{{ item.code }}</div>
                        <div class="itemCategory">{{ item.categoryName }}</div>
                      </div>
                      <div class="itemActions">
                        <el-button type="text" size="mini" @click="removeSelectedMaterial(index)"
                                   class="removeBtn errorColor">
                          <i class="el-icon-close"></i>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <span class="selectedCount">已选择 {{ selectedMaterials.length }} 条</span>
        <el-button type="primary" @click="confirmAddMaterials">确定</el-button>
        <el-button @click="cancelAddMaterials">取消</el-button>
      </div>
    </el-dialog>

    <!-- 批量填充用量弹窗 -->
    <el-dialog v-dialogDrag title="批量填充用量" :visible.sync="batchQuantityDialogVisible" width="400px"
               :before-close="cancelBatchUpdateQuantity" v-if="batchQuantityDialogVisible">
      <div class="batchQuantityDialog">
        <el-form ref="batchQuantityForm" :model="batchQuantityForm" :rules="batchQuantityRules" :label-width="$labelTwo">
          <el-form-item label="用量" prop="quantity">
            <el-input-number v-model="batchQuantityForm.quantity" :precision="2" :step="0.01" :min="0.01"
                             :max="999999.99" controls-position="right" placeholder="请输入用量" style="width: 100%;">
            </el-input-number>
          </el-form-item>
          <div class="form-tips">
            <i class="el-icon-info blueColor"></i>
            <span>此操作将批量修改所选物料的用量，支持两位小数</span>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmBatchUpdateQuantity">提交</el-button>
        <el-button @click="cancelBatchUpdateQuantity">取消</el-button>
      </div>
    </el-dialog>

    <!-- 隐藏的文件上传输入框 -->
    <input ref="fileInput" type="file" accept=".xlsx,.xls" style="display: none" @change="handleFileUpload">

    <!-- 复制BOM到其他商品弹窗 -->
    <el-dialog v-dialogDrag title="选择目标商品" :visible.sync="copyDialogVisible" width="1200px !important" v-if="copyDialogVisible">
      <div class="copyDialog">
        <el-row :gutter="20">
          <el-col :span="5" class="leftCategory">
            <div class="categoryTitle">
              <span>商品类别</span>
            </div>
            <div class="categoryTree">
              <el-scrollbar>
                <el-tree :data="categoryTree" :render-content="renderContent" :default-expand-all="true"
                         @node-click="handleCopyCategorySelect" :props="{
                    label: 'name',
                    children: 'children'
                  }" node-key="id"></el-tree>
              </el-scrollbar>
            </div>
          </el-col>
          <el-col :span="12" class="middleProduct">
            <div class="productTitle">
              <span>可选商品</span>
            </div>
            <div class="productSearch">
              <el-form :inline="true" :label-width="$labelFour" :model="copySearch" class="demo-form-inline">
                <el-form-item label="类别名称" prop="categoryName">
                  <selectInput
                    ref="selectInput"
                    v-model="copySearch.categoryName"
                    :inputType="'categoryName'"
                    @select="onCopyInputSearch($event)"
                    placeholder="请选择类别名称"
                    style="width: 160px;">
                  </selectInput>
                </el-form-item>
                <el-form-item label="商品编码" prop="code">
                  <el-input v-model.trim="copySearch.code" placeholder="请输入商品编码" style="width: 160px;"></el-input>
                </el-form-item>
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model.trim="copySearch.name" placeholder="请输入商品名称" style="width: 160px;"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="onCopySearch">搜索</el-button>
                  <el-button plain @click="resetCopySearch">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="productTable">
              <el-table ref="copyTable" style="width: 100%;" :data="copyCommodityList" border stripe height="380"
                        @selection-change="handleCopySelectionChange">
                <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
                <el-table-column label="序号" width="50" type="index"></el-table-column>
                <el-table-column label="商品编码" prop="code" min-width="100">
                  <template slot-scope="{row}">
                    <span>{{ row.code }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="商品名称" prop="name" min-width="120">
                  <template slot-scope="{row}">
                    <span>{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="类别" prop="categoryName" width="80">
                  <template slot-scope="{row}">
                    <span>{{ row.categoryName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="规格型号" prop="model" width="100">
                  <template slot-scope="{row}">
                    <span>{{ row.model }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="status" width="60">
                  <template slot-scope="{row}">
                    <span v-if="row.status === 0" class="errorColor">禁用</span>
                    <span v-if="row.status === 1" class="successColor">启用</span>
                  </template>
                </el-table-column>
              </el-table>
              <Pagination v-show="copyTotal > 0" :total="copyTotal" :page.sync="copyCurrentPage"
                          :limit.sync="copyPageSize" @pagination="getCopyCommodityList"/>
            </div>
          </el-col>
          <el-col :span="7" class="rightSelected">
            <div class="selectedTitle">
              <span>已选商品 ({{ copySelectedProducts.length }})</span>
              <el-button type="text" size="small" @click="clearCopySelectedProducts" v-if="copySelectedProducts.length > 0">
                清空
              </el-button>
            </div>
            <div class="selectedList">
              <el-scrollbar style="height: 450px;">
                <div v-if="copySelectedProducts.length === 0" class="emptySelected">
                  <i class="el-icon-warning-outline"></i>
                  <p>暂未选择商品</p>
                </div>
                <div v-else>
                  <div v-for="(item, index) in copySelectedProducts" :key="item.id" class="selectedItem">
                    <div class="itemContent">
                      <div class="itemImage noImage">
                        <i class="el-icon-goods"></i>
                      </div>
                      <div class="itemInfo">
                        <div class="itemName" :title="item.name">{{ item.name }}</div>
                        <div class="itemCode">{{ item.code }}</div>
                        <div class="itemCategory">{{ item.categoryName }}</div>
                      </div>
                      <div class="itemActions">
                        <el-button type="text" size="mini" @click="removeCopySelectedProduct(index)"
                                   class="removeBtn errorColor">
                          <i class="el-icon-close"></i>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <span class="selectedCount">已选择 {{ copySelectedProducts.length }} 条</span>
        <el-button type="primary" @click="confirmCopy">提交</el-button>
        <el-button @click="closeCopyDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from "@/components/Pagination"
import selectInput from "@/components/selectInput/selectInput.vue";
import FilterHeader from '@/components/filterHeader/filterHeader.vue';
import {addTabs, categoryTreeInfo, contextmenuSeat, renderTree, tableHeight} from "@/assets/js/common";
import {
  getProductcategoryAdd,
  getProductcategoryDel,
  getProductcategoryEdit,
  getProductcategoryTree,
  getProductList
} from "@/api/basicmgt";
import {
  addBomList,
  batchUpdateBomQuantity,
  copyBomToProducts,
  deleteBom,
  deleteBomList,
  downloadBomTemplate,
  exportBomList,
  getBomList,
  importBomList,
  updateBom
} from "@/api/mpsmgt";
import Vue from "vue";

export default {
  name: "basic_commodity_list",
  components: {Pagination, selectInput, FilterHeader},
  data() {
    return {
      isShow: false,
      // 搜索
      from: {
        code: "",
        name: "",
        categoryName: "",
        categoryId: "",
      },
      categoryTree: [], // 商品类别树结构
      categoryList: [], // 商品类别平铺数据
      commodityList: [], // 商品详情展示

      // 选中的商品信息
      selectedProduct: {
        id: null,
        code: "",
        name: ""
      },

      // BOM相关数据
      bomList: [], // BOM表格数据
      rightPageSize: 10, // 右侧分页大小
      rightCurrentPage: 1, // 右侧当前页
      rightTotal: 0, // 右侧总数
      activeTab: 'materials', // 当前激活的tab页签

      // 添加物料弹窗相关
      materialDialogVisible: false, // 添加物料弹窗显示状态
      materialSearch: { // 物料搜索条件
        code: "",
        name: "",
        categoryName: "",
        categoryId: "",
      },
      materialList: [], // 物料候选列表
      materialTotal: 0, // 物料列表总数
      materialCurrentPage: 1, // 物料列表当前页
      materialPageSize: 10, // 物料列表分页大小
      selectedMaterials: [], // 选中的物料
      materialCategoryTree: [], // 物料类别树

      currentNode: null, // 右键获取节点信息
      categoryNodeId: "", // 选中品牌类别ID
      // 分页
      pageSize: 10,
      currentPage: 1,
      total: 0,
      maximumHeight: 0,
      tableDataCopy: {}, // 深度拷贝的tableData对象，用来筛选数据
      conditionsFields: [], // 记录参与筛选的列信息
      statusList: [
        {label: "未设置", value: false},
        {label: "已设置", value: true},
      ],
      switchType: [
        {name: "是", code: 1},
        {name: "否", code: 0}
      ],
      // 商品类别弹框
      temp: {
        id: "",
        pid: "",
        name: "",
        sort: 1,
        categoryName: "",
      },
      tempRules: {
        categoryName: [{required: true, message: '上级类别不能为空', trigger: ['blur', 'change']}],
        name: [{required: true, message: '姓名不能为空', trigger: ['blur', 'change']}],
      },
      formLabelWidth: "100px",
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        categoryAdd: "新增商品类别",
        categoryEdit: "编辑商品类别",
        categoryRename: "重命名",
      },
      selectType: "",

      // 批量填充用量弹窗相关
      batchQuantityDialogVisible: false, // 批量填充用量弹窗显示状态
      batchQuantityForm: { // 批量填充用量表单
        quantity: null
      },
      batchQuantityRules: { // 批量填充用量表单验证规则
        quantity: [
          {required: true, message: '用量不能为空', trigger: 'blur'},
          {type: 'number', min: 0.01, max: 999999.99, message: '用量必须在0.01-999999.99之间', trigger: 'blur'}
        ]
      },

      // 表格编辑相关
      editingCell: {}, // 当前编辑的单元格状态 {rowId: fieldName}
      editingValues: {}, // 编辑中的临时值 {rowId: {field: value}}

      // 复制BOM到其他商品相关
      copyDialogVisible: false, // 复制弹窗显示状态
      copySearch: {
        code: '',
        name: '',
        categoryName: '',
        categoryId: '',
      },
      copyCommodityList: [], // 可选商品列表
      copyTotal: 0, // 商品总数
      copyCurrentPage: 1,
      copyPageSize: 10,
      copySelectedProducts: [], // 勾选的目标商品
    }
  },
  methods: {
    onInputSearch(type, $event) {
      if (type == 'from') {
        this.from.categoryName = $event.name;
        this.from.categoryId = $event.id;
      } else {
        this.temp.categoryName = $event.name;
        this.temp.pid = $event.id;
      }
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSearch()
      }
    },
    onSearch() {
      this.resetDataList();
    },

    // 处理左侧表格行点击
    handleRowClick(row) {
      this.selectedProduct = {
        id: row.id,
        code: row.code,
        name: row.name
      };
      // 重置右侧分页并获取BOM数据
      this.rightCurrentPage = 1;
      this.getBomList();
    },

    // 获取BOM数据
    getBomList() {
      if (!this.selectedProduct.id) {
        this.bomList = [];
        this.rightTotal = 0;
        return;
      }
      const param = {
        productId: this.selectedProduct.id,
        page: this.rightCurrentPage,
        size: this.rightPageSize
      };
      const formData = new URLSearchParams();
      for (const key in param) {
        formData.append(key, param[key]);
      }
      // 调用BOM API
      getBomList(formData).then(res => {
        if (res.data.code === 100) {
          this.bomList = res.data.data || [];
          this.rightTotal = res.data.total || 0;
        } else {
          this.bomList = [];
          this.rightTotal = 0;
          this.$DonMessage.error(res.data.msg || '获取BOM数据失败');
        }
      }).catch(err => {
        this.bomList = [];
        this.rightTotal = 0;
        this.$DonMessage.error('获取BOM数据失败');
      });
    },

    /**
     * 删除单个BOM物料
     * @param {Object} row - 当前行数据
     */
    deleteBomItem(row) {
      this.$confirm(`确定删除物料【${row.name}】吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用API删除
        deleteBom(row.id).then(res => {
          if (res.data && res.data.code === 100) {
            this.$DonMessage.success('删除成功');
            this.getBomList(); // 刷新BOM列表
            this.dataList(); // 刷新商品列表，更新物料清单状态
          } else {
            this.$DonMessage.error(res.data.msg || '删除失败');
          }
        }).catch(() => {
          this.$DonMessage.error('删除失败');
        });
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 重置
    reset() {
      if (this.$refs['from'].resetFields() !== undefined) {
        this.$refs['from'].resetFields()
      }
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.resetDataList();
    },
    // 商品类别树
    getCommodityTree() {
      var param = {};
      var list = [
        {
          id: "",
          name: "全部",
          children: []
        }
      ];
      getProductcategoryTree(param).then((res) => {
        if (res.data.code == "100") {
          list[0].children = Array.isArray(res.data.data) ? res.data.data : ""
          this.categoryTree = list
        } else {
          this.categoryTree = list
        }
      })
    },
    renderContent(h, {node, data}) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".commodityManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 点击商品类别获取详情
    handleCategorySelect(node) {
      this.categoryNodeId = node.id;
      this.resetDataList();
    },
    // 右键操作
    handleNodeContextmenu(event, data) {
      event.preventDefault()
      this.currentNode = data
      contextmenuSeat(event, ".commodityManage .contextmenuArea");
    },
    resetTemp() {
      this.temp = {
        id: "",
        pid: "",
        categoryName: "",
        name: "",
        sort: 1,
      }
      this.$refs['selectInput'].clear()
      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
    },
    // 新增
    onAddCategory() {
      this.dialogStatus = "categoryAdd";
      this.dialogFormVisible = true;
      this.resetTemp();
      this.temp.categoryName = this.currentNode.name;
      this.temp.pid = this.currentNode.id
    },
    // 编辑
    onEditCategory() {
      this.dialogStatus = "categoryEdit";
      this.dialogFormVisible = true;
      this.resetTemp();
      this.temp = Object.assign({}, this.currentNode);
      this.temp.categoryName = categoryTreeInfo(this.categoryTree, this.temp.pid);
    },
    // 重命名
    onRenameCategory() {
      this.dialogStatus = "categoryRename";
      this.dialogFormVisible = true;
      this.resetTemp();
      this.temp = Object.assign({}, this.currentNode);
    },
    // 删除
    onDeleteCategory() {
      var name = this.currentNode.name
      var id = this.currentNode.id
      this.$confirm("确定删除【" + name + "】的相关信息?", '删除商品类别', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getProductcategoryDel(id).then((res) => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.getCommodityTree();
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },
    // 提交
    handleCategorySubmit() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == "categoryAdd") {
            getProductcategoryAdd(this.temp).then((res) => {
              if (res.data.code == '100') {
                this.$DonMessage.success(this.$t('successTip.submitTip'))
                this.getCommodityTree();
                this.dialogFormVisible = false;
                this.dialogStatus = "";
              } else {
                this.$DonMessage.error(res.data.msg)
              }
            });
          } else {
            getProductcategoryEdit(this.temp).then((res) => {
              if (res.data.code == '100') {
                this.$DonMessage.success(this.$t('successTip.submitTip'))
                this.getCommodityTree();
                this.dialogFormVisible = false;
                this.dialogStatus = "";
              } else {
                this.$DonMessage.error(res.data.msg)
              }
            });
          }
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      })
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 列表数据
    dataList() {
      const param = {
        code: this.from.code,
        name: this.from.name,
        categoryName: this.from.categoryName,
        categoryId: this.categoryNodeId,
        brand: this.from.brand,
        purchasable: this.from.purchasable,
        salable: this.from.salable,
        canBeSubitem: this.from.canBeSubitem,
        canBeComponent: 1,
        outsourceable: this.from.outsourceable,
        selfProducible: this.from.selfProducible,
        page: this.currentPage,
        limit: this.pageSize,
      };
      console.log(param);
      getProductList(param).then(res => {
        if (res.data.code === 100) {
          this.commodityList = res.data.data
          this.total = res.data.total;
          this.tableDataCopy = JSON.parse(JSON.stringify(this.commodityList))
        }
        this.tableHeightArea();
      })
    },
    resetDataList() {
      this.currentPage = 1
      this.dataList()
    },
    // 新增物料
    addClick() {
      if (!this.selectedProduct.id) {
        this.$DonMessage.warning('请先选择商品');
        return;
      }
      this.materialDialogVisible = true;
      this.resetMaterialSearch();
      this.getMaterialCategoryTree();
      this.getMaterialList();
    },

    // 重置物料搜索条件
    resetMaterialSearch() {
      this.materialSearch = {
        code: "",
        name: "",
        categoryName: "",
        categoryId: "",
      };
      this.materialCurrentPage = 1;
      this.selectedMaterials = [];
    },

    // 获取物料类别树
    getMaterialCategoryTree() {
      var param = {};
      var list = [
        {
          id: "",
          name: "全部",
          children: []
        }
      ];
      getProductcategoryTree(param).then((res) => {
        if (res.data.code == "100") {
          list[0].children = Array.isArray(res.data.data) ? res.data.data : ""
          this.materialCategoryTree = list
        } else {
          this.materialCategoryTree = list
        }
      })
    },

    // 获取物料列表
    getMaterialList() {
      var param = {
          code: this.materialSearch.code,
          name: this.materialSearch.name,
          categoryName: this.materialSearch.categoryName,
          categoryId: this.materialSearch.categoryId,
          page: this.materialCurrentPage,
          size: this.materialPageSize,
          salable: this.from.salable,
          canBeSubitem: 1,
        };

      getProductList(param).then(res => {
        if (res.data.code === 100) {
          this.materialList = res.data.data || [];
          this.materialTotal = res.data.total || 0;
        } else {
          this.materialList = [];
          this.materialTotal = 0;
          this.$DonMessage.error(res.data.msg || '获取物料列表失败');
        }
      }).catch(err => {
        this.materialList = [];
        this.materialTotal = 0;
        this.$DonMessage.error('获取物料列表失败');
      });
    },

    // 物料搜索
    onMaterialSearch() {
      this.materialCurrentPage = 1;
      this.getMaterialList();
    },

    // 重置物料搜索表单
    resetMaterialForm() {
      this.materialSearch = {
        code: "",
        name: "",
        categoryName: "",
        categoryId: "",
      };
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.materialCurrentPage = 1;
      this.getMaterialList();
    },

    // 物料类别选择
    handleMaterialCategorySelect(node) {
      this.materialSearch.categoryId = node.id;
      this.materialCurrentPage = 1;
      this.getMaterialList();
    },

    // 物料输入搜索
    onMaterialInputSearch(type, $event) {
      this.materialSearch.categoryName = $event.name;
      this.materialSearch.categoryId = $event.id;
    },

    // 物料表格选择
    handleMaterialSelectionChange(selection) {
      this.selectedMaterials = selection;
    },

    /**
     * 确认添加物料
     * 调用addBomList接口，将选中的物料批量添加到BOM
     */
    confirmAddMaterials() {
      // 判断是否有选中物料
      if (this.selectedMaterials.length === 0) {
        this.$DonMessage.warning('请选择要添加的物料');
        return;
      }

      // 构造API参数
      const param = {
        materials: this.selectedMaterials.map(item => {
          return {
            productId: this.selectedProduct.id, // 商品ID
            materialId: item.id, // 物料ID
            quantity: 1, // 默认用量
            unitPrice: item.purchasePrice,
          }
        })
      };

      // 调用API
      addBomList(param).then(res => {
        if (res.data && (res.data.code === 100)) {
          this.$DonMessage.success('物料添加成功');
          this.materialDialogVisible = false;
          this.getBomList(); // 刷新BOM列表
          this.dataList(); // 刷新商品列表，更新物料清单状态
        } else {
          this.$DonMessage.error(res.data.msg || '物料添加失败');
        }
      }).catch(err => {
        this.$DonMessage.error('物料添加失败');
      });
    },

    // 取消添加物料
    cancelAddMaterials() {
      this.materialDialogVisible = false;
      this.selectedMaterials = [];
    },

    // 清空已选物料
    clearSelectedMaterials() {
      this.selectedMaterials = [];
      // 清除表格中的选择状态
      this.$nextTick(() => {
        if (this.$refs.materialTable) {
          this.$refs.materialTable.clearSelection();
        }
      });
    },

    // 移除单个已选物料
    removeSelectedMaterial(index) {
      const removedItem = this.selectedMaterials[index];
      this.selectedMaterials.splice(index, 1);

      // 同步更新表格中的选择状态
      this.$nextTick(() => {
        if (this.$refs.materialTable) {
          // 找到表格中对应的行并取消选择
          const tableRow = this.materialList.find(item => item.id === removedItem.id);
          if (tableRow) {
            this.$refs.materialTable.toggleRowSelection(tableRow, false);
          }
        }
      });
    },
    /**
     * 打开复制弹窗
     */
    openCopyDialog() {
      if (!this.selectedProduct.id) {
        this.$DonMessage.warning('请先选择商品');
        return;
      }
      this.copyDialogVisible = true;
      this.resetCopySearch();
      this.getCopyCommodityList();
    },

    /**
     * 关闭复制弹窗
     */
    closeCopyDialog() {
      this.copyDialogVisible = false;
      this.copySelectedProducts = [];
    },

    /**
     * 复制弹窗-输入搜索
     */
    onCopyInputSearch($event) {
      this.copySearch.categoryName = $event.name;
      this.copySearch.categoryId = $event.id;
    },

    /**
     * 复制弹窗-搜索
     */
    onCopySearch() {
      this.copyCurrentPage = 1;
      this.getCopyCommodityList();
    },

    /**
     * 复制弹窗-重置
     */
    resetCopySearch() {
      this.copySearch = {
        code: '',
        name: '',
        categoryName: '',
        categoryId: '',
      };
      if (this.$refs['selectInput'] != undefined) {
        this.$refs['selectInput'].clear()
      }
      this.copyCurrentPage = 1;
      this.getCopyCommodityList();
    },

    /**
     * 获取可选商品列表（排除当前商品）
     */
    getCopyCommodityList() {
      const param = {
        code: this.copySearch.code,
        name: this.copySearch.name,
        categoryName: this.copySearch.categoryName,
        categoryId: this.copySearch.categoryId,
        page: this.copyCurrentPage,
        size: this.copyPageSize,
        salable: this.from.salable,
        canBeSubitem: this.from.canBeSubitem,
        canBeComponent: 1,
      };
      // 复用商品列表API
      getProductList(param).then(res => {
        if (res.data.code === 100) {
          // 排除当前商品
          this.copyCommodityList = (res.data.data || []).filter(item => item.id !== this.selectedProduct.id);
          this.copyTotal = res.data.total - 1 >= 0 ? res.data.total - 1 : 0;
        } else {
          this.copyCommodityList = [];
          this.copyTotal = 0;
          this.$DonMessage.error(res.data.msg || '获取商品列表失败');
        }
      }).catch(() => {
        this.copyCommodityList = [];
        this.copyTotal = 0;
        this.$DonMessage.error('获取商品列表失败');
      });
    },

    /**
     * 复制弹窗-多选勾选
     */
    handleCopySelectionChange(selection) {
      this.copySelectedProducts = selection;
    },

    /**
     * 确认复制BOM到其他商品
     */
    confirmCopy() {
      if (!this.copySelectedProducts.length) {
        this.$DonMessage.warning('请选择要复制到的商品');
        return;
      }
      const targetIds = this.copySelectedProducts.map(item => item.id);
      const param = {
        currentProductId: this.selectedProduct.id,
        targetIdList: targetIds
      };
      // 调用API
      Vue.$loading.show();
      copyBomToProducts(param).then(res => {
        Vue.$loading.hide();
        if (res.data && res.data.code === 100) {
          this.$DonMessage.success('复制成功');
          this.copyDialogVisible = false;
          this.copySelectedProducts = [];
          this.getBomList();
          this.dataList();
        } else {
          this.$DonMessage.error(res.data.msg || '复制失败');
        }
      }).catch(() => {
        Vue.$loading.hide();
        this.$DonMessage.error('复制失败');
      });
    },
    copyMaterials() {
      this.$DonMessage.info('请使用"复制"按钮的弹窗进行复制操作');
    },
    /**
     * 批量填充用量
     * 获取表格多选项，显示批量填充用量弹窗
     */
    batchUpdateQuantity() {
      // 获取多选项
      const selection = this.$refs.rightTable ? this.$refs.rightTable.selection : [];
      if (!selection || selection.length === 0) {
        this.$DonMessage.warning('请先选择要修改用量的物料');
        return;
      }
      // 显示批量填充用量弹窗
      this.batchQuantityDialogVisible = true;
      this.batchQuantityForm.quantity = null;
      this.$nextTick(() => {
        if (this.$refs.batchQuantityForm) {
          this.$refs.batchQuantityForm.clearValidate();
        }
      });
    },

    /**
     * 确认批量填充用量
     * 调用batchUpdateBomQuantity接口更新选中物料的用量
     */
    confirmBatchUpdateQuantity() {
      this.$refs.batchQuantityForm.validate((valid) => {
        if (valid) {
          // 获取多选项
          const selection = this.$refs.rightTable ? this.$refs.rightTable.selection : [];
          if (!selection || selection.length === 0) {
            this.$DonMessage.warning('请先选择要修改用量的物料');
            this.batchQuantityDialogVisible = false;
            return;
          }

          // 构造API参数
          const items = selection.map(item => ({
            id: item.id,
            quantity: this.batchQuantityForm.quantity
          }));

          // 调用API批量更新
          batchUpdateBomQuantity(items).then(res => {
            if (res.data && res.data.code === 100) {
              this.$DonMessage.success('批量填充用量成功');
              this.batchQuantityDialogVisible = false;
              this.getBomList(); // 刷新BOM列表
              // 清除表格多选状态
              this.$refs.rightTable.clearSelection();
            } else {
              this.$DonMessage.error(res.data.msg || '批量填充用量失败');
            }
          }).catch(() => {
            this.$DonMessage.error('批量填充用量失败');
          });
        } else {
          this.$DonMessage.warning('请正确填写用量');
        }
      });
    },

    /**
     * 取消批量填充用量
     */
    cancelBatchUpdateQuantity() {
      this.batchQuantityDialogVisible = false;
      this.batchQuantityForm.quantity = null;
      this.$nextTick(() => {
        if (this.$refs.batchQuantityForm) {
          this.$refs.batchQuantityForm.clearValidate();
        }
      });
    },

    /**
     * 开始编辑单元格
     * @param {Object} row - 行数据
     * @param {String} field - 字段名
     */
    startEdit(row, field) {
      // 如果已有其他单元格在编辑，先取消
      this.cancelAllEdit();

      // 设置编辑状态
      this.$set(this.editingCell, row.id, field);

      // 初始化编辑值
      if (!this.editingValues[row.id]) {
        this.$set(this.editingValues, row.id, {});
      }
      this.$set(this.editingValues[row.id], field, row[field]);

      // 等DOM更新后聚焦输入框
      this.$nextTick(() => {
        const refName = field + 'Input';
        if (this.$refs[refName] && this.$refs[refName].length > 0) {
          const input = this.$refs[refName].find(ref => ref.$el);
          if (input) {
            if (field === 'remark') {
              input.focus();
            } else {
              input.$refs.input.focus();
              input.$refs.input.select();
            }
          }
        }
      });
    },

    /**
     * 保存编辑
     * @param {Object} row - 行数据
     * @param {String} field - 字段名
     */
    saveEdit(row, field) {
      if (!this.editingCell[row.id] || this.editingCell[row.id] !== field) {
        return;
      }

      const newValue = this.editingValues[row.id][field];
      const oldValue = row[field];

      // 检查值是否有变化
      if (newValue === oldValue) {
        this.cancelEdit(row.id, field);
        return;
      }

      // 验证数值范围
      if (field === 'quantity') {
        if (newValue < 0.01 || newValue > 999999.99) {
          this.$DonMessage.warning('用量必须在0.01-999999.99之间');
          return;
        }
      } else if (field === 'attritionRate') {
        if (newValue < 0.01 || newValue > 99.99) {
          this.$DonMessage.warning('损耗率必须在0.01-99.99之间');
          return;
        }
      }

      // 构造更新参数
      const updateData = {
        id: row.id,
        quantity: field === 'quantity' ? newValue : row.quantity,
        attritionRate: field === 'attritionRate' ? newValue : row.attritionRate,
        remark: field === 'remark' ? newValue : row.remark
      };

      // 调用API更新
      updateBom(updateData).then(res => {
        if (res.data && res.data.code === 100) {
          // 更新本地数据
          row[field] = newValue;
          this.$DonMessage.success('更新成功');
          this.cancelEdit(row.id, field);
        } else {
          this.$DonMessage.error(res.data.msg || '更新失败');
        }
      }).catch(() => {
        this.$DonMessage.error('更新失败');
      });
    },

    /**
     * 取消编辑
     * @param {String} rowId - 行ID
     * @param {String} field - 字段名
     */
    cancelEdit(rowId, field) {
      this.$delete(this.editingCell, rowId);
      if (this.editingValues[rowId]) {
        this.$delete(this.editingValues[rowId], field);
      }
    },

    /**
     * 取消所有编辑状态
     */
    cancelAllEdit() {
      this.editingCell = {};
      this.editingValues = {};
    },

    /**
     * 处理数字输入框变化
     * @param {Object} row - 行数据
     * @param {String} field - 字段名
     */
    handleNumberChange(row, field) {
      // 数字输入框值变化时的处理，可以用于实时验证或其他逻辑
      // 这里主要是为了确保blur事件能正常触发
    },

    /**
     * 处理导入下拉菜单命令
     * @param {String} command - 命令类型：downloadTemplate 或 uploadFile
     */
    handleImportCommand(command) {
      if (!this.selectedProduct.id) {
        this.$DonMessage.warning('请先选择商品');
        return;
      }

      if (command === 'downloadTemplate') {
        this.downloadTemplate();
      } else if (command === 'uploadFile') {
        this.triggerFileUpload();
      }
    },

    /**
     * 下载导入模板
     */
    downloadTemplate() {
      downloadBomTemplate().then(res => {
        if (!res || !res.data) {
          this.$DonMessage.error('下载模板失败');
          return;
        }
        // 处理文件下载
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'});
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'BOM物料导入模板.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$DonMessage.success('模板下载成功');
      }).catch(() => {
        this.$DonMessage.error('下载模板失败');
      });
    },

    /**
     * 触发文件上传选择
     */
    triggerFileUpload() {
      this.$refs.fileInput.click();
    },

    /**
     * 处理文件上传
     * @param {Event} event - 文件选择事件
     */
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) {
        return;
      }

      // 验证文件类型
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel' // .xls
      ];

      if (!allowedTypes.includes(file.type)) {
        this.$DonMessage.error('请选择Excel文件(.xlsx或.xls格式)');
        this.resetFileInput();
        return;
      }

      // 验证文件大小（限制10MB）
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        this.$DonMessage.error('文件大小不能超过10MB');
        this.resetFileInput();
        return;
      }

      this.uploadFile(file);
    },

    /**
     * 上传文件并导入数据
     * @param {File} file - 要上传的文件
     */
    uploadFile(file) {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      formData.append('productId', this.selectedProduct.id);

      Vue.$loading.show();


      // 调用导入API
      importBomList(formData).then(res => {
        Vue.$loading.hide();
        if (res.data && res.data.code === 100) {
          this.$DonMessage.success('导入成功');
          this.getBomList();
          this.dataList();
          this.resetFileInput();
        } else {
          // 导入失败时显示确认对话框
          this.$confirm(
            res.data.msg || '导入失败，请检查文件格式是否正确',
            '导入失败',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'error',
              dangerouslyUseHTMLString: true,
              customClass: 'wide-confirm-dialog'
            }
          ).then(() => {
            this.resetFileInput();
          });
        }
      }).catch(err => {
        Vue.$loading.hide();
        // 网络错误或其他异常时显示确认对话框
        this.$confirm(
          '导入失败，请检查文件格式和网络连接',
          '导入失败',
          {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'error'
          }
        ).then(() => {
          this.resetFileInput();
        });
      });
    },

    /**
     * 重置文件输入框
     */
    resetFileInput() {
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = '';
      }
    },
    // 导出
    exportClick() {
      if (!this.selectedProduct.id) {
        this.$DonMessage.warning('请先选择商品');
        return;
      }

      exportBomList(this.selectedProduct.id).then(res => {
        if (!res || !res.data) {
          this.$DonMessage.error('导出失败');
          return;
        }
        // 处理文件下载
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel'});
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'BOM物料导出.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$DonMessage.success('导出成功');
      }).catch(() => {
        this.$DonMessage.error('导出失败');
      });
    },

    /**
     * 批量删除BOM物料
     * 获取表格多选项，调用deleteBomList
     */
    batchDelete() {
      // 获取多选项
      const selection = this.$refs.rightTable ? this.$refs.rightTable.selection : [];
      if (!selection || selection.length === 0) {
        this.$DonMessage.warning('请先选择要删除的物料');
        return;
      }
      const idList = selection.map(item => item.id);
      this.$confirm(`确定批量删除选中的${idList.length}条物料吗？`, '批量删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用API批量删除
        deleteBomList(idList).then(res => {
          if (res.data && res.data.code === 100) {
            this.$DonMessage.success('批量删除成功');
            this.getBomList(); // 刷新BOM列表
            this.dataList();
            // 清除表格多选状态
            this.$refs.rightTable.clearSelection();
          } else {
            this.$DonMessage.error(res.data.msg || '批量删除失败');
          }
        }).catch(() => {
          this.$DonMessage.error('批量删除失败');
        });
      }).catch(() => {
        // 用户取消
      });
    },
    // 筛选
    tableFilter(data, reset) {
      let flag = true // 判断条件里有没有该列，用来判断是新增还是更新
      let filterData = this.tableDataCopy // 最终过滤信息
      if (!reset) {
        // 参与筛选的列信息，有则更新
        this.conditionsFields.forEach(item => {
          if (item.fieldName === data.fieldName) {
            item.conditions = data.conditions
            flag = false
          }
        })
        // 没有则添加
        if (flag) {
          this.conditionsFields.push(data)
        }
      }
      // 遍历所有筛选条件进行过滤
      this.conditionsFields.filter((fields, index) => {
        filterData = filterData.filter(item => {
          // 文本
          if (fields.filterType === 'text' && fields.conditions.text !== '') {
            return item[fields.fieldProp] && item[fields.fieldProp].indexOf(fields.conditions.text) > -1
          } else if (fields.filterType === 'radio' && fields.conditions.radio !== '') {
            return item[fields.fieldProp] !== null && item[fields.fieldProp].toString() == fields.conditions.radio.toString()
          } else {
            // 遍历完没找到符合条件的，则直接返回
            return item;
          }
        })
      })
      this.commodityList = this.$set(this, 'commodityList', filterData);
    },
    tableFilterReset(data) {
      // 清空当前列筛选条件
      this.conditionsFields.forEach((item, index) => {
        if (item.fieldName === data.fieldName) {
          this.conditionsFields.splice(index, 1);
        }
      });
      if (this.conditionsFields.length === 0) {
        // 没有筛选条件了直接请求列表
        this.resetDataList();
      } else {
        // 有筛选条件就再去筛选
        this.tableFilter(data, true);
      }
    },
    // 详情
    detailClick(row) {
      console.log(row);
      var title = row.code;
      this.$router.push({name: 'productDetail', params: {id: row.id}});
      addTabs(this.$route.path, title);
    },
    // 编辑
    editClick(row) {
      var title = "编辑 " + row.code;
      this.$router.push({name: 'addProduct', params: {id: row.id}});
      addTabs(this.$route.path, title);
    },
    // 禁用
    statusClick(row) {
    },
    // 表格高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    initialization() {
      this.dataList()
      this.getCommodityTree()
    },
    handleCopyCategorySelect(node) {
      this.copySearch.categoryName = node.name;
      this.copySearch.categoryId = node.id;
      this.getCopyCommodityList();
    },
    clearCopySelectedProducts() {
      this.copySelectedProducts = [];
      // 清除表格中的选择状态
      this.$nextTick(() => {
        if (this.$refs.copyTable) {
          this.$refs.copyTable.clearSelection();
        }
      });
    },
    removeCopySelectedProduct(index) {
      const removedItem = this.copySelectedProducts[index];
      this.copySelectedProducts.splice(index, 1);

      // 同步更新表格中的选择状态
      this.$nextTick(() => {
        if (this.$refs.copyTable) {
          // 找到表格中对应的行并取消选择
          const tableRow = this.copyCommodityList.find(item => item.id === removedItem.id);
          if (tableRow) {
            this.$refs.copyTable.toggleRowSelection(tableRow, false);
          }
        }
      });
    },
  },
  mounted() {
    this.initialization()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style scoped>
/* CSS变量定义 */
#bomList {
  --blue-color: #0a8fe2;
  --disabled-color: #c0c4cc;
  --gray-light: #909399;
  --gray-medium: #909399;
  --gray-dark: #303133;
  --border-light: #e6e6e6;
  --border-lighter: #f0f0f0;
  --bg-light: #f5f7fa;
  --bg-lighter: #fef0f0;
  --info-bg: #f0f9ff;
  --info-border: #b3d8ff;
  --select-color: rgba(64, 158, 255, 0.12);
}

#bomList .pagination-container {
  position: relative;
  bottom: 0;
  right: 0;
}

.productInfo {
  padding: 10px 0;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.productInfoItem {
  display: flex;
  align-items: center;
  width: 100%;
  padding-left: 8px;
}

.productInfoItem .label {
  font-weight: 500;
  color: var(--gray-medium);
  margin-right: 5px;
}


.bomTabs {
  margin-top: 10px;
}

.bomTabs .el-tabs__header {
  margin: 0 0 15px 0;
}

/* 空状态样式 */
.emptyContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: var(--gray-light);
}

.emptyIcon {
  font-size: 64px;
  color: var(--disabled-color);
  margin-bottom: 20px;
}

.emptyText {
  text-align: center;
}

.emptyText p {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: var(--gray-medium);
}

.emptyText .subText {
  font-size: 14px;
  color: var(--gray-light);
}

/* 添加物料弹窗样式 */
.materialDialog {
  height: 550px;
}

.materialDialog .leftCategory {
  border-right: 1px solid var(--border-light);
}

.materialDialog .categoryTitle {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--gray-dark);
}

.materialDialog .materialTitle {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--gray-dark);
}

.materialDialog .selectedTitle {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--gray-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.materialDialog .categoryTree {
  height: 480px;
}

.materialDialog .middleMaterial {
  border-right: 1px solid var(--border-light);
}

.materialDialog .materialSearch {
  margin-bottom: 10px;
}

.materialDialog .materialSearch .el-form {
  margin-bottom: 0;
}

.materialDialog .materialSearch .el-form-item {
  margin-bottom: 10px;
}

.materialDialog .materialTable {
  height: 450px;
}

.materialDialog .rightSelected {
  padding-left: 0;
}

.materialDialog .selectedList {
  border: 1px solid var(--border-light);
  border-top: none;
}

.materialDialog .emptySelected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--gray-light);
}

.materialDialog .emptySelected i {
  font-size: 48px;
  margin-bottom: 10px;
}

.materialDialog .emptySelected p {
  margin: 0;
  font-size: 14px;
}

.materialDialog .selectedItem {
  border-bottom: 1px solid var(--border-lighter);
  padding: 8px;
}

.materialDialog .selectedItem:last-child {
  border-bottom: none;
}

.materialDialog .itemContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.materialDialog .itemImage {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.materialDialog .itemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.materialDialog .itemImage.noImage {
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--disabled-color);
  font-size: 18px;
}

.materialDialog .itemInfo {
  flex: 1;
  min-width: 0;
}

.materialDialog .itemName {
  font-weight: 500;
  color: var(--gray-dark);
  font-size: 14px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 2px;
}

.materialDialog .itemCode {
  color: var(--gray-medium);
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 2px;
}

.materialDialog .itemCategory {
  color: var(--gray-light);
  font-size: 12px;
  line-height: 1.3;
}

.materialDialog .itemActions {
  flex-shrink: 0;
}

.materialDialog .removeBtn {
  padding: 4px;
}

.materialDialog .removeBtn:hover {
  background-color: var(--bg-lighter);
}

.materialDialog .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.materialDialog .selectedCount {
  color: var(--gray-medium);
  font-size: 14px;
}

/* 批量填充用量弹窗样式 */
.batchQuantityDialog {
  padding: 20px 0;
}

.batchQuantityDialog .el-form-item {
  margin-bottom: 20px;
}

.batchQuantityDialog .form-tips {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--info-bg);
  border: 1px solid var(--info-border);
  border-radius: 4px;
  font-size: 14px;
  margin-top: 10px;
}

.batchQuantityDialog .form-tips i {
  margin-right: 8px;
  font-size: 16px;
}

/* 可编辑表格样式 */
.view-cell {
  position: relative;
  padding: 4px 8px;
  cursor: pointer;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.view-cell:hover {
  background-color: var(--select-color);
  border-radius: 4px;
}

.view-cell:hover .edit-icon {
  opacity: 1;
}

.edit-icon {
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  margin-left: 8px;
  flex-shrink: 0;
  position: absolute;
  right: 8px;
  background-color: inherit;
  padding: 2px;
}

.remark-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 20px;
  /* 为编辑图标预留空间 */
}

.remark-placeholder {
  color: var(--disabled-color);
  font-style: italic;
}

.edit-cell {
  padding: 2px;
}

.edit-cell .el-input-number,
.edit-cell .el-input {
  width: 100%;
}

.edit-cell .el-input-number .el-input__inner,
.edit-cell .el-input .el-input__inner {
  border-color: var(--blue-color);
  box-shadow: 0 0 0 2px rgba(10, 143, 226, 0.2);
}

/* 紧凑型数字输入框样式 */
.compact-number-input {
  width: 100%;
}

.compact-number-input .el-input-number__increase,
.compact-number-input .el-input-number__decrease {
  width: 20px !important;
  height: 15px !important;
  line-height: 15px !important;
  font-size: 10px !important;
}

.compact-number-input .el-input__inner {
  padding-right: 25px !important;
  text-align: center;
}

/* 导入下拉菜单样式 */
.el-dropdown-menu__item {
  padding: 10px 20px;
}

.el-dropdown-menu__item i {
  margin-right: 8px;
  font-size: 14px;
}

.copyDialog {
  padding: 20px;
}

.copyDialog .leftCategory {
  border-right: 1px solid var(--border-light);
}

.copyDialog .categoryTitle {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--gray-dark);
}

.copyDialog .categoryTree {
  height: 480px;
}

.copyDialog .middleProduct {
  border-right: 1px solid var(--border-light);
}

.copyDialog .productTitle {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--gray-dark);
}

.copyDialog .productSearch {
  margin-bottom: 10px;
}

.copyDialog .productSearch .el-form {
  margin-bottom: 0;
}

.copyDialog .productSearch .el-form-item {
  margin-bottom: 10px;
}

.copyDialog .productTable {
  height: 450px;
}

.copyDialog .rightSelected {
  padding-left: 0;
}

.copyDialog .selectedTitle {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--gray-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyDialog .selectedList {
  border: 1px solid var(--border-light);
  border-top: none;
}

.copyDialog .emptySelected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--gray-light);
}

.copyDialog .emptySelected i {
  font-size: 48px;
  margin-bottom: 10px;
}

.copyDialog .emptySelected p {
  margin: 0;
  font-size: 14px;
}

.copyDialog .selectedItem {
  border-bottom: 1px solid var(--border-lighter);
  padding: 8px;
}

.copyDialog .selectedItem:last-child {
  border-bottom: none;
}

.copyDialog .itemContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copyDialog .itemImage {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.copyDialog .itemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.copyDialog .itemImage.noImage {
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--disabled-color);
  font-size: 18px;
}

.copyDialog .itemInfo {
  flex: 1;
  min-width: 0;
}

.copyDialog .itemName {
  font-weight: 500;
  color: var(--gray-dark);
  font-size: 14px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 2px;
}

.copyDialog .itemCode {
  color: var(--gray-medium);
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 2px;
}

.copyDialog .itemCategory {
  color: var(--gray-light);
  font-size: 12px;
  line-height: 1.3;
}

.copyDialog .itemActions {
  flex-shrink: 0;
}

.copyDialog .removeBtn {
  padding: 4px;
}

.copyDialog .removeBtn:hover {
  background-color: var(--bg-lighter);
}

.copyDialog .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyDialog .selectedCount {
  color: var(--gray-medium);
  font-size: 14px;
}
</style>

<!-- 全局样式，用于自定义确认对话框 -->
<style>
.wide-confirm-dialog {
  width: 500px !important;
  max-width: 90vw !important;
}

.wide-confirm-dialog .el-message-box__message {
  max-width: 100%;
  word-wrap: break-word;
  line-height: 1.5;
}
</style>
