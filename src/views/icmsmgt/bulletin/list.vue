<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFour" :model="formInline"
        class="demo-form-inline">
        <el-form-item label="通告标题" prop="title">
          <el-input v-model.trim="formInline.title" placeholder="请输入通告标题"></el-input>
        </el-form-item>

        <el-form-item label="通告类别" prop="type">
          <el-select v-model="formInline.type" clearable filterable>
            <el-option v-for="(item, index) of bulletinTypeList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文档类型" prop="docType">
          <el-select v-model="formInline.docType" clearable filterable>
            <el-option label="富文本" value="1"></el-option>
            <el-option label="pdf" value="2"></el-option>
            <el-option label="图片" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通告状态" prop="status">
          <el-select v-model="formInline.status" clearable filterable>
            <el-option label="草稿" value="1"></el-option>
            <el-option label="发布" value="2"></el-option>
            <el-option label="关闭" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss3A6B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addData(null)">新增</el-button>
      </div>
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="通告标题" prop="title" min-width="150"></el-table-column>
        <!-- <el-table-column label="文档概述" prop="summary" width="350">
            <template slot-scope="{row}">
              <span v-text="getSummary(row.summary)"></span>
          </template>
</el-table-column> -->
        <el-table-column label="通告类型" prop="typeName" width="100"></el-table-column>
        <el-table-column label="文档类型" prop="docType" width="100">
          <template slot-scope="{row}">
            <span v-if="row.docType === 1">富文本</span>
            <span v-if="row.docType === 2">pdf</span>
            <span v-if="row.docType === 3">图片</span>
          </template>
        </el-table-column>
        <el-table-column label="适用国家" prop="sltCountry" min-width="80"></el-table-column>
        <el-table-column label="访问次数" prop="visitTimes" width="80"></el-table-column>
        <el-table-column label="置顶" prop="isTop" width="80">
          <template slot-scope="{row}">
            <span v-if="row.top == true" class="successColor">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80">
          <template slot-scope="{row}">
            <span v-if="row.status === 1">草稿</span>
            <span v-if="row.status === 2" class="successColor">发布</span>
            <span v-if="row.status === 3" class="errorColor">关闭</span>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="100">
          <template slot-scope="{row}">
            <div class="sortArea">
              <el-input-number v-model.trim="row.sort" placeholder="请输入排序" @change="updatedSort(row)"
                controls-position="right" :min="1" :max="9999" :precision="0" :step="1"></el-input-number>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100"></el-table-column>
        <el-table-column label="更新人员" prop="username" width="100"></el-table-column>
        <el-table-column label="更新日期" prop="updatedTime" width="160">
          <template slot-scope="{row}">
            <div>
              {{ row.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="260">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss3A6B_104')" size="small"
              @click="headerDetail(row)">详情</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A6B_103')" type="text" size="small"
              @click="addData(row)">编辑</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss3A6B_119')" type="text" size="small"
              @click="statusEdit(row, 'close')">关闭</el-button>
            <el-button v-if="row.status == 3 && hasPerm('menuAsimss3A6B_119')" type="text" size="small"
              @click="statusEdit(row, 'open')">开启</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A6B_118')" type="text" size="small"
              @click="statusEdit(row, 'issue')">发布</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss3A6B_118')" type="text" size="small"
              @click="statusEdit(row, 'revocation')">撤回</el-button>
            <el-button v-if="row.top == 0 && hasPerm('menuAsimss3A6B_103')" type="text" size="small"
              @click="statusEdit(row, 'top')">置顶</el-button>
            <el-button v-if="row.top == 1 && hasPerm('menuAsimss3A6B_103')" type="text" size="small"
              @click="statusEdit(row, 'notop')">取消置顶</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A6B_102')" class="deleteButton" type="text"
              size="small" @click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
    </div>
  </div>
</template>
<script>
import { addTabs, tableHeight, DonMessageTip } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { bulletinData, bulletinStatusEdit, bulletinDelete, bulletinEdit, bulletinTypeData } from '@/api/icmsmgt.js'
export default {
  name: 'icmsmgt_bulletin_list',
  components: { Pagination },
  data() {
    return {
      formInline: {
        // 标题
        title: '',
        // 文档概述
        summary: '',
        // 状态
        status: '',
        // 文档类型
        docType: '',
        type: '', // 语种
      },
      temp: {
        id: '',
        title: '',
        summary: '',
        status: 1,
        docType: 1,
        docPath: '',
        sort: 1, // 排序
        content: '', // 富文本内容
        remark: '',
        updatedTime: '',
        visitTimes: ''
      },
      statusList: [
        { name: '草稿', code: 1 },
        { name: '发布', code: 2 },
        { name: '关闭', code: 3 },
      ],
      typeList: [
        { name: '富文本', code: 1 },
        { name: 'pdf', code: 2 },
        { name: '图片', code: 3 },
      ],
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      languageList: [],
      maximumHeight: 0,
      bulletinTypeList: [],
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('title', this.formInline.title)
      params.append('status', this.formInline.status)
      params.append('docType', this.formInline.docType)
      params.append('type', this.formInline.type)
      bulletinData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
        this.tableHeightArea();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    // 重置
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },

    // 新增
    addData(row) {
      var title = ""
      var id = ""
      if (row == undefined) {
        title = "新增技术通告"
        id = "add"
      } else {
        title = `编辑 ${row.title}`
        id = row.id
      }
      this.$router.push({ name: 'addBulletin', params: { id: id } })
      addTabs(this.$route.path, title);
    },
    // 删除
    del(row) {
      var _this = this
      this.$confirm('确定删除当前技术通告?', '删除技术通告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        bulletinDelete(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    // 修改排序
    updatedSort(row) {
      var params = new URLSearchParams()
      if (row.sort > 9999) {
        row.sort = 9999
      }
      params.append('id', row.id);
      params.append('title', row.title)
      params.append('sltCountry', row.countryCodeList.toString())
      params.append('docType', row.docType)
      params.append('docPath', row.docPath)
      params.append('type', row.type)
      params.append('sort', row.sort)
      bulletinEdit(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.operateTip'));
          // this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg);
        }
      }).catch(err => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })

    },
    // 修改公告状态
    statusEdit(row, sign) {
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('sign', sign)
      if (row.docType != 1) {
        params.append('docPath', row.docPath)
      }

      bulletinStatusEdit(params).then(res => {
        console.log(res);
        if (res.data.code === 100) {
          DonMessageTip(sign);
          this.dataList();
          window.systemReminder();
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(err => {
        console.log(err);
        if (err !== null && err !== '' && err.responseText !== null) {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        }
      })
    },

    // 获取类型
    getBulletinTypeList() {
      bulletinTypeData().then(res => {
        this.bulletinTypeList = res.data.data
      }).catch(e => {
        this.bulletinTypeList = []
      })
    },
    // 获取语种
    // getLanguage(){
    //   bulletinLanguageType().then(res => {
    //     this.languageList = res.data.data
    //     if(!this.languageList){
    //       this.languageList = []
    //     }
    //   })
    // },
    // 详情
    headerDetail(row) {
      this.$router.push({ name: 'bulletinDetails', params: { id: row.id } })
      addTabs(this.$route.path, row.title);
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted() {
    this.dataList();
    this.getBulletinTypeList();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },

}
</script>
