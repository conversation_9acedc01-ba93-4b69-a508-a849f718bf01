<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :label-width="$labelFour" ref="searchForm" :model="searchForm" class="demo-form-inline">

        <el-form-item label="品牌" prop="trainId">
          <el-select v-model="trainId" placeholder="请选择" clearable filterable @change="getPowerList('1')">
            <el-option v-for="item in trainList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车型" prop="yearId">
          <el-select v-model="powerId" placeholder="请选择" clearable filterable @change="getYearList('1')">
            <el-option v-for="item in powerList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年款" prop="powerId">
          <el-select v-model="yearId" placeholder="请选择" clearable filterable>
            <el-option v-for="item in yearList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="手册类别" prop="targetType">
          <el-select v-model="searchForm.targetType" clearable>
            <el-option v-for="(item, index) of manualList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目录" prop="targetName">
          <el-input v-model.trim="targetName" placeholder="请输入目录"></el-input>
        </el-form-item>
        <el-form-item label="是否满意" prop="score">
          <el-select v-model="searchForm.score" clearable filterable>
            <el-option v-for="(item,index) in resultListInfo" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchForm.status" clearable filterable>
            <el-option v-for="(item,index) in statusListInfo" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()">{{ $t('button.search') }}</el-button>
          <el-button plain @click="resetSearch('searchForm')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <!-- <el-button type="text" icon="el-icon-s-data"  @click="statisticsClick()">统计</el-button> -->
        <el-button v-if="hasPerm('menuAsimss3A8B_103')" type="text" size="small" icon="el-icon-edit" @click="editStatus(null)">
          处理
        </el-button>
        <el-button v-if="hasPerm('menuAsimss3A8B_102')" type="text" size="small" icon="el-icon-delete" @click="batchDelete">
          删除
        </el-button>
      </div>
      <!-- 列表内容 -->
      <el-table
        style="width:100%"
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        border
        stripe
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column label="序号" type="index" width="60" ></el-table-column> -->
        <el-table-column type="selection" width="40" align="center" fixed="left"></el-table-column>
        <el-table-column label="序号" type="index" width="60"></el-table-column>

        <el-table-column label="手册类别" prop="targetType" width="150">
<!--          <template slot-scope="{row}">-->
<!--            <span>{{ getManual(row) }}</span>-->
<!--          </template>-->
        </el-table-column>
        <el-table-column label="目录" prop="targetName" min-width="200"></el-table-column>
        <el-table-column label="品牌" prop="modelName" width="140"></el-table-column>
        <el-table-column label="车型" prop="yearName" width="140"></el-table-column>
        <el-table-column label="年款" prop="powerName" width="140"></el-table-column>
        <el-table-column label="是否满意" prop="score" width="85">
          <template slot-scope="{row}">
            <span v-if="row.score === 5">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="85">
          <template slot-scope="{row}">
            <span v-if="row.status === 1" class="errorColor">未处理</span>
            <span v-else-if="row.status === 2" class="successColor">已处理</span>
            <span v-else>忽略</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="评价人员" prop="username" min-width="100"  ></el-table-column> -->
        <el-table-column label="评价时间" prop="operateTime" width="180">
          <template slot-scope="{row}">
            <div>
              {{ row.operateTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="160">
          <template slot-scope="{row}">
            <el-button type="text" size="small" v-if="hasPerm('menuAsimss3A8B_104')" @click="headerDetail(row)">详情
            </el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A8B_103')" type="text" size="small" @click="editStatus(row)">
              处理
            </el-button>
            <el-button v-if="hasPerm('menuAsimss3A8B_102')" class="deleteButton" type="text" size="small" @click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>

      <!-- 详情 -->
      <el-dialog v-dialogDrag title="评价详情" width="730px !important" :visible.sync="dialogdetailsFormVisible" :close-on-click-modal="false">
        <div style="width:100%; max-height: 570px;">
          <table width="100%" class="tabtop13">
            <tr>
              <td class="tdTitle" width="15%">手册类别</td>
              <td width="85%">{{ temp.targetType }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">目录</td>
              <td width="85%">{{ temp.targetName }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">品牌</td>
              <td width="85%">{{ temp.modelName }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">车型</td>
              <td width="85%">{{ temp.yearName }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">年款</td>
              <td width="85%">{{ temp.powerName }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">是否满意</td>
              <td width="85%">{{ temp.score === 5 ? "满意" : "不满意" }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">评价时间</td>
              <td width="85%">{{ temp.operateTime | conversion("yyyy-MM-dd HH:mm:ss") }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">评价用户</td>
              <td width="85%">{{ temp.username }}</td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">选项</td>
              <td width="85%">
                <el-row v-for="(itm, index) in temp.options.split(',')" :key="index" style="margin: 5px 0px;">
                  <span>{{ index+1 }}</span><span>&nbsp;&nbsp; {{ itm }}</span>
                </el-row>
              </td>
            </tr>
            <tr>
              <td class="tdTitle" width="15%">内容</td>
              <td width="85%">
                <el-row>
                  {{ temp.content }}
                </el-row>
                <!-- <el-row v-if="temp.translateContent && temp.translateContent.length > 0" style="font-size: 12px;color: #999999;margin: 5px 0px 0px;">
                  <span>译：</span><span>{{ temp.translateContent }}</span>
                </el-row> -->
              </td>
            </tr>
          </table>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {tableHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {commentData, commentStatus, commentDel, commentInfo, commentManual} from '@/api/material.js'
import {getCarTrainList, getCarConfigList} from '@/api/statistics.js'

export default {
  name: 'material_comment_list',
  components: {Pagination},
  data() {
    return {
      formLabelWidth: '100px',
      // 搜索表单
      searchForm: {
        trainId: '',  // 整编
        targetType: '',  // 手册
        score: "", // 是否满意
        status: "", // 状态
      },
      // 当前页
      currentPage: 1,
      // 每页显示的条数
      pagesize: 10,
      // 总条数
      total: 0,
      // 获取的数据集
      resultList: [],
      // 手册结果集
      manualList: [],

      dialogdetailsFormVisible: false,   // 详情弹窗

      temp: {
        id: "",
        trainId: "",
        moduleCode: "",
        targetName: "",
        targetCode: "",
        targetType: "",
        status: "",
        score: "",
        options: "",
        content: "",
        operatorId: "",
        operateTime: "",
        updatedTime: "",
        updatedUser: "",
        username: ""
      },

      deleteList: [],
      maximumHeight: 0,

      // 品牌-车型
      trainList: [],
      modelList: [],

      yearList: [],
      powerList: [],
      configList: [],
      // 车型ID
      trainId: '',
      // 年款
      yearId: '',
      // 动力类型
      powerId: '',
      // 配置
      configId: '',

      targetName: "",

      resultListInfo:[
        {
          "code":"5",
          "name":"满意",
        },{
          "code":"1",
          "name":"不满意",
        }
      ],
      statusListInfo:[
        {
          "code":"2",
          "name":"已处理",
        },{
          "code":"1",
          "name":"未处理",
        }
      ],
    }
  },

  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      var params = new URLSearchParams();
      params.append('page', this.currentPage);
      params.append('limit', this.pagesize);
      params.append('yearId', this.yearId)
      params.append('modelId', this.trainId)
      params.append('powerId', this.powerId)
      params.append('targetType', this.searchForm.targetType);
      params.append('score', this.searchForm.score);
      params.append('status', this.searchForm.status);
      params.append('targetName', this.targetName)
      commentData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total;   // 总条数
          this.resultList = res.data.data;   // 数据
        } else {
          this.$DonMessage.error(res.data.msg);
        }
        this.tableHeightArea();
      }).catch(err => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      });
    },

    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.currentPage = 1;
      this.dataList();
    },

    // 重置搜索表单
    resetSearch() {
      // 车型ID
      this.trainId = '';
      // 年款
      this.yearId = '';
      // 动力类型
      this.powerId = '';
      this.yearList = [];
      this.powerList = [];
      this.searchForm.targetType = "";
      this.searchForm.score = "";
      this.searchForm.status = "";
      this.currentPage = 1;
      this.targetName = "";
      this.dataList();
    },

    // 批量删除
    handleSelectionChange(val) {
      this.deleteList = val
    },

    // 执行批量删除操作
    batchDelete(){
      if (this.deleteList.length <= 0) {
        this.$DonMessage.warning("请选择需要删除的数据");
        return false;
      }
      this.$confirm('确定删除【' + this.deleteList.length + '】条数据吗?', '删除评价', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let ids = ""
        this.deleteList.forEach(item => {
          ids += item.id + ","
        })
        var params = new URLSearchParams()
        params.append('ids', ids)
        commentDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            this.dataList()
          } else {
            this.$DonMessage.error(res.data.msg);
          }
        })
      })
    },


    // 修改状态
    editStatus(row) {
      let ids = ""
      if (row != null) {
        ids = row.id
      } else {
        this.deleteList.forEach(item => {
          ids += item.id + ","
        })
      }
      if (ids.length <= 0) {
        this.$DonMessage.warning('请选择未处理的问题')
        return false;
      }
      var params = new URLSearchParams()
      params.append('ids', ids)
      params.append('type', "2")
      commentStatus(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.operateTip'))
          this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(err => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },

    // 删除
    del(row) {
      this.$confirm('确定删除【' + row.targetName + '】的评价吗?', '删除评价', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('ids', row.id)
        commentDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList();
          } else {
            this.$DonMessage.error('删除失败，' + res.data.msg)
          }
        })
      })
    },

    // 详情
    headerDetail(row) {
      this.temp = Object.assign({}, row)
      commentInfo(row.id).then(res => {
        this.temp.options = res.data.data.options
        this.dialogdetailsFormVisible = true
      }).catch(res => {
        // this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },

    // 查询 品牌 - 车系
    getTrainList() {
      this.trainList = []
      getCarTrainList().then(res => {
        // this.trainList = res.data.data

        res.data.data.forEach(e => {
          if (Array.isArray(e.children)) { // 确保 e.children 是数组
            this.trainList.push(...e.children); // 展开并添加
          } else {
            this.trainList.push(e.children); // 如果不是array数组，直接添加
          }
        })

        this.modelList = []
        this.trainList.forEach(e => {
          if (Array.isArray(e.children)) { // 确保 e.children 是数组
            this.modelList.push(...e.children); // 展开并添加
          } else {
            this.modelList.push(e.children); // 如果不是array数组，直接添加
          }
        })

      }).catch(e => {
        this.trainList = []
      })

    },


    // 获取年款
    getYearList(type) {
      let pId = this.powerId
      let lists = this.modelList
      let list = []
      for (let i = 0; i < lists.length; i++) {
        const t = lists[i];
        if (pId === t.id) {
          t.children.forEach(e => {
            list.push(e)
          })
        }
      }

      this.yearList = list
      this.powerId = ''
      this.configList = []
      this.configId = ''

    },

    getPowerList(type) {
      let pId = this.trainId
      let list = []
      for (let i = 0; i < this.trainList.length; i++) {
        const t = this.trainList[i];
        if (pId === t.id) {
          list = t.children
          break;
        }
      }

      this.powerList = list
      this.yearId = ''
      this.yearList = []
      this.powerId = ''
      this.configList = []
      this.configId = ''


    },


    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 手册类型
    getManualList() {
      commentManual().then(res => {
        this.manualList = res.data.data
      }).catch(e => {
        this.manualList = []
      })
    },
    getManual(row) {
      if (!this.manualList) {
        return "";
      }
      let name = ""
      for (let i = 0; i < this.manualList.length; i++) {
        const t = this.manualList[i];
        if (t.code === row.targetType) {
          name = t.name;
          break
        }
      }
      return name;
    }

  },
  mounted() {
    this.dataList();
    this.getManualList();
    this.getTrainList();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style>
/* //设置输入框超出长度隐藏并显示省略号 */
.inputDeep >>> .el-textarea__inner {
  border: 0;
  resize: none; /* 这个是去掉 textarea 下面拉伸的那个标志，如下图 */
}
</style>
