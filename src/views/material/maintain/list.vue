<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelTwo" :model="formInline" class="demo-form-inline">
        <el-form-item label="标题" prop="title">
          <el-input v-model.trim="formInline.title" placeholder="请输入案例标题"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formInline.status" clearable filterable>
            <el-option label="草稿" value="1"></el-option>
            <el-option label="发布" value="2"></el-option>
            <el-option label="关闭" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss1A7B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addData(null)">新增</el-button>
      </div>
      <!-- 列表内容 -->
      <el-table style="width:100%" highlight-current-row :data="resultList" border stripe>
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="标题" prop="title" min-width="150"></el-table-column>
        <el-table-column label="车系" prop="trainName" min-width="100"></el-table-column>
        <el-table-column label="年款" prop="trainYear" min-width="100"></el-table-column>
        <el-table-column label="车型" prop="modelName" min-width="100"></el-table-column>
        <el-table-column label="访问次数" prop="visitTimes" width="100"></el-table-column>

        <el-table-column label="状态" prop="status" width="80">
          <template slot-scope="{row}">
            <span v-if="row.status == '1'">草稿</span>
            <span v-if="row.status == '2'">发布</span>
            <span v-if="row.status == '3'">关闭</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="createdTime" width="140">
          <template slot-scope="{row}">
            <div>
              {{ row.createdTime | conversion("yyyy-MM-dd") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="260">
          <template slot-scope="{row}">
            <el-button type="text" size="small" v-if="hasPerm('menuAsimss1A7B_104')" @click="headerDetail(row)">详情
            </el-button>
            <el-button v-if="row.status == '1' && hasPerm('menuAsimss1A7B_103')" type="text" size="small"
              @click="addData(row)">
              编辑
            </el-button>
            <el-button v-if="row.status == '2' && hasPerm('menuAsimss1A7B_119')" type="text" size="small"
              @click="statusEdit(row, 'close')">
              关闭
            </el-button>
            <el-button v-if="row.status == '3' && hasPerm('menuAsimss1A7B_119')" type="text" size="small"
              @click="statusEdit(row, 'open')">
              开启
            </el-button>
            <el-button v-if="row.status == '1' && hasPerm('menuAsimss1A7B_118')" type="text" size="small"
              @click="statusEdit(row, 'issue')">
              发布
            </el-button>
            <el-button v-if="row.status == '2' && hasPerm('menuAsimss1A7B_118')" type="text" size="small"
              @click="statusEdit(row, 'revocation')">
              撤回
            </el-button>
            <el-button v-if="row.status == '1' && hasPerm('menuAsimss1A7B_102')" class="deleteButton" type="text"
              size="small" @click="del(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />
    </div>
  </div>
</template>
<script>
import { addTabs, DonMessageTip } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { caseData, caseStatus, caseDel } from '@/api/material.js'

export default {
  name: 'material_maintain_list',
  components: { Pagination },
  data() {
    return {
      formInline: {
        title: '',
        status: '',
      },
      temp: {
        id: '',
        title: '',
        content: '',
        status: '',
        type: '',
        visitTimes: '',
        isTop: '',
        isRecmd: '',
      },
      statusList: [
        { name: '草稿', code: 1 },
        { name: '发布', code: 2 },
        { name: '关闭', code: 3 }
      ],
      typeList: [],
      formLabelWidth: '100px',
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
    }
  },
  methods: {
    // 数据
    dataList() {
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('title', this.formInline.title)
      params.append('status', this.formInline.status)
      caseData(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },

    // 搜索
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    // 更改状态
    statusEdit(row, sign) {
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('sign', sign)
      caseStatus(params).then(res => {
        if (res.data.code === 100) {
          DonMessageTip(sign);
          this.dataList()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(err => {
        if (err !== null && err !== '' && err.responseText !== null) {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        }
      })
    },
    // 重置
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.formInline = {
        title: '',
        status: '',
      };
      this.currentPage = 1;
      this.dataList();
    },
    // 新增
    addData(row) {
      var title = ""
      sessionStorage.setItem("caseInfo", JSON.stringify(row));
      if (row == undefined) {
        title = "维修案例>新增"
      } else {
        title = "维修案例>编辑"
      }
      this.$router.push({ name: 'maintainAdd' })
      addTabs(this.$route.path, title);
    },
    // 删除
    del(row) {
      var _this = this
      this.$confirm('确定删除当前系统公告', '删除公告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        caseDel(row.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },
    // 详情
    headerDetail(row) {
      let title = "维修案例>预览"
      this.$router.push({ name: 'maintainDetail', query: { id: row.id } })
      addTabs(this.$route.path, title);
    },
  },
  mounted() {
    this.dataList()
  }
}
</script>
