<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="$labelFive" :model="formInline" class="demo-form-inline">
        <el-form-item label="问题主题" prop="problemTheme">
          <el-input v-model.trim="formInline.problemTheme" placeholder="请输入问题主题"></el-input>
        </el-form-item>
        <el-form-item label="问题分类" prop="problemType">
          <el-select v-model="formInline.problemType" clearable filterable>
            <el-option v-for="(item, index) in problemTypeList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item :label="empowerChina ? '服务店名称' : '客户名称'" prop="stationName"> -->
        <el-form-item label="服务店名称" prop="stationName">
          <el-input v-model.trim="formInline.stationName" placeholder="请输入服务店名称"></el-input>
        </el-form-item>
        <!-- <el-form-item :label="empowerChina ? '服务店代码' : '客户代码'" prop="stationCode"> -->
        <el-form-item label="服务店代码" prop="stationCode">
          <el-input v-model.trim="formInline.stationCode" placeholder="请输入服务店编码"></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="stationCode">
          <el-select v-model="formInline.modelId" clearable filterable>
            <el-option v-for="(item, index) in trains" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formInline.status" clearable>
            <el-option label="未回复" value="1"></el-option>
            <el-option label="已回复" value="2"></el-option>
            <el-option label="已结束" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="urgent">
          <el-select v-model="formInline.urgent" clearable>
            <el-option label="加急" value="1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="提问时间">
          <el-date-picker :clearable="false" :editable="false" prop="begintime" align="center" type="date"
            placeholder="开始日期" v-model="valueDate.start" :picker-options="pickerBeginTime"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker :clearable="false" :editable="false" prop="endtime" align="center" type="date"
            placeholder="结束日期" v-model="valueDate.end" :picker-options="pickerEndTime"></el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ $t('button.search') }}</el-button>
          <el-button plain @click="reset('formInline')">{{ $t('button.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" size="small" icon="el-icon-download" @click="exportData()">
          导出数据
        </el-button>
      </div>
      <!-- 分页查询 -->
      <el-table style="width:100%" ref="table" highlight-current-row :max-height="maximumHeight" :data="resultList"
        border stripe @header-dragend="changeColWidth" @selection-change="handleSelectionChange" :row-key="getRowKey">
        <el-table-column type="selection" width="40" fixed="left" align="center" reserve-selection></el-table-column>
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="主题" prop="problemTheme" min-width="120">
          <template slot-scope="{row}">
            <el-badge v-if="row.type == 1" is-dot class="item"></el-badge>
            <span @click="hasPerm('menuAsimss3A11B_101') ? handelReply(row) : ''"
              style="text-decoration: underline;cursor: pointer;">{{ row.problemTheme }}</span>
          </template>
        </el-table-column>
        <el-table-column label="品牌" prop="trainName" min-width="100">
          <template slot-scope="{row}">
            <span>{{ getTrainName(row.brandId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车型" prop="yearName" min-width="100">
          <template slot-scope="{row}">
            <span>{{ getTrainName(row.modelId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="年款" prop="powerName" min-width="100">
          <template slot-scope="{row}">
            <span>{{ getModelName(row.yearId, 'year') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="配置" prop="modelName" min-width="100">
          <template slot-scope="{row}">
            <span>{{ row.configName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="问题分类" prop="problemType" min-width="100">
          <template slot-scope="{row}">
            <span>{{ getFeedbackType(row.problemType) }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="国家" prop="countryName" min-width="80"></el-table-column>-->
        <!-- <el-table-column :label="empowerChina ? '服务店代码' : '客户代码'" prop="stationCode" min-width="120"></el-table-column>
        <el-table-column :label="empowerChina ? '服务店名称' : '客户名称'" prop="stationName" min-width="120"></el-table-column> -->
        <el-table-column label="服务店代码" prop="stationCode" min-width="120"></el-table-column>
        <el-table-column label="服务店名称" prop="stationName" min-width="120"></el-table-column>
        <el-table-column label="提问时间" prop="createdTime" width="160">
          <template slot-scope="{row}">
            {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="车型" prop="trainName" min-width="100"></el-table-column> -->
        <el-table-column label="状态" prop="status" min-width="80">
          <template slot-scope="{row}">
            <span>{{ row.status == 1 ? "未回复" : row.status == 2 ? "已回复" : "已结束" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类型" prop="urgent" min-width="60">
          <template slot-scope="{row}">
            <img v-if="row.urgent" :src="require('../../../assets/image/indexIcon/urgentIcon.png')"
              style="width: 13px;vertical-align: middle;margin-top: -2px;" />
            <span class="errorColor">{{ row.urgent ? "加急" : "" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss3A11B_101')" :disabled="row.status == 3" size="small"
              @click="handelReplyClick(row)">
              回复
            </el-button>
            <el-button type="text" class="deleteButton" v-if="hasPerm('menuAsimss3A11B_102')" :disabled="row.status == 3"
              size="small" @click="handelDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />

      <!-- 国内回复 -->
      <el-dialog v-dialogDrag width="1000px !important" title="回复" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false" v-if="dialogFormVisible">
        <feedbackDetail :replyId="replyId" :countryCode="countryCode" :rowObj="rowObj" :empowerChina="empowerChina">
        </feedbackDetail>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import feedbackDetail from './feedbackDetail.vue';
import {
  sysServerUrl,
  tableHeight,
} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  feedbackData,
  feedbackDel,
  feedbackTypeList,
  feedbackExport
} from '@/api/material.js';
import { importAttach, trainFindList, modelFindList } from '@/api/sysmgt.js'

export default {
  name: 'material_feedback_list',
  components: { Pagination, feedbackDetail },
  data() {
    return {
      formInline: {
        problemTheme: '',
        stationName: '',
        urgent: '',
        stationCode: '',
        trainId: '',
        problemType: '',
        status: '',
        modelId: '',
      },
      replyId: '',
      countryCode: '',
      replyList: [],
      dialogFormVisible: false,
      imageUrl: sysServerUrl + 'sys/upload/display?filePath=',
      resultList: [],
      trainList: [],
      problemTypeList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      replyLabelWidth: '180px',
      maximumHeight: 0,
      empowerChina: true,
      rowObj: {},
      trains: [],
      trainFindList: [],
      modelFindList: [],
      // 时间段
      valueDate: {
        start: '',
        end: '',
      },
      pickerBeginTime: {
        disabledDate: (time) => {
          return this.valueDate.end != null ? time.getTime() > new Date(this.valueDate.end) : false //只能选结束日期之前的日期
          //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
        }
      },
      pickerEndTime: {
        disabledDate: (time) => {
          return this.valueDate.start != null ? time.getTime() < new Date(this.valueDate.start) : false //只能选开始时间之后的日期
          //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
        }
      },

      // 导出数据选中的数据
      exportDataList: [],
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    getRowKey(row) {
      return row.id;
    },
    // 分页查询数据
    dataList() {
      var params = new FormData();
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('stationName', this.formInline.stationName)
      params.append('problemTheme', this.formInline.problemTheme)
      params.append('urgent', this.formInline.urgent)
      params.append('stationCode', this.formInline.stationCode)
      params.append('problemType', this.formInline.problemType)
      params.append('status', this.formInline.status)
      params.append('trainId', this.formInline.trainId)
      params.append('modelId', this.formInline.modelId)
      if (this.valueDate.start) {
        params.append('start', this.$options.filters.conversion(this.valueDate.start, "yyyy-MM-dd"))
      }
      if (this.valueDate.end) {
        params.append('end', this.$options.filters.conversion(this.valueDate.end, "yyyy-MM-dd"))
      }

      feedbackData(params).then(res => {
        if (res.data.code == "100") {
          this.total = res.data.total;
          this.resultList = res.data.data;
        } else {
          this.$DonMessage.error(res.data.msg);
        }
        this.tableHeightArea()
      })
    },

    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList();
    },

    handelReplyClick(row) {
      if (row.status == 3) {
        return;
      }
      this.handelReply(row);
    },
    // 回复
    handelReply(row) {
      this.replyId = row.id;
      this.countryCode = row.countryCode;
      this.rowObj = row
      this.dialogFormVisible = true;
    },
    // 删除
    handelDelete(row) {
      if (row.type == 4) {
        return;
      }
      this.$confirm('确定删除问题【' + row.problemTheme + '】吗?', '删除反馈记录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append("id", row.id)
        params.append("type", "1")
        feedbackDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
            window.systemReminder();
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },

    // 重置
    reset(formInline) {
      if (this.$refs[formInline]) {
        if (this.$refs[formInline].resetFields() !== undefined) {
          this.$refs[formInline].resetFields()
        }
      }
      this.valueDate = {
        start: '',
        end: '',
      },

        this.formInline.problemTheme = ''
      this.formInline.stationName = ''
      this.formInline.stationCode = ''
      this.formInline.urgent = ''
      this.formInline.problemType = '';
      this.formInline.trainId = '';
      this.formInline.status = '';
      this.currentPage = 1
      this.dataList()
    },
    // 获取品牌车系
    // getBrandTrainList(){
    //   feedbackTreeList().then(res => {
    //     this.trainList = res.data.data
    //   }).catch(err => {
    //     this.$DonMessage.error(this.$t('errorTip.systemTip'));
    //   })
    // },
    // 问题分类
    getFeedbackTypeList() {
      feedbackTypeList().then(res => {
        this.problemTypeList = res.data.data
      }).catch(err => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })
    },

    getFeedbackType(type) {
      let name = "";
      let len = this.problemTypeList.length;
      for (let i = 0; i < len; i++) {
        if (type === this.problemTypeList[i].code) {
          name = this.problemTypeList[i].name;
          break
        }
      }

      return name;
    },
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },

    // 车型
    getTrainList() {
      trainFindList().then(res => {
        this.trainFindList = res.data.data
        this.trainFindList.forEach(o => {
          if (o.type === 'model') {
            this.trains.push(o);
          }
        })
      }).catch(e => {
        this.trainFindList = []
        this.trains = [];
      })
    },
    // 配置
    getModelList() {
      modelFindList().then(res => {
        this.modelFindList = res.data.data
      }).catch(e => {
        this.modelFindList = []
      })
    },
    getTrainName(id) {
      let len = this.trainFindList.length
      let name = "";
      for (let i = 0; i < len; i++) {
        if (id === this.trainFindList[i].id) {
          name = this.trainFindList[i].name;
          break
        }
      }
      return name;
    },
    getModelName(id, type) {
      let len = this.modelFindList.length
      let name = "";
      for (let i = 0; i < len; i++) {
        if (id === this.modelFindList[i].id && type === this.modelFindList[i].type) {
          name = this.modelFindList[i].name;
          break
        }
      }
      return name;
    },


    // ===== 导出数据
    // 选中
    handleSelectionChange(val) {
      this.exportDataList = val
    },
    // 导出
    exportData() {
      let list = [];
      for (let i = 0; i < this.exportDataList.length; i++) {
        list.push(this.exportDataList[i].id);
      }
      if (list.length <= 0) {
        this.$DonMessage.warning("请选择反馈记录")
        reutrn;
      }
      var formData = new FormData();
      formData.append('ids', list.toString());
      feedbackExport(formData).then(res => {
        if (!res.data) {
          this.$DonMessage.warning(this.$t("errorTip.exportTip"));
          return
        }
        var header = res.headers["content-disposition"].split("filename=")[1];
        var name = decodeURI(header);
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    }

  },
  mounted() {
    this.getTrainList()
    this.getModelList()
    this.dataList();
    // this.getLangeList();
    this.getFeedbackTypeList();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style>
.el-dialog .memberInfo .el-form-item__label {
  line-height: 26px;
}

.el-dialog .memberInfo .el-form-item__content {
  line-height: 26px;
}

.memberInfo .el-tree {
  max-height: 240px;
  overflow: auto;
}

.el-dialog-div {
  height: 80vh;
  overflow-x: hidden;
}

.layoutContainer img {
  width: 100%;
  position: relative;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

.feek_access:hover {
  text-decoration: underline;
}
</style>
