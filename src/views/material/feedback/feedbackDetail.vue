<template>
  <div>
    <div>
      <table class="tabtop13" width="100%">
        <tr>
          <!-- <td class="tdTitle" width="15%">{{ empowerChina ? '服务店编码' : '客户代码' }}</td> -->
          <td class="tdTitle" width="15%">服务店编码</td>
          <td width="35%">{{ temp.stationCode }}</td>
          <!-- <td class="tdTitle" width="15%">{{ empowerChina ? '服务店名称' : '客户名称' }}</td> -->
          <td class="tdTitle" width="15%">服务店名称</td>
          <td width="35%">{{ temp.stationName }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">提问人姓名</td>
          <td width="35%">{{ temp.questioner }}</td>
          <td class="tdTitle" width="15%">提问人电话</td>
          <td width="35%">{{ temp.phone }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">邮箱</td>
          <td width="35%">{{ temp.email }}</td>
          <td class="tdTitle" width="15%">提问时间</td>
          <td width="35%">{{ temp.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">品牌</td>
          <td width="35%">{{ temp.trainName }}</td>
          <td class="tdTitle" width="15%">车型</td>
          <td width="35%">{{ temp.yearName }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">年款</td>
          <td width="35%">{{ temp.powerName }}</td>
          <td class="tdTitle" width="15%">配置</td>
          <td width="35%">{{ temp.modelName }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">VIN</td>
          <td width="35%">{{ temp.vinCode }}</td>
          <td class="tdTitle" width="15%">问题类型</td>
          <td width="35%">{{ temp.urgent ? "加急" : " " }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">问题分类</td>
          <td width="85%" colspan="3">{{ temp.problemType }}</td>

        </tr>
        <tr>
          <td class="tdTitle" width="15%">问题主题</td>
          <td width="85%" colspan="3">
            {{ temp.problemTheme }}
            <div v-if="temp.translateTheme" style="color: #666666;margin-top: 5px;">
              <div>译: </div>
              <div>{{ temp.translateTheme }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">问题描述</td>
          <td width="85%" colspan="3">
            <div>{{ temp.problemDesc }}</div>
            <div v-if="temp.translateDesc" style="color: #666666;margin-top: 5px;">
              <div>译: </div>
              <div>{{ temp.translateDesc }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">附件</td>
          <td colspan="3" width="85%">
            <div class="uploadFileInfo">
              <div v-for="accessory in temp.accessorys" :key="accessory.id"
                @click="previewFormat.includes(accessory.format) ? previewImage(temp, accessory) : handleDownload(accessory)">
                <div>
                  <img :src="getIoc(accessory, 'smallIoc')" />
                </div>
                <div>
                  {{ accessory.fileName }}
                </div>
              </div>
            </div>
          </td>
        </tr>
      </table>
      <!-- 历史回复 -->
      <div class="historyReply">
        <p>历史回复</p>
        <div>
          <div v-for="(item) in replyList" :key="item.id">
            <p class="topInfo">
              <span>
                <span>{{ item.replyName }}</span>{{ item.replyType == 2 ? "追问" : "回复" }}：
              </span>
              <span> {{ item.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
            </p>
            <el-row style="padding: 2px 12px; word-break:break-all; white-space: pre-line">
              {{ item.replyContent }}
            </el-row>
            <el-row
              style="padding: 2px 12px;word-break: break-all;word-break: break-word;white-space: pre-line;color: #666666;"
              v-if="item.translateContent">
              <div>译：</div>{{ item.translateContent }}
            </el-row>
            <div class="accessoryInfo" v-if="item.feedbackAccessoryList && item.feedbackAccessoryList.length > 0">
              <div v-for="(itm) in item.feedbackAccessoryList" :key="itm.id">
                <div @click=" previewFormat.includes(itm.format) ? previewImage(item, itm) : ''">
                  <img :src="getIoc(itm, 'bigIoc')">
                </div>
                <div>
                  <p>{{ itm.fileName }}</p>
                  <p>
                    <span v-if="previewFormat.includes(itm.format)" @click="previewImage(item, itm)">预览</span>
                    <span @click="handleDownload(itm)">下载</span>
                  </p>
                </div>
              </div>
            </div>
            <el-row class="rightDel">
              <el-col :span="24">
                <span v-if="hasPerm('menuAsimss3A11B_102') && rowObj.status != 3" class="deleteButton"
                  @click="replyDelete(item)" style="cursor: pointer;">删除</span>
              </el-col>
            </el-row>
          </div>
          <div v-if="!temp.replys || temp.replys.length == 0">暂无回复</div>
        </div>
      </div>
      <!-- 回复 -->
      <div class="replyArea">
        <p>回复</p>
        <div v-if="translate">
          <el-row>
            <el-col :span="5">
              <el-select v-model="from" placeholder="源语言">
                <el-option v-for="item in langeList" :key="item.id" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-col>
            <el-col :span="2">
              <i class="el-icon-sort" @click="exchange"></i>
            </el-col>
            <el-col :span="5">
              <el-select v-model="to" placeholder="目标语言">
                <el-option v-for="item in langeList" :key="item.id" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-col>
            <el-button type="primary" size="medium" @click="translateStrint()">翻译</el-button>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-input v-model="temp.replyContent" :rows="5" placeholder="请输入回复内容" maxlength="500" show-word-limit
                type="textarea"></el-input>
            </el-col>
            <el-col :span="12">
              <el-input type="textarea" :rows="5" v-model="temp.translateContent" :readonly="true"></el-input>
            </el-col>
          </el-row>
        </div>
        <div v-else>
          <el-input v-model.trim="temp.replyContent" type="textarea" :rows="5" maxlength="500" show-word-limit
            placeholder="请输入回复内容"></el-input>
        </div>
      </div>
      <!-- 附件 -->
      <div class="attachmentInfo">
        <p>附件</p>
        <div @paste="handlePaste">
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            multiple
            action="#"
            :http-request="uploadAttach"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :before-upload="beforeAvatarUpload"
            :on-exceed="handleExceed"
            :limit="5"
            :file-list="imgList"
          >
            <div class="el-upload_text">
              <svg-icon icon-class="fileUpload"></svg-icon>
              <p>拖拽附件至此或点击空白区域后粘贴上传</p>
              <el-button type="primary" @click.stop="triggerUpload">点击上传</el-button>
            </div>
            <div slot="tip" class="el-upload__tip">
              {{ $t("identifying.uploadTip", {format: 'jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip', count: 5, size: '100MB'}) }}
            </div>
          </el-upload>
          <el-progress v-if="progressFlag" :percentage="percentage"></el-progress>
        </div>
      </div>
      <div class="submitArea">
        <el-button v-if="hasPerm('menuAsimss3A11B_101')" size="medium" type="primary"
          @click="replyClick(translate == true ? '1' : '0')">
          提交
        </el-button>
        <el-button v-if="hasPerm('menuAsimss3A11B_101')" size="medium"
          style="background-color: #D91C1C !important;border-color:#D91C1C !important;" type="primary"
          @click="endFeedback()">
          结束反馈
        </el-button>
      </div>
    </div>
    <image-viewer v-if="showViewer" :initial-index="imgSrcIndex" :on-close="closeViewer" :url-list="imageShowList" />
  </div>
</template>
<script>
import ImageViewer from '@/components/imageViewer/imageViewer.vue';
import { sysServerUrl, iconZoom, uploadIcon } from '@/assets/js/common.js'
import {
  feedbackInfo,
  feedbackDel,
  feedbackReply,
  feedbackLangeList,
  feedbackTranslate,
  feedbackEnd,
} from '@/api/material.js';
export default {
  name: "feedbackDetail",
  components: { ImageViewer },
  props: {
    replyId: String,
    countryCode: String,
    empowerChina: Boolean,
    rowObj: Object
  },
  watch: {
    replyId(newValue) {
      if (newValue && newValue != "") {
        this.getHandleReplay();
      } else {
        this.replyId = "";
        this.countryCode = "";
        this.empowerChina = "";
      }
    }
  },
  data() {
    return {
      previewFormat: ['jpg', 'png'],
      temp: {
        id: "",// id
        problemTheme: "",// 主题
        problemDesc: "",// 内容
        problemType: "", // 分类
        productName: "", //品牌
        trainName: "",// 车系
        yearName: "",// 年款
        powerName: "",// 动力类型
        modelName: "",// 配置
        vinCode: "",// 出厂编码
        vehicleName: "",// 整编
        questioner: "",// 提问人
        phone: "", // 提问人电话
        stationCode: "", // 服务店
        stationName: "",
        urgent: "", // 问题类型 true 加急
        status: "",// 问题状态
        createdTime: "", // 创建时间
        updatedTime: "",// 首次回复时间
        accessorys: [],// 附件
        replys: [],// 回复记录
        newReplyAccessory: [],// 回复的附件
        replyContent: '',
      },

      replyList: [],
      isfinish: true,
      isFlag: true,
      imgList: [],
      langeList: [],
      to: 'cn',
      from: 'zh-CHS',
      // 进度条
      progressFlag: false,
      percentage: 0,
      fileList: [],
      fileNum: 0,
      // 图片放大展示
      showViewer: false,
      imageShowList: [],
      imgSrcIndex: 0, // 默认显示的预览图下
      uploadPath: sysServerUrl + 'sys/upload/display?filePath=',
      translate: false, // 翻译状态
    }
  },
  methods: {
    getHandleReplay() {
      var _this = this;
      _this.to = 'en'
      _this.from = 'zh-CHS'
      _this.resetRealy();
      var params = {
        id: _this.replyId,
      }
      // 查询问题详情
      feedbackInfo(params).then(res => {
        if (res.data.code == 100 && res.data.data) {
          this.dialogFormVisible = true;
          this.temp = Object.assign({}, res.data.data)
          // 附件集合
          this.temp.accessorys = res.data.data.feedbackAccessoryList
          // 回复集合
          this.temp.replys = res.data.data.feedbackReplyDtoList
          this.replyList = this.temp.replys;
          if (this.temp.replys && this.temp.replys.length > 0) {
            this.temp.newReplyFlag = true;
          }
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(e => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));
      })

      // if (_this.countryCode == 'cn') {
      _this.translate = false;
      // } else {
      //   _this.translate = true;
      // }
      _this.uploadFileShow()
      setTimeout(() => {
        const dragArea = this.$refs.upload.$el.querySelector('.el-upload-dragger');
        dragArea.addEventListener('click', (e) => {
          if (!e.target.closest('.el-button')) {
            this.$refs.upload.$el.querySelector('.el-upload__input').focus();
            e.stopPropagation();  // 阻止点击空白区域触发上传
          }
        });
      }, 100);
    },
    resetRealy() {
      this.temp = {
        id: "",
        problemTheme: "",
        problemDesc: "",
        problemType: "",
        productName: "",
        trainName: "",
        vehicleName: "",
        questioner: "",
        phone: "",
        stationCode: "",
        stationName: "",
        urgent: "",
        status: "",
        createdTime: "",
        updatedTime: "",
        accessorys: [],
        replys: [],
        newReplyAccessory: [],
        replyContent: '',
        translateContent: '',
        translateTheme: '',
        translateDesc: '',
      }

      this.isfinish = true
      this.imgList = []
      this.isFlag = true
    },
    // ==== 上传附件 ===== //
    beforeAvatarUpload(file) {
      if (file == null) {
        this.$DonMessage.warning('请选择上传文件')
        return false;
      }
      this.isfinish = true
      let suffix = ['jpg', 'png', 'mp4', 'mp3', 'xls', 'xlsx', 'doc', 'docx', 'zip']
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const isLt2M = file.size / 1024 / 1024 <= 100
      if (!suffix.includes(fileName)) {
        this.$DonMessage.warning(this.$t('identifying.fileTip', { fileType: 'jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip' }))
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        this.$DonMessage.warning(this.$t('identifying.fileSize', { size: '100MB' }))
        this.isFlag = false;
        return false;
      }
      // 判断文件是否存在重名
      // if (this.imgList.length > 0) {
      //   let b = false
      //   var list = [];
      //   for (let i = 0; i < this.imgList.length; i++) {
      //     const obj = this.imgList[i];
      //     if (file.name.includes(obj.name)) {
      //       b = true
      //       break
      //     }
      //   }
      //   if (b) {
      //     handleAlert('warning', '文件已上传')
      //     this.isFlag = false;
      //     return false;
      //   }
      // }
      // return isLt2M
      return true;
    },
    initialState() {
      setTimeout(() => {
        this.percentage = 0;
        this.progressFlag = false;
      }, 100);
    },
    uploadAttach(param) {
      var _this = this
      _this.fileList.push(param);
      _this.isFlag = true
      var formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', "temp/feedback/" + this.temp.id);
      _this.progressFlag = true;
      var url = sysServerUrl + "sys/upload/attach";
      _this.$axios.post(url, formData, {
        onUploadProgress: (progressEvent) => {
          const complete = parseInt(((progressEvent.loaded / progressEvent.total) * 100) | 0, 10);
          _this.percentage = complete;
        },
      }).then((res) => {
        if (res.data.code == 100) {
          _this.isfinish = false
          _this.percentage = 100;
          let obj = {
            fileName: res.data.data.fileName,
            name: res.data.data.fileName,
            path: res.data.data.fileUrl,
            uid: param.file.uid,
          }
          _this.imgList.push(obj);
          _this.fileNum += 1;
        } else {
          _this.fileNum += 1;
        }
        if (_this.fileList.length == _this.fileNum) {
          _this.initialState();
          _this.fileList = [];
          _this.fileNum = 0;
        }
        _this.isFlag = true;
        uploadIcon();
        setTimeout(() => {
          $('.el-upload-list__item-name').mouseenter((e) => {
            $(e.target).css('text-decoration', 'underline');
          }).mouseleave((e) => {
            $(e.target).removeAttr('style');
          });
        }, 150);
      })
    },
    uploadFileShow() {
      var _this = this;
      $(document).on('click', '.el-upload-list__item.is-success a', function (e) {
        _this.imageShowList = [];
        let nameFile = $(e.target)[0].innerText;
        let imgIndex = $(this).closest('.el-upload-list__item').index();
        let typeFile = nameFile.substring(nameFile.lastIndexOf('.') + 1).toLowerCase();
        if (_this.previewFormat.includes(typeFile)) {
          _this.imgList.forEach((res, idx) => {
            var formatType = res.name.substring(res.name.lastIndexOf('.') + 1).toLowerCase();
            if (_this.previewFormat.includes(formatType)) {
              iconZoom();
              _this.showViewer = true;
              _this.imageShowList.push(_this.uploadPath + res.path);
              if (res.name == nameFile && idx == imgIndex) {
                _this.imageShowList.forEach((pathItem, index) => {
                  if (pathItem == _this.uploadPath + res.path) {
                    _this.imgSrcIndex = index;
                  }
                });
              }
            }
          });
        } else {
          _this.imgList.forEach((res, idx) => {
            if (res.name == nameFile && idx == imgIndex) {
              var params = {
                path: _this.uploadPath + res.path,
                fileName: res.fileName,
              };
              _this.handleDownload(params)
            }
          });
        }
      })
    },
    // 点击上传
    triggerUpload() {
      this.$refs.upload.$el.querySelector('.el-upload__input').click()
    },
    // 附件复制粘贴上传
    handlePaste(e) {
      var clipboardData = e.clipboardData; // IE
      if (!clipboardData) {
        //chrome
        clipboardData = e.originalEvent.clipboardData;
      }
      var items = '';
      items = (e.clipboardData || window.clipboardData).items;
      let file = null;
      if (!items || items.length === 0) {
        this.$DonMessage.warning('当前浏览器不支持粘贴本地图片，请打开图片复制后再粘贴！');
        return;
      }
      if (this.imgList.length + items.length > 5) {
        this.handleExceed();
        e.preventDefault();
        return
      }
      // 搜索剪切板items
      for (let i = 0; i < items.length; i++) {
        // 限制上传文件类型
        if (items[i].kind === 'file') {
          file = items[i].getAsFile();
          if (!this.beforeAvatarUpload(file)) {
            e.preventDefault();
            return
          }
          let fileData = {};
          fileData.file = file;
          if (file) {//对复制黏贴的类型进行判断，若是非文件类型，比如复制黏贴的文字，则不会调用上传文件的函数
            this.uploadAttach(fileData);//再次调用上传文件的函数
          }
        }
      }
    },
    handleRemove(file, fileList) {
      for (let i = 0; i < this.imgList.length; i++) {
        const obj = this.imgList[i];
        if (obj.uid === file.uid) {
          this.imgList.splice(i, 1)
          break;
        }
      }
      if (fileList.length <= 0) {
        this.isfinish = true
        this.imgList = []
        this.isFlag = true;
      }
    },
    handleExceed() {
      this.$DonMessage.warning(this.$t('identifying.limitTip', {count : 5}))
      return;
    },
    beforeRemove(file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    replyDelete(row) {
      var _this = this
      this.$confirm('确定删除【' + row.replyName + '】的回复吗?', '删除回复', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append("id", row.id)
        params.append("type", "2")
        feedbackDel(params).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success(this.$t('successTip.deleteTip'))
            // 去掉回显
            this.replyList = [];
            for (let k = 0; k < _this.temp.replys.length; k++) {
              const itm = _this.temp.replys[k];
              if (itm.id !== row.id) {
                this.replyList.push(itm)
              }
            }
            this.temp.replys = this.replyList
          } else {
            this.$DonMessage.error('删除失败')
          }
        })
      })
    },
    // 图标
    getIoc(itm, type) {
      if (itm.format == null || itm.format == undefined || itm.format.length <= 0) {
        itm.format = itm.path.substring(itm.path.lastIndexOf(".") + 1)
      }
      if (itm.fileName == null || itm.fileName == undefined || itm.fileName.length <= 0) {
        itm.fileName = itm.path.substring(itm.path.lastIndexOf("/") + 1)
      }
      if (type === 'bigIoc' && (itm.format === 'png' || itm.format === 'jpg')) {
        return sysServerUrl + itm.path;
      }
      if (itm.format === 'png') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/png.png')
      } else if (itm.format === 'jpg') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/jpg.png')
      } else if (itm.format === 'zip') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/zip.png')
      } else if (itm.format === 'mp3') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/mp3.png')
      } else if (itm.format === 'mp4') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/mp4.png')
      } else if (itm.format === 'doc' || itm.format === 'docx') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/doc.png')
      } else if (itm.format === 'xls' || itm.format === 'xlsx') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/xls.png')
      }
    },
    // 预览
    closeViewer() {
      this.showViewer = false;
      this.imgSrcIndex = 0;
      this.imageShowList = [];
    },
    previewImage(item, itm) {
      this.showViewer = true;
      iconZoom();
      item.feedbackAccessoryList.forEach((res) => {
        var name = res.format.substring(res.format.lastIndexOf('.') + 1).toLowerCase();
        if (this.previewFormat.includes(name)) {
          this.imageShowList.push(res.path);
          this.imageShowList.forEach((pathItem, index) => {
            if (pathItem == itm.path) {
              this.imgSrcIndex = index;
            }
          });
        }
      })
    },
    // 下载
    getBolb(fileUrl) {
      return new Promise((resolve, reject) => {
        // 审查xhr对象是否存在
        let xhr;
        if (window.XMLHttpRequest) {
          xhr = new XMLHttpRequest();
        } else {
          // 兼容旧版本的IE
          xhr = new ActiveXObject("Microsoft.XMLHTTP");
        }
        // 发起 GET 请求获取文件内容
        xhr.open("GET", fileUrl, true);
        xhr.responseType = "blob";
        xhr.onload = function (e) {
          if (xhr.status === 200) {
            // 获取到的文件内容存储在xhr.response中
            resolve(xhr.response);
          } else {
            reject(new Error('系统异常'))
          }
        };
        xhr.onerror = () => {
          reject(new Error('系统异常'))
        };
        xhr.send();
      })
    },
    handleDownload(item) {
      this.getBolb(sysServerUrl + item.path).then(blob => {
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", item.fileName);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(e => {
        this.$DonMessage.error(this.$t('errorTip.systemTip'));

      })
    },
    // 获取语言
    getLangeList() {
      feedbackLangeList().then(res => {
        if (res.data.code == 100) {
          this.langeList = res.data.data
        }
      })
    },
    // 翻译
    translateStrint() {
      if (this.to == null || this.to == undefined || this.to.length <= 0) {
        this.$DonMessage.warning("请选择目标语言")
        return false
      }
      if (this.temp.replyContent == '') {
        this.$DonMessage.warning("翻译内容不能为空")
        this.translate = false;
        this.temp.translateContent = ""
        this.translate = true;
        return false
      }
      var params = new URLSearchParams()
      params.append("q", this.temp.replyContent)
      params.append("to", this.to)
      params.append("from", this.from)
      feedbackTranslate(params).then(res => {
        if (res.data.code == 100) {
          this.translate = false;
          this.temp.translateContent = res.data.data
          this.translate = true;
        } else {
          this.$DonMessage.error("网络不稳定")
        }
      }).catch(e => {
        this.$DonMessage.error("网络不稳定")
      })
    },
    // 交换语言
    exchange() {
      let t = this.to
      this.to = this.from
      this.from = t
    },
    // 确定回复
    replyClick(type) {
      if (!this.temp.replyContent || this.temp.replyContent.length <= 0) {
        this.$DonMessage.warning('回复内容不能为空')
        return
      }
      if (type === '1' && (this.temp.translateContent == null || this.temp.translateContent == undefined || this.temp.translateContent.length <= 0)) {
        this.$confirm('确定不翻译吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.replyFun();
        })
      } else {
        this.replyFun();
      }

    },

    replyFun() {
      let params = {
        "problemId": this.temp.id,
        "replyContent": this.temp.replyContent,
        "translateContent": this.temp.translateContent,
        "feedbackAccessoryList": this.imgList
      }
      feedbackReply(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success(this.$t('successTip.operateTip'))
          this.$parent.$parent.dataList();
          this.$parent.$parent.dialogFormVisible = false;
        } else {
          this.$DonMessage.error('操作失败')
        }
      })
    },

    // 结束反馈
    endFeedback() {
      this.$confirm('确定结束问题【' + this.temp.problemTheme + '】吗?', '结束反馈', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        feedbackEnd(this.temp.id).then(res => {
          if (res.data.code === 100) {
            this.$DonMessage.success("问题【" + this.temp.problemTheme + "】已结束")
            this.rowObj.status = 3;
            this.rowObj.type = 3;
            this.dialogFormVisible = false;
            this.dialogOverseasFormVisible = false;
          } else {
            this.$DonMessage.error(res.data.msg)
          }

        }).catch(e => {
          this.$DonMessage.error(this.$t('errorTip.systemTip'));
        })
      })
    },
  },
  mounted() {
    this.getHandleReplay();
    this.getLangeList();
  },
}
</script>
