<template>
  <div class="login">
    <div class="loginHeader">
      <div class="logoArea">
        <img src="../../assets/image/headerIcon/logo.png"/>
      </div>
      <!-- <div class="languageArea">
        <el-select v-model="selectValue" @change="langChange">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div> -->
      <!--  下载客户端    -->
      <!-- <div class="downArea"> -->
<!--        <span @click="downloadApp">下载客户端</span>-->
        <!-- <span>帮助</span>
      </div> -->
    </div>

    <!-- 登录表单 -->
    <el-form v-if="!showAccountSelection" rel="loginForm" :model="loginform" :rules="loginrules" class="login-form" autocomplete="on" label-position="left">
      <p class="login-title">{{ $t('login.title') }}</p>
      <el-form-item prop="mobile">
        <el-input
          type="text"
          :placeholder="$t('login.userInput')"
          v-model="loginform.mobile">
          <template slot="prepend">{{ $t('login.mobile') }}</template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input type="password" :placeholder="$t('login.passInput')" v-model="loginform.password">
          <template slot="prepend">{{ $t('login.password') }}</template>
        </el-input>
      </el-form-item>
      <el-form-item class="captCha" prop="captCha">
        <el-input type="text" :placeholder="$t('login.codeInput')" v-model="loginform.captCha">
          <template slot="prepend">{{ $t('login.code') }}</template>
        </el-input>
        <img @click="getCaptCha" :src="loginform.src" class="CaptChaImg">
        <input type="hidden" v-model="loginform.id">
      </el-form-item>
      <el-form-item class="bottomBut">
        <el-button class="button" @click="login" type="primary">{{ $t('login.button') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 子账号选择 -->
    <div v-if="showAccountSelection" class="login-form account-selection-form">
      <div class="select-account-header">
        <div class="login-title">{{ $t('login.selectAccount') }}</div>
      </div>

      <div class="select-account-content">
        <el-button type="text" icon="el-icon-back" class="back-btn" @click="backToLogin">{{ $t('login.backBtn') }}</el-button>
        <div class="account-tip">{{ $t('login.accountTip', { count: subAccounts.length }) }}</div>

        <div class="account-info">主账号：{{ mainAccount.username }}</div>

        <div class="sub-accounts-container">
          <template v-for="account in subAccounts">
            <div class="account-box" :key="account.id" @click="selectAccount(account)" :class="{'active': selectedAccount && account.id === selectedAccount.id}">
              <div class="account-name">{{ account.username }}</div>
              <div class="account-id">({{ account.tenantName }})</div>
            </div>
          </template>
        </div>
      </div>

      <div class="select-account-footer">
        <el-button type="primary" class="continue-btn" @click="continueLogin">{{ $t('login.continueBtn') }}</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { setRequestURL } from '@/assets/js/common.js'
import {onLogin, querySubAccounts, currentUserInfo, captCha, getUserInfo, appInfo, downFile} from '@/api/sysmgt.js'


export default {
  name: 'login',
  data() {
    return {
      selectValue: '',
      options: [
        {
          value: 'cn',
          label: '中文'
        },
        // {
        //   value: 'en',
        //   label: 'English'
        // }
      ],
      loginform: {
        mobile: '',
        password: '',
        captCha: '',
        id: null,
        src: ''
      },
      // 表单验证，需要在el-form-item元素中增加prop属性
      loginrules: {
        mobile: [
          {required: true, message: this.$t('login.userInput'), trigger: ['blur', 'change']},
          {pattern: /^[0-9]+$/, message: '手机号只能输入数字', trigger: 'blur'}
        ],
        password: [
          {required: true, message: this.$t('login.passTip'), trigger: ['blur', 'change']}
        ],
        captCha: [
          {required: true, message: this.$t('login.codeTip'), trigger: ['blur', 'change']}
        ]
      },
      loginStatus: false,
      // 对话框显示和隐藏
      dialogVisible: false,
      // 子账号选择相关
      showAccountSelection: false,
      subAccounts: [],
      mainAccount: {},
      selectedAccount: null,
      loginParams: {}
    }
  },
  created() {
    var that = this
    that.selectValue = 'cn'
    // that.selectValue = localStorage.lang === undefined ? 'cn' : localStorage.lang
    // localStorage.lang = that.selectValue
    if (window.trendsURL){
      // === 截取第一个斜线 ==== //
      let url = window.location.href;
      setRequestURL(url.substring(0, url.indexOf("/")+1));
    }
  },
  methods: {
    // 语言切换
    langChange(e) {
      localStorage.setItem('lang', e)
      this.$i18n.locale = e
      location.reload()
    },
    getCaptCha() {
      // sessionStorage.clear();
      this.$store.commit('del_token');
      // this.$store.commit("clearVUEX");
      this.$axios.defaults.headers.common['Authorization'] = '';
      captCha().then(res => {
        if (res.data.code === 100) {
          this.loginform.id = res.data.data.id
          this.loginform.src = res.data.data.image
        }
      })
    },
    getUserDetail() {
      currentUserInfo().then(res => {
        if (res.data.code == 100) {
          this.$store.commit('SET_ROLES', res.data.data.roleList)
          this.$store.commit('SET_PERMS', res.data.data.permissionList)
          this.$store.commit("set_realName", res.data.data.realName);
          this.$router.push('/home')
          // languageType()
          this.loginStatus = true;
        } else {
          this.loginStatus = false;
        }
      }).catch(err => {
        if (err != null && err !== '' && err.responseText !== null) {
          this.loginStatus = false;
          this.$DonMessage.error('系统登录异常')
        }
      })
    },
    login() {
      var _this = this
      // sessionStorage.clear();
      if (_this.loginStatus == true) {
        this.$DonMessage.warning("正在登录中，请勿重复操作")
        return;
      }
      // _this.$store.commit("clearVUEX");
      if (!_this.loginform.mobile) {
        this.$DonMessage.warning(_this.$t('login.userInput'))
        return
      }
      // 验证手机号是否全是数字
      if (!/^[0-9]+$/.test(_this.loginform.mobile)) {
        this.$DonMessage.warning('手机号只能输入数字')
        return
      }
      if (!_this.loginform.password) {
        this.$DonMessage.warning(_this.$t('login.passTip'))
        return
      }
      if (!_this.loginform.captCha) {
        this.$DonMessage.warning(_this.$t('login.codeTip'))
        return
      }
      _this.loginStatus = true;
      // _this.$store.commit('del_token');

      // 构造查询参数
      var params = {
        mobile: _this.loginform.mobile,
        password: _this.loginform.password,
        vid: _this.loginform.id,
        verifycode: _this.loginform.captCha,
        type: '1'
      }

      // 保存登录参数，方便后续使用
      _this.loginParams = params;

      // 先查询关联子账号
      querySubAccounts({
        mobile: _this.loginform.mobile,
        password: _this.loginform.password,
        verifycode: _this.loginform.captCha,
        vid: _this.loginform.id,
        type: '1'
      }).then(res => {
        if (res.data.code === 100) {
          const accounts = res.data.data;

          // 无关联账号或仅有一个账号，直接登录
          if (!accounts || accounts.length <= 1) {
            params.username = accounts[0].username;
            _this.directLogin(params);
          } else {
            // 有多个关联账号，显示子账号选择
            _this.loginStatus = false;

            // 设置子账号和主账号信息
            _this.subAccounts = accounts;
            _this.mainAccount = {
              username: _this.loginform.mobile,
              tenantName: accounts[0].tenantName
            };

            // 默认选中主账号
            _this.selectedAccount = _this.mainAccount;

            // 显示子账号选择界面
            _this.showAccountSelection = true;
          }
        } else {
          _this.loginStatus = false;
          this.$DonMessage.error(res.data.msg || '查询关联账号失败');
        }
      }).catch(err => {
        _this.loginStatus = false;
        this.$DonMessage.error('查询关联账号异常');
      });
    },

    // 选择账号
    selectAccount(account) {
      this.selectedAccount = account;
    },

    // 返回登录
    backToLogin() {
      this.showAccountSelection = false;
    },

    // 继续登录
    continueLogin() {
      if (!this.selectedAccount) {
        this.$DonMessage.warning(this.$t('login.selectAccountTip'));
        return;
      }

      const params = {
        ...this.loginParams,
        username: this.selectedAccount.username,
        accountId: this.selectedAccount.id
      };
      this.directLogin(params);
    },

    // 直接登录方法
    directLogin(params) {
      var _this = this;
      onLogin(params).then(res => {
        if (res.data.code === 100) {
          var tokenVal = res.data.data.tokenHead + res.data.data.token
          _this.$store.commit('set_token', tokenVal)
          _this.$store.commit("set_userName", params.username);
          //_this.$store.commit("set_password", params.password);
          _this.$store.commit("setRefreshToken",res.data.data.refreshToken);
          sessionStorage.setItem("store", JSON.stringify(_this.$store.state));
          _this.initialState(res.data.data.initial);
          // languageType()
          if(window.bc){
            window.bc.postMessage(JSON.stringify(_this.$store.state))
          }
          _this.loginStatus = true;
        } else {
          _this.loginStatus = false;
          this.$DonMessage.error(res.data.msg)
        }
      }).catch(err => {
        if (err != null && err !== '' && err.responseText !== null) {
          _this.loginStatus = false;
          this.$DonMessage.error('系统登录异常')
        }
      })
    },
    initialState(initialVal) {
      if (initialVal == true) {
      this.$router.push("/initialPassword");
      } else {
        this.getUserDetail()
      }
    },
    keyDown(e) {
      if (e.keyCode === 13) {
        this.login()
      }
    },

    // 下载App
    downloadApp(){
      var routeUrl = this.$router.resolve({path:"/download"});
      window.open(routeUrl.href, '_blank');
    },
  },
  mounted() {
    this.getCaptCha();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style>
.login {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: url('../../assets/image/login.png') no-repeat;
  background-position: 0 3%;
  background-size: cover;
  position: relative;
}
/* 顶部标题 */
.loginHeader {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1.5%;
  z-index: 5;
  position: relative;
}
/* logo */
.loginHeader .logoArea {
  height: 50px;
  display: flex;
  align-items: center;
}

.loginHeader .logoArea img {
  max-height: 80%;
}

.languageArea {
  width: 100px;
  height: 38px;
}

/* .loginHeader .el-input .el-input__inner {
  padding: 0 20px 0 10px;
  height: 38px;
  line-height: 38px;
  width: 100%;
  border: 1px solid #eee !important;
  border-radius: 20px;
  background: transparent !important;
  color: #fff !important;
}

.loginHeader .el-input__icon {
  line-height: 38px;
  width: auto !important;
} */
.loginHeader .languageArea .el-input__suffix {
  right: 5px !important;
}
.loginHeader .el-input--suffix .el-input__inner {
  padding-right: 20px;
}
.loginHeader .downArea {
  font-size: 15px;
  color: #333;
}
/* .loginHeader .downArea span:first-child::after {
  content: "";
  display: inline-block;
  width: 1px;
  height: 14px;
  background: rgb(154, 154, 154);
  margin: -1px 10px 0;
  vertical-align: middle;
} */
.loginHeader .downArea span {
  cursor: pointer;
}
/* 登录区域 */
.login .el-form-item__content {
  display: flex;
}
.login-form {
  width: 460px;
  position: absolute;
  right: 10%;
  top: 50%;
  transform: translate(0%, -50%);
  background: #fff;
  border-radius: 6px;
}
.login-form .login-title {
  font-size: 28px;
  color: #000;
  margin: 10% 0 8%;
  text-align: center;
}
.select-account-header .login-title {
  margin: 8% 0;
}
/* 输入框 */
.login-form  .el-form-item {
  padding: 0 10%;
}
.login-form .el-form-item__content {
  border: 1px solid #ccc;
  border-radius: 4px;
}
.login-form .el-input-group__prepend {
  width: 85px;
  font-size: 16px !important;
  color: #333 !important;
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  white-space: nowrap;
  text-align-last: justify;
  background: #fff;
  border: none;
  border-right: 1px solid;
  border-image: linear-gradient(180deg, #fff, #e7e7e7, #fff) 1 10;
  box-sizing: border-box;
}
.login-form .el-input__inner {
  -webkit-appearance: none;
  background-color: #fff !important;
  background-image: none;
  border: none !important;
  height: 40px !important;
  line-height: 40px !important;
  color: #333 !important;
  font-size: 16px;
}

.login-form .el-input__inner::placeholder {
  font-size: 16px;
  color: #ccc !important;
}


/* 验证码 */
.CaptChaImg {
  width: 75px;
  height: 40px;
  margin-left: 10px;
}

/* 登录按钮 */
.login-form .bottomBut .el-form-item__content{
  border: none;
}
.login-form .el-button {
  font-size: 16px !important;
  letter-spacing: 3px;
  width: 100%;
  margin: 6% 0 10%;
  padding: 14px 0px !important;
  height: auto;
}
.login-form .el-button.el-button--text {
  padding: 0 !important;
}
.login-form .el-form-item.is-required {
  margin-bottom: 22px;
}
.login-form .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.login-form .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  content: '*';
  color: transparent !important;
  margin-right: 4px;
}
.login .el-button.el-button--primary {
  color: #FFF;
  background-color: #0067b9 !important;
  border-color: #0067b9 !important;
}
.login .el-button--primary:hover{
  color: #FFF;
  background-color: #0067b9 !important;
  border-color: #0067b9 !important;
  opacity: 0.8;
}
.login .login-form .el-input__inner:hover,
.login .login-form .el-input__inner:focus {
  border-color: #0067b9 !important;
}

/* 子账号选择样式 */
.select-account-content .back-btn {
  text-align: left;
}
.select-account-content .el-button.back-btn.el-button--text {
  color: #0067b9 !important;
  height: auto;
  font-weight: bold;
  margin: 2px 0 10px 0;
}
.select-account-content .back-btn i {
  font-weight: bold;
  margin-right: 5px;
}
.select-account-content .back-btn:hover,
.select-account-content .back-btn:focus,
.select-account-content .back-btn:active {
  color: #0067b9 !important;
  background: transparent !important;
  opacity: 0.8;
}
.select-account-content .account-tip {
  margin: 5px 0 15px;
  font-size: 14px;
  color: #666;
}
.account-info {
  font-weight: bold;
  margin: 5px 0 15px;
}
.sub-accounts-container {
  overflow-y: auto;
  max-height: 240px;
}





.select-account-content {
  flex: 1;
  padding: 0 10%;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
}
.account-box {
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #f5f5f5;
}
.account-box.active {
  border-color: #0067b9;
  background-color: rgba(0, 103, 185, 0.05);
}
.account-box:hover {
  border-color: #0067b9;
}
.account-name,
.account-id {
  font-size: 15px;
  color: #333;
  display: inline-block;
}
.account-id {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}
.select-account-footer {
  padding: 0 10%;
  margin-top: 10px;
}
.select-account-footer.bottomBut {
  margin-bottom: 25px;
}
@media screen and (max-width: 1600px) and (min-width: 1440px) {
  /* 顶部标题 */
  .languageArea {
    width: 105px;
    height: 34px;
  }


  /* .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 14px;
  }

  .loginHeader .el-input .el-input__inner {
    height: 34px;
    line-height: 34px;
  } */
/* 
  .loginHeader .el-input__icon {
    line-height: 34px;
  } */
  /* 登录区域 */
  .login-form {
    width: 460px;
    right: 10%;
  }
  .login-form .login-title {
    font-size: 26px;
  }
  /* 输入框 */
  .login-form .el-input-group__prepend{
    font-size: 15px !important;
    line-height: 36px;
    height:36px;
  }
  .login-form .el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    font-size: 15px;
  }
  .login-form .el-input__inner::placeholder {
    font-size: 15px;
  }
  /* 验证码 */
  .CaptChaImg {
    width: 65px;
    height: 36px;
  }
  /* 登录按钮 */
  .login-form .el-button {
    font-size: 16px !important;
    padding: 10px 0 !important;
  }
}

@media screen and (max-width: 1440px) and (min-width: 1366px) {
  /* 顶部标题 */
  .languageArea {
    width: 100px;
    height: 34px;
  }

  /* .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 14px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 34px;
    line-height: 34px;
  }

  .loginHeader .el-input__icon {
    line-height: 34px;
  } */
  /* 登录区域 */
  .login-form {
    width: 470px;
    right: 10%;
  }
  .login-form .login-title {
    font-size: 26px;
  }
  /* 输入框 */
  .login-form .el-input-group__prepend {
    font-size: 15px !important;
    line-height: 38px;
    height: 38px;
  }
  .login-form .el-input__inner {
    height: 38px !important;
    line-height: 38px !important;
    font-size: 15px;
  }
  .login-form .el-input__inner::placeholder {
    font-size: 15px;
  }
  /* 验证码 */
  .CaptChaImg {
    width: 68px;
    height: 38px;
  }
  /* 登录按钮 */
  .login-form .el-button {
    font-size: 16px !important;
    padding: 10px 0 !important;
  }
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {
  /* 顶部标题 */
  .loginHeader {
    height: 45px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 45px;
  }

  .languageArea {
    width: 95px;
    height: 32px;
  }

  /* .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 13px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 32px;
    line-height: 32px;
  }

  .loginHeader .el-input__icon {
    line-height: 32px;
  } */
  .loginHeader .downArea {
    font-size: 13px;
  }
  /* 登录区域 */
  .login-form {
    width: 420px;
    right: 8%;
  }
  .login-form .login-title {
    font-size: 24px;
  }
  /* 输入框 */
  .login-form .el-input-group__prepend {
    width: 75px;
    padding: 0 8px;
    font-size: 14px !important;
    height: 34px;
    line-height: 34px;
  }
  .login-form .el-input__inner {
    height: 34px !important;
    line-height: 34px !important;
    font-size: 14px;
  }
  .login-form .el-input__inner::placeholder {
    font-size: 14px;
  }
  /* 验证码 */
  .CaptChaImg {
    width: 65px;
    height: 34px;
  }
  /* 登录按钮 */
  .login-form .el-button {
    font-size: 15px;
    padding: 12px 0 !important;
  }
}

@media screen and (max-width: 1280px) and (min-width: 1024px) {
  /* 顶部标题 */
  .loginHeader {
    height: 40px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 40px;
  }

  .languageArea {
    width: 90px;
    height: 30px;
  }

  /* .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 12px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 30px;
    line-height: 30px;
  }

  .loginHeader .el-input__icon {
    line-height: 30px;
  } */
  .loginHeader .downArea {
    font-size: 12px;
  }
  /* .loginHeader .downArea span:first-child::after {
    width: 1px;
    height: 11px;
    margin: -1px 6px 0;
  } */
  /* 登录区域 */
  .login-form {
    width: 380px;
    right: 8%;
  }
  .login-form .login-title {
    font-size: 22px;
  }
  /* 输入框 */
  .login-form .el-input-group__prepend {
    width: 70px;
    padding: 0 8px;
    font-size: 14px !important;
    height: 34px;
    line-height: 34px;
  }
  .login-form .el-input__inner {
    height: 34px !important;
    line-height: 34px;
    font-size: 14px;
  }
  .login-form .el-input__inner::placeholder {
    font-size: 14px;
  }
  /* 验证码 */
  .CaptChaImg {
    width: 60px;
    height: 34px;
  }
  /* 登录按钮 */
  .login-form .el-button {
    padding: 10px 0px !important;
    font-size: 14px !important;
  }
}

@media screen and (max-width: 1024px) {
  /* 顶部标题 */
  .loginHeader {
    height: 40px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 40px;
  }

  .languageArea {
    width: 90px;
    height: 30px;
  }

  /* .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 12px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 30px;
    line-height: 30px;
  }

  .loginHeader .el-input__icon {
    line-height: 30px;
  } */
  .loginHeader .downArea {
    font-size: 12px;
  }
  /* .loginHeader .downArea span:first-child::after {
    width: 1px;
    height: 11px;
    margin: -1px 6px 0;
  } */
  /* 登录区域 */
  .login-form {
    width: 360px;
    right: 6%;
  }
  .login-form .login-title {
    font-size: 22px;
  }
  /* 输入框 */
  .login-form .el-input-group__prepend {
    width: 65px;
    padding: 0 6px;
    font-size: 13px !important;
    height: 32px;
    line-height: 32px;
  }
  .login-form .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    font-size: 13px;
  }
  .login-form .el-input__inner::placeholder {
    font-size: 13px;
  }
  /* 验证码 */
  .CaptChaImg {
    width: 60px;
    height: 32px;
  }
  /* 登录按钮 */
  .login-form .el-button {
    font-size: 14px !important;
    padding: 8px 0 !important;
  }
}
</style>
