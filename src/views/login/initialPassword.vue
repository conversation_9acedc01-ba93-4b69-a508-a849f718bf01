<template>
  <div class="initialContent" v-cloak>
    <div class="loginHeader">
      <div class="logoArea">
        <img src="../../assets/image/headerIcon/logo.png" />
      </div>
    </div>
    <div class="initialDetail">
      <div class="detailForm">
        <div class="title">
          修改初始密码
        </div>
        <div>
          <el-form
            ref="form"
            :model="form"
            :label-width="labelWidth"
            :rules="formRules"
          >
            <el-form-item
              :label="$t('login.mobile')"
              prop="mobile"
            >
              <el-input v-model="form.mobile" disabled></el-input>
            </el-form-item>
            <el-form-item
              label="新密码"
              prop="password"
              class="password"
            >
              <el-input
                v-model="form.password"
                maxlength="16"
                placeholder="请输入新密码"
                type="password"
              ></el-input>
              <img
                class="seeArea"
                @click="noSeeClick('password')"
                src="../../assets/image/sightIocn.png"
              />
              <img
                class="noSeeArea"
                @click="seeClick('password')"
                src="../../assets/image/noSeeIcon.png"
              />
            </el-form-item>
            <el-form-item
              label="确认密码"
              prop="confirmPwd"
              class="confirmPwd"
            >
              <el-input
                v-model="form.confirmPwd"
                maxlength="16"
                placeholder="请输入确认密码"
                type="password"
              ></el-input>
              <img
                class="seeArea"
                @click="noSeeClick('confirmPwd')"
                src="../../assets/image/sightIocn.png"
              />
              <img
                class="noSeeArea"
                @click="seeClick('confirmPwd')"
                src="../../assets/image/noSeeIcon.png"
              />
            </el-form-item>
            <div style="text-align: center">
              <el-button
                disabled
                class="grayButton"
                v-if="form.password == '' || form.confirmPwd == ''"
              >
              确定
              </el-button>
              <el-button
                @click="determineClick('form')"
                class="redButton"
                v-if="form.password != '' && form.confirmPwd != ''"
                >确定</el-button
              >
              <el-button @click="resetClick">重置</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <el-dialog
      width="auto !important"
      :modal-append-to-body="false"
      :visible.sync="dialogFormVisible"
      :show-close="false"
      :close-on-press-escape="false"
      title="修改成功"
    >
      <div class="jumpPrompt">
        <img src="../../assets/image/icon.png" alt="" />
        <div>
          密码修改成功,将在
          <b> {{ time }} </b>
          秒后返回登录界面，请重新登录！
        </div>
        <div @click="loginClick()">立即登录</div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { currentUserInfo, initialPsw } from "@/api/sysmgt.js";
export default {
  name: "initialPassword",
  data() {
    return {
      dialogFormVisible: false,
      time: 5,
      form: {
        mobile: "",
        username: "",
        password: "",
        confirmPwd: "",
      },
    };
  },
  computed: {
    labelWidth() {
      return "140px";
    },
    formRules() {
      return {
        password: [
          {
            required: true,
            message: "新密码不能为空",
            trigger: ["blur", "change"],
          },
          {
            min: 6,
            pattern: /^(?=.*\d+)(?=.*[A-Za-z]+)[\w]{6,16}$/,
            required: true,
            message: "新密码为6~16位字母和数字组合",
            trigger: ["blur", "change"],
          },
        ],
        confirmPwd: [
          {
            required: true,
            message: "确认密码不能为空",
            trigger: ["blur", "change"],
          },
        ],
      };
    },
  },
  watch: {
    time(newVal) {
      if (newVal == 0) {
        this.loginClick();
      }
    },
  },
  methods: {
    beforeunloadFn(e) {
      this.time = 5;
    },
    // 密码不可见
    noSeeClick(text) {
      $("." + text + " .seeArea").hide();
      $("." + text + " .noSeeArea").show();
      $("." + text + " .el-input .el-input__inner").attr("type", "password");
    },
    // 密码可见
    seeClick(text) {
      $("." + text + " .seeArea").show();
      $("." + text + " .noSeeArea").hide();
      $("." + text + " .el-input .el-input__inner").attr("type", "text");
    },
    // 获取用户信息
    userData() {
      currentUserInfo().then((res) => {
        if (res.data.code == 100) {
          this.form.mobile = res.data.data.mobile;
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      });
    },
    countDown() {
      var _this = this;
      _this.time--;
    },
    // 确定
    determineClick(form) {
      var _this = this;
      _this.$refs[form].validate((valid) => {
        if (valid) {
          var params = new FormData();
          params.append("newPwd", this.form.password);
          params.append("confirmPwd", this.form.confirmPwd);
          initialPsw(params).then((res) => {
            if (res.data.code === 100) {
              _this.dialogFormVisible = true;
              _this.time = 5;
              setInterval(_this.countDown, 1000);
              this.$DonMessage.success(res.data.msg)
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          });
        } else {
          this.$DonMessage.warning(this.$t('tips.infoTip'));
        }
      });
    },
    loginClick() {
      this.$store.commit("del_token");
      this.$router.push("/");
    },
    // 重置
    resetClick() {
      $(".seeArea").hide();
      $(".noSeeArea").show();
      $(".initialDetail .el-input .el-input__inner").attr("type", "password");
      this.form.password = "";
      this.form.confirmPwd = "";
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    heightArea() {
      setTimeout(() => {
        var headerVal = $(".loginHeader").outerHeight(true);
        var allHeight = $(".initialContent").outerHeight(true);
        var val = allHeight - headerVal;
        $(".initialDetail").css("height", val);
      }, 100);
    },
    initialSize() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function () {
        _this.heightArea();
      });
    },
  },
  mounted() {
    this.initialSize();
    this.userData();
  },
  created() {
    window.addEventListener("beforeunload", (e) => this.beforeunloadFn(e));
  },
  destroyed() {
    window.removeEventListener("beforeunload", (e) => this.beforeunloadFn(e));
  },
};
</script>
<style>
.initialContent {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: url("../../assets/image/bgImage.png") center center
    no-repeat;
  background-size: cover;
}
.initialContent .loginHeader {
  border-bottom: 1px solid #ccc;
  box-sizing: border-box;
}
.initialContent .loginHeader .logoArea img {
  filter: invert(100%);
}
.initialDetail {
  position: relative;
}
.detailForm {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  padding: 10px 15px;
}
.initialDetail .title {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8%;
  border-bottom: 1px solid #ccc;
  padding: 20px 0 15px;
}
.initialContent .el-form {
  margin: 20px 0 30px;
}
.initialContent .el-form-item {
  margin-bottom: 28px;
  padding: 0 80px 0 40px;
}
.initialContent .el-form-item .el-form-item__label {
  font-size: 20px;
  line-height: 42px !important;
}
.initialContent .el-form-item .el-form-item__content {
  width: 430px;
  line-height: 42px !important;
}
.initialContent .el-input .el-input__inner {
  /* padding: 0 12px; */
  border: 1px solid #ddd !important;
}
.initialContent .el-input .el-input__inner:focus,
.initialContent .el-input .el-input__inner:hover {
  border: 1px solid #dddddd;
}
.initialContent .el-form-item .el-input .el-input__inner {
  height: 42px !important;
  line-height: 42px !important;
  font-size: 18px !important;
  padding: 0 30px 0 12px !important;
}
.initialContent .el-button {
  font-size: 20px !important;
  padding: 13px 45px !important;
  margin: 15px 20px !important;
  height: auto;
}
.initialContent .el-button.is-disabled.grayButton {
  color: #bbbbbb !important;
  background: rgb(245, 245, 245) !important;
  border: 1px solid rgb(245, 245, 245) !important;
}
.initialContent .el-button.el-button--default.redButton {
  color: #fff !important;
  background: #0067b9 !important;
  border: 1px solid #0067b9 !important;
}
.initialContent .el-button:last-child {
  border: 1px solid #bbbbbb !important;
  color: #bbbbbb !important;
  background: #fff !important;
  cursor: pointer;
}
.initialContent .el-dialog,
.headerContent .tipDialog .el-dialog{
  background: #fff;
}
.initialContent  .el-dialog .el-dialog__header,
.headerContent .tipDialog .el-dialog .el-dialog__header {
  background: #fff;
  text-align: center;
  border-bottom: 1px solid #f6f6f6;
  padding:3% 0;
  margin:0 10px
}
.initialContent  .el-dialog .el-dialog__body,
.headerContent .tipDialog .el-dialog .el-dialog__body {
  margin:0 10px
}
.initialContent .el-dialog .el-dialog__header .el-dialog__title,
.headerContent .tipDialog .el-dialog .el-dialog__header .el-dialog__title {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
}
.jumpPrompt {
  font-size: 15px;
  text-align: center;
  margin: 10px 0;
}
.headerContent .tipDialog .jumpPrompt img,
.initialContent .jumpPrompt img {
  width: 80%;
  margin-bottom: 20px;
}
.headerContent .tipDialog .jumpPrompt div b,
.initialContent .jumpPrompt div b {
  color: rgb(24, 122, 255);
}
.headerContent .tipDialog .jumpPrompt div:last-child,
.initialContent .jumpPrompt div:last-child {
  color: rgb(24, 122, 255);
  margin: 15px 0;
  text-decoration: underline;
  cursor: pointer;
}
.initialDetail .el-form img {
  cursor: pointer;
  width: 20px;
  position: absolute;
  top: 50%;
  right: 6px;
  transform: translate(0, -50%);
}
.initialDetail .el-form .seeArea {
  display: none;
}
@media screen and (max-width: 1600px) and (min-width: 1440px) {
  .initialDetail .title {
    font-size: 25px;
    margin-bottom: 7%;
    padding: 15px 0 12px;
  }
  .initialContent .el-form {
    margin: 15px 0 30px;
  }
  .initialContent .el-form-item {
    padding: 0 65px 0 10px;
  }
  .initialContent .el-form-item .el-form-item__label {
    font-size: 17px;
    line-height: 38px !important;
  }
  .initialContent .el-form-item .el-form-item__content {
    width: 370px;
    line-height: 38px !important;
  }
  .initialContent .el-form-item .el-input .el-input__inner {
    height: 38px !important;
    line-height: 38px !important;
    font-size: 16px !important;
  }
  .initialContent .el-button {
    font-size: 16px !important;
    padding: 12px 35px !important;
    margin: 10px 20px !important;
  }
  .initialDetail .el-form img {
    width: 19px;
  }
  .initialContent .el-dialog .el-dialog__header .el-dialog__title,
  .headerContent .tipDialog .el-dialog .el-dialog__header .el-dialog__title {
    font-size: 25px;
  }
}
@media screen and (max-width: 1440px) and (min-width: 1366px) {
  .initialDetail .el-form img {
    width: 19px;
  }
}
@media screen and (max-width: 1366px) and (min-width: 1280px) {
  .initialDetail .title {
    font-size: 22px;
    margin-bottom: 7%;
    padding: 15px 0 12px;
  }
  .initialContent .el-form {
    margin: 10px 0 20px;
  }
  .initialContent .el-form-item {
    margin-bottom: 25px;
    padding: 0 55px 0 5px;
  }
  .initialContent .el-form-item .el-form-item__label {
    font-size: 16px;
    line-height: 35px !important;
  }
  .initialContent .el-form-item .el-form-item__content {
    width: 320px;
    line-height: 35px !important;
  }
  .initialContent .el-form-item .el-input .el-input__inner {
    height: 35px !important;
    line-height: 35px !important;
    font-size: 15px !important;
  }
  .initialContent .el-button {
    font-size: 15px !important;
    padding: 10px 30px !important;
    margin: 10px 20px !important;
  }
  .initialDetail .el-form img {
    width: 16px;
  }
  .initialContent .el-dialog .el-dialog__header .el-dialog__title,
  .headerContent .tipDialog .el-dialog .el-dialog__header .el-dialog__title {
    font-size: 22px;
  }
}
@media screen and (max-width: 1280px) and (min-width: 1024px) {
  .initialDetail .title {
    font-size: 19px;
    margin-bottom: 6%;
    padding: 15px 0 10px;
  }
  .initialContent .el-form {
    margin: 5px 0 15px;
  }
  .initialContent .el-form-item {
    margin-bottom: 20px;
    padding: 0 45px 0 5px;
  }
  .initialContent .el-form-item .el-form-item__label {
    font-size: 16px;
    line-height: 32px !important;
  }
  .initialContent .el-form-item .el-form-item__content {
    width: 300px;
    line-height: 32px !important;
  }
  .initialContent .el-form-item .el-input .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    font-size: 14px !important;
  }
  .initialContent .el-button {
    font-size: 14px !important;
    padding: 8px 25px !important;
    margin: 8px 20px !important;
  }
  .initialDetail .el-form img {
    width: 15px;
  }
  .initialContent .el-dialog .el-dialog__header .el-dialog__title,
  .headerContent .tipDialog .el-dialog .el-dialog__header .el-dialog__title {
    font-size: 19px;
  }
}
@media screen and (max-width: 1024px) {
  .initialDetail .title {
    font-size: 20px;
    margin-bottom: 6%;
    padding: 15px 0 10px;
  }
  .initialContent .el-form {
    margin: 5px 0 10px;
  }
  .initialContent .el-form-item {
    margin-bottom: 20px;
    padding: 0 45px 0 5px;
  }
  .initialContent .el-form-item .el-form-item__label {
    font-size: 15px;
    line-height: 32px !important;
  }
  .initialContent .el-form-item .el-form-item__content {
    width: 290px;
    line-height: 32px !important;
  }
  .initialContent .el-form-item .el-input .el-input__inner {
    height: 32px !important;
    line-height: 32px !important;
    font-size: 14px !important;
  }
  .initialContent .el-button {
    font-size: 15px !important;
    padding: 8px 25px !important;
    margin: 8px 20px !important;
  }
  .initialDetail .el-form img {
    width: 15px;
  }
  .initialContent .el-dialog .el-dialog__header .el-dialog__title,
  .headerContent .tipDialog .el-dialog .el-dialog__header .el-dialog__title {
    font-size: 20px;
  }
}
</style>
