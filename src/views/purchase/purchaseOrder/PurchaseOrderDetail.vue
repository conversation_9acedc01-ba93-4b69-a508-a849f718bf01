<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>采购订单详情</div>
      <div>
        <el-button plain @click="onCancel">导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="采购订单号">
                <span>{{ form.purchaseNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="单据日期">
                <span>{{ conversion(form.purchaseTime, 'yyyy-MM-dd') }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="用途">
                <span>{{ form.usages }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="采购申请单号">
                <span>{{ form.applyNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="销售订单号">
                <span>{{ form.salesOrderNo }}</span>
              </el-descriptions-item>
<!--              <el-descriptions-item label="需求日期">-->
<!--                <span>{{ conversion(form.requiredTime, 'yyyy-MM-dd') }}</span>-->
<!--              </el-descriptions-item>-->
              <el-descriptions-item label="供应商">
                <span>{{ form.supplierName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="联系人">
                <span>{{ form.contactsName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                <span>{{ form.contactPhone }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="申请人">
                <span>{{ form.applyUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="申请部门">
                <span>{{ form.applyDeptName }}</span>
                <!--                <span>{{ findDept(form.applyDept) }}</span>-->
              </el-descriptions-item>
              <el-descriptions-item label="总数量">
                <span>{{ form.totalQty }}</span>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ form.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>

          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                商品信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.purchaseOrderDetailVOS"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              show-summary
              :summary-method="getSummaries"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="code" label="物料编码" width="120" />
              <el-table-column prop="name" label="物料名称" width="80" />
              <el-table-column prop="imageUrl" label="图片" width="100">
                <template slot-scope="{ row }">
                  <img v-if="row.imageUrl !== ''" class="pictureShow" :src="$filePath + row.imageUrl" alt=""
                       @click="previewImage(row.imageUrl, row.name || '产品图片')" style="cursor: pointer;" />
                </template>
              </el-table-column>
              <el-table-column prop="specifications" label="规格" width="120" />
              <el-table-column prop="model" label="型号" width="120" />
              <el-table-column prop="brand" label="品牌" width="100" />
              <el-table-column prop="colour" label="颜色" width="80" />
              <el-table-column prop="quantity" label="采购数量" width="80">
                <template slot-scope="scope">
                  <span>{{ scope.row.quantity}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="weight" label="重量(kg)" width="80"></el-table-column>
              <el-table-column prop="unit" label="单位" width="80">
              </el-table-column>
              <el-table-column prop="unitPrice" label="单价(￥)" width="100">
                <template slot-scope="scope">
                  <span>{{ (scope.row.unitPrice).toFixed(2)}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="金额(￥)" width="100">
                <template slot-scope="scope">
                  {{scope.row.unitPrice * scope.row.quantity}}
                </template>
              </el-table-column>
              <el-table-column prop="requiredTime" label="需求日期" width="120">
                <template slot-scope="scope">
                  <span>{{ conversion(scope.row.requiredTime, "yyyy-MM-dd")}}</span>
                </template>
              </el-table-column>
<!--              <el-table-column prop="contactPhone" label="联系电话" width="120" />-->
              <el-table-column prop="referLink" label="参考链接" width="160"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="110">
                <template slot-scope="scope">
                  <span>{{ scope.row.remark}}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>

<!--          <el-collapse-item name="status">-->
            <!-- <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                状态信息
              </span>
            </template> -->
<!--            <div style="height: 200px;">-->
<!--              <el-steps direction="vertical">-->
<!--                <el-step title="步骤 1"></el-step>-->
<!--                <el-step title="步骤 2"></el-step>-->
<!--                <el-step title="步骤 3" description="这是一段很长很长很长的描述性文字"></el-step>-->
<!--              </el-steps>-->
<!--            </div>-->
<!--          </el-collapse-item>-->

          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                发货信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="发货状态">
                <div :class="getShippingStatusClass(form.shippingStatus)">
                  {{ formatShippingStatus(form.shippingStatus) }}
                </div>
              </el-descriptions-item>
            </el-descriptions>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.purchaseOrderLogistics"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="shippedTime" label="发货日期" width="300">
                <template slot-scope="scope">
                  {{conversion(scope.row.shippedTime, "yyyy-MM-dd")}}
                </template>
              </el-table-column>
              <el-table-column prop="logisticsNo" label="物流单号" width="350"/>
              <el-table-column prop="logisticsName" label="物流公司" width="350"/>
              <el-table-column prop="shippedAmount" label="发货数量" width="120"/>
              <el-table-column prop="remark" label="备注" width="auto"/>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <!-- 图片预览组件 -->
    <ImagePreview
      :visible.sync="previewVisible"
      :image-url="previewImageUrl"
      :image-name="previewImageName"
    />

  </div>
</template>

<script>
import {conversion} from "../../../store/filters";
import {getContactsBySupplierId, getSupplierByProductId, getUnitList} from "@/api/basicmgt";
import {
  addPurchaseApply,
  getPurchasePersonnel, purchaseApplyAudit, purchaseOrderInfo
} from "@/api/purchasemgt";
import {findDeptAll} from "@/api/sysmgt";
import {sysServerUrl, removeTabs, collapseArea} from "@/assets/js/common";
import ImagePreview from "@/components/ImagePreview/ImagePreview.vue";
import {getShippingStatusClass} from "@/assets/js/utils";

export default {
  name: "purchaseOrderDetail",
  components: {ImagePreview},
  data() {
    return {
      shippingStatusList:  [
        {
          label: '待发货',
          value: 0
        },
        {
          label: '部分发货',
          value: 1
        },
        {
          label: '已发货',
          value: 2
        }
      ],
      previewVisible: false,
      previewImageUrl: '',
      previewImageName: '',
      productSupplierList: [], //选择的物料的供应商列表
      departmentList: [],
      defaultProps: {
        parent: 'path',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          return val.children && val.children.length > 0;
        }
      },
      selectProductIds:[], //选择的商品id信息
      addMaterialType: "",
      dialogVisibleAddDetail: false, //添加物料dialog框
      dialogVisibleAddMaterial:false, //按物料添加
      productId: "", //id
      stateType: "", //类型
      orderId: "", //申请单id
      purchasePersonList:[],
      // 基本信息
      form: {
        applyNo: '',                     // 采购申请单号
        applyTime: '',                   // 申请日期
        purchaseTime: '',                // 采购日期
        usages: '',                      // 用途
        salesOrderNo: '',                // 销售订单号
        customerOrderNo: '',             // 客户订单编号
        applyUser: null,                 // 申请人（用户ID）
        applyDept: '',                   // 申请部门
        creditsUser: null,              // 制单人（用户ID）
        creditsTime: '',                 // 制单时间
        status: null,                    // 申请单状态（建议绑定下拉枚举）
        remark: '',                      // 备注
        purchaseOrderDetailVOS: [],
        accessoryList:[],
        purchaseOrderLogistics: [] //物流信息
      },
      rules: {
        applyTime: [{ required: true, message: '申请日期不能为空', trigger: ['blur', 'change'] }],
        usages: [{ required: true, message: '用途不能为空', trigger: ['blur', 'change'] }],
        applyUser: [{ required: true, message: '申请人不能为空', trigger: ['blur', 'change'] }],
        applyDept: [{ required: true, message: '申请部门不能为空', trigger: ['blur', 'change'] }],
      },
      unitData: [], //计量单位
      detailInfo: {}, // 商品信息
      tableData: [], // 商品特性 供应商
      rowEditIndex: "",
      colimnEditIndex: "",
      accessoryList: [], // 图片信息
      fileList: [], // 图片上传列表
      activeNames: ["base", "product", "status", "more"], // 全部展开
      // 弹框
      dialogFormVisible: false, //
      enableState: "", //
      // 进度条
      progressFlag: false,
      percentage: 0,
      sysServerUrl:sysServerUrl,
    }
  },
  methods:{
    getShippingStatusClass,
    //转换发货状态
    formatShippingStatus(shippingStatus){
      const findShippingStatus = this.shippingStatusList.find(item => item.value === shippingStatus);
      return findShippingStatus ? findShippingStatus.label : '';
    },
    /**
     * 预览图片
     */
    previewImage(imageUrl, imageName = '产品图片') {
      this.previewImageUrl = this.$filePath + imageUrl;
      this.previewImageName = imageName + '.' + imageUrl.substring(imageUrl.lastIndexOf('.') + 1);
      this.previewVisible = true;
    },
    //筛选出部门名称
    findDept(deptId){
      this.departmentList.find(item => item.id == deptId)
    },
    //选择供应商后
    onSupplierChange(row){
      const selectedSupplier = row.supplierList.find(item => item.supplierId === row.supplierId);
      if (selectedSupplier){
        //根据供应商id去查询联系人电话
        getContactsBySupplierId(selectedSupplier.supplierId).then(res =>{
          if (res.data.code === 100 && res.data.data.length > 0){
            //TODO 暂时取第一条联系人的信息
            row.contactPhone = res.data.data[0].mobile
            row.contactsId = res.data.data[0].id
          }
        })
        //参考链接
        row.referLink = selectedSupplier.referLink
        row.unitPrice = selectedSupplier.price
      }else {
        console.warn("未找到供应商信息")
      }
    },
    //格式化供应商名
    formatSupplier(row){
      let supplierId = row.supplierId;
      let findSupplier = row.productSupplierList.find(item => item.supplierId === supplierId);
      return findSupplier ? findSupplier.supplierName : "";
    },
    //搜索供应商信息
    loadSupplierList(row){
      if (!row.productId) {
        this.$DonMessage.warning("请先选择产品");
        return;
      }
      // 如果已经加载过就不重复查
      // if (row.supplierList && row.supplierList.length > 0) return;

      getSupplierByProductId(row.productId).then(res => {
        if (res.data.code === 100){
          this.$set(row, 'supplierList', res.data.data || []);
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //删除物料列表
    delClick(index, list){
      console.log("delClick",index, list)

      if (!Array.isArray(list)) return;
      list.splice(index, 1);
    },
    // 采购人员
    getPurchasePersonList() {
      getPurchasePersonnel().then(res => {
        if (res.data.code === 100){
          this.purchasePersonList = res.data.data
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['form'].validateField('applyDept')
      }
    },
    // 所属部门
    getDepartment() {
      let _this = this;
      _this.departmentMap = new Map();
      findDeptAll().then(res => {
        if (res.data.data) {
          _this.departmentList = res.data.data
          _this.getDepartmentMap(_this.departmentList, "");
        } else {
          _this.departmentList = []
        }
      })
    },
    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },
    //保存
    onSubmit(type){
      if (type === "save"){
        //保存状态设置为草稿
        this.form.status = 0;
      }else if (type === "submit"){
        //保存状态设置为待审核
        this.form.status = 1;
      }
      addPurchaseApply(this.form).then(res => {
        if (res.data.code === 100){
          this.$DonMessage.success("保存成功")
          this.selectApplyOrderInfo(this.applyOrderId);
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //反审核
    auditReject(){
      const params = {
        id: this.form.id,
        status: 3
      }
      purchaseApplyAudit(params).then(res => {
        if (res.data.code === 100){
          this.$DonMessage.success("反审核成功")
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //取消
    onCancel() {

    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    conversion,
    //双击单元格
    dbClickCell(scope) {
      var _this = this;
      _this.rowEditIndex = scope.$index
      _this.colimnEditIndex = scope.column.id
      _this.$nextTick(() => {
        _this.$refs.tableRowInputRef.focus();
      });
    },
    //格式化单位
    formatUnit(unit){
      let findUnit = this.unitData.find(item => item.code === unit);
      return findUnit ? findUnit.name : ""
    },
    // 输入框失去焦点
    onInputTableBlur(scope) {
      var _this = this;
      _this.rowEditIndex = "";
      _this.colimnEditIndex = "";
    },
    //按分类添加物料
    addMaterial(type){
      console.log("添加物料",type)
      if (type === "sale"){
        this.addMaterialType = "sale"
        this.dialogVisibleAddDetail = true
      } else if (type ==="bom"){
        this.addMaterialType = "bom"
        this.dialogVisibleAddDetail = true
      }else if (type ==="material"){
        this.addMaterialType = "material"
        this.dialogVisibleAddMaterial = true
      }
    },
    //选择销售订单
    handleSelectSalesOrder(salesOrder){
      this.form.salesOrderNo = salesOrder.salesNo
    },
    //关闭添加物料弹窗
    onAddDetailDialogClose(){

    },
    // 获取计量单位
    getUnitData() {
      var param = {}
      getUnitList(param).then((res) => {
        if (res.data.code == '100') {
          this.unitData = res.data.data
        }
      });
    },
    //根据申请单id，获取申请单详情
    selectOrderInfo(orderId){
      purchaseOrderInfo(orderId).then((res) => {
        if (res.data.code === 100){
          this.form = res.data.data
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        switch (column.property) {
          case "quantity":
            this.form.purchaseOrderDetailVOS.forEach((item) => {
              if (item.quantity) {
                count += item.quantity;
              }
            });
            sums[index] = count;
            break;
          case "amount":
            const prize = this.form.purchaseOrderDetailVOS.map((item) =>
              item["unitPrice"] * item["quantity"].toFixed(2)
            );
            sums[index] = prize.reduce((prev, curr) => {
              return prev + curr;
            }, 0);
            sums[index] = sums[index].toFixed(2);
            break;
        }
      })
      return sums;
    },
    areaHeight() {
      setTimeout(() => {
        var allHeight = $(".layoutContainer").height();
        var titleHeight = $(".elTabtitle").height();
        var marginVal = 2 * Number($(".elTabsContent .el-collapse-item").css("marginTop").split("px")[0]);
        var val = allHeight - titleHeight - marginVal - 5;
        $(".elTabsContent").css("height", val);
      }, 60);
    },
    contentSize() {
      var _this = this;
      _this.areaHeight();
      window.addEventListener("resize", function () {
        _this.areaHeight();
      })
    },
    //
    onInputSearch(type, $event) {
      if (type == "name") {
        this.form.categoryName = $event.name;
        this.form.categoryId = $event.id;
      } else if (type == "warehouse") {
        this.form.warehouseName = $event.name;
        this.form.warehouseId = $event.id;
      } else if (type == "location") {
        this.form.locationName = $event.name;
        this.form.locationId = $event.id;
      } else if (type == "brand") {
        this.form.brand = $event.name
      }
    },
  },
  mounted() {
    this.productId = this.$route.params.type;
    this.orderId = this.$route.params.id;
    if (this.productId === "add") {
      this.stateType = "add";
      this.form.createdTime = new Date().getTime();
      this.form.createdUserName = this.$store.state.realName;
    } else {
      this.stateType = "detail";
      //根据申请单id查询申请单详情
      this.selectOrderInfo(this.orderId);
    }
    this.contentSize();
    //查询单位
    this.getUnitData();
    this.getDepartment();
    this.getPurchasePersonList();
  },
}
</script>

<style scoped>
.more-info{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
