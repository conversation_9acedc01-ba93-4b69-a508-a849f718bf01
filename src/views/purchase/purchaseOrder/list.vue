<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" :label-width="$labelSix">
        <!-- 申请日期 -->
        <el-form-item label="单据日期">
          <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
        </el-form-item>
        <!-- 采购申请单号 -->
        <el-form-item label="采购申请单号" prop="applyNo">
          <el-input
            v-model="queryParams.applyNo"
            placeholder="请输入采购申请单号"
            clearable
          />
        </el-form-item>
        <!-- 销售订单号 -->
        <el-form-item label="销售订单号" prop="salesOrderNo">
          <el-input
            v-model="queryParams.salesOrderNo"
            placeholder="请输入销售订单号"
            clearable
          />
        </el-form-item>
        <!-- 状态 -->
        <el-form-item label="发货状态" prop="status">
          <el-select
            v-model="queryParams.shippingStatus"
            placeholder="请选择发货状态"
            clearable
          >
            <el-option v-for="item in shippingStatusList" :value="item.value" :key="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="入库状态" prop="inbountStatus" v-if="isShow">
          <el-select v-model.trim="queryParams.inbountStatus" clearable filterable>
            <el-option v-for="(item, index) of purchaseStoreStatus" :key="index" :label="item.name"
                       :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请人" prop="applyUser" v-if="isShow">
          <el-select v-model.trim="queryParams.applyUser" clearable filterable>
            <el-option v-for="(item, index) of purchasePersonList" :key="index" :label="item.realName"
                       :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请部门" prop="applyDept" v-if="isShow">
          <select-tree ref="modelSelectTree" :options="departmentList" v-model.trim="queryParams.applyDept"
                       :props="defaultProps" :expand_on_click_node="true" :check_on_click_node="false"
                       @getCurrentNode="getCurrentNode" placeholder="请选择部门"/>
        </el-form-item>
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i
            class="el-icon-arrow-up el-icon--right"></i></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="bulkDown-icon" @click="generateOrderClick()">生成待入库单</el-button>
          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
          <el-button type="text" icon="disable-icon" @click="auditClick('abrogate')">作废</el-button>
          <el-button type="text" icon="deleteRed-icon" @click="delClick()">删除</el-button>
          <el-button type="text" icon="el-icon-truck" @click="orderLogistics()">物流信息</el-button>
        </div>
        <!--        <div>-->
        <!--          <el-button type="text" icon="el-icon-setting">自定义列</el-button>-->
        <!--          <el-button type="text" icon="el-icon-refresh">刷新</el-button>-->
        <!--        </div>-->
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange"
                @row-click="handleRowClick"
                show-summary
                :summary-method="getSummaries"
      >
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="applyTime" label="单据日期" width="100">
          <template slot-scope="scope">
            {{ conversion(scope.row.purchaseTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="purchaseNo" label="采购订单号" width="160">
          <template #default="{row}">
            <span class="linkStyle" @click="handleDetail(row)">{{ row.purchaseNo }}</span>
          </template>
        </el-table-column>
<!--        <el-table-column prop="status" label="状态" width="100">-->
<!--          <template #default="{row}">-->
<!--            <div :class="getStatusClass(row.status)">-->
<!--              {{ formatAuditStatus(row.status) }}-->
<!--            </div>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column prop="applyNo" label="采购申请单号" width="160">
          <template #default="{row}">
            <span class="linkStyle" @click="handleApplyDetail(row)">{{ row.applyNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="salesOrderNo" label="销售订单号" width="160">
          <template #default="{row}">
            <span class="linkStyle" @click="handleSalesDetail(row)">{{ row.salesOrderNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="usages" label="用途" width="150"/>
        <el-table-column prop="shippingStatus" label="发货状态" width="150">
          <template slot-scope="scope">
            <div :class="getShippingStatusClass(scope.row.shippingStatus)">
              {{ formatShippingStatus(scope.row.shippingStatus) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="inbountStatus" label="入库状态" width="100">
          <template slot-scope="scope">
            <div :class="getInbountStatusClass(scope.row.inbountStatus)">
              {{formatInbount(scope.row.inbountStatus)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="inboundOrderStatus" label="待入库单" width="100">
          <template #default="{row}">
            <div :class="getInbountOrderStatusClass(row.inboundOrderStatus)">
              {{row.inboundOrderStatus === 0 ? '未生成' : '已生成'}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="totalQty" label="数量" />
        <!--        <el-table-column prop="requiredTime" label="需求日期" />-->
        <el-table-column prop="supplierName" label="供应商" width="120">
        </el-table-column>
        <el-table-column prop="applyUserName" label="申请人" />
        <el-table-column prop="applyDeptName" label="申请部门" />
        <el-table-column prop="auditUserName" label="审核人" width="100"/>
        <el-table-column prop="auditTime" label="审核时间" width="160">
          <template slot-scope="scope">
            {{ conversion(scope.row.auditTime, 'yyyy-MM-dd HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注"/>
        <el-table-column prop="creditsUserName" label="制单人" width="100"/>
        <el-table-column prop="creditsTime" label="制单时间" width="160">
          <template slot-scope="scope">
            {{ conversion(scope.row.creditsTime, 'yyyy-MM-dd HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
            <!--            <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>
    </div>

    <!--    物流信息弹窗-->
    <el-dialog
      :visible.sync="dialogVisible"
      title="物流信息"
      width="500px"
      @close="handleClose"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="logisticsForm"
        label-width="100px"
      >
        <el-form-item label="采购订单号" prop="purchaseNo">
          <el-input v-model="form.purchaseNo" disabled />
        </el-form-item>

        <el-form-item label="发货日期" prop="shippedTime">
          <el-date-picker
            v-model="form.shippedTime"
            type="date"
            placeholder="请选择"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="物流单号" prop="logisticsNo">
          <el-input v-model="form.logisticsNo" placeholder="请输入物流单号" />
        </el-form-item>

        <el-form-item label="物流公司" prop="logisticsName">
          <el-select
            v-model="form.logisticsId"
            placeholder="请选择"
            filterable
            style="width: 100%;"
            @change="handleLogisticsChange"
          >
            <el-option
              v-for="item in logisticsList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="发货数量" prop="shippedAmount">
          <el-input
            v-model="form.shippedAmount"
            placeholder="请输入发货数量"
            type="number"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="2"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button plain @click="dialogVisible = false">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {
  addPurchaseOrderLogistics,
  deleteOrderById, generateBeInStock,
  generatePurchaseOrder, getPurchasePersonnel,
  getPurchaseStoreEnum,
  purchaseOrderAudit,
  purchaseOrderList
} from "@/api/purchasemgt";
import {addTabs, tableHeight} from "@/assets/js/common";
import {dictTypeQueryData, findDeptAll} from "@/api/sysmgt";
import {getInbountStatusClass, getShippingStatusClass, getStatusClass} from "@/assets/js/utils";
import SelectTree from "@/components/TreeView/SelectTree.vue";
import {getLogisticsAllList} from "@/api/basicmgt";
import {getSalesOrderId} from "@/api/salesmgt";

export default {
  name: "list",
  components: {SelectTree, DateRangeSelector, Pagination},
  data() {
    return {
      logisticsList:[],
      dialogVisible: false, // 控制弹窗显示
      form: {
        selectedLogistics: null,
        purchaseId: '',
        purchaseNo: "",
        logisticsNo: "",
        logisticsId: "",
        logisticsName: "",
        shippedAmount: 0,
        shippedTime: "",
        remark: "",
      },
      rules: {
        shippedTime: [{ required: true, message: "请选择发货日期", trigger: "change" }],
        logisticsNo: [{ required: true, message: "请输入物流单号", trigger: "blur" }],
        logisticsName: [{ required: true, message: "请选择物流公司", trigger: "change" }],
        shippedAmount: [
          { required: true, message: "请输入发货数量", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error("发货数量必须大于0"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }],
      },
      clickedRow: null,
      purchasePersonList:[],
      purchaseStoreStatus: [],//入库状态枚举
      auditStatusDicts: [],
      shippingStatusList:  [
        {
          label: '待发货',
          value: 0
        },
        {
          label: '部分发货',
          value: 1
        },
        {
          label: '已发货',
          value: 2
        }
      ],
      selectedRows: [],
      deleteList: [],
      departmentList: [],
      defaultProps: {
        parent: 'path',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          return val.children && val.children.length > 0;
        }
      },
      maximumHeight: 0,
      total: 0,
      isShow: false,
      resultList: [],
      queryParams: {
        shippingStatus: null,
        inbountStatus: null,
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        applyNo: '',
        purchaseNo:'',
        salesOrderNo: '',
        status: null,
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10,
      },
    }
  },
  methods: {
    //获取是否生成待入库单样式
    //匹配入库状态style
    getInbountOrderStatusClass(status){
      switch (status) {
        case 0:
          return 'errorColor'; // 未生成
        case 1:
          return 'successColor'; // 已生成
        default:
          return '';
      }
    },
    getShippingStatusClass,
    //物流公司选择变化
    handleLogisticsChange(id) {
      const selected = this.logisticsList.find(item => item.id === id);
      this.form.logisticsName = selected ? selected.name : '';
    },
    //提交物流信息
    submitForm() {
      this.$refs.logisticsForm.validate((valid) => {
        if (valid) {
          // 模拟提交
          addPurchaseOrderLogistics(this.form).then(res => {
            if (res.data.code === 100){
              this.$DonMessage.success("物流信息添加成功")
              this.dialogVisible = false;
              this.handleSearch()
            }else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        }
      });
    },
    handleClose() {
      this.$refs.logisticsForm.resetFields();
    },
    getInbountStatusClass,
    getStatusClass,
    //转换状态显示
    formatAuditStatus(status){
      const match = this.auditStatusDicts.find(item => item.code == status);
      return match ? match.name : '';
    },
    //转换发货状态
    formatShippingStatus(shippingStatus){
      const findShippingStatus = this.shippingStatusList.find(item => item.value === shippingStatus);
      return findShippingStatus ? findShippingStatus.label : '';
    },
    //转换入库状态
    formatInbount(InbountStatus){
      let findInbountStatus = this.purchaseStoreStatus.find(item => item.code === InbountStatus);
      return findInbountStatus ? findInbountStatus.name : '';
    },
    //新增
    addClick() {
      var title = "新增采购申请单";
      this.$router.push({
        name: 'addPurchaseApply',
        params: {id: 'add'}
      });
      addTabs(this.$route.path, title);
    },
    //批量导入
    batchImport() {

    },
    //多类型审核
    auditClick(type) {
      let status = 0;
      switch (type){
        case 'reviewPass':
          status = 2;
          break;
        case 'reviewReject':
          status = 3;
          break;
        case 'abrogate':
          status = 4;
          break;
      }
      if (this.selectedRows === null || this.selectedRows.length <= 0){
        this.$DonMessage.warning("请选择要操作的订单")
        return
      }
      this.selectedRows.map(item => {
        const params = {
          id: item.id,
          status: status
        }
        purchaseOrderAudit(params).then(res => {
          if (res.data.code === 100){
            this.$DonMessage.success("成功")
            this.handleSearch();
          }else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    orderLogistics(){
      //物流信息
      if (!this.selectedRows || this.selectedRows.length <= 0){
        this.$DonMessage.warning("请选择要操作的订单")
        return;
      }
      if (this.selectedRows.length > 1){
        this.$DonMessage.warning("只能选择一条订单")
        return;
      }
      this.dialogVisible = true
      this.form.purchaseNo = this.selectedRows[0].purchaseNo
      this.form.purchaseId = this.selectedRows[0].id
    },
    //获取点击的那行
    handleRowClick(row, column, event) {
      // row 就是你点击的整行对象
      this.clickedRow = row
    },
    // 所属部门
    getDepartment() {
      let _this = this;
      _this.departmentMap = new Map();
      findDeptAll().then(res => {
        if (res.data.data) {
          _this.departmentList = res.data.data
          _this.getDepartmentMap(_this.departmentList, "");
        } else {
          _this.departmentList = []
        }
      })
    },
    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['form'].validateField('applyDept')
      }
    },
    //生成采购订单
    generateOrderClick() {
      //给当前选中的采购申请单生成采购订单
      if (!this.selectedRows || this.selectedRows.length <= 0){
        this.$DonMessage.warning("请勾选要操作的订单")
        return;
      }
      if (this.selectedRows.length > 1){
        this.$DonMessage.warning("只能选择一条订单")
        return;
      }
      this.selectedRows.map(item => {
        generateBeInStock(item.id).then(res => {
          if (res.data.code === 100){
            this.$DonMessage.success("生成待入库单成功")
            this.handleSearch()
          }else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    //删除
    delClick() {
      if (!this.selectedRows || this.selectedRows.length <= 0) {
        this.$DonMessage.warning("请勾选要操作的订单")
        return
      }

      this.$confirm('确定删除【' + this.selectedRows.length + '】条数据吗?', '删除评价', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectedRows.map(item => {
          deleteOrderById(item.id).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success(res.data.msg)
              this.handleSearch();
            } else {
              this.$DonMessage.error(item.applyNo + '-删除失败:' + res.data.msg)
            }
          })
        })
      })
    },
    // 采购人员
    getPurchasePersonList() {
      getPurchasePersonnel().then(res => {
        if (res.data.code === 100) {
          this.purchasePersonList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //导出
    exportClick() {

    },
    //详情
    handleDetail(row) {
      const title = row.purchaseNo;
      this.$router.push({
        name: 'purchaseOrderDetail',
        params: {
          id: row.id,
          type: "detail"
        }
      });
      addTabs(this.$route.path, title)
    },
    //编辑
    handleEdit(row) {
      var title = "编辑采购申请单"
      this.$router.push({
        name: 'addPurchaseApply',
        params: {
          id: 'edit',
          applyOrderId: row.id
        }
      });
      addTabs(this.$route.path, title)
    },
    // 采购订单表格高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //跳转到销售订详情页
    handleSalesDetail(row){
      const title = row.salesOrderNo;
      const params = {
        salesNo: row.salesOrderNo
      }
      getSalesOrderId(params).then(res =>{
        if (res.data.code === 100){
          this.$router.push({ name: 'salesOrderDetail', params: { id: res.data.data }});
          addTabs(this.$route.path, title);
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //跳转采购申请单详情
    handleApplyDetail(row){
      const title = row.applyNo
      this.$router.push({
        name: 'purchaseApplyOrderDetail',
        params: {
          id: row.applyId,
          type: 'detail'
        }
      });
      addTabs(this.$route.path, title)
    },
    //时间格式化方法
    conversion,
    //查询
    handleSearch() {
      // 发起请求查询接口
      this.dataList(this.queryParams)
    },
    //  处理选中的行
    handleSelectionChange(val) {
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },
    //重置查询条件
    handleReset() {
      this.queryParams = {
        startDate: '',
        endDate: '',
        applyNo: '',
        salesOrderNo: '',
        status: null,
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10
      }
      // 使用 nextTick 重置部门选择组件
      this.$nextTick(() => {
        if (this.$refs.modelSelectTree) {
          this.$refs.modelSelectTree.labelModel = ''
          this.$refs.modelSelectTree.valueModel = ''
          this.$refs.modelSelectTree.sltCheckedId = ''
          this.$refs.modelSelectTree.$refs.tree.setCurrentKey(null)
          this.$refs.modelSelectTree.$refs.tree.setCheckedKeys([])
        }
      })
      //重置日期选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      this.handleSearch()
    },
    //分页查询数据
    dataList(params) {
      purchaseOrderList(params).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //totalQty
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        switch (column.property) {
          case "totalQty":
            this.resultList.forEach((item) => {
              if (item.totalQty) {
                count += item.totalQty;
              }
            });
            sums[index] = count;
            break;
        }
      })
      return sums;
    },
    // 获取日期范围选择
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      this.handleSearch();
    },
    getPurchaseStoreStatus(){
      getPurchaseStoreEnum().then(res => {
        if (res.data.code === 100){
          this.purchaseStoreStatus = res.data.data
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    getLogisticsListAll(){
      getLogisticsAllList().then(res => {
        if (res.data.code === 100) {
          this.logisticsList = res.data.data
        }
      })
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
  mounted() {
    this.handleSearch();
    //获取审核状态数据字典
    this.getAuditStatusDict();
    //查询入库类型枚举值
    this.getPurchaseStoreStatus();
    this.getDepartment();
    this.getPurchasePersonList();
    //获取所有物流公司信息列表
    this.getLogisticsListAll();
  },
}
</script>

<style scoped>

</style>
