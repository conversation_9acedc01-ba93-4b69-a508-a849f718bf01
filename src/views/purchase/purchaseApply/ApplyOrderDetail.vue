<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div>采购申请单详情</div>
      <div>
<!--        <el-button plain @click="auditReject">反审核</el-button>-->
        <el-button plain @click="onCancel">导出</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="采购申请单号">
                <span>{{ form.applyNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="单据日期">
                <span>{{ conversion(form.applyTime, 'yyyy-MM-dd') }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="用途">
                <span>{{ form.usages }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="销售订单号">
                <span>{{ form.salesOrderNo }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="申请人">
                <span>{{ form.applyUserName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="申请部门">
                <span>{{ form.applyDeptName }}</span>
<!--                <span>{{ findDept(form.applyDept) }}</span>-->
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions>
              <el-descriptions-item label="备注">
                <span>{{ form.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>

          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                物料信息
              </span>
            </template>
            <el-table
              style="width: 100%;"
              ref="table"
              :data="form.purchaseApplyDetailVOS"
              border
              stripe
              highlight-current-row
              @header-dragend="changeColWidth"
              show-summary
              :summary-method="getSummaries"
            >
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="code" label="物料编码" width="140" />
              <el-table-column prop="name" label="物料名称" width="120" />
              <el-table-column prop="imageUrl" label="图片" width="100">
                <template slot-scope="{ row }">
                  <img v-if="row.imageUrl !== ''" class="pictureShow" :src="$filePath + row.imageUrl" alt=""
                       @click="previewImage(row.imageUrl, row.name || '产品图片')" style="cursor: pointer;" />
                </template>
              </el-table-column>
              <el-table-column prop="specifications" label="规格" width="120" />
              <el-table-column prop="model" label="型号" width="120" />
              <el-table-column prop="brand" label="品牌" width="100" />
              <el-table-column prop="colour" label="颜色" width="80" />
              <el-table-column prop="requiredTime" label="需求日期" width="120">
                <template slot-scope="scope">
                  <span>{{ conversion(scope.row.requiredTime, "yyyy-MM-dd")}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="采购数量" width="80"></el-table-column>
              <el-table-column label="重量(kg)" prop="weight"></el-table-column>
              <el-table-column prop="currentInventory" label="当前库存" width="100" />
              <el-table-column prop="lockQty" label="锁定库存" width="100" />
              <el-table-column prop="unit" label="单位" width="80">
              </el-table-column>
              <el-table-column prop="supplierId" label="供应商" width="170">
                <template slot-scope="scope">
                  {{formatSupplier(scope.row)}}
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" label="单价(￥)" width="100">
                <template slot-scope="scope">
                  <span>{{ (scope.row.unitPrice).toFixed(2)}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="金额(￥)" width="100">
              </el-table-column>
              <el-table-column prop="contactPhone" label="联系电话" width="120" />
              <el-table-column prop="referLink" label="参考链接" width="160"></el-table-column>
              <el-table-column label="备注" prop="remark" min-width="110">
                <template slot-scope="scope">
                  <span>{{ scope.row.remark}}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item label="制单人">
                {{ form.creditsUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="制单日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="修改人" v-if="this.stateType == 'edit'">
                {{ form.updatedUserName }}
              </el-descriptions-item>
              <el-descriptions-item label="修改日期" v-if="this.stateType == 'edit'">
                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item label="附件" :span="2">
                <FileUpload :drag="false"
                            :value="fileList"
                            :is-detail="true"></FileUpload>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <el-dialog v-if="dialogVisibleAddDetail"
               :visible.sync="dialogVisibleAddDetail"
               title="添加物料"
               width="1400px !important"
               :destroy-on-close="true"
               @close="onAddDetailDialogClose">
      <AddApplyOrderDetail :addMaterialType="addMaterialType" :sales-no="this.form.salesOrderNo" @select="handleSelectProducts"></AddApplyOrderDetail>
    </el-dialog>
    <el-dialog v-if="dialogVisibleAddMaterial"
               :visible.sync="dialogVisibleAddMaterial"
               title="添加物料"
               width="1400px !important"
               :destroy-on-close="true"
               @close="onAddDetailDialogClose">
      <AddMaterialInfo @select="handleSelectProducts"></AddMaterialInfo>
    </el-dialog>

    <!-- 图片预览组件 -->
    <ImagePreview
      :visible.sync="previewVisible"
      :image-url="previewImageUrl"
      :image-name="previewImageName"
    />

    <!-- //反审核组件 -->
<!--    <el-dialog-->
<!--      title="反审核"-->
<!--      :visible.sync="dialogVisibleReject"-->
<!--      width="800px !important"-->
<!--      @close="dialogVisibleReject = false"-->
<!--    >-->
<!--      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">-->
<!--        <el-form-item label="采购申请单号">-->
<!--          <el-input v-model="form.applyNo" disabled></el-input>-->
<!--        </el-form-item>-->

<!--        <el-form-item label="驳回原因" prop="rejectReason" required>-->
<!--          <el-input-->
<!--            type="textarea"-->
<!--            v-model="form.rejectReason"-->
<!--            placeholder="请输入驳回原因"-->
<!--            :rows="4"-->
<!--          />-->
<!--        </el-form-item>-->

<!--        <el-form-item label="附件">-->
<!--          <el-upload-->
<!--            class="upload-demo"-->
<!--            drag-->
<!--            action="/api/upload"-->
<!--            :on-preview="handlePreview"-->
<!--            :on-remove="handleRemove"-->
<!--            :file-list="form.fileList"-->
<!--            :auto-upload="false"-->
<!--            :limit="5"-->
<!--            :on-exceed="handleExceed"-->
<!--            :before-upload="beforeUpload"-->
<!--            :http-request="handleUpload"-->
<!--            accept=".jpg,.png,.xlsx,.docx,.zip"-->
<!--          >-->
<!--            <i class="el-icon-upload"></i>-->
<!--            <div class="el-upload__text">-->
<!--              拖拽附件至此或 Ctrl+V 粘贴上传<br/>-->
<!--              <el-link type="primary" @click.stop.prevent>点击上传</el-link>-->
<!--            </div>-->
<!--            <div class="el-upload__tip" slot="tip">-->
<!--              支持上传 jpg, png, xlsx, docx, zip 格式的文件，且文件数不超过 5 个，单个文件大小不超过 100M-->
<!--            </div>-->
<!--          </el-upload>-->
<!--        </el-form-item>-->
<!--      </el-form>-->

<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button type="primary" @click="handleReject">提交</el-button>-->
<!--        <el-button plain @click="dialogVisibleReject = false">取消</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->

  </div>
</template>

<script>
import {conversion} from "../../../store/filters";
import {getContactsBySupplierId, getUnitList} from "@/api/basicmgt";
import AddApplyOrderDetail from "@/views/purchase/purchaseApply/AddApplyOrderDetail.vue";
import AddMaterialInfo from "@/views/purchase/purchaseApply/AddMaterialInfo.vue";
import {
  addPurchaseApply,
  getPurchasePersonnel,
  purchaseApplyAudit,
  purchaseApplyEdit,
  purchaseApplyInfo,
  queryProductsOrderDetail
} from "@/api/purchasemgt";
import {findDeptAll} from "@/api/sysmgt";
import {sysServerUrl} from "@/assets/js/common";
import FileUpload from "@/components/FileUpload/FileUpload.vue";
import ImagePreview from "@/components/ImagePreview/ImagePreview.vue";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "ApplyOrderDetail",
  components: {ImagePreview, FileUpload, AddApplyOrderDetail, AddMaterialInfo},
  data() {
    return {
      dynamicColumn: getColumnNumber(this),
      previewVisible: false,
      previewImageUrl: '',
      previewImageName: '',
      sysServerUrl: sysServerUrl,
      dialogVisibleReject: false,
      productSupplierList: [], //选择的物料的供应商列表
      departmentList: [],
      defaultProps: {
        parent: 'path',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          return val.children && val.children.length > 0;
        }
      },
      selectProductIds:[], //选择的商品id信息
      addMaterialType: "",
      dialogVisibleAddDetail: false, //添加物料dialog框
      dialogVisibleAddMaterial:false, //按物料添加
      productId: "", //id
      stateType: "", //类型
      applyOrderId: "", //申请单id
      purchasePersonList:[],
      // 基本信息
      form: {
        applyNo: '',                     // 采购申请单号
        applyTime: '',                   // 申请日期
        purchaseTime: '',                // 采购日期
        usages: '',                      // 用途
        salesOrderNo: '',                // 销售订单号
        customerOrderNo: '',             // 客户订单编号
        applyUser: null,                 // 申请人（用户ID）
        applyDept: '',                   // 申请部门
        creditsUser: null,              // 制单人（用户ID）
        creditsTime: '',                 // 制单时间
        status: null,                    // 申请单状态（建议绑定下拉枚举）
        remark: '',                      // 备注
        purchaseApplyDetailVOS: [],
        accessoryList:[],
        rejectReason:''
      },
      rules: {
        applyTime: [{ required: true, message: '申请日期不能为空', trigger: ['blur', 'change'] }],
        usages: [{ required: true, message: '用途不能为空', trigger: ['blur', 'change'] }],
        applyUser: [{ required: true, message: '申请人不能为空', trigger: ['blur', 'change'] }],
        applyDept: [{ required: true, message: '申请部门不能为空', trigger: ['blur', 'change'] }],
        rejectReason: [{ required: true, message: '驳回原因不能为空', trigger: ['blur', 'change'] }],
      },
      unitData: [], //计量单位
      detailInfo: {}, // 商品信息
      tableData: [], // 商品特性 供应商
      rowEditIndex: "",
      colimnEditIndex: "",
      accessoryList: [], // 图片信息
      fileList: [], // 图片上传列表
      activeNames: ["base", "product", "more"], // 全部展开
      // 弹框
      dialogFormVisible: false, //
      enableState: "", //
      // 进度条
      progressFlag: false,
      percentage: 0,
    }
  },
  methods:{
    /**
     * 预览图片
     */
    previewImage(imageUrl, imageName = '产品图片') {
      this.previewImageUrl = this.$filePath + imageUrl;
      this.previewImageName = imageName + '.' + imageUrl.substring(imageUrl.lastIndexOf('.') + 1);
      this.previewVisible = true;
    },
    //格式化供应商名
    formatSupplier(row){
      let supplierId = row.supplierId;
      let findSupplier = row.productSupplierList.find(item => item.supplierId === supplierId);
      return findSupplier ? findSupplier.supplierName : "";
    },
    //删除物料列表
    delClick(index, list){
      if (!Array.isArray(list)) return;
      list.splice(index, 1);
    },
    // 采购人员
    getPurchasePersonList() {
      getPurchasePersonnel().then(res => {
        if (res.data.code === 100){
          this.purchasePersonList = res.data.data
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['form'].validateField('applyDept')
      }
    },
    // 所属部门
    getDepartment() {
      let _this = this;
      _this.departmentMap = new Map();
      findDeptAll().then(res => {
        if (res.data.data) {
          _this.departmentList = res.data.data
          _this.getDepartmentMap(_this.departmentList, "");
        } else {
          _this.departmentList = []
        }
      })
    },
    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },
    //处理选择的物料，根据productId查询信息
    handleSelectProducts(productIds){
      this.selectProductIds = productIds;
      this.selectProductsInfo(productIds);
    },
    //根据productId查询物料详细信息
    selectProductsInfo(productIds) {
      const  params = {
        productIds: productIds
      }
      queryProductsOrderDetail(params).then(res => {
        if (res.data.code === 100){
          this.tableData = res.data.data
          this.form.purchaseApplyDetailVOS.push(...res.data.data)
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //编辑
    onEdit(type){
      if (type === "save"){
        //修改，不修改状态
      }else if (type === "submit"){
        //修改提交审核
        this.form.status = 1;
      }
      purchaseApplyEdit(this.form).then(res => {
        if (res.data.code === 100){

          this.$DonMessage.success(res.data.msg)
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //保存
    onSubmit(type){
      if (type === "save"){
        //保存状态设置为草稿
        this.form.status = 0;
      }else if (type === "submit"){
        //保存状态设置为待审核
        this.form.status = 1;
      }
      addPurchaseApply(this.form).then(res => {
        if (res.data.code === 100){
          this.$DonMessage.success("保存成功")
          this.selectApplyOrderInfo(this.applyOrderId);
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //反审核
    auditReject(){
      this.dialogVisibleReject = true
    },
    handleReject(){
      const params = {
        id: this.form.id,
        status: 3,
        rejectReason: this.form.rejectReason
      }
      purchaseApplyAudit(params).then(res => {
        if (res.data.code === 100){
          this.$DonMessage.success("反审核成功")
          this.dialogVisibleReject = false
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },

    //取消
    onCancel() {

    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    conversion,
    //双击单元格
    dbClickCell(scope) {
      var _this = this;
      _this.rowEditIndex = scope.$index
      _this.colimnEditIndex = scope.column.id
      _this.$nextTick(() => {
        _this.$refs.tableRowInputRef.focus();
      });
    },
    // 输入框失去焦点
    onInputTableBlur(scope) {
      var _this = this;
      _this.rowEditIndex = "";
      _this.colimnEditIndex = "";
    },
    //按分类添加物料
    addMaterial(type){
      if (type === "sale"){
        this.addMaterialType = "sale"
        this.dialogVisibleAddDetail = true
      } else if (type ==="bom"){
        this.addMaterialType = "bom"
        this.dialogVisibleAddDetail = true
      }else if (type ==="material"){
        this.addMaterialType = "material"
        this.dialogVisibleAddMaterial = true
      }
    },
    //选择销售订单
    handleSelectSalesOrder(salesOrder){
      this.form.salesOrderNo = salesOrder.salesNo
    },
    //关闭添加物料弹窗
    onAddDetailDialogClose(){

    },
    // 获取计量单位
    getUnitData() {
      var param = {}
      getUnitList(param).then((res) => {
        if (res.data.code == '100') {
          this.unitData = res.data.data
        }
      });
    },
    //根据申请单id，获取申请单详情
    selectApplyOrderInfo(applyOrderId){
      purchaseApplyInfo(applyOrderId).then((res) => {
        if (res.data.code === 100){
          this.form = res.data.data
          //附件列表数据
          this.fileList = res.data.data.accessoryList
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        var count = 0;
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        switch (column.property) {
          case "quantity":
            this.form.purchaseApplyDetailVOS.forEach((item) => {
              if (item.quantity) {
                count += item.quantity;
              }
            });
            sums[index] = count;
            break;
          case "amount":
            const prize = this.form.purchaseApplyDetailVOS.map((item) =>
              item["unitPrice"] * item["quantity"].toFixed(2)
            );
            sums[index] = prize.reduce((prev, curr) => {
              return prev + curr;
            }, 0);
            sums[index] = sums[index].toFixed(2);
            break;
        }
      })
      return sums;
    },
    areaHeight() {
      setTimeout(() => {
        var allHeight = $(".layoutContainer").height();
        var titleHeight = $(".elTabtitle").height();
        var marginVal = 2 * Number($(".elTabsContent .el-collapse-item").css("marginTop").split("px")[0]);
        var val = allHeight - titleHeight - marginVal - 5;
        $(".elTabsContent").css("height", val);
      }, 60);
    },
    contentSize() {
      var _this = this;
      _this.areaHeight();
      window.addEventListener("resize", function () {
        _this.areaHeight();
      })
    },
    //
    onInputSearch(type, $event) {
      if (type == "name") {
        this.form.categoryName = $event.name;
        this.form.categoryId = $event.id;
      } else if (type == "warehouse") {
        this.form.warehouseName = $event.name;
        this.form.warehouseId = $event.id;
      } else if (type == "location") {
        this.form.locationName = $event.name;
        this.form.locationId = $event.id;
      } else if (type == "brand") {
        this.form.brand = $event.name
      }
    },
  },
  mounted() {
    this.productId = this.$route.params.type;
    this.applyOrderId = this.$route.params.id;
    if (this.productId === "add") {
      this.stateType = "add";
      this.form.createdTime = new Date().getTime();
      this.form.createdUserName = this.$store.state.realName;
    } else {
      this.stateType = "detail";
      //根据申请单id查询申请单详情
      this.selectApplyOrderInfo(this.applyOrderId);
    }
    this.contentSize();
    //查询单位
    this.getUnitData();
    this.getDepartment();
    this.getPurchasePersonList();
  },
}
</script>

<style scoped>
.more-info{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
