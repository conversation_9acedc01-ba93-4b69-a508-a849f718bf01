<template>
<div class="appOrderDetail">
  <div class="dialogSearch">
    <el-input v-if="addMaterialType==='bom'" placeholder="商品编码" v-model="searchForm.code" clearable></el-input>
    <el-input v-if="addMaterialType==='bom'" placeholder="商品名称" v-model="searchForm.name" clearable></el-input>
    <el-checkbox  v-model="searchForm.underStock">仅显示库存不足</el-checkbox>
    <el-button v-if="addMaterialType==='bom'" type="primary" @click="handleSearch">搜索</el-button>
  </div>
  <div class="infoDetail" v-if="addMaterialType==='sale' || addMaterialType==='bom'">
    <el-row :gutter="10">
      <el-col :span="12">
        <div style="">
          <div class="scrollClass">
            <div style="margin-bottom: 5px;">
              <span>商品列表</span>
            </div>
            <el-table
              :data="productList"
              ref="table"
              style="width: 100%"
              highlight-current-row
              border
              stripe
              @row-click="handleRowClick"
            >
              <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
              <el-table-column v-if="addMaterialType==='sale'" label="客户货号" prop="customerProCode" width="100"></el-table-column>
              <el-table-column v-if="addMaterialType==='sale'" label="客户货名" prop="customerProName" width="100"></el-table-column>
              <el-table-column label="商品编码" prop="productCode" width="100"></el-table-column>
              <el-table-column label="商品名称" prop="productName" width="auto"></el-table-column>
              <el-table-column v-if="addMaterialType==='sale'" label="订单数量" prop="quantity" width="100"></el-table-column>
              <el-table-column label="库存" prop="currentInventory" width="50"></el-table-column>
            </el-table>
            <pagination v-show="searchForm.total > 0" :total="searchForm.total" :page.sync="searchForm.currentPage" :limit.sync="searchForm.pagesize" @pagination="getProductListByType" />
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <el-row>
          <div style="margin-bottom: 5px;">
            <span>物料列表</span>
          </div>
          <el-col :span="17" class="nationalInfo">
            <div>
              <el-table
                :data="materialList"
                style="width: 100%;"
                ref="table"
                highlight-current-row
                border
                stripe
                @header-dragend="changeColWidth"
                @selection-change="handleSelection"
              >
                <el-table-column type="selection" width="30" fixed="left" align="center"></el-table-column>
                <el-table-column label="物料编码" prop="code" width="80"></el-table-column>
                <el-table-column label="物料名称" prop="name" width="80"></el-table-column>
                <el-table-column label="单件用量" prop="quantity" width="80"></el-table-column>
                <el-table-column v-if="addMaterialType === 'sale'" label="订单用量" prop="orderUsage" width="80">
                  <template #default="scope">
                    {{sumQuantity(scope.row)}}
                  </template>
                </el-table-column>
                <el-table-column label="当前库存" prop="currentInventory" width="80"></el-table-column>
                <el-table-column label="锁定库存" prop="lockQty" width="auto"></el-table-column>
              </el-table>
              <pagination v-show="materialSearch.total > 0" :total="materialSearch.total" :page.sync="materialSearch.currentPage" :limit.sync="materialSearch.pagesize" @pagination="getMaterialList" />
            </div>
          </el-col>
          <el-col :span="7" class="nationalSelect">
            <el-table
              style="width:100%"
              border
              stripe
              highlight-current-row
              :data="determineModelList"
              :header-cell-style="{}"
            >
              <el-table-column prop="title">
                <template slot="header">
                  <div class="authorityTitle">
                    <div>
                      <span>已选(<b> {{ selectNum }} </b>)</span>
                    </div>
                    <div>
                      <span class="clearAction" @click="emptyCountry">清空</span>
                    </div>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div :class="'nationalList ' + '_' + scope.row.id">
                    <span>{{ scope.row.code }} {{  scope.row.name }}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <div class="submitArea">
      <el-button type="primary" @click="supplierSubmit()">{{ $t('button.submit') }}</el-button>
      <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
    </div>
  </div>
</div>
</template>

<script>
import {getProductListBySaleNo} from "@/api/purchasemgt";
import Pagination from "@/components/Pagination/index.vue";
import {getProductList} from "@/api/basicmgt";
import {getBomList} from "@/api/mpsmgt";

export default {
  name: "addApplyOrderDetail",
  components: {Pagination},
  props:{
    addMaterialType:{
      type: String,
      default: ""
    },
    salesNo:{
      type: String,
      default: ""
    }
  },
  data() {
    return {
      handleQuantity: 0, //销售订单中的数量
      productList: [],
      materialList: [],
      determineModelList: [], // 选中的对象
      selectNum: 0, // 选中的数量
      searchForm:{
        code: '', //商品编码
        name: '', //商品名称
        underStock: false,
        currentPage: 1,
        pagesize: 10,
        total: 0
      },
      materialSearch: {
        searchParams: '',
        currentPage: 1,
        pagesize: 10,
        total: 0
      },
    }
  },
  methods: {
    // 提交
    supplierSubmit() {
      // this.productIds = this.determineModelList.map(item => item.productId)
      if (this.addMaterialType === 'bom'){
        this.determineModelList.map(item => {
          item.orderUsage = item.quantity
        })
      }
      this.$emit('select',this.determineModelList)
      this.$parent.$parent.dialogVisibleAddDetail = false;
    },
    cancelClick() {
      this.$parent.$parent.dialogVisibleAddDetail = false;
    },
    //计算用量
    sumQuantity(row) {
      if (!row.quantity){
        return 0;
      }
      let number = row.quantity * this.handleQuantity;
      row.orderUsage = number;
      return number;
    },
    //清空选择
    emptyCountry() {
      this.handleSelection([])
      this.$refs.applytable.clearSelection();
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //
    handleSelection(val) {
      this.selectNum = val.length
      this.determineModelList = val
    },
    //选择的商品
    handleRowClick(row) {
      //根据选择的商品id，查询bom单，获取物料列表
      if (row.quantity){
        this.handleQuantity = row.quantity
      }
      let productId;

      if (this.addMaterialType === 'sale'){
        productId = row.productId;
      }else if (this.addMaterialType === 'bom'){
        productId = row.id;
      }
      const params ={
        productId: productId,
        page: this.materialSearch.currentPage,
        limit: this.materialSearch.pagesize
      }
      const formData = new URLSearchParams();
      for (const key in params) {
        formData.append(key, params[key]);
      }
      getBomList(formData).then(res =>{
        if (res.data.code === 100){
          this.materialList = res.data.data
          this.materialSearch.total = res.data.total
          if (this.materialList.length === 0){
            const newRow = {...row}
            newRow.productId = productId
            newRow.code = row.productCode
            newRow.name = row.productName
            newRow.quantity = 1
            this.materialList.push(newRow);
            this.materialSearch.total = 1
          }
        }else {
          this.$DonMessage.error(res.data.msg || '获取BOM数据失败')
        }
      })
    },
    // 搜索
    handleSearch() {
      this.searchProductLit();
    },
    //查询物料列表
    getMaterialList(){

    },
    //根据不同的添加类型获取商品列表
    getProductListByType() {
      if (this.addMaterialType === 'sale'){
        getProductListBySaleNo({salesNo: this.salesNo}).then(res => {
          if (res.data.code === 100){
            this.productList = res.data.data
          }else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      } else if (this.addMaterialType === 'bom'){
        //按bom添加，获取产品列表
        this.searchProductLit();
      }
    },
    searchProductLit() {
      getProductList(this.searchForm).then(res => {
        if (res.data.code === 100) {
          this.productList = res.data.data.map(item => {
            return {
              id: item.id,
              productName: item.name,
              productCode: item.code,
              customerProCode: item.customerProCode,
              customerProName: item.customerProName,
              currentInventory: item.currentInventory
            }
          })
          this.searchForm.total = res.data.total
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
  },
  mounted() {
    this.getProductListByType();
  },
}
</script>

<style scoped>
.appOrderDetail {
  height: 680px;
  width: 100%;
}
.appOrderDetail .dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.appOrderDetail .infoDetail {
  height: 550px;
}
</style>
