<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :model="queryParams" :label-width="$labelSix">
        <!-- 申请日期 -->
        <el-form-item label="单据日期">
          <DateRangeSelector ref="dateRangeSelector" @change="handleDateRangeSelect"/>
        </el-form-item>
        <!-- 采购申请单号 -->
        <el-form-item label="采购申请单号" prop="applyNo">
          <el-input
            v-model="queryParams.applyNo"
            placeholder="请输入采购申请单号"
            clearable
          />
        </el-form-item>

        <!-- 状态 -->
        <el-form-item label="申请单状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option v-for="item in auditStatusDicts" :value="item.code" :key="item.code" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请部门" prop="applyDept">
          <select-tree ref="modelSelectTree" :options="departmentList" v-model.trim="queryParams.applyDept"
                       :props="defaultProps" :expand_on_click_node="true" :check_on_click_node="false"
                       @getCurrentNode="getCurrentNode" placeholder="请选择部门"/>
        </el-form-item>
        <el-form-item label="采购订单号" prop="purchaseNo" v-if="isShow">
          <el-input
            v-model="queryParams.purchaseNo"
            placeholder="请输入采购订单号"
            clearable
          />
        </el-form-item>
        <el-form-item label="申请人" prop="applyUser" v-if="isShow">
          <el-select v-model.trim="queryParams.applyUser" clearable filterable>
            <el-option v-for="(item, index) of purchasePersonList" :key="index" :label="item.realName"
                       :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- 销售订单号 -->
        <el-form-item label="销售订单号" prop="salesOrderNo"  v-if="isShow">
          <el-input
            v-model="queryParams.salesOrderNo"
            placeholder="请输入销售订单号"
            clearable
          />
        </el-form-item>
        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">{{ $t('button.search') }}</el-button>
          <el-button plain @click="handleReset">{{ $t('button.reset') }}</el-button>
          <el-button type="text" @click="isShow = true" v-if="!isShow">更多<i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-button type="text" @click="isShow = false" v-if="isShow">收起<i
            class="el-icon-arrow-up el-icon--right"></i></el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <div>
          <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
          <el-button type="text" icon="bulkImport-icon" @click="batchImport()">批量导入</el-button>
          <el-dropdown @command="auditClick">
            <span>
              <i class="process-icon"></i>
              审核
              <i class="el-icon-arrow-down el-icon--right"></i>
           </span>
            <el-dropdown-menu slot="dropdown">
              <div>
                <el-dropdown-item command="reviewPass">审核通过</el-dropdown-item>
                <el-dropdown-item command="reviewReject">审核驳回</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="text" icon="bulkDown-icon" @click="generateOrderClick()">生成采购订单</el-button>
          <el-button type="text" icon="bulkDown-icon" @click="exportClick()">导出</el-button>
          <el-button type="text" icon="disable-icon" @click="auditClick('abrogate')">作废</el-button>
          <el-button type="text" icon="deleteRed-icon" @click="delClick()">删除</el-button>
        </div>
        <!--        <div>-->
        <!--          <el-button type="text" icon="el-icon-setting">自定义列</el-button>-->
        <!--          <el-button type="text" icon="el-icon-refresh">刷新</el-button>-->
        <!--        </div>-->
      </div>
      <el-table style="width:100%"
                border
                stripe
                ref="table"
                highlight-current-row
                :max-height="maximumHeight"
                :data="resultList"
                @header-dragend="changeColWidth"
                @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="applyTime" label="单据日期" width="150">
          <template #default="scope">
            {{ conversion(scope.row.applyTime, 'yyyy-MM-dd') }}
          </template>
        </el-table-column>
        <el-table-column prop="applyNo" label="采购申请单号" width="150">
          <template #default="{row}">
            <span class="linkStyle" @click="handleDetail(row)">{{row.applyNo}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <div :class="getStatusClass(scope.row.status)">
              {{ formatAuditStatus(scope.row.status) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="usages" label="用途" width="120"/>
        <el-table-column prop="salesOrderNo" label="销售订单号" width="160">
          <template #default="{row}">
            <span class="linkStyle" @click="handleSalesDetail(row)">{{ row.salesOrderNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="customerOrderNo" label="客户订单号" width="150"></el-table-column>
        <el-table-column prop="applyUserName" label="申请人" width="100"/>
        <el-table-column prop="applyDeptName" label="申请部门" width="120"/>
        <el-table-column prop="purchaseOrderStatus" label="采购订单" width="140">
          <template slot-scope="scope">
            <div :class="scope.row.purchaseOrderStatus === 1 ? 'successColor' : 'errorColor' ">
              {{ scope.row.purchaseOrderStatus === 1 ? '已生成' : '未生成' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="auditUserName" label="审核人" width="100"/>
        <el-table-column prop="auditTime" label="审核时间" width="160">
          <template slot-scope="scope">
            {{ conversion(scope.row.auditTime, 'yyyy-MM-dd HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注"/>
        <el-table-column prop="creditsUserName" label="制单人" width="100"/>
        <el-table-column prop="creditsTime" label="制单时间" width="160">
          <template slot-scope="scope">
            {{ conversion(scope.row.creditsTime, 'yyyy-MM-dd HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
            <el-button v-if="(row.status === 0 || row.status === 3)" type="text" size="small" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 底部分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.pageSize"
                  @pagination="handleSearch"/>

    </div>

    <!--  反审核组件-->
    <el-dialog
      title="审核驳回"
      :visible.sync="dialogVisibleReject"
      width="800px !important"
      @close="handleSearch"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="采购申请单号">
          <el-input v-model="form.applyNo" disabled></el-input>
        </el-form-item>

        <el-form-item label="驳回原因" prop="rejectReason">
          <el-input
            type="textarea"
            v-model="form.rejectReason"
            placeholder="请输入驳回原因"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="附件">
          <FileUpload :drag="true" upload-flag="purchase" v-model="form.accessoryList"></FileUpload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleReject">提交</el-button>
        <el-button plain @click="closeRejectDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination/index.vue";
import {conversion} from "@/store/filters";
import DateRangeSelector from "@/components/DateRange/DateRangeSelector.vue";
import {
  deleteApplyOrderById,
  generatePurchaseOrder,
  getPurchasePersonnel,
  purchaseApplyAudit,
  purchaseApplyList
} from "@/api/purchasemgt";
import {addTabs, tableHeight} from "@/assets/js/common";
import {dictTypeQueryData, findDeptAll} from "@/api/sysmgt";
import router from "@/router";
import {getStatusClass} from "@/assets/js/utils";
import SelectTree from "@/components/TreeView/SelectTree.vue";
import {getSalesOrderId} from "@/api/salesmgt";
import { importAttach } from '@/api/sysmgt';
import FileUpload from "@/components/FileUpload/FileUpload.vue";

export default {
  name: "list",
  components: {FileUpload, SelectTree, DateRangeSelector, Pagination},
  data() {
    return {
      rules: {
        rejectReason: [
          {required: true, message: '请输入驳回原因', trigger: 'blur'}
        ]
      },
      form: {
        id: '',
        applyNo: '',
        rejectReason: '',
        accessoryList: []
      },
      dialogVisibleReject: false,
      defaultProps: {
        parent: 'path',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          return val.children && val.children.length > 0;
        }
      },
      purchasePersonList: [],
      departmentList: [],
      auditStatusDicts: [],
      selectedRows: [],
      deleteList: [],
      maximumHeight: 0,
      total: 0,
      isShow: false,
      resultList: [],
      queryParams: {
        purchaseNo: '',
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        applyNo: '',
        salesOrderNo: '',
        status: null,
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10
      },
    }
  },
  watch: {
    /**
     * 监听驳回对话框显示状态的变化
     * @param {Boolean} newVal - 新的显示状态
     * @param {Boolean} oldVal - 旧的显示状态
     */
    dialogVisibleReject(newVal, oldVal) {
      if (newVal) {
        console.log('驳回对话框已打开');
        // 对话框打开时的逻辑
        // 例如：重置表单、初始化数据等
        // this.initRejectForm();
      } else {
        console.log('驳回对话框已关闭');
        // 对话框关闭时的逻辑 重置驳回数据
        this.form = {
          id: '',
          applyNo: '',
          rejectReason: '',
          accessoryList: []
        }
      }
    }
  },
  methods: {
    getStatusClass,
    //转换状态显示
    formatAuditStatus(status) {
      const match = this.auditStatusDicts.find(item => item.code == status);
      return match ? match.name : '';
    },
    //新增
    addClick() {
      const title = "新增采购申请单";
      this.$router.push({
        name: 'addPurchaseApply',
        params: {
          id: 'add'
        }
      });
      addTabs(this.$route.path, title);
    },
    //批量导入
    batchImport() {

    },
    //多类型审核
    auditClick(type) {
      let status = 0;
      switch (type) {
        case 'reviewPass':
          status = 2;
          if (this.selectedRows.length === 0) {
            this.$DonMessage.warning("请选择要审核的申请单")
            return;
          }
          break;
        case 'reviewReject':
          status = 3;
          if (this.selectedRows.length === 0) {
            this.$DonMessage.warning("请选择要审核的申请单")
            return;
          }
          break;
        case 'abrogate':
          status = 4;
          if (this.selectedRows.length === 0) {
            this.$DonMessage.warning("请选择要作废的申请单")
            return;
          }
          break;
      }

      if (status === 2 || status === 3) {
        if (this.selectedRows.length > 1) {
          this.$DonMessage.warning("一次只能选择一个申请单")
          return;
        }
      }
      this.selectedRows.map(item => {
        if (status === 3) {
          this.form.id = item.id
          this.form.applyNo = item.applyNo
          this.dialogVisibleReject = true;
          return;
        }
        const params = {
          id: item.id,
          status: status
        }
        purchaseApplyAudit(params).then(res => {
          if (res.data.code === 100) {
            if (status === 2){
              this.$DonMessage.success("审核通过成功")
            }else if (status === 3){
              this.$DonMessage.success("审核驳回成功")
            }else if (status === 4){
              this.$DonMessage.success("作废成功")
            }
            this.handleSearch();
          } else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },

    /**
     * 处理审核驳回提交
     */
    handleReject() {
      const params = {
        id: this.form.id,
        status: 3,
        rejectReason: this.form.rejectReason,
        accessoryList: this.form.accessoryList
      }
      purchaseApplyAudit(params).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success("反审核成功")
          this.dialogVisibleReject = false
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },

     /**
      * 关闭驳回对话框
      */
     closeRejectDialog() {
       this.dialogVisibleReject = false;
     },

     //生成采购订单
    generateOrderClick() {
      //给当前选中的采购申请单生成采购订单
      if (this.selectedRows.length === 0){
        this.$DonMessage.warning("请选择要生成的采购申请单")
        return;
      }
      if (this.selectedRows.length > 1){
        this.$DonMessage.warning("只能选择一个采购申请单")
        return;
      }
      this.selectedRows.map(item => {
        generatePurchaseOrder(item.id).then(res => {
          if (res.data.code === 100){
            this.$DonMessage.success("生成采购订单成功")
            this.handleSearch()
          }else {
            this.$DonMessage.error(res.data.msg)
          }
        })
      })
    },
    //删除
    delClick() {
      if (this.selectedRows.length === 0){
        this.$DonMessage.warning("请选择要删除的申请单")
        return;
      }

      this.$confirm('确定删除【' + this.selectedRows.length + '】条数据吗?', '删除申请单数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectedRows.map(item => {
          deleteApplyOrderById(item.id).then(res =>{
            if (res.data.code === 100){
              this.$DonMessage.success(res.data.msg)
              this.handleSearch();
            }else {
              this.$DonMessage.error(item.applyNo + '-删除失败:' +res.data.msg)
            }
          })
        })
      })
    },
    //导出
    exportClick() {

    },
    //跳转到销售订详情页
    handleSalesDetail(row){
      const title = row.salesOrderNo;
      const params = {
        salesNo: row.salesOrderNo
      }
      getSalesOrderId(params).then(res =>{
        if (res.data.code === 100){
          this.$router.push({ name: 'salesOrderDetail', params: { id: res.data.data }});
          addTabs(this.$route.path, title);
        }else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //详情
    handleDetail(row) {
      const title = row.applyNo
      this.$router.push({
        name: 'purchaseApplyOrderDetail',
        params: {
          id: row.id,
          type: 'detail',
        }
      });
      addTabs(this.$route.path, title)
    },
    //编辑
    handleEdit(row) {
      const title = "编辑 " + row.applyNo
      this.$router.push({
        name: 'addPurchaseApply',
        params: {
          id: row.id,
        }
      });
      addTabs(this.$route.path, title)
    },
    // 采购订单表格高度
    maximumArea() {
      const loadHeight = async () => {
        var result = await tableHeight();
        this.maximumHeight = result;
      }
      loadHeight()
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    //时间格式化方法
    conversion,
    // 所属部门
    getDepartment() {
      let _this = this;
      _this.departmentMap = new Map();
      findDeptAll().then(res => {
        if (res.data.data) {
          _this.departmentList = res.data.data
          _this.getDepartmentMap(_this.departmentList, "");
        } else {
          _this.departmentList = []
        }
      })
    },
    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['form'].validateField('applyDept')
      }
    },
    //查询
    handleSearch() {
      const params = {
        purchaseNo: this.queryParams.purchaseNo,
        applyNo: this.queryParams.applyNo,
        salesOrderNo: this.queryParams.salesOrderNo,
        status: this.queryParams.status,
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate,
        customerOrderNo: this.queryParams.customerOrderNo,
        applyUser: this.queryParams.applyUser,
        applyDept: this.queryParams.applyDept,
        page: this.queryParams.page,
        pageSize: this.queryParams.pageSize
      }
      // 发起请求查询接口
      this.dataList(params)
    },
    //  处理选中的行
    handleSelectionChange(val) {
      this.selectedRows = val; // val 是一个数组，包含所有选中行
    },
    //重置查询条件
    handleReset() {
      this.queryParams = {
        dateRange: [], // [startDate, endDate]
        startDate: '',
        endDate: '',
        applyNo: '',
        salesOrderNo: '',
        status: null,
        customerOrderNo: '',
        applyUser: '',
        applyDept: '',
        page: 1,
        pageSize: 10
      }
      // 使用 nextTick 重置部门选择组件
      this.$nextTick(() => {
        if (this.$refs.modelSelectTree) {
          this.$refs.modelSelectTree.labelModel = ''
          this.$refs.modelSelectTree.valueModel = ''
          this.$refs.modelSelectTree.sltCheckedId = ''
          this.$refs.modelSelectTree.$refs.tree.setCurrentKey(null)
          this.$refs.modelSelectTree.$refs.tree.setCheckedKeys([])
        }
      })
      //重置日期选择组件
      if (this.$refs.dateRangeSelector) {
        this.$refs.dateRangeSelector.reset()
      }
      this.handleSearch()
    },
    //分页查询数据
    dataList(params) {
      purchaseApplyList(params).then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data;
          this.total = res.data.total;
          this.tableHeightArea()
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 获取日期范围选择
    handleDateRangeSelect(range) {
      this.queryParams.startDate = range.startDate;
      this.queryParams.endDate = range.endDate;
      this.handleSearch();
    },
    // 采购人员
    getPurchasePersonList() {
      getPurchasePersonnel().then(res => {
        if (res.data.code === 100) {
          this.purchasePersonList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    // 获取审核状态数据字典
    getAuditStatusDict() {
      const queryParams = 'auditStatusType';
      dictTypeQueryData(queryParams).then(res => {
        if (res.data.code === 100) {
          this.auditStatusDicts = res.data.data.map(item => {
            return {
              name: item.name,
              code: item.code
            }
          });
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    }
  },
    mounted() {
      this.handleSearch();
      //获取审核状态数据字典
      this.getAuditStatusDict();
      this.getDepartment();
      this.getPurchasePersonList();
    },
}
</script>

<style scoped>
/* 图片预览容器样式 */
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 20px;
}

/* 预览图片样式 */
.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  cursor: zoom-in;
  transition: transform 0.3s ease;
}

.preview-image:hover {
  transform: scale(1.02);
}

/* 预览弹窗样式调整 */
.el-dialog__wrapper .el-dialog {
  margin-top: 5vh !important;
}

.el-dialog__header {
  text-align: center;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
}

.el-dialog__title {
  font-weight: 600;
  color: #303133;
}
</style>
