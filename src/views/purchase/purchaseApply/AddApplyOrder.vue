<template>
  <div class="layoutContainer actionFlowDetail">
    <div class="elTabtitle">
      <div v-if="this.stateType === 'add'">新增采购申请单</div>
      <div v-if="this.stateType === 'edit'">编辑采购申请单</div>
      <div>
        <el-button type="primary" @click="onSubmit('save')" v-if="this.stateType === 'add'">保存</el-button>
        <el-button plain @click="onSubmit('submit')" v-if="this.stateType === 'add'">提交</el-button>
        <el-button plain @click="onSubmit('audit')" v-if="this.stateType === 'add'">提交并审核</el-button>
        <el-button type="primary" @click="onEdit('save')" v-if="this.stateType === 'edit'">保存</el-button>
        <el-button plain @click="onEdit('submit')" v-if="this.stateType === 'edit'">提交</el-button>
        <el-button plain @click="onEdit('audit')" v-if="this.stateType === 'edit'">提交并审核</el-button>
        <el-button plain @click="onCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </div>
    <div class="elTabsContent">
      <div class="collapseContent">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="base">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                基本信息
              </span>
            </template>
            <div class="secondFloat">
              <el-form :inline="true" :model="form" :rules="rules" ref="form" :label-width="$labelFive">
                <el-form-item label="采购申请单号" prop="applyNo">
                  <el-input v-model="form.applyNo" placeholder="采购申请单号由系统生成" disabled />
                </el-form-item>
                <el-form-item label="单据日期" prop="applyTime">
                  <el-date-picker v-model="form.applyTime" placeholder="请选择申请日期" disabled></el-date-picker>
                </el-form-item>
                <el-form-item label="用途" prop="usages">
                  <el-input v-model="form.usages" placeholder="请输入用途" />
                </el-form-item>
                <el-form-item label="销售订单号" prop="salesOrderNo">
                  <SelectSalesOrder ref="selectInput" v-model="form.salesOrderNo" :input-param="form.salesOrderNo"
                    input-type="salesOrder" placeholder="请选择销售订单" @select="handleSelectSalesOrder"></SelectSalesOrder>
                </el-form-item>
                <el-form-item label="申请人" prop="applyUser">
                  <el-select v-model="form.applyUser" clearable filterable>
                    <el-option v-for="(item, index) of purchasePersonList" :key="index" :label="item.realName"
                      :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="申请部门" prop="applyDept">
                  <select-tree ref="modelSelectTree" :options="departmentList" v-model.trim="form.applyDept"
                    :props="defaultProps" :expand_on_click_node="true" :check_on_click_node="false"
                    @getCurrentNode="getCurrentNode" placeholder="请选择部门" />
                </el-form-item>
                <el-row>
                  <el-col :span="14">
                    <el-form-item label="备注" prop="remark" class="inlineTextArea">
                      <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" maxlength="500"
                        show-word-limit />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-collapse-item>
          <el-collapse-item name="product">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                物料信息
              </span>
            </template>
            <div class="tableHandle spaceBbetwee">
              <el-dropdown @command="addMaterial" trigger="click">
                <span>
                  <el-button><i class="addProducts-icon"></i>添加物料<i
                      class="el-icon-arrow-down el-icon--right"></i></el-button>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <div>
                    <el-dropdown-item command="sale">按销售订单添加</el-dropdown-item>
                    <el-dropdown-item command="bom">按BOM添加</el-dropdown-item>
                    <el-dropdown-item command="material">按物料添加</el-dropdown-item>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
              <div>
                <el-button type="text" icon="import-icon">批量导入</el-button>
                <el-button type="text" icon="setIcon-icon">批量设置</el-button>
                <el-button type="text" icon="deleteRed-icon">批量删除</el-button>
              </div>
            </div>
            <el-table style="width: 100%;" ref="table" :data="form.purchaseApplyDetailVOS" border stripe
              highlight-current-row @header-dragend="changeColWidth" show-summary :summary-method="getSummaries">
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" width="50" type="index"></el-table-column>
              <el-table-column prop="code" label="物料编码" min-width="140" />
              <el-table-column prop="name" label="物料名称" min-width="120" />
              <el-table-column prop="imageUrl" label="图片" width="100">
                <template slot-scope="{ row }">
                  <img v-if="row.imageUrl !== ''" class="pictureShow" :src="$filePath + row.imageUrl" alt=""
                    @click="previewImage(row.imageUrl, row.name || '产品图片')" style="cursor: pointer;" />
                </template>
              </el-table-column>
              <el-table-column prop="specifications" label="规格" width="120" />
              <el-table-column prop="model" label="型号" width="120" />
              <el-table-column prop="brand" label="品牌" width="100" />
              <el-table-column prop="colour" label="颜色" width="80" />
              <el-table-column prop="requiredTime" label="需求日期" width="150">
                <template #header>
                  <span class="required-field">需求日期</span>
                </template>
                <template #default="scope">
                  <div class="rowEditShow">
                    <el-date-picker v-model="scope.row.requiredTime" type="date" placeholder=""
                      value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="采购数量" width="80">
                <template #header>
                  <span class="required-field">数量</span>
                </template>
                <template #default="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.quantity" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="重量(kg)" prop="weight">
                <template #default="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.weight" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="currentInventory" label="当前库存" width="100" />
              <el-table-column prop="lockQty" label="锁定库存" width="100" />
              <el-table-column prop="unit" label="单位" width="80">
                <!--                <template #header>-->
                <!--                  <span class="required-field">单位</span>-->
                <!--                </template>-->
                <!--                <template slot-scope="scope">-->
                <!--                  <div class="rowEditShow">-->
                <!--                    <el-select-->
                <!--                      ref="tableRowInputRef"-->
                <!--                      v-model="scope.row.unit"-->
                <!--                      @blur="onInputTableBlur(scope)"-->
                <!--                    >-->
                <!--                      <el-option v-for="(item, index) of unitData" :key="index" :label="item.name"-->
                <!--                                 :value="item.code"></el-option>-->
                <!--                    </el-select>-->
                <!--                  </div>-->
                <!--                </template>-->
              </el-table-column>
              <el-table-column prop="supplierId" label="供应商" width="160">
                <template #header>
                  <span class="required-field">供应商</span>
                </template>
                <template slot-scope="scope">
                  <div>
                    <el-select placeholder="" size="small" ref="tableRowInputRef" v-model="scope.row.supplierId"
                      @focus="loadSupplierList(scope.row)" @blur="onInputTableBlur(scope)"
                      @change="onSupplierChange(scope.row)">
                      <el-option v-for="(item) of scope.row.productSupplierList" :key="item.supplierId"
                        :label="item.supplierName" :value="item.supplierId"></el-option>
                    </el-select>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" label="单价(￥)" width="100">
                <template #header>
                  <span class="required-field">单价(￥)</span>
                </template>
                <template #default="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.unitPrice" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="金额(￥)" width="100">
                <template slot-scope="scope">
                  {{ scope.row.amount = ((Number(scope.row.unitPrice) || 0) * (Number(scope.row.quantity) || 0)).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="contactPhone" label="联系电话" width="130">
                <template slot-scope="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.contactPhone"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="referLink" label="参考链接" width="160">
                <template slot-scope="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.referLink"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" min-width="180">
                <template slot-scope="scope">
                  <div class="rowEditShow">
                    <el-input v-model="scope.row.remark"></el-input>
                  </div>
                </template>
                <!--                <template slot-scope="scope">-->
                <!--                  <div class="rowEditShow">-->
                <!--                    <el-input-->
                <!--                      v-if="-->
                <!--                      rowEditIndex == scope.$index &&-->
                <!--                      colimnEditIndex == scope.column.id"-->
                <!--                      ref="tableRowInputRef"-->
                <!--                      v-model="scope.row.remark"-->
                <!--                      @blur="onInputTableBlur(scope)"-->
                <!--                    >-->
                <!--                    </el-input>-->
                <!--                    <div-->
                <!--                      v-else-->
                <!--                      :class="'textShow ' + scope.$index + '_9 ' + scope.row.id + ' remark'"-->
                <!--                      @dblclick="dbClickCell(scope)"-->
                <!--                    >-->
                <!--                      <span>{{ scope.row.remark }}</span>-->
                <!--                    </div>-->
                <!--                  </div>-->
                <!--                </template>-->
              </el-table-column>
              <el-table-column label="操作" width="80" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" class="deleteButton"
                    @click="delClick(scope.$index, form.purchaseApplyDetailVOS)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item name="more">
            <template #title>
              <span>
                <i class="el-icon-arrow-right"></i>
                更多信息
              </span>
            </template>
            <el-descriptions :column="dynamicColumn">
              <el-descriptions-item v-if="this.stateType === 'edit'" label="制单人">
                {{ form.creditsUserName }}
              </el-descriptions-item>
              <el-descriptions-item v-if="this.stateType === 'edit'" label="制单日期">
                {{ form.creditsTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <el-descriptions-item v-if="this.stateType === 'add'" label="制单人">
                {{ form.createdUserName }}
              </el-descriptions-item>
              <el-descriptions-item v-if="this.stateType === 'add'" label="制单日期">
                {{ form.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </el-descriptions-item>
              <!--              <el-descriptions-item label="修改人" v-if="this.stateType === 'edit'">-->
              <!--                {{ form.updatedUserName }}-->
              <!--              </el-descriptions-item>-->
              <!--              <el-descriptions-item label="修改日期" v-if="this.stateType === 'edit'">-->
              <!--                {{ form.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}-->
              <!--              </el-descriptions-item>-->
              <el-descriptions-item label="附件" :span="2">
                <FileUpload :drag="false" v-model="form.accessoryList" :list-type="'text'"></FileUpload>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 图片预览组件 -->
    <ImagePreview
      :visible.sync="previewVisible"
      :image-url="previewImageUrl"
      :image-name="previewImageName"
    />

    <!-- 按bom和销售订单添加 -->
    <el-dialog v-if="dialogVisibleAddDetail" :visible.sync="dialogVisibleAddDetail" :title="addDetailTitle"
               width="1800px !important">
      <dialogTabular :type="addMaterialType === 'bom' ? 'bomProduct' : 'saleProduct'" :paramId="form.salesOrderNo"
                     :formList.sync="form.purchaseApplyDetailVOS" :dialogStatue.sync="dialogVisibleAddDetail"
                     :columns="productColumns"></dialogTabular>
    </el-dialog>
    <!-- <el-dialog v-if="dialogVisibleAddDetail"
               :visible.sync="dialogVisibleAddDetail"
               title="添加物料"
               width="1400px !important"
               :destroy-on-close="true"
               @close="onAddDetailDialogClose">
      <AddApplyOrderDetail :addMaterialType="addMaterialType" :sales-no="this.form.salesOrderNo"
                           @select="handleSelectProducts"></AddApplyOrderDetail>
    </el-dialog> -->
    <!-- 按物料添加 -->
    <dialogTable v-if="isReload" :isReload.sync="isReload" type="material" :formList.sync="form.purchaseApplyDetailVOS"
      :columns.sync="materialColumns">
      <template #imageUrl="scope">
        <img v-if="scope.row.imageUrl !== ''" class="pictureShow" :src="$filePath + scope.row.imageUrl" alt="">
      </template>
    </dialogTable>
  </div>
</template>

<script>
import SelectSalesOrder from "@/views/purchase/purchaseApply/SelectSalesOrder.vue";
import {conversion} from "../../../store/filters";
import {getContactsBySupplierId, getSupplierByProductId, getUnitList} from "@/api/basicmgt";
import {
  addPurchaseApply,
  getPurchasePersonnel,
  purchaseApplyEdit,
  purchaseApplyInfo,
  queryProductsOrderDetail
} from "@/api/purchasemgt";
import {findDeptAll} from "@/api/sysmgt";
import SelectTree from "@/components/TreeView/SelectTree.vue";
import {beforeRouteInfo, collapseArea, getContentData, removeTabs} from "@/assets/js/common";
import dialogTable from "@/components/dialogTable/dialogTable.vue";
import dialogTabular from "@/components/dialogTable/dialogTabular.vue";
import {
  customerColumns,
  inventoryColumns,
  materialCopyColumns,
  orderColumns,
  productColumns
} from "@/assets/js/tableHeader";
import FileUpload from "@/components/FileUpload/FileUpload.vue";
import ImagePreview from "@/components/ImagePreview/ImagePreview.vue";
import { getColumnNumber } from "@/assets/js/heightResize";

export default {
  name: "AddApplyOrder",
  components: {ImagePreview, FileUpload, SelectTree, SelectSalesOrder, dialogTable, dialogTabular},
  data() {
    return {
      previewVisible: false,
      previewImageUrl: '',
      previewImageName: '',
      dynamicColumn: getColumnNumber(this),
      addDetailTitle: "",
      materialColumns: materialCopyColumns,
      productColumns: [],
      productUsageList:[],
      productSupplierList: [], //选择的物料的供应商列表
      departmentList: [],
      defaultProps: {
        parent: 'path',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          return val.children && val.children.length > 0;
        }
      },
      selectProductIds: [], //选择的商品id信息
      addMaterialType: "",
      dialogVisibleAddDetail: false, //添加物料dialog框
      productId: "", //id
      stateType: "", //类型
      applyOrderId: "", //申请单id
      purchasePersonList: [],
      // 基本信息
      form: {
        applyNo: '',                     // 采购申请单号
        applyTime: '',                   // 申请日期
        purchaseTime: '',                // 采购日期
        usages: '',                      // 用途
        salesOrderNo: '',                // 销售订单号
        customerOrderNo: '',             // 客户订单编号
        applyUser: null,                 // 申请人（用户ID）
        applyDept: '',                   // 申请部门
        creditsUser: null,              // 制单人（用户ID）
        creditsTime: '',                 // 制单时间
        status: null,                    // 申请单状态（建议绑定下拉枚举）
        remark: '',                      // 备注
        purchaseApplyDetailVOS: [],
        accessoryList: []
      },
      rules: {
        // applyTime: [{required: true, message: '申请日期不能为空', trigger: ['blur', 'change']}],
        usages: [{required: true, message: '用途不能为空', trigger: ['blur', 'change']}],
        applyUser: [{required: true, message: '申请人不能为空', trigger: ['blur', 'change']}],
        applyDept: [{required: true, message: '申请部门不能为空', trigger: ['blur', 'change']}],
      },
      unitData: [], //计量单位
      detailInfo: {}, // 商品信息
      tableData: [], // 商品特性 供应商
      rowEditIndex: "",
      colimnEditIndex: "",
      accessoryList: [], // 图片信息
      activeNames: ["base", "product", "more"], // 全部展开
      // 弹框
      dialogFormVisible: false, //
      enableState: "", //
      // 添加商品
      isReload: false,
      // 进度条
      progressFlag: false,
      percentage: 0,
    }
  },
  // 合计
  // 在 computed 中添加
  computed: {
    /**
     * 计算总数量
     */
    totalQuantity() {
      return this.form.purchaseApplyDetailVOS.reduce((total, item) => {
        return total + (Number(item.quantity) || 0);
      }, 0);
    },
    /**
     * 计算总金额
     */
    totalAmount() {
      return this.form.purchaseApplyDetailVOS.reduce((total, item) => {
        const amount = (Number(item.unitPrice) || 0) * (Number(item.quantity) || 0);
        return total + amount;
      }, 0).toFixed(2);
    }
  },
  methods: {
    /**
     * 预览图片
     */
    previewImage(imageUrl, imageName = '产品图片') {
      this.previewImageUrl = this.$filePath + imageUrl;
      this.previewImageName = imageName + '.' + imageUrl.substring(imageUrl.lastIndexOf('.') + 1);
      this.previewVisible = true;
    },

    //选择供应商后
    async onSupplierChange(row) {
      const selectedSupplier = row.productSupplierList.find(item => item.supplierId === row.supplierId);
      if (selectedSupplier) {
        //根据供应商id去查询联系人电话
        const res = await getContactsBySupplierId(selectedSupplier.supplierId);
          if (res.data.code === 100 && res.data.data.length > 0) {
            //TODO 暂时取第一条联系人的信息
            row.contactPhone = res.data.data[0].mobile
            row.contactsId = res.data.data[0].id
            row.contactsName = res.data.data[0].name
          }else{
            this.$DonMessage.warning("未找到供应商信息")
          }
        //参考链接
        row.referLink = selectedSupplier.referLink
        row.unitPrice = selectedSupplier.price
      } else {
        console.warn("未找到供应商信息")
      }
    },
    //搜索供应商信息
    loadSupplierList(row) {
      // console.log("loadSupplierList", row)
      if (!row.productId) {
        this.$DonMessage.warning("请先选择产品");
        return;
      }
      // 如果已经加载过就不重复查
      // if (row.supplierList && row.supplierList.length > 0) return;

      getSupplierByProductId(row.productId).then(res => {
        if (res.data.code === 100) {
          this.$set(row, 'productSupplierList', res.data.data || []);
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //删除物料列表
    delClick(index, list) {
      this.$confirm('确定删除此条数据吗?', '删除物料', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (!Array.isArray(list)) return;
        list.splice(index, 1);
      })
    },
    // 采购人员
    getPurchasePersonList() {
      getPurchasePersonnel().then(res => {
        if (res.data.code === 100) {
          this.purchasePersonList = res.data.data
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['form'].validateField('applyDept')
      }
    },
    // 所属部门
    getDepartment() {
      let _this = this;
      _this.departmentMap = new Map();
      findDeptAll().then(res => {
        if (res.data.data) {
          _this.departmentList = res.data.data
          _this.getDepartmentMap(_this.departmentList, "");
        } else {
          _this.departmentList = []
        }
      })
    },
    getDepartmentMap(list, name) {
      if (!list || list.length <= 0) {
        return;
      }
      if (name) {
        name = name + " > ";
      }
      // deptId
      for (let i = 0; i < list.length; i++) {
        let deptName = name + list[i].name;
        this.departmentMap.set(list[i].id, deptName);
        this.getDepartmentMap(list[i].children, deptName)
      }

    },
    //处理按物料选择的商品
    handleSelectByMaterial(productIds) {
      //处理选择的物料，根据productId查询信息
      this.selectProductIds = productIds;
      //初始化采购数量
      this.selectProductIds.map(item => {
        const productUsage = {
          productId: item,
          orderUsage: 1,
        }
        this.productUsageList.push(productUsage)
      })
      this.selectProductsInfo(productIds);
    },
    //处理按bom和销售订单选择的物料，根据productId查询信息
    handleSelectProducts(productList) {
      this.productUsageList = productList;
      this.selectProductIds = productList.map(item => item.productId);
      this.selectProductsInfo(this.selectProductIds);
    },
    //根据productId查询物料详细信息
    selectProductsInfo(productIds) {
      const params = {
        productIds: productIds
      }
      queryProductsOrderDetail(params).then(res => {
        if (res.data.code === 100) {
          res.data.data.forEach(item => {
            const findUsage = this.productUsageList.find(productUsage => item.productId === productUsage.productId);
            item.quantity = findUsage.orderUsage
            item.remark = ''
          })
          this.form.purchaseApplyDetailVOS.push(...res.data.data)
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    /**
     * 编辑申请单
     * @param {String} type - 操作类型：save保存，submit提交，audit提交并审核
     */
    onEdit(type) {
      if (type === "save") {
        //修改，不修改状态
      } else if (type === "submit") {
        //修改 - 待审核
        this.form.status = 1;
      }
      else if (type === "audit") {
        //提交并审核 - 审核通过
        this.form.status = 2;
      }
      purchaseApplyEdit(this.form).then(res => {
        if (res.data.code === 100) {
          this.$DonMessage.success("保存成功")
          // if (type !== "save"){
          //   //非保存跳转到列表
          //   removeTabs(this.$route)
          // }
          removeTabs(this.$route)
        } else {
          this.$DonMessage.error(res.data.msg)
        }
      })
    },
    //保存
    onSubmit(type) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 所有校验通过，允许提交
          if (type === "save") {
            //保存 - 草稿
            this.form.status = 0;
          } else if (type === "submit") {
            //提交 - 待审核
            this.form.status = 1;
          }else if (type === "audit"){
            //提交并审核 - 审核通过
            this.form.status = 2;
          }

          //筛选过滤商品信息，不能出现相同商品相同供应商
          let sameProduct = false;
          const map = new Map();
          for (const item of this.form.purchaseApplyDetailVOS) {
            if (!item.productId || !item.supplierId) continue; // 忽略空值
            // const key = `${item.productId}_${item.supplierId}`;
            const key = `${item.productId}`;
            if (map.has(key)) {
              sameProduct = true; // 存在重复
              this.$DonMessage.warning("商品："+ item.name + " 存在相同的采购信息，请检查")
            }
            map.set(key, true);
          }

          if (sameProduct){
            return;
          }
          addPurchaseApply(this.form).then(res => {
            if (res.data.code === 100) {
              this.$DonMessage.success("保存成功")
              this.applyOrderId = res.data.data
                removeTabs(this.$route)
              this.selectApplyOrderInfo(this.applyOrderId);
            } else {
              this.$DonMessage.error(res.data.msg)
            }
          })
        } else {
          // 有校验不通过，阻止提交
          this.$DonMessage.warning("请先完善表单信息");
          return false;
        }
      });
    },
    //取消
    onCancel() {
      removeTabs(this.$route)
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    conversion,
    //双击单元格
    dbClickCell(scope) {
      var _this = this;
      _this.rowEditIndex = scope.$index
      _this.colimnEditIndex = scope.column.id
      _this.$nextTick(() => {
        _this.$refs.tableRowInputRef.focus();
      });
    },
    // 输入框失去焦点
    onInputTableBlur(scope) {
      var _this = this;
      _this.rowEditIndex = "";
      _this.colimnEditIndex = "";
    },
    //按分类添加物料
    addMaterial(type) {
      let newList = [];
      if (type === "sale") {
        if (!this.form.salesOrderNo) {
          this.$DonMessage.warning("请先选择销售订单！")
          return;
        }
        this.addMaterialType = "sale"
        this.addDetailTitle = '按销售订单添加'
        newList = [
          orderColumns[2],
          inventoryColumns[0]
        ]
        this.productColumns = customerColumns.slice(0, 2).concat(productColumns.slice(0, 2)).concat(newList);
        this.dialogVisibleAddDetail = true
      } else if (type === "bom") {
        this.addMaterialType = "bom"
        this.addDetailTitle = '按BOM添加'
        newList = [
          inventoryColumns[0]
        ]
        this.productColumns = productColumns.slice(0,2).concat(newList);
        this.dialogVisibleAddDetail = true
      } else if (type === "material") {
        this.addMaterialType = "material"
        // 添加商品
        this.isReload = true;
      }
    },
    //选择销售订单
    handleSelectSalesOrder(salesOrder) {
      this.form.salesOrderNo = salesOrder.salesNo
      this.form.customerOrderNo = salesOrder.customerNo
    },
    //关闭添加物料弹窗
    onAddDetailDialogClose() {

    },
    // 获取计量单位
    getUnitData() {
      var param = {}
      getUnitList(param).then((res) => {
        if (res.data.code === 100) {
          this.unitData = res.data.data
        }
      });
    },
    /**
     * 根据申请单id，获取申请单详情
     * @param {String} applyOrderId - 申请单ID
     */
    async selectApplyOrderInfo(applyOrderId) {
      try {
        const res = await purchaseApplyInfo(applyOrderId)
        if (res.data.code === 100) {
          this.form = res.data.data
        } else {
          this.$DonMessage.warning("当前信息不存在");
          this.onCancel()
        }
      } catch {
        this.$DonMessage.warning("当前信息不存在");
        this.onCancel()
      }
    },
    // 修改 getSummaries 方法
    getSummaries(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计:";
          return;
        }
        switch (column.property) {
          case "quantity":
            sums[index] = this.totalQuantity;
            break;
          case "amount":
            sums[index] = this.totalAmount;
            break;
          default:
            sums[index] = '';
            break;
        }
      });
      return sums;
    },
    // areaHeight() {
    //   setTimeout(() => {
    //     var allHeight = $(".layoutContainer").height();
    //     var titleHeight = $(".elTabtitle").height();
    //     var marginVal = 2 * Number($(".elTabsContent .el-collapse-item").css("marginTop").split("px")[0]);
    //     var val = allHeight - titleHeight - marginVal - 5;
    //     $(".elTabsContent").css("height", val);
    //   }, 60);
    // },
    // contentSize() {
    //   var _this = this;
    //   _this.areaHeight();
    //   window.addEventListener("resize", function () {
    //     _this.areaHeight();
    //   })
    // },
    formClear() {
      this.form = {
        applyNo: '',
        applyTime: '',
        purchaseTime: '',
        usages: '',
        salesOrderNo: '',
        customerOrderNo: '',
        applyUser: null,
        applyDept: '',
        creditsUser: null,
        creditsTime: '',
        status: null,
        remark: '',
        purchaseApplyDetailVOS: [],
        accessoryList: []
      },

      this.$nextTick(function () {
        this.$refs.form.clearValidate();
      })
      if (this.$refs['selectInput'] !== undefined) {
        this.$refs['selectInput'].clear()
      }
    },
    onInputSearch(type, $event) {
      if (type === "name") {
        this.form.categoryName = $event.name;
        this.form.categoryId = $event.id;
      } else if (type === "warehouse") {
        this.form.warehouseName = $event.name;
        this.form.warehouseId = $event.id;
      } else if (type === "location") {
        this.form.locationName = $event.name;
        this.form.locationId = $event.id;
      } else if (type === "brand") {
        this.form.brand = $event.name
      }
    },
    async initialState() {
      this.productId = this.$route.params.id;
      //查询单位
      this.getUnitData();
      this.getDepartment();
      this.getPurchasePersonList();
      if (this.productId === "add") {
        await this.formClear()
        this.stateType = "add";
        this.form.createdTime = new Date().getTime();
        this.form.applyTime = new Date().getTime();
        this.form.createdUserName = this.$store.state.realName;
      }else if(this.productId === "salesAdd"){
        // await this.formClear()
        this.form.salesOrderNo = '';
        this.stateType = "add";
        this.form.salesOrderNo = this.$route.params.salesOrderNo
        this.form.customerOrderNo = this.$route.params.customerNo
        this.form.createdTime = new Date().getTime();
        this.form.applyTime = new Date().getTime();
        this.form.createdUserName = this.$store.state.realName;
      } else {
        this.applyOrderId = this.$route.params.id;
        this.stateType = "edit";
        this.form.updatedTime = new Date().getTime();
        this.form.updatedUserName = this.$store.state.realName;
        //编辑时，根据申请单id查询申请单详情
        await this.selectApplyOrderInfo(this.applyOrderId);
      }
      // this.contentSize();
      getContentData(this)
      if (this.productId === "salesAdd"){
        this.form.salesOrderNo = this.$route.params.salesOrderNo
      }
      collapseArea()
    }
  },
  mounted() {
    this.initialState()
  },
  beforeRouteLeave(to, from, next) {
    var _this = this;
    beforeRouteInfo(from.path, _this.form);
    next()
  },
  watch: {
    $route(to, from) {
      console.log(to);
      if (to.name === 'addPurchaseApply') {
        beforeRouteInfo(from.path, this.form);
        this.initialState();
      }
    }
  },
}
</script>

<style scoped>
.more-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗头部样式 */
.el-dialog__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.el-dialog__title {
  font-weight: 600;
  color: #333;
}
</style>
