<template>
  <div class="supplierContainer">
    <div class="dialogSearch">
      <el-input  placeholder="物料编码" v-model="searchForm.code" clearable></el-input>
      <el-input  placeholder="物料名称" v-model="searchForm.name" clearable></el-input>
      <el-checkbox v-model="searchForm.underStock">仅显示库存不足</el-checkbox>
      <el-button  type="primary" @click="handleSearch">搜索</el-button>
    </div>
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span>商品分类</span>
            </div>
            <div class="scrollClass elTreeStyle">
              <!-- <el-scrollbar> -->
              <el-tree :data="productTreeList" node-key="id" :render-content="renderContent"
                       :default-expand-all="true" @node-click="handleCategorySelect" :props="{
                    label: 'name',
                    children: 'children'
                  }" ref="categoryTree"></el-tree>
              <!-- </el-scrollbar> -->
            </div>
          </div>
        </el-col>
        <el-col :span="19">
          <el-row>
            <el-col :span="18" class="nationalInfo">
              <div>
                <el-table
                  :data="productList"
                  :height="tableHeight"
                  style="width: 100%;"
                  ref="table"
                  highlight-current-row
                  border
                  stripe
                  @header-dragend="changeColWidth"
                  @selection-change="handleSelection"
                >
                  <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
<!--                  <el-table-column label="客户货号" prop="customerCode"></el-table-column>-->
<!--                  <el-table-column label="客户货名" prop="customerName"></el-table-column>-->
                  <el-table-column label="物料编码" prop="code"></el-table-column>
                  <el-table-column label="物料编名称" prop="name"></el-table-column>
<!--                  <el-table-column label="图片" prop="imageUrl" width="60">-->
<!--                    <template slot-scope="{row}">-->
<!--                      <img :src="row.imageUrl" alt="" />-->
<!--                    </template>-->
<!--                  </el-table-column>-->

                  <el-table-column label="类别" prop="categoryName"></el-table-column>
                  <el-table-column label="规格型号" prop="model"></el-table-column>
                  <el-table-column label="计量单位" prop="unit"></el-table-column>
<!--                  <el-table-column label="状态">-->
<!--                    <template slot-scope="{row}">-->
<!--                      <span class="successColor" v-if="row.status === 1">生效</span>-->
<!--                      <span class="errorColor" v-if="row.status === 0">失效</span>-->
<!--                    </template>-->
<!--                  </el-table-column>-->
                  <el-table-column label="备注" prop="remark"></el-table-column>
                </el-table>
                <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList" />
              </div>
            </el-col>
            <el-col :span="6" class="nationalSelect">
              <el-table
                style="width:100%"
                :height="maxHeight"
                border
                stripe
                highlight-current-row
                :data="determineModelList"

                :header-cell-style="{}"

              >
                <el-table-column prop="title">
                  <template slot="header">
                    <div class="authorityTitle">
                      <div>
                        <span>已选(<b> {{ selectNum }} </b>)</span>
                      </div>
                      <div>
                        <span class="clearAction" @click="emptyCountry">清空</span>
                      </div>
                    </div>
                  </template>
                  <template slot-scope="scope">
                    <div :class="'nationalList ' + '_' + scope.row.id">
                      <span>{{ scope.row.code }} {{  scope.row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="submitArea">
      <el-button type="primary" @click="supplierSubmit()">{{ $t('button.submit') }}</el-button>
      <el-button plain @click="cancelClick">{{ $t('button.cancel') }}</el-button>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { productCategoryTree, getCategoryTreeData } from "@/views/basic/basicCommon";
import {getCategoryTree, getProductList, getSupplierList} from '@/api/basicmgt'
import { addTabs, renderTree, tableHeight, contextmenuSeat } from "@/assets/js/common";
export default {
  name: "AddMaterialInfo",
  components: { Pagination },
  emits:["select"],
  data() {
    return {
      searchForm: {
        name: '',
        code: '',
        type: '',
        status: '',
        currentPage: 1,
        pagesize: 10,
        total: 0
      },
      productIds: [],
      enableState: false,
      productTreeList: [],
      productList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      tableHeight:0,
      maxHeight:0,
      // 选中的对象
        determineModelList: [],
      // 选中的数量
      selectNum: 0,
      customerId: this.$parent.$parent.form.customerId
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    handleSearch(){
      this.dataList();
    },
    renderContent(h, { node, data }) {
      var dataName = ""
      if (data.pid == 0 && data.children.length == 0) {
        dataName = "noChildIcon"
      }
      renderTree(".commodityManage");
      return (<span data={dataName} title={node.label}>{node.label}</span>)
    },
    // 商品分类
    getProductTree() {
      const tree = async () => {
        var result = await productCategoryTree();
        this.productTreeList = result;
      }
      tree();
    },
    // 树结构点击事件
    handleCategorySelect(row) {
      this.selectedCategoryId = row.id;
      this.currentPage = 1;
      this.dataList();
    },
    // 商品列表
    dataList() {
      const params = {
        name: this.searchForm.name,
        code: this.searchForm.code,
        categoryId: this.selectedCategoryId,
        page: this.currentPage,
        size: this.pagesize,
        customerId: this.customerId
      }
      getProductList(params).then((res) => {
        if (res.data.code === 100) {
          this.productList = res.data.data
          this.total = res.data.total;
          this.sizeArea()
        }
      });
    },
    // 表格多选
    handleSelection(val) {
      this.determineModelList = val
      this.selectNum = this.determineModelList.length
    },
    // 清空
    emptyCountry() {
      this.handleSelection([])
      this.$refs.applytable.clearSelection();
    },
    // 提交
    supplierSubmit() {
      this.productIds = this.determineModelList.map(item => item.id)
      this.$emit('select',this.productIds)
      this.$parent.$parent.dialogVisibleAddMaterial = false;
    },
    cancelClick() {
      this.$parent.$parent.dialogVisibleAddMaterial = false;
    },
    // selectapply() {
    //   this.dialogCountryVisible = false
    //   let list = []
    //   if (this.determineModelList.length > 0) {
    //     this.determineModelList.forEach(row => {
    //       list.push(row.countryCode)
    //     })
    //   }
    //   this.temp.country = list

    //   this.targetName = list.length > 0 ? "已选择" : "未选择"
    // },
    // 分配国家 权限选择
    //  cellMouseEnter(row) {
    //   $(".nationalList._" + row.countryId + " .el-icon-close").show()
    // },
    // cellLeaveEnter() {
    //   $(".nationalList .el-icon-close").hide()
    // },
    heightArea() {
      var allHeight = $(".supplierContainer .infoDetail").height();
      var topHeight = $(".infoDetail .topButton").outerHeight(true);
      var leftVal = allHeight - topHeight;
      $(".supplierContainer .infoDetail .scrollClass").css("height", leftVal);
      var pageHeight = $(".nationalInfo .pagination-container").outerHeight(true);
      this.tableHeight = allHeight - pageHeight;
      this.maxHeight = allHeight;
    },
    sizeArea() {
      var _this = this;
      _this.heightArea();
      window.addEventListener("resize", function() {
        _this.heightArea();
      })
    },
  },
  mounted() {
    this.getProductTree();
    this.dataList()
  },
}
</script>
<style>
.el-dialog .dialogSearch .el-input {
  width: 300px !important;
  margin-right: 10px;
}

.dialogSearch {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

/* .dialogSearch .el-input {
  width: 200px !important;
  margin-right: 10px;
} */

.dialogSearch .el-input .el-input__inner {
  width: 100% !important;
}
.dialogSearch .el-checkbox {
  margin-right: 10px !important;
}
/* .el-dialog */
.supplierContainer .infoDetail {
  height: 550px;
}
.supplierContainer .infoDetail .scrollClass {
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}
.supplierContainer .pagination-container {
  border-bottom: 1px solid var(--table-border);
  border-left: 1px solid var(--table-border);
  border-right: 1px solid var(--table-border);
}
</style>
