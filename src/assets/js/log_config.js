//日志管理
window.showLog = false;
console.log = (function (logFun) {
  return function () {
    if (showLog) {
      logFun.apply(this, arguments);
    }
  }
})(console.log);


console.warn = (function (logFun) {
  return function () {
    if (showLog) {
      logFun.apply(this, arguments);
    }
  }
})(console.warn);


console.error = (function (logFun) {
  return function () {
    if (showLog) {
      logFun.apply(this, arguments);
    }
  }
})(console.error);

console.info = (function (logFun) {
  return function () {
    if (showLog) {
      logFun.apply(this, arguments);
    }
  }
})(console.info);
