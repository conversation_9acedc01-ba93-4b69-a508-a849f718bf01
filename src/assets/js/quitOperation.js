// 引入storageClear工具函数和router文件
import router from '@/router'
import store from '@/store/index'
import {loginStatus} from '@/assets/js/common.js'
import storageClear from "@/assets/js/storageClear";
import DonMessage from '@/plugins/message';
let lastTime = new Date().getTime();
let currentTime = new Date().getTime();
//设置超时时间: 4 小时
let timeOut = 4 * 60 * 60 * 1000;

// window.onload = function() {
//   window.document.onmousedown = function() {
//     storageClear.setItem("lastTime", new Date().getTime());
//   };
// };

function checkTimeout() {
  currentTime = new Date().getTime(); //更新当前时间
  lastTime = storageClear.getItem("lastTime");
  console.log("检测未操作时间：", (currentTime - lastTime), timeOut);
  if (lastTime === undefined || isNaN(currentTime - lastTime) || currentTime - lastTime > timeOut) {
    // 清除缓存
    storageClear.clear()
    if (router.history.current.path === "/" || router.history.current.name === 'DLogin') {
      // 当前已经是登陆页时不做跳转
      return;
    }
    // 跳到登陆页
    clearTokenAndGoLogin();
  }
}

export default function () {
  /* 定时器 间隔10分钟检测是否长时间未操作页面 */
  // worker 只计时
  // let workCode = `
  // var fun =  function(){
  // self.postMessage(null);
  //     setTimeout(fun, 600000);
  // };
  // setTimeout(fun, 600000)
  // `;
  // let worker;
  // var workBlob = new Blob([workCode]);
  // // 创建方法
  // const url = URL.createObjectURL(workBlob);
  // if (!worker) {
  //   console.log("worker线程");
  //     // 开启 worker 线程
  //     worker = new Worker(url);
  // }
  // console.log("===开启检测===");
  // worker.onmessage = function (data) {
  //     checkTimeout();
  // }
}


function clearTokenAndGoLogin() {
  if (window.onClearToken) {
    return
  }
  window.onClearToken = true
  store.commit('del_token');
  DonMessage.error("当前登录已过期，请重新登录！")
  setTimeout(() => {
    clearTimeout()
    window.onClearToken = false
    loginStatus();
  }, 1000)

}
