export let supplierColumns = [
  { label:'供应商编码', prop:'code', minWidth:'120' },
  { label:'供应商名称', prop:'name', minWidth:'120' },
  { label:'类别', prop:'categoryName', minWidth:'100' },
  { label:'状态', prop:'status', width:'90' },
  { label:'备注', prop:'remark', minWidth:'110' },
]
export let customerColumns = [
  { label:'客户货号', prop:'customerProCode', minWidth:'110' },
  { label:'客户货名', prop:'customerProName', minWidth:'110' },
  { label:'客户编码', prop:'code', minWidth:'110' },
  { label:'客户名称', prop:'name', minWidth:'110' },
  { label:'类别', prop:'categoryName', minWidth:'100' },
  { label:'状态', prop:'status', width:'90' },
  { label:'备注', prop:'remark', minWidth:'110' },
]
export let partnersColumns = [
  { label:'单位编码', prop:'code', minWidth:'120' },
  { label:'单位名称', prop:'name', minWidth:'120' },
  { label:'类别', prop:'categoryName', minWidth:'100' },
  { label:'状态', prop:'status', width:'90' },
  { label:'备注', prop:'remark', minWidth:'110' },
]
export let productColumns = [
  { label:'商品编码', prop:'code', minWidth:'110' },
  { label:'商品名称', prop:'name', minWidth:'110' },
  { label:'图片', slotName: 'imageUrl', width:'80' },
  { label:'类别', prop:'categoryName', width:'100' },
  { label:'条形码', prop:'barCode', minWidth:'100' },
  { label:'品牌', prop:'brand', width:'100' },
  { label:'规格型号', prop:'model', width:'100' },
  { label:'计量单位', prop:'unit', width:'80' },
  { label:'状态', prop: 'status', width:'60' },
  { label:'默认仓库', prop:'warehouseName', width:'110' },
  { label:'默认库位', prop:'locationName', width:'100' },
  { label:'备注', prop:'remark', minWidth:'100' },
]
export let orderColumns = [
  { label:'单件用量', prop:'quantity', width:'100'},
  { label:'订单用量', prop:'orderUsage', width:'100'},
  { label:'订单数量', prop:'quantity', width:'100'},
]
export let inventoryColumns = [
  { label:'当前库存', prop:'currentInventory', width:'100'},
  { label:'锁定库存', prop:'lockQty', width:'100'},
]
export let brandColumns = [
  { label:'品牌编码', prop:'code', minWidth:'120' },
  { label:'品牌名称', prop:'name', minWidth:'120' },
  { label:'类别', prop:'categoryName', minWidth: "100" },
  { label:'状态', prop:'status', width:'90' },
  { label:'备注', prop:'remark', minWidth:'100'}
]
export let  warehouseColumns = [
  { label:'仓库编码', prop:'code', minWidth:'120' },
  { label:'仓库名称', prop:'name', minWidth:'120' },
  { label:'仓库管理员', prop:'keeperUser', minWidth:'100' },
  { label:'状态', prop:'status', width:'90' },
]
export let locationColumns = [
  { label:'库位编码', prop:'code', minWidth:'120' },
  { label:'库位名称', prop:'name', minWidth:'120' },
  { label:'状态', prop:'status', width:'90' },
]
export let materialColumns = [
  { label:'物料编码', prop:'code', minWidth:'110'},
  { label:'物料名称', prop:'name', minWidth:'110'},
  { label:'图片', slotName: 'imageUrl', width:'80'},
  { label:'类别', prop:'categoryName', width:'100'},
  { label:'品牌', prop:'brand', width:'100'},
  { label:'单件用量', prop:'quantity', width:'100'},
  { label:'当前库存', prop:'currentInventory', width:'100'},
  { label:'锁定库存', prop:'lockQty', width:'100'},
  { label:'备注', prop:'remark', minWidth:'100'},
]
export let materialCopyColumns = [
  { label:'物料编码', prop:'code', minWidth:'110'},
  { label:'物料名称', prop:'name', minWidth:'110'},
  { label:'图片', slotName: 'imageUrl', width:'80'},
  { label:'类别', prop:'categoryName', width:'100'},
  { label:'品牌', prop:'brand', width:'100'},
  { label:'当前库存', prop:'currentInventory', width:'100'},
  { label:'锁定库存', prop:'lockQty', width:'100'},
  { label:'备注', prop:'remark', minWidth:'100'},
]
