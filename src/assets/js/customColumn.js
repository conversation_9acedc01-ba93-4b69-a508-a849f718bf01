// import { getAttributeType } from "@/api/cmsmgt.js";
// export let columnArrayList = [];
// export function getColumnShowList(_this) {
//   _this.columnShowList = [];
//   var typeList = [];
//   getAttributeType().then((res) => {
//     if (res.data.code == 100) {
//       typeList = res.data.data;
//     }
//     const merged = [...typeList, ..._this.columnList];
//     const uniqueMap = new Map(merged.map(item => [item.attributeKey, item]));
//     const result = Array.from(uniqueMap.values());
//     columnArrayList = result;
//   });
//   if (localStorage.getItem("customColumn") != null) {
//     _this.columnShowList = JSON.parse(
//     localStorage.getItem("customColumn")
//   );
//   } else {
//     _this.columnShowList = _this.columnList;
//   }
// }
// export function customColumnSet(_this) {
//   _this.noFixedColumnList = [];
//   _this.requiredOptionList = [];
//   setTimeout(() => {
//     requiredOption(_this);
//   })
// }
// export function requiredOption(_this) {
//   var list = [];
//   if (localStorage.getItem("allCustomColumn") != null) {
//     list = JSON.parse(localStorage.getItem("allCustomColumn"));
//   } else {
//     list = columnArrayList;
//   }
//   list.forEach((item) => {
//     _this.noFixedColumnList.push(item);
//   });
//   _this.columnShowList.forEach((item, index) => {
//     _this.requiredOptionList.push(item.desc);
//   });
// }
// export function columnSubmit(_this) {
//   let sortList = [];
//   _this.uploadSortList.forEach((item) => {
//     if (item.indexOf("(必选)") != -1) {
//       var text = item.replace(" (必选)", "");
//       sortList.push(text.replaceAll(/(\r\n|\n|\r)/g, ""));
//     } else {
//       sortList.push(item.replaceAll(/(\r\n|\n|\r)/g, ""));
//     }
//   });
//   let columnData = [];
//   columnArrayList.forEach((item) => {
//     _this.requiredOptionList.forEach((itm) => {
//       if (item.desc.indexOf(itm) != -1) {
//         columnData.push(item);
//       }
//     });
//   });
//   _this.columnShowList = columnData;
//   if (sortList.length > 0) {
//     _this.noFixedColumnList.sort(
//       (a, b) => sortList.indexOf(a.desc) - sortList.indexOf(b.desc)
//     );
//     _this.columnShowList.sort(
//       (a, b) => sortList.indexOf(a.desc) - sortList.indexOf(b.desc)
//     );
//   }
//   localStorage.setItem(
//     "allCustomColumn",
//     JSON.stringify(_this.noFixedColumnList)
//   );
//   localStorage.setItem("customColumn", JSON.stringify(_this.columnShowList));
//   columnClose(_this);
// }
// export function columnClose(_this) {
//   _this.columnShowList = [];
//   _this.dataList();
//   _this.showTable = false;
//   _this.$nextTick(() => {
//     _this.showTable = true;
//   });
//   _this.dialogColumnVisible = false;
// }
// export function recoveryOperate(_this) {
//   _this.$confirm("确定恢复为默认列设置", "恢复默认", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning",
//   }).then(() => {
//     localStorage.removeItem("allCustomColumn");
//     localStorage.removeItem("customColumn");
//     columnClose(_this);
//   });
// }