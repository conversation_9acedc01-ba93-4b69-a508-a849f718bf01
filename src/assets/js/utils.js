import {cmsServerUrl} from "@/assets/js/common";


//匹配发货状态style
export function getShippingStatusClass(status) {
  switch (status) {
    case 0:
      return 'errorColor'; // 未发货
    case 1:
      return 'blueColor'; // 部分发货
    case 2:
      return 'successColor'; // 已发货
    default:
      return '';
  }
}

//匹配审核状态style
export function getStatusClass(status) {
  switch (status) {
    case 0:
      return 'status-draft'; // 草稿
    case 1:
      return 'errorColor'; // 待审核
    case 2:
      return 'successColor'; // 审核通过
    case 3:
      return 'abnormalColor'; // 驳回
    case 4:
      return 'errorColor'; // 作废
    case 5:
      return 'blueColor'; // 审核中
    default:
      return '';
  }
}

//匹配入库状态style
export function getInbountStatusClass(status){
  switch (status) {
    case 0:
      return 'errorColor'; // 未入库
    case 1:
      return 'blueColor'; // 部分入库
    case 2:
      return 'successColor'; // 已入库
    default:
      return '';
  }
}

export function dateFormat(dateValue) {
  if (dateValue !== null) {
    const date = new Date(dateValue)
    const Y = date.getFullYear() + '-'
    const M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-'
    const D = date.getDate() < 10 ? '0' + date.getDate() + ' ' : date.getDate() + ' '
    const h = date.getHours() < 10 ? '0' + date.getHours() + ':' : date.getHours() + ':'
    const m = date.getMinutes() < 10 ? '0' + date.getMinutes() + ':' : date.getMinutes() + ':'
    const s = date.getSeconds() < 10 ? '0' + date.getSeconds() + '' : date.getSeconds() + ''
    return Y + M + D + h + m + s
  }
}

export function dateFormatWithMills (dateValue) {
  if (dateValue !== null) {
    const date = new Date(parseInt(dateValue))
    const Y = date.getFullYear()
    const Month = (date.getMonth() + 1).toString().padStart(2, '0')
    const D = date.getDate().toString().padStart(2, '0')

    const H = date.getHours().toString().padStart(2, '0');
    const M = date.getMinutes().toString().padStart(2, '0');
    const S = date.getSeconds().toString().padStart(2, '0');

    const Mills = date.getMilliseconds() % 1000

    return Y + "-" + Month + "-" + D + ":"+H+":"+M+":"+S+"::" + Mills.toString().padStart(3, '0')
  }
}

/**
 * 下载文件 通过文件接口id下载
 * @param fileId
 */
export function downloadFile (fileId) {
  var aLink = document.createElement("a");
  aLink.style.display = "none";
  aLink.href = cmsServerUrl + "cms/download/downloadFile?id="+fileId;
  //aLink.setAttribute("download", res.data.data.name);
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink); //下载完成移除元素
}

