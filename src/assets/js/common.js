import Vue from 'vue';
import $ from 'jquery';
import i18n from "@/i18n/i18n";
import store from "@/store/index";
import router from '@/router';
import { currentUserInfo } from '@/api/sysmgt.js'
import DonMessage from '@/plugins/message';
/** 计算文件md5值
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
import SparkMD5 from 'spark-md5'

const envs = {
  prod: {
    sysServerUrl: '/runta-erp-server/',
    frontHome: 'http://*************/runta-tis-front/#/index',
    indexHome: 'http://*************/runta-tis-front/#/detailInfo',
    frontLogin: 'http://*************/runta-tis-front/#/index',
    cavanLoginUrl: 'https://idm-portal-test.cavanauto.com',
    // useCommonLogin: true,
    useCommonLogin: false,
    showLog: false,
  },
  test: {
    sysServerUrl: '/180-server/runta-erp-server/',
    frontHome: 'http://*************/runta-tis-front/#/index',
    indexHome: 'http://*************/runta-tis-front/#/detailInfo',
    frontLogin: 'http://*************/runta-tis-front/#/index',
    cavanLoginUrl: 'https://idm-portal-test.cavanauto.com',
    // useCommonLogin: true,
    useCommonLogin: false,
    showLog: false,
  },
  dev: {
    sysServerUrl: 'http://localhost:9999/runta-erp-server/',
    // sysServerUrl: 'http://*************:8233/runta-erp-server/',
    frontHome: '',
    indexHome: 'http://*************/web/cavan-tis-front/#/detailInfo',
    cavanLoginUrl: 'https://idm-portal-test.cavanauto.com',
    frontLogin: '',
    useCommonLogin: false,
    showLog: true,
  },
}
const current = envs.dev;
window.showLog = current.showLog;
export const sysServerUrl = current.sysServerUrl;
export const cmsServerUrl = sysServerUrl
export const frontHome = current.frontHome;
export const indexHome = current.indexHome;
export const frontLogin = current.frontLogin;
export const useCommonLogin = current.useCommonLogin;
export const cavanLoginUrl = current.cavanLoginUrl;
export function loginStatus() {
  if (store.state.defaultLoginState == store.state.loginState) {
    router.push({ name: 'DLogin' });
  } else {
    window.location.href = cavanLoginUrl;
  }
  Vue.$loading.hide();
}
/**
 * 数字格式化为n位，不足n位则前面补0
 * @param {*} n
 */
Number.prototype.paddingZero = function (n = 2) {
  let s = "";
  for (let i = 1; i < n; i++) {
    s += '0';
  }
  return (s + this).slice(-1 * n);
}
// 添加keepalive缓存
export function addTabs(path, title) {
  var name = path.replaceAll("/", "");
  store.commit("addKeepAliveCache", name);
  var submenu = {
    path: path,
    name: name,
    label: title,
  };
  store.commit("selectMenu", submenu);
}
export function getContentData(_this) {
  var list = store.state.contentCatchMenu;
  var path = router.history.current.path;
  for (var i = 0; i < list.length; i++) {
    if (list[i].path == path) {
      _this.form = list[i].data
    }
  }
}
// 跳转页面执行
export let getRouteInfo = []
export function beforeRouteInfo(path, data) {
  var list = {
    path: path,
    data: data,
  }
  getRouteInfo = list
}
export function contentInfo(path, data) {
  if (path == undefined) {
    return;
  }
  var name = path.replaceAll("/", "");
  var submenu = {
    path: path,
    name: name,
    data:data,
  };
  store.commit("addContentCatch", submenu);
}
// 关闭nav选项卡
export function getTabJumpData() {
  var path = router.history.current.path;
  var tabAllList = store.state.tabList;
  var tabList = tabAllList.find(item => item.path == path);
  var index = tabAllList.findIndex(item => item.path === path);
  window.tabHandleClose(tabList, index);
}
// 关闭选项卡跳转页面
export function backInfoList(tab, oldTabList) {
  if (tab.path == getRouteInfo.path) {
    getRouteInfo = []
  }
  var tabState = '';
  var num = '';
  getJumpPath(tab)
  tabState = oldTabList.find(item => item.path === tabPathVal);
  if (tabState != undefined) {
    num = oldTabList.findIndex(item => item.path === tabPathVal);
    return num;
  }
}
export let tabPathVal = "";
export function getJumpPath(tab) {
  console.log(tab)

  var path = tab.path.toLowerCase()
   console.log(path.indexOf("instock"));
  if (path.indexOf('notice') != -1) {
    tabPathVal = '/sysmgt/notice/list';
  }
  if (path.indexOf('bulletin') != -1) {
    tabPathVal = '/cmsmgt/bulletin/list';
  }
  if (path.indexOf('product') != -1) {
    tabPathVal = '/basic/commodity/list';
  }
  if (path.indexOf('customer') != -1) {
    tabPathVal = '/basic/customer/list'
  }
  if (path.indexOf('warehouse') != -1) {
    tabPathVal = '/basic/warehouse/list';
  }
  if (path.indexOf('supplier') != -1) {
    tabPathVal = '/basic/supplier/list';
  }
  if (path.indexOf('purchaseapply') !== -1) {
    tabPathVal = '/purchase/purchaseApply/list';
  }
  if (path.indexOf('purchaseorder') !== -1) {
    tabPathVal = '/purchase/purchaseOrder/list';
  }
  if (path.indexOf('outstock') !== -1) {
    tabPathVal = '/stock/outStock/list';
  }
  if (path.indexOf("instock") !== -1) {
    tabPathVal = '/stock/inStock/list';
  }
  if (path.indexOf('logistics') !== -1){
    tabPathVal = '/logistics/list';
  }
  if (path.indexOf('sales') !== -1) {
    tabPathVal = '/sales/order/list'
  }
  if (path.indexOf('task') !== -1) {
    tabPathVal = '/mps/task-order/list'
  }
  if (path.indexOf('issue') !== -1) {
    tabPathVal = '/mps/issue/list'
  }
  if (path.indexOf('return') !== -1) {
    tabPathVal = '/mps/return/list'
  }
  if (path.indexOf('transferdetail') !== -1) {
    tabPathVal = '/stock/transferStock/list'
  }
  clearSessionStorage(tab.path)
}
// tab删除
export function removeTabs(routeVal) {
  getJumpPath(routeVal);
  getTabJumpData();
}
export function clearSessionStorage(path) {
  if (path == "/addProduct/add") {
    sessionStorage.removeItem("commodityName");
  }
  if (path == "/addCustomer/add/add") {
    sessionStorage.removeItem('customerName');
  }
  if (path == "/addWarehouse/add/add") {
    sessionStorage.removeItem('warehouseName');
  }
  if (path == "/addSupplier/add/add"){
    sessionStorage.removeItem('supplierName');
  }
}
// export function projectDate() {
//   store.commit("removeKeepAliveCache", 'cmsmgtmanuallist');
//   store.commit("removeKeepAliveCache", 'cmsmgttasklist');
//   store.commit("removeKeepAliveCache", 'cmsmgtauditlist');
//   store.commit("removeKeepAliveCache", 'releasemgtissuelist');
//   store.commit("removeKeepAliveCache", 'releasemgtpublishSetlist');
// }

// 统计区域高度
export function statisticalSize() {
  setTimeout(() => {
    let tabHeight = 0;
    if ($(".el-tabs__header").length != 0) {
      tabHeight = $(".el-tabs__header").outerHeight(true);
    }
    let allHeight = $(".layoutContainer").height();
    let searchHeight = $(".secondFloat").outerHeight(true);
    let val = allHeight - searchHeight - tabHeight - 2;
    $(".statisticalArea").css({ "height": val, "overflow": "auto" });
  }, 120);
}

export function statisticalHeight() {
  statisticalSize();
  window.addEventListener("resize", function () {
    statisticalSize();
  })
}

// 数列表内容高度
export function contentHeight() {
  if ($('.leftData').length !== 0) {
    let leftDataHeight = $('.leftData').outerHeight(true);
    if ($(".scrollClass").length !== 0) {
      let butHeight = $('.topButton').outerHeight(true) == null ? 0 : $('.topButton').outerHeight(true)
      let treeHeader = $(".treeHeader").outerHeight(true) == null ? 0 : $('.treeHeader').outerHeight(true)
      let distance = leftDataHeight - butHeight - treeHeader - 2;
      $(".scrollClass").css('height', distance);
    }
    if ($(".taskCenter").length !== 0) {
      let butHeight = "";
      if ($(".taskCenter").css("display") == "none") {
        butHeight = 0
      } else {
        butHeight = $('.taskCenter').outerHeight(true)
      }
      let taskHeight = leftDataHeight - butHeight;
      $(".taskManualList").css('height', taskHeight)
    }
  }
  if ($(".fromRight").length !== 0) {
    let fromRightHeight = $(".fromRight").outerHeight(true);
    if ($(".detailInfo").length != 0) {
      let rightHeight = $(".rightTitle").outerHeight(true) == null ? 0 : $(".rightTitle").outerHeight(true);
      let detailHeight = fromRightHeight - rightHeight;
      $(".detailInfo").css('height', detailHeight);
    }
    if ($(".fromRight .el-form").length != 0) {
      var titleHeight = $(".formTitle").outerHeight(true) == null ? 0 : $(".formTitle").outerHeight(true);
      var fromHeight = fromRightHeight - titleHeight - 2;
      $(".fromRight .el-form").css('height', fromHeight);
    }
  }
}
export function contentSize() {
  setTimeout(() => {
    contentHeight()
    window.addEventListener("resize", function () {
      contentHeight()
    })
  }, 100)
}

// 表格高度
export function tableHeight() {
  return new Promise((resolve) => {
    setTimeout(() => {
      if ($('.el-table') && $('.el-table').length != 0) {
        let allHeight = 0;
        if ($(".layoutContainer").length != 0) {
          allHeight = $(".layoutContainer").height();
        }
        if ($(".el-dialog").css("display") != "none") {
          if ($(".el-dialog .el-table").length != 0) {
            allHeight = allHeight - 20
          }
        }
        var headerTab = 0
        if ($(".headerTab").length != 0) {
          headerTab = $(".headerTab").outerHeight(true)
        }
        let tabHeight = 0;
        if ($(".el-tabs__header").length != 0) {
          tabHeight = $(".el-tabs__header").outerHeight(true)
        }
        let titleHeight = 0;
        if ($(".rightTitle").length != 0) {
          titleHeight = $(".rightTitle").outerHeight(true)
        }
        let searchHeight = 0;
        if ($(".el-tabs") && $(".el-tabs").length != 0) {
          var idVal = $(".el-tabs__item.is-active").attr("aria-controls");
          searchHeight = $(`.el-tabs__content #${idVal} .secondFloat`).outerHeight(true) == null ? 0 : $(`.el-tabs__content #${idVal} .secondFloat`).outerHeight(true);
        } else {
          searchHeight = $(".secondFloat").outerHeight(true) == null ? 0 : $(".secondFloat").outerHeight(true);
        }
        let handleHeight = 0;
        if ($(".tableHandle").length != 0) {
          if ($(".tableHandle").length > 1) {
            for (var i = 0; i < $(".tableHandle").length; i++) {
              var buttonHeight = $(".tableHandle").eq(i).height();
              if (buttonHeight != 0) {
                handleHeight = $(".tableHandle").eq(i).outerHeight(true);
              }
            }
          } else {
            handleHeight = $(".tableHandle").outerHeight(true)
          }
        }
        let paddingTop = 0;
        let paddingBottom = 0;
        let pagHeight = 0;
        if ($(".pagination-container").length != 0) {
          paddingTop = Number($(".pagination-container").parent().css("paddingTop").split('px')[0]);
          paddingBottom = Number($(".pagination-container").parent().css("paddingBottom").split('px')[0]);
          if ($(".pagination-container").length > 1) {
            for (var i = 0; i < $(".pagination-container").length; i++) {
              var display = $(".pagination-container").eq(i).css("display");
              if (display != "none") {
                if ($(".pagination-container").eq(i).outerHeight(true) != "0") {
                  pagHeight = $(".pagination-container").eq(i).outerHeight(true);
                }
              }
            }
          } else {
            pagHeight = $(".pagination-container").outerHeight(true)
          }
        }
        var val = allHeight - tabHeight - searchHeight - handleHeight - pagHeight - headerTab - titleHeight - paddingBottom - paddingTop;
        resolve(val);
      }
    }, 100)
  })
}
// tab中表格高度
export function tabContentSize() {
  setTimeout(() => {
    var allHeight = $(".layoutContainer > .el-tabs").outerHeight(true);
    var tabHeader = $(".layoutContainer > .el-tabs > .el-tabs__header").outerHeight(true);
    var val = allHeight - tabHeader;
    $(".layoutContainer > .el-tabs > .el-tabs__content").css("height", val);
  }, 100);
}
// el-collapse 新增高度
function collapseHeight() {
  setTimeout(() => {
    var allHeight = $(".layoutContainer").height();
    var titleHeight = 0;
    if ($(".elTabtitle").length != 0) {
      titleHeight = $(".elTabtitle").height();
    }
    var marginVal = 0;
    if ($(".elTabsContent .el-collapse-item").length != 0) {
      marginVal = 2 * Number($(".elTabsContent .el-collapse-item").css("marginTop").split("px")[0]);
    }
    var tabHeight = 0;
    if ($(".el-tabs__header.is-top").length != 0) {
      tabHeight = $(".el-tabs__header.is-top").outerHeight(true);
    }
    var val = allHeight - titleHeight - marginVal - tabHeight - 10;
    if ($(".elTabsContent .el-tabs__content").length != 0) {
      $(".elTabsContent .el-tabs__content").css("height", val);
    } else {
      $(".elTabsContent").css("height", val);
    }
  }, 60);
}
export function collapseArea() {
  collapseHeight();
  window.addEventListener("resize", function () {
    collapseHeight();
  })
}

// 目录展开状态
export let expandData = []
export function stateExpand(data) {
  if (data[0].children.length > 0) {
    for (var i = 0; i < data[0].children.length; i++) {
      if (data[0].children.length > 0) {
        stateExpand(data[0].children)
      }
    }
  } else {
    expandData = data[0]
  }
}

export function expandEvents(data) {
  stateExpand(data)
  return expandData
}

export let treeData = []

export function stateTree(data) {
  if (data[0].children !== null) {
    for (var i = 0; i < data[0].children.length; i++) {
      if (data[0].children !== null) {
        stateTree(data[0].children)
      }
    }
  } else {
    treeData = data[0]
  }
}

export function expandTree(data) {
  stateTree(data)
  return treeData
}

// 展开记录
// 数节点展开
export function nodeExpand(expandList, data) {
  // 保存当前展开的节点
  let flag = false
  expandList.some(item => {
    if (item === data.id) {
      //判断当前节点是否存在，存在不做处理
      flag = true
      return true
    }
  })
  if (!flag) {
    // 不存在则存到数组里
    expandList.push(data.id)
  }
  return expandList
}

export function nodeCollapse(list, data) {
  // 删除当前关闭的节点
  list.some((item, i) => {
    if (item === data.id) {
      list.splice(i, 1)
    }
  })
  removeChildren(list, data)
  return list
}

// 删除树子节点
export function removeChildren(list, data) {
  if (data.children !== null) {
    data.children.forEach(function (item) {
      const index = list.indexOf(item.id)
      if (index !== -1) {
        list.splice(index, 1)
      }
      removeChildren(list, item)
    })
  }
}


export function getmd5(file, chunkSize) {
  return new Promise((resolve, reject) => {
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
    let chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    let spark = new SparkMD5.ArrayBuffer();
    let fileReader = new FileReader();
    fileReader.onload = function (e) {
      spark.append(e.target.result);
      currentChunk++;
      if (currentChunk < chunks) {
        loadNext();
      } else {
        let md5 = spark.end();
        resolve(md5);

      }
    };
    fileReader.onerror = function (e) {
      reject(e);
    };

    function loadNext() {
      let start = currentChunk * chunkSize;
      let end = start + chunkSize;
      if (end > file.size) {
        end = file.size;
      }
      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }

    loadNext();
  });
}


// 语种数据
export function languageType() {
  // axios.get(`${sysServerUrl}sys/dict/query?dictType=languageType`).then(res => {
  //   if (res.data.code == 100) {
  //     sessionStorage.setItem('language', JSON.stringify(res.data.data))
  //   }
  // })
}

// 记录访问的菜单
export function accessRecordLog(menu) {
  // axios.get(`${sysServerUrl}sys/access/accessMenu?menu=` + menu).then(res => {

  // })
  // axios.post(`${sysServerUrl}sys/menu/accessMenu`, menu).then(res => {

  // })
}

export const quickEntryMap = new Map([
  ["技术通告", "technicalCircular.png"],
  ["反馈评价", "SBOMIocn.png"],
  ["在线反馈", "feedback.png"],
  ["系统公告", "systemNotice.png"],
  ["手册管理", "manualManage.png"],
  ["车型管理", "vehicleInfo.png"],
  ["采购申请单", "vehicleInfo.png"]
]);

/**
 * 递归查找并合并所有type为指定值的节点
 * @param {Array|Object} treeData - 树结构数据
 * @param type
 * @returns {Array} 所有type为brand的节点组成的数组
 */
export function findAndMergeNodes(treeData, type = 'brand') {
  // 存储所有brand类型的节点
  let brandNodes = [];

  // 处理单个节点的情况
  if (!Array.isArray(treeData)) {
    treeData = [treeData];
  }

  // 递归函数
  function traverse(nodes) {
    if (!nodes || nodes.length === 0) return;

    for (let node of nodes) {
      if (node.type === type) {
        brandNodes.push(node);
      }
      // 如果不是brand类型，则继续遍历其子节点
      else if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    }
  }

  traverse(treeData);
  return brandNodes;
}
// 主题色
export function getSystemTheme() {
  $("body").removeClass("blackTheme,greenTheme");
  currentUserInfo().then(res => {
    if (res.data.code == 100) {
      var list = res.data.data;
      // sessionStorage.setItem("realName", res.data.data.realName)
      store.commit('SET_ROLES', list.roleList)
      store.commit('SET_PERMS', list.permissionList)
      if (document.getElementById("theme") == null) {
        let link = document.createElement("link");
        link.type = "text/css";
        link.id = "theme";
        link.rel = "stylesheet";
        link.href = "./static/theme/themeBlack.css";
        if (link.href.indexOf("defaultTheme.css") != -1) {
          $("body").addClass("greenTheme");
        }
        if (link.href.indexOf("themeBlack.css") != -1) {
          $("body").addClass("blackTheme");
        }
        if (link.href.indexOf("themeCyan.css") != -1) {
          $("body").addClass("cyanTheme");
        }
        // $("body").addClass("blackTheme");
        // if (
        //   list.theme == "black" ||
        //   list.theme == undefined ||
        //   list.theme == null ||
        //   list.theme == ""
        // ) {
        //   link.href = "./static/theme/themeBlack.css";
        //   $("body").addClass("blackTheme");
        // } else if (list.theme == "blue") {
        //   link.href = "./static/theme/themeBlue.css";
        //   $("body").removeClass("blackTheme");
        // } else if (list.theme == "cyan") {
        //   link.href = "./static/theme/themeCyan.css";
        //   $("body").removeClass("blackTheme");
        // } else if (list.theme == "green") {
        //   link.href = "./static/theme/defaultTheme.css";
        //   $("body").removeClass("blackTheme");
        // }else{
        //   link.href = "./static/theme/themeBlack.css";
        //   $("body").addClass("blackTheme");
        // }
        document.getElementsByTagName("head")[0].appendChild(link);
      }
      languageType()
    }
  }).catch(err => {
    if (sessionStorage.token) {
      currentUserInfo().then(res => {
        if (res.data.code == 100) {
          var list = res.data.data;
          store.commit('SET_ROLES', list.roleList)
          store.commit('SET_PERMS', list.permissionList)
        }
      })
    } else {
      DonMessage.error('系统开小差...')
    }
  })
}
// 右键弹框位置
export function contextmenuSeat(event, className) {
  $(className).show()
  var allHeight = $(".el-container").height();
  var allWidth = $(".el-container").width();
  setTimeout(() => {
    var menuHeight = $(className).height();
    var menuWidth = $(className).width();
    if (event.clientY + menuHeight > allHeight) {
      $(className).css('top', event.clientY - menuHeight)
    } else {
      $(className).css('top', event.clientY)
    }
    if (event.clientX + menuWidth > allWidth) {
      $(className).css('left', event.clientX - menuWidth)
    } else {
      $(className).css('left', event.clientX)
    }
  })
}

export function DonMessageTip(sign) {
  // 关闭
  if (sign == 'close') {
    DonMessage.success(i18n.t('successTip.closeTip'))
  }
  // 开启
  if (sign == 'open') {
    DonMessage.success(i18n.t('successTip.openTip'))
  }
  // 发布
  if (sign == 'issue') {
    DonMessage.success(i18n.t('successTip.releaseTip'))
  }
  // 撤回
  if (sign == 'revocation') {
    DonMessage.success(i18n.t('successTip.withdrawTip'))
  }
  // 置顶
  if (sign == 'top') {
    DonMessage.success(i18n.t('successTip.topTip'))
  }
  // 取消置顶
  if (sign == 'notop') {
    DonMessage.success(i18n.t('successTip.noTopTip'))
  }
}
export function iconZoom() {
  setTimeout(() => {
    $(".el-image-viewer__mask").css("pointer-events", "none");
    $(".el-icon-full-screen").on("click", () => {
      $(".el-icon-c-scale-to-original").attr("title", "还原");
      $(".el-icon-full-screen").attr("title", "全屏");
    });
    $(".el-icon-zoom-out").attr("title", "缩小");
    $(".el-icon-zoom-in").attr("title", "放大");
    $(".el-icon-full-screen").attr("title", "全屏");
    $(".el-icon-c-scale-to-original").attr("title", "还原");
    $(".el-icon-refresh-left").attr("title", "右旋转");
    $(".el-icon-refresh-right").attr("title", "左旋转");
    $(".el-image-viewer__prev").attr("title", "上一张");
    $(".el-image-viewer__next").attr("title", "下一张");
    var imgSrc = $(".el-image-viewer__canvas img").attr("src");
    $(".el-image-viewer__canvas img").attr("src", imgSrc);
    Vue.$loading.show();
    $(".el-image-viewer__canvas img").on("load", function () {
      Vue.$loading.hide();
    });
    $(".el-image-viewer__canvas img").on("error", function () {
      Vue.$loading.hide();
    })
    $(".el-image-viewer__prev").on("click", () => {
      Vue.$loading.show();
      $(".el-image-viewer__canvas img").on("load", function () {
        Vue.$loading.hide();
      });
      $(".el-image-viewer__canvas img").on("error", function () {
        Vue.$loading.hide();
      })
    });
    $(".el-image-viewer__next").on("click", () => {
      Vue.$loading.show();
      $(".el-image-viewer__canvas img").on("load", function () {
        Vue.$loading.hide();
      });
      $(".el-image-viewer__canvas img").on("error", function () {
        Vue.$loading.hide();
      })
    });
  }, 100);
}
export function uploadIcon(uploadFilePath) {
  setTimeout(() => {
    var length = $(".el-upload-list.el-upload-list--text").children("li").length;
    for (var i = 0; i < length; i++) {
      var text = $(".el-upload-list.el-upload-list--text").children("li")[i].innerText;
      var type = '';
      if (uploadFilePath != undefined) {
        type = uploadFilePath.substring(uploadFilePath.lastIndexOf(".") + 1).toLowerCase();
      } else {
        type = text.substring(text.lastIndexOf(".") + 1).toLowerCase();
      }
      if ($(".el-upload-list__item.is-ready:nth-child(" + (i + 1) + ")").length != 0) {
        $(".el-upload-list__item.is-ready:nth-child(" + (i + 1) + ")")
          .children("a")
          .children("i")
          .addClass(type);
      }
      if ($(".el-upload-list__item.is-success:nth-child(" + (i + 1) + ")").length != 0) {
        $(".el-upload-list__item.is-success:nth-child(" + (i + 1) + ")")
          .children("a")
          .children("i")
          .addClass(type);
      }
    }
  }, 150);
}
// 图标样式
export function renderTree(className) {
  setTimeout(() => {
    if ($(className + " span[data='noChildIcon']").length != 0) {
      $(className + " span[data='noChildIcon']").parent().addClass("noChildNode");
    }
  });
}

// 获取上级类别名称
export function categoryTreeInfo(data, pid) {
  if (pid == 0 || pid == '') {
    return "全部"
  }
  for (var item of data) {
    if (item.id === pid) {
      console.log(item.name);
      return item.name // 找到匹配项，返回名称
    }
    if (item.children && item.children.length > 0) {
      const result = categoryTreeInfo(item.children, pid); // 递归查询子级
      if (result) return result; // 子级找到后直接返回
    }
  }
  return null; // 未找到
}

