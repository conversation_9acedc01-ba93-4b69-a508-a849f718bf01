
// 客户信息 商品信息高度
export function tabTableHeight(_this) {
  return new Promise((resolve) => {
    if ($(".layoutContainer.actionFlowDetail.addCustomer").length > 0) {
      var allHeight = $(".layoutContainer").height();
      var titleHeight = $(".elTabtitle").outerHeight(true);
      var tabHeight = $(".elTabsContent .el-tabs__header").outerHeight(true);
      var paddingVal = 2 * Number($(".elTabsContent .el-tabs__content").css("marginTop").split("px")[0]);
      var marginVal = 2 * Number($(".elTabsContent .tableDetail").css("marginTop").split("px")[0]);
      var searchHeight = 0;
      if ($(".secondFloat.productInfo").length != 0) {
        searchHeight = $(".secondFloat.productInfo").outerHeight(true);
        marginVal = marginVal / 2;
      }
      var val = allHeight - titleHeight - tabHeight - paddingVal - marginVal - searchHeight;
      console.log($("tableDetail .tableHandle"));
      $(".tableDetail").css("height", val);
      var handleHeight = 0;
      if ($(".tableDetail .tableHandle").length > 0) {
        handleHeight = $(".tableDetail .tableHandle").outerHeight(true);
      }
      var pageHeight = 0;
      if ($(".pagination-container").length > 0) {
        pageHeight = $(".pagination-container").outerHeight(true);
      }
      var heightVal = val - handleHeight - paddingVal - marginVal - pageHeight;
      resolve(heightVal)
    }
   });
}
export async function tabHeightArea(_this) {
  setTimeout(() => {
    const handleResize = async () => {
      const heightVal = await tabTableHeight();
      if (_this) {
        _this.maximumHeight = heightVal;
      }
    };
    handleResize()
    window.addEventListener("resize", handleResize);
  })
}
export function getColumnVal() {
  var val = document.body.clientWidth;
  if (val > 1920) {
    return 4
  }
  if (val <= 1920 && val > 1600) {
    return 4;
  }
  if (val <= 1600 && val > 1440) {
    return 3;
  }
  if (val <= 1440 && val > 1366) {
    return 3;
  }
  if (val <= 1366 && val > 1280) {
    return 3;
  }
  if (val <= 1280 && val > 1024) {
    return 2;
  }
  if (val <= 1024 && val > 740) {
    return 2;
  }
  if (val <= 740) {
    return 1;
  }
}
export function getColumnNumber(_this) {
  _this.dynamicColumn = getColumnVal()
  window.addEventListener("resize", function() {
    _this.dynamicColumn = getColumnVal()
  });
}