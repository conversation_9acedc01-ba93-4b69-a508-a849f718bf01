:root {
    --border-color: #dcdfe6;
    --border-radius: 4px;
    --disabled-color: #c0c4cc;
    --disabled-bgColor: #f5f7fa;
    --disabled-border: #e4e8ed;
    --secondary-text: #909399;
    --tip-color: #cccccc;
    --font-text: #222222;
    --text-color: #333333;
    --manual-bgColor: #fafafa;
    --subColor: #187aff;
    --work-bgColor: #f2f3f5;
    --other-color:#edeff3;
    --header-text: #445061;
    --table-header: #f5f7fa;
    --table-border: #e6eaf0;
    --hover-color: #f5f7fa;
    --striped-color: #fbfcff;
}

[v-cloak] {
    display: none !important;
}

/** 清除内外边距 **/
body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, /* structural elements 结构元素 */
dl, dt, dd, ul, ol, li, /* list elements 列表元素 */
pre, /* text formatting elements 文本格式元素 */
form, fieldset, legend, button, input, textarea, /* form elements 表单元素 */
th, td /* table elements 表格元素 */
{
    margin: 0;
    padding: 0;
}

/** 设置默认字体 **/
body,
button, input, select, textarea /* for ie */
{
    font: 12px/1.5;
    font-family: "微软雅黑", arial, \5b8b\4f53, sans-serif !important;
    box-sizing: border-box;
    color: var(--text-color);
}

h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
}

address, cite, dfn, em, var {
    font-style: normal;
}

/* 将斜体扶正 */
code, kbd, pre, samp {
    font-family: courier new, courier, monospace;
}

/* 统一等宽字体 */
small {
    font-size: 12px;
}

/* 小于 12px 的中文很难阅读，让 small 正常化 */

/** 重置列表元素 **/
ul, ol {
    list-style: none;
}

/** 重置文本格式元素 **/
a {
    text-decoration: none;
}

a:hover {
    text-decoration: none;
}


/** 重置表单元素 **/
legend {
    color: #000;
}

/* for ie6 */
fieldset, img {
    border: 0;
}

/* img 搭车：让链接里的 img 无边框 */
button, input, select, textarea {
    font-size: 100%;
}

/* 使得表单元素在 ie 下能继承字体大小 */
/* 注：optgroup 无法扶正 */

/** 重置表格元素 **/
table {
    border-collapse: collapse;
    border-collapse: unset;
    border-spacing: 0;
}

/* 清除浮动 */
.ks-clear:after, .clear:after {
    content: '\20';
    display: block;
    height: 0;
    clear: both;
}

.ks-clear, .clear {
    *zoom: 1;
}

.hideStyle {
    display: none;
}

/* 弹框 */
.alert {
    display: none;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 99999 !important;
    padding: 10px 15px !important;
    border-radius: var(--border-radius) !important;
    font-size: 15px !important;
}

.alert-success {
    font-size: 15px !important;
    color: #fff !important;
    background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 滚动条样式 */
.el-scrollbar__wrap {
    overflow-x: hidden;
    margin-bottom: 0 !important;
}

::-webkit-scrollbar {
    /* 滚动条整体样式 */
    width: 10px;
    height: 10px;
}

.el-textarea__inner::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-thumb {
    /* 滚动条里面小方块 */
    border-radius: 15px;
    background: rgb(221, 222, 224);
    cursor: pointer !important;
}

::-webkit-scrollbar-thumb:hover {
    background: rgb(199, 201, 204);
    cursor: pointer;
}

::-webkit-scrollbar-track {
    border-radius: 15px;
    /* 滚动条里面轨道 */
    background: rgb(249, 249, 249);
}

.el-select-dropdown__wrap::-webkit-scrollbar-track {
    background: transparent;
}

.el-table::-webkit-scrollbar-thumb,
.el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
    height: 10px;
    position: absolute;
    cursor: pointer;
}

/* 提交提醒 */
.el-loading-mask  {
    transition: 0s !important;
}
.loadMessage.el-loading-parent--relative {
    position: absolute !important;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 9999
}

.loadMessage .el-loading-mask {
    background-color: transparent;
}

.loadMessage .el-loading-spinner {
    background: #fff;
    top: 50%;
    margin-top: 0px;
    width: auto;
    text-align: center;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    box-shadow: 0px 0px 5px 0px #d8d8d8;
    padding: 10px 23px;
    border-radius: 7px;
}

.loadMessage .el-loading-spinner i {
    margin: 0 5px;
    font-size: 25px;
}

.loadMessage .el-loading-spinner p {
    display: inline-block;
}

.loadMessage .el-loading-spinner .el-loading-text {
    color: #333;
    font-size: 15px;
    margin: 0 5px;
}

@media screen and (max-width: 1600px) and (min-width: 1400px) {
    ::-webkit-scrollbar {
        /* 滚动条整体样式 */
        width: 8px;
        height: 8px;
    }

    .el-table::-webkit-scrollbar-thumb,
    .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
        height: 8px;
    }

    .el-textarea__inner::-webkit-scrollbar {
        width: 5px;
    }
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {
    ::-webkit-scrollbar {
        /* 滚动条整体样式 */
        width: 6px;
        height: 6px;
    }

    .el-table::-webkit-scrollbar-thumb,
    .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
        height: 6px;
    }

    .el-textarea__inner::-webkit-scrollbar {
        width: 6px;
    }
}

@media screen and (max-width: 1280px) and (min-width: 1024px) {
    ::-webkit-scrollbar {
        /* 滚动条整体样式 */
        width: 5px;
        height: 5px;
    }

    .el-table::-webkit-scrollbar-thumb,
    .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
        height: 5px;
    }

    .el-textarea__inner::-webkit-scrollbar {
        width: 6px;
    }
}

@media screen and (max-width: 1024px) {
    ::-webkit-scrollbar {
        /* 滚动条整体样式 */
        width: 5px;
        height: 5px;
    }

    .el-table::-webkit-scrollbar-thumb,
    .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
        height: 5px;
    }

    .el-textarea__inner::-webkit-scrollbar {
        width: 4px;
    }
}
