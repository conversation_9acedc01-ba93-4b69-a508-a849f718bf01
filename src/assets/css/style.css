/* svg */
.svg-icon {
    width: 15px;
    height: 15px;
    color: var(--icon-color) !important;
}

.is-required {
    font-weight: normal;
}
.secondFloat .el-form-item.is-required {
    /* margin: 8px 15px 18px 0px !important; */
}
/* el-badge */
.el-badge.mark {
    margin-top: -4px;
}

.el-badge__content,
.el-progress.is-exception .el-progress-bar__inner {
    background-color: #f44336;
}

.el-badge__content {
    font-size: 12px;
    height: 15px;
    line-height: 15px;
}

.topOptArea .mark .el-badge__content.is-fixed {
    position: absolute;
    top: 40%;
    right: -8px;
    transform: translateY(-50%) translateX(0%);
}

.auditMenuArea .topButton .mark .el-badge__content.is-fixed {
    position: absolute;
    top: 40%;
    right: -9px;
    transform: translateY(-50%) translateX(0%);
}

/* 文字 */
.successColor {
    color: #1c903b !important;
}

.errorColor {
    color: #F44336 !important;
}

.abnormalColor {
    color: #fea927 !important
}

.blueColor {
    color: #0a8fe2 !important;
}

.disabledColor {
    color: var(--disabled-color) !important;
}

.normalColor {
    color: #333;
}

.linkStyle {
    color: var(--subColor);
    text-decoration: underline;
    cursor: pointer;
}

.required-field::before {
    content: "* ";
    color: #F56C6C;
}

/*  */
.trStyle,
.trStyle:hover {
    background: #fff1d5 !important;
}

.readState {
    color: #bfbfbf;
    font-size: 12px;
    margin-right: 10px;
    white-space: nowrap;
}

/* 提示框 */
.el-message .el-icon-success,
.el-message--success .el-message__content {
    color: #25d07c !important;
}

.el-message .el-icon-error,
.el-message--error .el-message__content {
    color: #F44336 !important;
}

.el-message .el-icon-warning,
.el-message--warning .el-message__content {
    color: #fea927 !important;
}

.el-icon-close-tip {
    display: none !important;
}

/* el-tab */
.el-tabs__nav-wrap::after {
    height: 0px !important;
    background-color: var(--border-color) !important;
}

.el-tabs__header {
    margin: 0 0 0px;
}

.el-tabs__item {
    height: 35px;
    line-height: 35px;
    color: var(--text-color) !important;
    box-sizing: border-box;
    border-bottom: 2px solid transparent;
    margin: 0 12px;
    padding: 0 5px !important;
}

.el-tabs__item:hover {
    color: var(--theme-color) !important;
    border-bottom: 2px solid var(--theme-color);
}

.el-tabs__item.is-active {
    color: var(--theme-color) !important;
    font-weight: 700;
    border-bottom: 2px solid var(--theme-color);
}

.el-tabs__active-bar {
    display: none;
}

.el-tabs--border-card>.el-tabs__content {
    padding: 20px;
}

.el-tabs .el-tabs__nav-next,
.el-tabs .el-tabs__nav-prev {
    font-size: 15px;
    line-height: 60px;
    color: var(--font-text);
}

/* 搜索输入框 */
.layoutContainer,
.latestNewsPage {
    margin: 12px;
    box-sizing: border-box;
    overflow: hidden;
    height: calc(100% - 24px);
    border-radius: var(--border-radius);
}
/* el-collapse */
.layoutContainer.actionFlowDetail,
.layoutContainer.addProduct {
    margin: 0;
    height: 100%;
}
/* 页面标题 */
.layoutContainer > .elTabtitle {
    background: #fff;
    padding: 15px 12px;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
/* 内容 */
.layoutContainer > .elTabsContent {
    overflow-y: auto;
    margin-top: 4px;
}
.layoutContainer > .elTabsContent .collapseContent .secondFloat .el-textarea .el-textarea__inner {
    resize: vertical;
}

.layoutContainer > .elTabsContent .collapseContent .secondFloat .el-textarea .el-input__count {
    right: 10px;
    width: calc(100% - 11px);
}

.layoutContainer > .elTabsContent .collapseContent .secondFloat .el-textarea .el-textarea__inner::-webkit-scrollbar {
    width: 10px;
}


.layoutContainer>.elTabsContent>.collapseContent {
    margin: 0 12px;
}

.layoutContainer.actionFlowDetail.addCustomer .elTabsContent {
    margin-top: 0;
}
.layoutContainer > .elTabsContent .el-collapse-item__wrap {
    border-bottom: none;
}

.layoutContainer>.elTabsContent .el-collapse-item {
    margin: 12px 0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 标题 */
.layoutContainer>.elTabsContent .el-collapse-item__header {
    display: block;
    font-size: 16px;
    font-weight: bold;
    padding: 12px;
    height: auto;
    line-height: 1.6;
    color: #333 !important;
}

.layoutContainer>.elTabsContent .el-collapse-item__arrow {
    float: left;
    font-size: 15px;
    vertical-align: middle;
    margin-top: 5px;
    display: none;
}

.el-collapse-item__header.is-active,
.el-descriptions-item__content {
    color: #333 !important;
}

.el-collapse-item__header {
    pointer-events: none; /* 禁止整个标题行响应点击 */
}

.el-collapse-item__header span {
    pointer-events: auto; /* 仅允许文字点击 */
    cursor: pointer;
}

.el-collapse-item__header span i {
    font-size: 15px;
    vertical-align: middle;
    margin-top: -4px;
}

.el-collapse-item__header.is-active span > i{
    transform: rotate(90deg);
}

.el-collapse-item__header .subTitle {
    color: var(--secondary-text) !important;
    font-weight: normal;
    font-size: 12px;
    margin-left: 5px;
}
/* 内容 */
/* label */
.el-descriptions-item__label.has-colon {
    color: var(--secondary-text) !important;
}
/* el-descriptions 描述列表*/
.layoutContainer>.elTabsContent .el-collapse-item__content {
    padding: 0 30px 12px;
}
.el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    padding: 0 2px 10px;
}
.el-descriptions-item__label:not(.is-bordered-label) {
    margin-right: 6px;
}
.el-descriptions-item__container .el-descriptions-item__content {
    /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block; */
    /* max-width: 200px; */
    /* max-width: min(100%, 200px); */
}
/* el-descriptions 输入框 */
.layoutContainer>.elTabsContent .secondFloat {
    border: none;
}
/* 备注 */
.layoutContainer>.elTabsContent .secondFloat .inlineTextArea {
    width: 100%;
}

.layoutContainer>.elTabsContent .secondFloat .inlineTextArea .el-form-item__content {
    width: calc(100% - 130px);
}
/* 按钮 搜索框 */
.layoutContainer>.elTabsContent .secondFloat.productInfo,
.layoutContainer>.elTabsContent .tableDetail {
    background: #fff;
    padding: 12px;
    margin: 12px 0;
    border-radius: var(--border-radius);
}
/* el-tab */
.layoutContainer>.elTabsContent .el-tabs__header {
    background: #fff;
    padding-bottom: 5px;
}

.layoutContainer>.elTabsContent .el-tabs__content {
    padding: 0 12px;
    overflow-y: auto;
}
.layoutContainer.actionFlowDetail.addCustomer >.elTabsContent .el-tabs__content {
    margin-top: 5px;
}
.layoutContainer>.el-tabs,
.layoutContainer>.el-tabs .el-tab-pane {
    height: 100%;
}
.layoutContainer>.el-tabs .el-tab-pane .tableDetail {
    margin: 12px 0;
}

/* 图片 */
.layoutContainer .imageShowArea {
    display: flex;
    flex-wrap: wrap;
    padding: 0 20px;
    margin-left: -10px;
}

.layoutContainer .imageShowArea div {
    width: 150px;
    height: 150px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-left: 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}
.layoutContainer .imageShowArea div img {
    width: 100%;
    max-height: 100%;
}

.el-dialog .layoutContainer {
    margin: 0;
}

.inline-block {
    display: inline-block
}

.margin-right-10 {
    margin-right: 10px;
}

.margin-left-10 {
    margin-left: 10px;
}

/* el-tree */
.custom-tree-node,
.el-tree-node__label {
    font-size: 14px;
    flex: 1;
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-tree-node__label {
    display: inline-block;
}

.custom-tree-node>span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.custom-tree-node .attribute {
    text-overflow: initial
}

/* 属性 */
.attribute {
    padding: 0 10px;
}

/* 输入框 */
.el-input,
.el-select {
    border: none !important;
    outline: none !important;
}

.el-form-item__error {
    white-space: nowrap;
}

.el-form-item {
    margin-bottom: 22px !important;
}

/* 禁止输入 */
.el-input.is-disabled .el-input__inner,
.el-textarea.is-disabled .el-textarea__inner {
    background-color: var(--manual-bgColor) !important;
    border: 1px solid var(--disabled-color) !important;
    color: var(--font-text) !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* 输入框样式 */
.el-form-item__content {
    line-height: 36px !important;
}

.el-input__inner {
    padding: 0 8px !important;
    background: #fff !important;
    font-size: 14px !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
    height: 36px !important;
    line-height: 36px !important;
    border-radius: var(--border-radius);
    box-sizing: border-box;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner,
.secondFloat .el-input__inner {
    width: 220px;
}

.additionalInfo .el-input__inner {
    width: 200px;
}

.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-input__inner:focus,
.el-form-item.is-error .el-textarea__inner,
.el-form-item.is-error .el-textarea__inner:focus,
.el-message-box__input input.invalid,
.el-message-box__input input.invalid:focus {
    border-color: #FF5F5F !important;
}

.el-message-box__headerbtn:focus .el-message-box__close,
.el-message-box__headerbtn:hover .el-message-box__close {
    color: var(--theme-color) !important;
}

.el-message-box__headerbtn .el-message-box__close {
    color: var(--font-text) !important;
}

.el-textarea__inner {
    border-radius: var(--border-radius);
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
    padding: 5px 8px 16px 8px !important;
    font-size: 14px !important;
    resize: none;
}

.el-textarea .el-input__count {
    line-height: 1;
    font-size: 12px;
    background: #fff;
    bottom: 1px;
    right: 7px;
    width: calc(100% - 10px);
    padding: 1px 2px;
    text-align: right;
    border-bottom: 1px solid #fff;
    color: var(--tip-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.el-textarea__inner:focus,
.el-textarea__inner:hover,
.el-input__inner:focus,
.el-input__inner:hover {
    border: 1px solid var(--theme-color) !important;
}

.el-textarea__inner::placeholder,
.el-input__inner::placeholder {
    color: var(--disabled-color) !important;
}

.el-input__suffix {
    right: 2px;
}

.el-input .el-input__inner[limit="limit"] {
    padding: 0 52px 0 8px !important;
}

.el-input .el-input__count .el-input__count-inner,
.el-input .el-input__count {
    width: 52px;
    color: var(--tip-color);
    padding: 0 2px;
    background: transparent;
    text-align: right;
    font-weight: normal;
}

.el-input__icon {
    color: var(--disabled-color) !important;
    width: 22px !important;
    line-height: 36px !important;
}

/* label */
.el-form-item__label {
    color: var(--text-color);
    font-size: 14px;
    line-height: 36px;
    padding: 0 10px 0 0;
    white-space: nowrap;
}

/* select */
.el-select .el-input__inner {
    padding: 0 22px 0 8px !important;
}

.el-select-dropdown {
    border-radius: var(--border-radius);
}

.el-select-group .el-select-dropdown__item {
    padding-left: 30px !important;
}

.el-select-dropdown__item {
    font-size: 14px !important;
    padding: 8px 10px !important;
    height: auto !important;
    line-height: normal !important;
    color: var(--font-text) !important;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    background: transparent !important;
}

.el-select-dropdown__item.selected.hover,
.el-select-dropdown__item.selected {
    background-color: var(--select-color) !important;
    color: var(--theme-color) !important;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover,
.el-select-group .el-select-dropdown__item.hover {
    color: var(--text-color) !important;
    background-color: var(--hover-color) !important;
}

.el-select-group__title {
    line-height: 28px !important;
    color: var(--secondary-text) !important;
}

.el-select-group__wrap:not(:last-of-type)::after {
    left: 5px !important;
    right: 5px !important;
}

.el-select .el-select__tags {
    flex-wrap: nowrap;
    overflow-x: auto;
}

.el-tag.el-tag--info {
    color: rgba(89, 89, 89, 0.88) !important;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color) !important;
    background-color: var(--manual-bgColor) !important;
}

.el-tag.el-tag--info .el-tag__close {
    color: rgba(89, 89, 89, 0.88) !important;
}

.el-select .el-tag__close.el-icon-close {
    background-color: transparent !important;
}

.el-tag--small .el-icon-close {
    transform: scale(1) !important;
}

/* 日期选择框 */
.el-form-item__content .line {
    margin: 0 3px;
    vertical-align: top;
}

.el-input.el-date-editor--month .el-input__inner,
.el-input.el-date-editor--date .el-input__inner {
    padding-left: 25px !important;
    padding-right: 8px !important;
}

.el-input--suffix .el-input__inner {
    padding-right: 25px !important;
}

.el-date-picker__header {
    padding: 8px 10px;
    border-bottom: 1px solid #E4E7ED;
    margin: 0 !important;
}

.el-picker-panel {
    line-height: 25px !important;
}

.el-date-table th {
    border-bottom: none !important;
}

.el-date-table td {
    width: 29px;
    height: 24px;
    padding: 2px 0 !important;
}

.el-date-table td span {
    width: 18px !important;
    height: 16px !important;
    line-height: 16px !important;
    position: absolute;
    border-radius: var(--border-radius) !important;
    border: 1px solid transparent;
    box-sizing: content-box;
}

.el-date-table td div {
    height: 24px !important;
}

.el-date-table td.current:not(.disabled) span,
.el-date-table td:hover span {
    color: var(--theme-color) !important;
    border: 1px solid var(--theme-color);
    background-color: transparent;
}
.el-date-table td.today.current span,
.el-date-table td.today span {
    color: #fff !important;
    background-color: var(--theme-color) !important;
}

.el-date-table td span {
    border: 1px solid transparent;
}

/* 下拉框 */
.el-dropdown {
    color: var(--text-color);
}
.el-dropdown-menu {
    padding: 8px 0 !important;
    box-shadow: 0 0 3px 0px rgba(73, 73, 73, 0.2) !important;
    border-radius: var(--border-radius);
}

.el-dropdown-menu__item {
    font-size: 14px;
    line-height: 1 !important;
    padding: 8px 16px !important;
    margin: 2px 0;
    color: var(--text-color) !important;
}

.el-dropdown-menu__item:hover {
    background-color: var(--hover-color) !important;
}

.el-dropdown-menu__item span {
    color: var(--text-color) !important;
}

/* 排序框 */
.el-input-number {
    width: 100%;
    line-height: 36px;
}

.el-dialog .el-input-number.is-controls-right {
    width: 95%;
}

.el-input-number.is-controls-right .el-input {
    width: 100% !important;
}

.el-input-number.is-controls-right .el-input .el-input__inner {
    padding: 0 22px 0 8px !important;
    text-align: left;
}

.el-input-number.is-controls-right .el-input-number__decrease,
.el-input-number.is-controls-right .el-input-number__increase {
    color: var(--text-color) !important;
    background: transparent;
    border: none;
    width: 20px;
    font-size: 14px;
    line-height: 18px;
}

.el-input-number.is-controls-right .el-input-number__decrease:hover,
.el-input-number.is-controls-right .el-input-number__increase:hover {
    color: var(--theme-color) !important;
}

.el-input-number.is-controls-right .el-icon-arrow-up:before {
    content: "\e78f";
}

.el-input-number.is-controls-right .el-icon-arrow-down:before {
    content: "\e790";
}

.el-input-number.is-controls-right .el-input-number__decrease {
    bottom: 4px;
}

.el-input-number.is-controls-right .el-input-number__increase {
    top: 4px;
}

.el-input-number.is-controls-right .el-input-number__decrease [class*=el-icon],
.el-input-number.is-controls-right .el-input-number__increase [class*=el-icon] {
    transform: scale(1);
}

.el-input-number.is-controls-right .el-input-number__decrease.is-disabled,
.el-input-number.is-controls-right .el-input-number__increase.is-disabled,
.el-input-number.is-controls-right .el-input-number__decrease.is-disabled:hover,
.el-input-number.is-controls-right .el-input-number__increase.is-disabled:hover {
    color: var(--disabled-color) !important;
}

/* 查询筛选条件 */
.secondFloat {
    margin-bottom: 12px;
    box-sizing: border-box;
    border-bottom: 1px solid var(--border-color);
}

.secondFloat.switchArea {
    border-bottom: 1px solid var(--border-color);
}

.el-dialog .secondFloat {
    border-bottom: none;
}

.el-dialog .secondFloat .el-select {
    width: 100%;
}

.additionalInfo .el-dialog .el-input,
.el-dialog .secondFloat .el-select,
.el-dialog .secondFloat .el-input,
.el-dialog .secondFloat .el-date-editor,
.el-dialog .secondFloat .el-date-editor.el-input.el-input--prefix.el-input--suffix.el-date-editor--date {
    width: auto !important;
}

.el-dialog .leftData>div {
    margin-right: 12px !important;
}

/* 输入框 */
.secondFloat .el-form-item {
    margin: 8px 15px 12px 0px !important;
}

.secondFloat .el-form-item:last-child {
    margin: 8px 0px 12px 0px !important;
}
.elAutocomplete .el-autocomplete {
    width: 100%;
}
.elAutocomplete .el-autocomplete .el-input .el-input__inner,
.inputGroup .el-input .el-input__inner {
    padding: 0 25px 0 8px !important;
}
.elAutocomplete > .el-button,
.inputGroup .el-button {
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 100%;
    margin: 0 !important;
}
/* .el-dialog .elAutocomplete > .el-button,
.el-dialog .inputGroup .el-button {
    right: 5%;
} */

/* 按钮 */
.headerContent .el-button {
    border: 1px solid var(--theme-color) !important;
    box-sizing: border-box;
    background-color: var(--theme-color) !important;
    color: #fff !important;
}

.el-dialog .pendingTitle {
    margin: -10px 0 10px;
    display: flex;
}

.el-dialog .pendingTitle .el-input {
    width: 220px !important;
    margin-right: 10px;
}

/* 表格区域 */
.tableDetail {
    box-sizing: border-box;
}

/* 表格操作区域 */
.tableHandle {
    margin-bottom: 12px;
}

.tableHandle.spaceBbetwee {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tableHandle.spaceBetween {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.tableHandle .el-dropdown {
    /* border-left: 1px solid;
    border-right: 1px solid; */
    margin: 0 8px;
}
.tableHandle .el-dropdown .el-icon--right {
    margin-left: 0px;
    font-size: 12px;
}
/*按钮 */
.el-icon-plus,
.el-icon-edit,
.el-icon-document-copy,
.el-icon-setting,
.secondFloat .el-icon-arrow-down,
.secondFloat .el-icon-arrow-up,
.tableHandle .el-icon-delete,
.topButton .el-icon-delete,
.el-icon-delete,
.tableHandle .el-icon-setting,
.topButton .el-icon-setting,
.el-icon-s-promotion,
.tableHandle .el-icon-document-delete,
.topButton .el-icon-document-delete,
.tableHandle .el-icon-upload,
.topButton .el-icon-upload,
.tableHandle .el-icon-upload2,
.topButton .el-icon-upload2,
.tableHandle .el-icon-download,
.tableHandle .el-icon-odometer,
.tableHandle .el-icon-magic-stick,
.topButton .el-icon-download,
.tableHandle .el-icon-coin,
.topButton .el-icon-coin,
.topButton .el-icon-finished,
.topButton .el-icon-connection,
.el-icon-bank-card,
.el-icon-connection,
.el-icon-refresh {
    color: var(--theme-color);
    font-size: 15px;
    margin-right: 1px;
}

.tableHandle .el-icon-document-checked,
.topButton .el-icon-document-checked {
    color: #0e8e0d;
    font-size: 15px;
}

.tableHandle .el-icon-delete,
.topButton .el-icon-delete,
.el-icon-delete,
.tableHandle .el-icon-document-remove,
.topButton .el-icon-document-remove {
    color: #f45050;
    font-size: 15px;
}

.bulkDown-icon::before,
.import-icon::before,
.bulkImport-icon::before,
.adopt-icon::before,
.reject-icon::before,
.opinion-icon::before,
.push-icon::before,
.preview-icon::before,
.preserve-icon::before,
.process-icon::before,
.save-icon::before,
.power-icon::before,
.checkIn-icon::before,
.checkOut-icon::before,
.svgLook-icon::before,
.deleteRed-icon::before,
.permission-icon::before,
.compile-icon::before,
.rename-icon::before,
.enable-icon::before,
.disable-icon::before,
.filter-icon::before,
.addProducts-icon::before,
.setIcon-icon::before,
.selectIcon-icon::before,
.download-icon::before,
.upload-icon::before,
.unlock-inventory::before,
.receipt-icon::before,
.lock-inventory::before,
.production-tasks::before {
    content: "";
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url("../image/icon/templateIcon.png") no-repeat;
    background-size: 100%;
    margin-right: 1px;
    vertical-align: middle;
    margin-top: -3px;
}

.greenTheme .bulkDown-icon::before,
.greenTheme .import-icon::before,
.greenTheme .bulkImport-icon::before,
.greenTheme .adopt-icon::before,
.greenTheme .reject-icon::before,
.greenTheme .opinion-icon::before,
.greenTheme .push-icon::before,
.greenTheme .preview-icon::before,
.greenTheme .preserve-icon::before,
.greenTheme .save-icon::before,
.greenTheme .power-icon::before,
.greenTheme .process-icon::before,
.greenTheme .checkIn-icon::before,
.greenTheme .checkOut-icon::before,
.greenTheme .svgLook-icon::before,
.greenTheme .question-icon::before,
.greenTheme .permission-icon::before,
.greenTheme .compile-icon::before,
.greenTheme .rename-icon::before,
.greenTheme .setIcon-icon::before,
.greenTheme .unlock-inventory::before,
.greenTheme .receipt-icon::before,
.greenTheme .lock-inventory::before,
.greenTheme .production-tasks::before {
    filter: brightness(0.1) invert(23.2%) sepia(1) saturate(3000%) hue-rotate(136deg) brightness(1) contrast(78%);
}

.import-icon::before {
    background: url("../image/icon/importIcon.png") no-repeat;
}

.bulkImport-icon::before {
    background: url("../image/icon/bulkImport.png") no-repeat;
}

.adopt-icon::before {
    background: url("../image/icon/adoptIcon.png") no-repeat;
}

.opinion-icon::before {
    background: url("../image/icon/opinionIcon.png") no-repeat;
}

.power-icon::before {
    background: url("../image/icon/powerIcon.png") no-repeat;
}

.reject-icon::before {
    background: url("../image/icon/rejectIcon.png") no-repeat;
}

.push-icon::before {
    background: url("../image/icon/pushIcon.png") no-repeat;
}

.checkIn-icon::before {
    background: url("../image/icon/checkIn.png") no-repeat;
}

.checkOut-icon::before {
    background: url("../image/icon/checkOut.png") no-repeat;
}

.preview-icon::before {
    background: url("../image/icon/preview.png") no-repeat;
}

.preserve-icon::before {
    background: url("../image/icon/preserve.png") no-repeat;
}

.process-icon::before {
    background: url("../image/icon/processIcon.png") no-repeat;
    background-size: 100%;
}

.save-icon::before {
    background: url("../image/icon/saveAs.png") no-repeat;
}

.svgLook-icon::before {
    background: url("../image/icon/viewIcon.png") no-repeat;
}

.deleteRed-icon::before {
    background: url("../image/icon/deleteIcon.png") no-repeat;
    filter: none !important;
}

.compile-icon::before {
    background: url("../image/icon/editIcon.png") no-repeat;
    background-size: 100%;
}

.rename-icon::before {
    background: url("../image/icon/renameIcon.png") no-repeat;
    background-size: 100%;
}

.permission-icon::before {
    background: url("../image/icon/permission.png") no-repeat;
}

.setIcon-icon::before {
    background: url("../image/icon/setIcon.png") no-repeat;
}

.download-icon::before {
    background: url("../image/icon/downloadIocn.png") no-repeat;
}

.upload-icon::before {
    background: url("../image/icon/uploadIcon.png") no-repeat;
}
.unlock-inventory::before {
    background: url("../image/icon/unlockInventory.png") no-repeat;
}
.receipt-icon::before {
    background: url("../image/icon/receiptIcon.png") no-repeat;
}
.lock-inventory::before {
    background: url("../image/icon/lockInventory.png") no-repeat;
}
.production-tasks::before {
    background: url("../image/icon/productionTasks.png") no-repeat;
}
.addProducts-icon::before {
    background: url("../image/icon/addProducts.png") no-repeat;
}

.enable-icon::before {
    background: url("../image/icon/enableIcon.png") no-repeat;
    margin-right: 2px;
}

.disable-icon::before {
    background: url("../image/icon/disableIcon.png") no-repeat;
    margin-right: 2px;
    /* width: 13px;
    height: 13px;
    background-size: 100%; */
}

.filter-icon::before {
    /* content: "";
    display: inline-block; */
    background: url("../image/icon/filterIcon.png") no-repeat;
    margin-top: 1px;
    margin-left: 1px;
    margin-right: 0;
    /* width: 15px;
    height: 15px;
    background-size: 100%; */
}

.selectIcon-icon::before {
    background: url("../image/icon/selectIcon.png") no-repeat;
    margin-top: 4px;
    margin-right: 0px
}

/* .coefficient-icon::before {
    background: url("../image/icon/coefficientIoc.png") no-repeat;
    content: "";
    display: inline-block;
    width: 13px;
    height: 13px;
    background-size: 100%;
    margin-right: 1px;
    vertical-align: middle;
    margin-top: -3px;
} */

.question-icon::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("../image/icon/questionIcon.png") no-repeat;
    vertical-align: middle;
    margin-top: -1px;
    filter: none !important;
}

/* 表格 */
.el-table__body,
.el-table__footer,
.el-table__header {
    table-layout: fixed;
    border-spacing: 0;
    border-collapse: collapse;
    border-collapse: unset;
}

.el-table {
    color: var(--text-color);
    font-size: 14px;
}

.el-table thead th {
    color: var(--header-text);
    font-size: 14px;
    background-color: var(--table-header) !important;
}

.el-table th.el-table__cell.gutter {
    background-color: var(--table-header);
}

.el-table [class*=el-table__row--level] .el-table__expand-icon {
    color: var(--header-text);
}

.el-table .el-table__expand-icon {
    vertical-align: middle;
    margin-top: -2px;
}

.el-table .el-table__expand-icon.el-table__expand-icon--expanded {
    margin-top: -4px;
}

/* 筛选 */
.tableFilter[sort="sort"] .svg-icon {
    margin-left: 18px;
}

.tableFilter[sort="sort"]~.caret-wrapper {
    margin-left: -35px;
}

/* 排序 */
.el-table .caret-wrapper {
    height: 25px;
    width: 20px;
}

.el-table .sort-caret {
    left: 6px;
}

.el-table .sort-caret.ascending {
    border-bottom-color: #b6bcc6;
    top: 0px;
}

.el-table .sort-caret.descending {
    border-top-color: #b6bcc6;
    bottom: 2px;
}

.el-table .sort-caret.ascending:hover,
.el-table .ascending .sort-caret.ascending {
    border-bottom-color: var(--theme-color);
}

.el-table .sort-caret.descending:hover,
.el-table .descending .sort-caret.descending {
    border-top-color: var(--theme-color);
}

.el-table--border.el-loading-parent--relative {
    border: 1px solid var(--table-border);
    border-right: none;
    border-bottom: none;
    box-sizing: border-box;
}

.el-table .el-loading-mask {
    border: 1px solid var(--table-border);
    box-sizing: border-box;
}

.el-loading-mask.previewImage {
    background-color: transparent;
}

/* 表格边框线 */
.el-table thead.is-group th.el-table__cell {
    border-right: 1px solid var(--table-border);
}

.el-table thead.is-group th.el-table__cell,
.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right: 1px solid var(--table-border);
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf,
.el-table--border th.el-table__cell,
.el-table__fixed-right-patch {
    border-bottom: 1px solid var(--table-border);
}

.el-table__fixed-right-patch {
    background-color: var(--table-header);
}

.el-table--border,
.el-table--group {
    border: 1px solid var(--table-border);
}

.el-table--border {
    border-right: none;
    border-bottom: none;
}

.el-table .el-table__cell {
    padding: 5px 0;
    box-sizing: border-box;
}

.el-table__fixed-right {
    box-shadow: none !important;
    border-left: 1px solid var(--table-border);
}

.el-table__fixed {
    box-shadow: none !important;
    /* border-right: 1px solid var(--table-border); */
}

.el-table__empty-block {
    border-bottom: 1px solid var(--table-border);
}

.el-table--border::after,
.el-table--group::after,
.el-table::before {
    background-color: var(--table-border);
    z-index: 4;
}

.el-table__fixed-right::before,
.el-table__fixed::before {
    height: 0 !important;
}

.el-table .cell {
    line-height: 33px;
}
.el-table .cell .pictureShow {
    width: 36px;
    max-height: 36px;
}
.el-table .cell .rowEditShow{
    height: 100%;
    width: 100%;
    cursor: pointer;
}
.el-table .cell .rowEditShow>div {
    height: 100%;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
.el-table .cell .el-form-item  {
    margin-bottom: 0 !important;
}
.el-table .cell .rowEditShow,
.el-table .cell .el-input__inner {
    height: 36px !important;
}
.el-table .cell .el-input
.el-table .cell .el-select {
    width: 100%;
}
.el-table .cell,
.el-table--border .el-table__cell:first-child .cell,
.el-table th.el-table__cell>.cell {
    padding-left: 8px;
    word-break: break-word;
}

.el-table .cell,
.el-table th.el-table__cell>.cell {
    padding-right: 8px;
    word-break: break-word;
}

.el-table th.el-table__cell>.cell {
    white-space: nowrap;
}

.el-table td.el-table__cell>div,
.el-table td.el-table__cell .cell>div {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.manualManage .pagination-container {
    border: none !important;
    padding-right: 5px;
    padding-left: 5px;
}

.manualManage .el-table__fixed,
.manualManage .el-table__fixed-right {
    border: none;
}

/* 表格hover */
.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell,
.el-table__body .el-table__row.hover-row td {
    background: var(--hover-color) !important;
}

/* 表格点击背景 */
.el-table .el-table__body tr.trStyle:hover>td.el-table__cell,
.el-table .el-table__body tr.current-row:hover>td.el-table__cell,
.el-table .el-table__body .el-table__row.current-row,
.el-table .el-table__body .el-table__row.current-row td {
    background: #fff1d5 !important;
}

/* 表格点击背景 */
.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: #fcfdff;
}
/* 合计 */
.el-table__fixed-footer-wrapper tbody td.el-table__cell,
.el-table__footer-wrapper tbody td.el-table__cell{
    background-color: #fff !important;
    border-right: none !important;
    box-shadow: none;
    font-weight: bold;
}
.el-table .el-table__fixed-footer-wrapper tbody .el-table__cell:first-child .cell {
    text-overflow: initial !important;
}
/* 详情表格 */
.tabtop13 {
    border: none;
    margin: 0px 12px 15px 12px;
    border-collapse: collapse;
    color: var(--text-color);
}

.tabtop13 tr,
.tabtop13 td {
    background-color: #ffffff;
    border: 1px solid #e2e6ee;
    padding: 10px 8px;
    word-break: break-all;
    color: var(--text-color);
}

.tabtop13 th {
    color: var(--header-text);
}

.el-dialog .tabtop13 .el-textarea {
    width: 100% !important;
}

.tabtop13 .tdTitle {
    font-size: 15px;
    background: var(--table-header);
    color: var(--text-color);
    text-align: left;
    font-weight: bold;
}

.tabtop13 .uploadFileInfo {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.tabtop13 .uploadFileInfo>div {
    display: flex;
    align-items: center;
    margin: 3px 20px 3px 0px;
}

.tabtop13 .uploadFileInfo>div img {
    width: 16px;
    height: 16px;
    margin-right: 3px;
    vertical-align: middle;
}

.tabtop13 .uploadFileInfo>div>div:last-child:hover {
    text-decoration: underline;
}

/* 历史回复  */
.historyReply,
.replyArea,
.attachmentInfo {
    border-top: 1px solid var(--other-color);
    padding: 15px 0;
}

.historyReply>p,
.replyArea>p,
.attachmentInfo>p {
    font-weight: bold;
    background: var(--table-header);
    padding: 8px 10px;
    border: 1px solid var(--table-border);
    border-bottom: none;
}

.historyReply>div {
    min-height: 180px;
    padding: 8px 10px;
    border: 1px solid var(--table-border);
}

.historyReply>div>div {
    padding: 5px 0;
    border-bottom: 1px solid var(--table-border);
}

.historyReply>div>div:last-child {
    border-bottom: none;
}

/* top */
.historyReply>div>div .topInfo {
    display: flex;
    justify-content: space-between;
}

.historyReply>div>div .topInfo span:first-child {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.historyReply>div>div .topInfo>span>span {
    color: #999;
    margin-right: 8px;
}

.historyReply>div>div .topInfo>span:last-child {
    color: #999;
    margin-left: 8px;
}

/* 回复 */
.replyArea>div>.el-row {
    margin: 8px 10px;
}

.replyArea>div>.el-row:first-child .el-select {
    width: 100% !important;
}

.replyArea>div>.el-row:first-child .el-icon-sort {
    font-size: 20px;
    transform: rotate(90deg);
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, 35%) rotate(90deg);
}

.replyArea>div>.el-row:first-child>.el-button {
    margin-left: 30px;
}

.replyArea>div>.el-row:last-child>.el-col:first-child {
    padding-right: 15px;
}

.replyArea>div>.el-row:last-child>.el-col:last-child {
    padding-left: 15px;
}

.replyArea>div>.el-row:last-child>.el-col .el-textarea {
    border: 1px solid #DCDFE6 !important;
    margin-top: 10px;
}

/* 附件信息 */
.historyReply .accessoryInfo>div {
    display: flex;
    align-items: center;
    margin: 8px 0 4px;
}

.historyReply .accessoryInfo>div>div:first-child {
    width: 30px;
    height: 30px;
    margin-right: 5px;
}

.historyReply .accessoryInfo>div>div:first-child img {
    width: 100%;
    max-height: 100%;
}

.historyReply .accessoryInfo>div>div:last-child>p:last-child {
    margin-top: 2px;
}

.historyReply .accessoryInfo>div>div:last-child>p:last-child span {
    color: #409EFF;
    cursor: pointer;
    margin-right: 8px;
}

/* 删除 */
.historyReply .rightDel {
    text-align: right;
    color: #f44336;
}

/* 回复 */
.replyArea>div {
    border: 1px solid var(--table-border);
}

.replyArea .el-textarea,
.el-dialog .replyArea .el-textarea {
    border: none;
    width: 100% !important;
}

.replyArea .el-textarea__inner {
    border-radius: 0 !important;
    border: none !important;
}

/* 附件上传 */
.attachmentInfo>div {
    padding: 8px 10px;
    border: 1px solid var(--table-border);
}

.attachmentInfo .upload-demo {
    line-height: 1.6;
}

.attachmentInfo .upload-demo .el-upload {
    width: 100%;
}

.attachmentInfo .el-upload__input {
    display: none;
}

.el-dialog .attachmentInfo .upload-demo .el-upload .el-upload-dragger,
.attachmentInfo .upload-demo .el-upload .el-upload-dragger {
    width: 100%;
    height: 140px;
    border: 1px dashed var(--border-color) !important;
    background-color: var(--hover-color) !important;
    border-radius: 2px;
}

.attachmentInfo .upload-demo .el-upload .el-upload-dragger .el-upload_text {
    margin-top: 2%;
    color: var(--text-color);
    transform: none;
}

.attachmentInfo .upload-demo .el-upload .el-upload-dragger .el-upload_text .el-button {
    padding: 8px 10px;
    margin-top: 8px;
}

.attachmentInfo div svg {
    width: 35px;
    height: 35px;
    color: var(--theme-color) !important;
}

.upload-demo .el-upload-list__item {
    transition: none !important;
}

/* el-tree */
.el-tree,
.el-tree-node__expand-icon {
    color: var(--text-color);
    font-size: 14px;
}

.el-tree-node__content {
    height: 30px !important;
    line-height: 29px !important;
}

.el-tree-node__content>.el-tree-node__expand-icon {
    padding: 2px 3px 2px 5px;
}

.custom-tree-node,
.el-tree-node__label {
    font-size: 14px;
    flex: 1;
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-tree-node__label {
    display: inline-block;
}

.custom-tree-node>span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.custom-tree-node .attribute {
    text-overflow: initial
}

.attribute {
    padding: 0 10px;
}

.el-tree .treeLabel {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.scrollClass .el-tree-node__expand-icon.is-leaf,
.el-dialog .el-tree-node__expand-icon.is-leaf {
    color: transparent
}

.el-tree-node__content>label.el-checkbox {
    margin-right: 3px;
}

.el-tree-node__content:hover,
.scrollClass .el-tree-node__content:hover,
.scrollClass .el-upload-list__item:hover {
    background-color: var(--hover-color);
}

/* .scrollClass  */
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content,
.el-tree-node.is-current>.el-tree-node__content {
    background-color: var(--select-color);
}

/* upload */
.upload-demo {
    line-height: 1.6;
}

.el-dialog .upload-demo .el-upload-list.el-upload-list--picture,
.upload-demo .el-upload-list.el-upload-list--picture {
    width: 150px;
}

.upload-demo .el-upload {
    text-align: left;
}

.el-dialog .attachmentInfo .upload-demo .el-upload-list,
.el-dialog .upload-demo .el-upload.el-upload--text,
.upload-demo .el-upload.el-upload--text {
    width: 100%;
}

.upload-demo .el-upload .el-upload-dragger {
    width: 100%;
    height: 160px;
    border: 1px dashed var(--border-color) !important;
    color: var(--text-color) !important;
    background-color: var(--manual-bgColor) !important;
    border-radius: var(--border-radius);
}

.el-dialog .upload-demo .el-upload-list,
.el-dialog .upload-demo .el-upload .el-upload-dragger {
    width: 95%;
}

.upload-demo .el-upload .el-upload-dragger:hover,
.upload-demo .el-upload .el-upload-dragger:focus {
    border: 1px dashed var(--theme-color) !important;
}

.upload-demo .el-upload-list .el-progress {
    display: none;
}

.uplaodImage .upload-demo {
    margin-top: 5px;
}

.uplaodImage .el-upload .el-upload-dragger {
    width: 96%;
}

.el-upload-dragger .el-upload__text {
    color: var(--secondary-text);
    font-size: 14px;
    text-align: center;
    line-height: 1;
}

.upload-demo .el-upload_text svg {
    width: 35px;
    height: 35px;
    color: var(--theme-color);
}

.el-upload_text {
    transform: translate(0, 70%);
}

.upload-ol {
    list-style: decimal;
    margin-left: 15px;
}

.upload-ol>li {
    margin: 10px 0 35px 5px;
}

.upload-ol>li>div {
    margin: 5px;
}

.upload-ol>li>div:first-child {
    font-weight: bold;
}

.el-upload-dragger .el-upload__text em,
.upload-ol a {
    text-decoration: underline;
    color: var(--theme-color);
}

a {
    cursor: pointer;
}

.el-upload__tip {
    font-size: 12px;
    color: var(--secondary-text) !important;
    margin-top: 0px;
    white-space: nowrap;
    word-wrap: break-word;
}

.el-dialog .el-upload__tip,
.el-form-item .el-form-item__content .elUploadResult {
    width: 95%;
}

.elUploadResult .el-upload {
    display: none;
}

/*左右布局 列表信息 */
.infoDetail .leftData>div {
    margin-right: 12px;
    overflow: hidden;
    border-radius: var(--border-radius);
}

.infoDetail .leftData>div:first-child {
    background: #fff;
}

.infoDetail .leftData>div .scrollClass {
    padding: 0 8px;
}

.infoDetail,
.infoDetail .el-row,
.infoDetail .leftData,
.infoDetail .leftData>div {
    height: 100%;
}

.infoDetail .leftData>div .topButton,
.infoDetail .leftData>div .taskCenter {
    border-bottom: 1px solid var(--border-color);
}

.infoDetail .fromRight .rightTitle.none,
.infoDetail .leftData>div .topButton.none {
    border-bottom: none;
}

.infoDetail .fromRight .rightTitle.none {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.el-dialog .fromRight .formTitle,
.el-dialog .leftData>div .topButton {
    background-color: var(--manual-bgColor) !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.el-dialog .leftData>div .topButton {
    border: 1px solid var(--border-color);
    border-bottom: none;
}

.el-dialog .topicContent .dialogCatalog .topicCatalogScroll,
.el-dialog .fromRight {
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.infoDetail .leftData>div .topButton,
.infoDetail .leftData>div .taskCenter,
.infoDetail .fromRight .rightTitle,
.infoDetail .fromRight .formTitle {
    box-sizing: border-box;
    height: 38px;
    line-height: 38px;
    padding: 0 8px;
    font-size: 15px;
    font-weight: bold;
    color: var(--font-text);
}

.infoDetail .leftData>div .topButton {
    min-height: 38px;
    height: auto;
}

.infoDetail .leftData>div .topButton>span {
    margin-left: 5px;
}

.topButton .el-button+.el-button,
.topButton .el-button {
    margin-left: 0 !important;
    margin: 0 5px !important;
    padding: 0 0 !important;
}

/* task */
.infoDetail .leftData .taskManualList {
    overflow-y: auto;
}

.infoDetail .fromRight {
    background: #fff;
    height: calc(100% - 0px);
    overflow-x: hidden;
    overflow-y: hidden;
    box-sizing: border-box;
    min-width: 260px;
    border-radius: var(--border-radius);
}

.infoDetail .fromRight .formTitle {
    display: flex;
    overflow: hidden;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    box-sizing: border-box;
    font-size: 15px;
    font-weight: bold;
    text-align: center;
    box-sizing: border-box;
}

.infoDetail .fromRight .formTitle>div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.infoDetail .fromRight .rightTitle {
    /* padding-left: 10px; */
    border-bottom: 1px solid var(--border-color);
    box-sizing: border-box;
}

.rightTitle .el-button--text {
    color: var(--text-color);
}

.infoDetail .fromRight .butArea {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.infoDetail .fromRight .butArea .el-form-item__content {
    width: 100% !important;
    display: inline-block !important;
    text-align: center !important;
    margin: 0 auto !important
}

.infoDetail .fromRight .el-select {
    width: 100%
}

.infoDetail .el-scrollbar {
    height: 100% !important;
}

.infoDetail .fromRight .el-form-item {
    padding: 10px 55px 10px 20px;
    margin-bottom: 0px !important;
    box-sizing: border-box;
}

.infoDetail .fromRight .el-form-item:last-child {
    border-bottom: none;
}

.fromRight .el-input__inner {
    border: 1px solid var(--border-color);
    color: var(--text-color) !important;
}

.fromRight .el-form {
    /* height: calc(100% - 38px); */
    overflow-y: auto;
}

.fromRight .detailInfo.rightTableArea {
    padding: 0 12px 12px;
}

/* Dialog 弹框 */
.el-dialog__wrapper {
    overflow: hidden;
    display: flex;
    align-items: center;
}

.el-dialog {
    width: 600px !important;
    border-radius: var(--border-radius);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: calc(100% - 20px);
    max-width: calc(100% - 20px);
    min-width: 600px;
    margin: auto !important;
}

.el-dialog .el-input__suffix {
    right: 0
}

.el-dialog__header,
.el-message-box__header {
    font-weight: bold;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    display: flex;
}

.el-dialog__header .el-dialog__title,
.el-dialog__header .el-dialog__headerbtn .el-dialog__close {
    line-height: 1.3;
    font-size: 16px;
    font-weight: bold;
    color: var(--font-text);
}

.el-dialog__header .el-dialog__title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 40px;
}

.el-message-box__message {
    overflow-x: auto;
    max-height: 500px;
}

.el-dialog__headerbtn,
.el-message-box__headerbtn {
    top: 50%;
    transform: translate(0, -47%)
}

.el-dialog .el-form-item {
    /* margin-bottom: 20px; */
}

.el-dialog__body {
    color: var(--text-color);
    padding: 20px !important;
    flex: 1;
    font-size: 14px;
    overflow-x: hidden;
    overflow-y: auto;
}
.el-dialog .elAutocomplete,
.el-dialog .el-input,
.el-dialog .el-textarea,
.el-dialog .el-select {
    width: 95% !important
}
.el-dialog .elAutocomplete .el-autocomplete,
.el-dialog .elAutocomplete .el-autocomplete .el-input,
.el-dialog .el-select .el-input,
.el-dialog .el-select .el-input.el-input--suffix,
.fromRight .el-input.el-input--suffix {
    width: 100% !important;
    max-width: none;
}

/* .fromRight .el-pagination .el-select .el-input.el-input--suffix {
    width: 100px !important;
} */

.el-popover {
    border: none;
}

.el-popover__reference-wrapper {
    width: 50%
}

.el-dialog .el-popover__reference-wrapper .el-input.el-input--suffix {
    width: 95% !important;
    max-width: none;
}

.el-dialog .el-date-editor.el-input.el-input--prefix.el-input--suffix.el-date-editor--date {
    width: 95% !important;
    max-width: none;
}

.el-dialog .el-input--prefix .el-input__inner {
    padding: 0 10px;
}

.el-dialog .memberInfo .el-form-item__label {
    line-height: 26px;
}

.el-dialog .memberInfo .el-form-item__content {
    line-height: 26px;
}

.memberInfo .el-tree {
    max-height: 240px;
    overflow: auto;
}

/* 删除弹框 MessageBox*/
.el-message-box {
    padding-bottom: 15px;
}

.el-message-box__title {
    font-size: 16px;
    color: var(--font-text);
    font-weight: bold
}

.el-message-box__header {
    padding: 15px 20px 12px;
}

.el-message-box__content {
    padding: 20px 15px 15px;
}

.el-message-box__status {
    font-size: 22px !important;
}

.el-select {
    vertical-align: top
}

/* 无背景按钮 */
.el-message-box__btns .el-button.el-button--default.el-button--small.el-button--primary,
.el-button {
    border: 1px solid var(--theme-color) !important;
    background: var(--theme-color) !important;
    color: #fff !important;
}

.el-button {
    border-radius: var(--border-radius);
    font-size: 14px;
    padding: 0 25px;
    height: 36px;
}

.el-button.is-disabled {
    background: var(--disabled-color) !important;
    border: 1px solid var(--disabled-color) !important;
    color: #fff !important;
    opacity: 1 !important;
}

.el-message-box__btns .el-button.el-button--default.el-button--small.el-button--primary:hover,
.el-button:hover {
    opacity: 0.88;
}

.el-message-box__btns .el-button.el-button--default.el-button--small.el-button--primary:hover {
    color: #fff !important;
}

.el-button [class*='el-icon-']+span {
    margin-left: 0px !important;
}

.el-button svg {
    width: 15px;
    height: 15px;
    font-weight: bold;
    vertical-align: middle;
    margin-top: -3px;
    margin-right: 1px;
}

.el-button.el-button--default.is-plain,
.el-button.is-plain {
    border: 1px solid var(--theme-color) !important;
    background: transparent !important;
    color: var(--theme-color) !important;
}

/* .el-button.el-button--default.is-plain, */
.el-message-box__btns .el-button.el-button--default.el-button--small {
    background: transparent !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color) !important;
}

.submitArea .el-button.is-plain,
.submitArea .el-button.el-button--default.is-plain,
.submitArea .el-button.el-button--default.is-plain:hover,
.submitArea .el-button.is-disabled.is-plain:hover,
.submitArea .el-button.is-disabled.is-plain {
    background: #fff !important;
}

.el-button.el-button--default.is-plain:hover,
.el-message-box__btns .el-button.el-button--default.el-button--small:hover {
    color: var(--theme-color) !important;
    border: 1px solid var(--theme-color) !important;
}

.el-button.is-disabled.is-plain:hover,
.el-button.is-disabled.is-plain {
    color: var(--disabled-color) !important;
    background: var(--hover-color) !important;
    border: 1px solid var(--disabled-border) !important;
}

.el-button.el-button--text {
    background: transparent !important;
    color: var(--text-color) !important;
    border: none !important;
    padding: 0px;
    height: 20px;
    margin: 2px 8px;
}

.el-table .el-button.el-button--text {
    margin: 0px 5px 0 0;
}

.el-button.el-button--text:hover {
    opacity: 0.9;
}

.secondFloat .el-button.el-button--text,
.el-table .el-button.el-button--text {
    color: var(--subColor) !important;
}

.secondFloat .el-button.el-button--text {
    margin-left: 10px;
}

.secondFloat .el-button.el-button--text .el-icon--right {
    margin-left: 2px;
    font-size: 13px;
}

.deleteButton,
.el-main .el-button.deleteButton,
.el-button.deleteButton {
    color: #f44336 !important;
}

.el-button.el-button--text.is-disabled i {
    filter: grayscale(100%) brightness(1.69);
}

.el-button.el-button--text.is-disabled:hover,
.el-button.el-button--text.is-disabled {
    color: var(--disabled-color) !important;
}

.el-table .el-button+.el-button {
    margin: 0 5px;
}

.el-input-group__append {
    overflow: hidden;
}

.el-input-group__append .el-button {
    background-color: var(--hover-color) !important;
    color: var(--text-color) !important;
    border: none !important;
}

/* 单选 */
.el-radio,
.el-radio-button__inner,
.el-radio-group {
    line-height: inherit;
}

.el-radio__inner {
    border: 1px solid var(--border-color) !important;
}

.el-radio__inner:hover {
    border: 1px solid var(--theme-color) !important;
}

.el-radio__input.is-checked .el-radio__inner {
    border-color: var(--theme-color) !important;
    background: var(--theme-color) !important;
}

.el-radio__label {
    font-size: 15px !important;
    color: var(--font-text) !important;
}

.el-switch.is-checked .el-switch__core,
.el-radio__input.is-checked .el-radio__inner {
    border-color: var(--theme-color);
    background: var(--theme-color);
}

.el-radio__input.is-checked+.el-radio__label {
    color: var(--theme-color);
}

/* 开关按钮 */
.el-switch__core {
    border: 1px solid var(--disabled-color);
    background-color: var(--disabled-color);
}

.el-switch__label.is-active {
    color: var(--theme-color);
}

/* 多选 */
.el-checkbox {
    color: var(--text-color);
}

.el-checkbox__inner {
    border: 1px solid var(--border-color) !important;
    width: 15px;
    height: 15px;
    margin-right: 3px;
}

.el-checkbox__inner::after {
    left: 5px;
}

.el-checkbox__inner:hover {
    border: 1px solid var(--theme-color) !important;
}

.el-checkbox__label {
    padding-left: 5px;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
    color: var(--text-color) !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: var(--theme-color);
    border: 1px solid var(--theme-color) !important;
}

.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner,
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner,
.el-checkbox__input.is-disabled .el-checkbox__inner {
    background: var(--disabled-bgColor) !important;
    border: 1px solid var(--disabled-border) !important;
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #cecece !important;
}

.el-checkbox.is-bordered.is-checked {
    border-color: var(--theme-color);
}

.el-checkbox.is-bordered {
    padding: 8px 10px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    box-sizing: border-box;
    line-height: normal;
    height: auto;
    margin: 10px 6px !important;
}

/* 上传图片区域 */
.imgShow {
    width: 150px;
}

.el-upload-list__item {
    transition: none !important;
}

.upload-ol .el-upload-list__item-name,
.upload-demo .el-upload-list__item-name {
    text-decoration: none;
}

.el-upload-list__item-name {
    color: var(--secondary-text) !important;
}

.upload-demo .el-upload-list__item:hover {
    background: var(--hover-color);
}

.el-upload-list--picture .el-upload-list__item {
    padding: 10px 20px 10px 10px !important;
    height: 100px;
    background: #eee !important;
}

.el-upload-list--picture .el-upload-list__item-thumbnail {
    height: 80px;
    width: auto !important;
    margin-left: 0 !important;
    background: transparent !important;
    max-width: 112px;
}

.el-upload.el-upload--picture-card {
    background-color: rgb(251, 253, 255);
    border: 1px dashed var(--border-color);
    color: var(--secondary-text);
    line-height: 1.5;
}
.el-upload.el-upload--picture-card .el-upload-dragger {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: var(--border-radius);
}
.el-upload .uploadTip {
    transform: translateY(-50%);
    margin-top: 50%;
}

.el-upload.el-upload--picture-card .el-icon-plus {
    font-size: 30px;
    color: var(--secondary-text) !important;
}

.el-upload-list--picture-card .el-upload-list__item {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.el-upload-list__item-preview {
    display: none;
}

/* 进度条 */
.el-progress-bar__outer {
    height: 6px !important;
}

/* ============= 下拉多选添加复选框 开始 =============== */
.el-select-dropdown.is-multiple .el-select-dropdown__item:after {
    content: "";
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected:after {
    content: "";
}

/*参考el-checkbox实现checkbox样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item .checkbox {
    display: inline-block;
    position: relative;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-sizing: border-box;
    width: 14px;
    height: 14px;
    background-color: #fff;
    z-index: 1;
    transition: border-color .25s cubic-bezier(.71, -.46, .29, 1.46), background-color .25s cubic-bezier(.71, -.46, .29, 1.46);
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .checkbox {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

/*参考el-select多选对号样式实现checkbox中对号的样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .checkbox::after {
    position: absolute;
    top: -10px;
    font-family: element-icons;
    content: "\e6da";
    font-size: 12px;
    font-weight: 700;
    -webkit-font-smoothing: antialiased;
    color: #fff;

}

/*设置置灰内容样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .checkbox {
    background: var(--disabled-bgColor) !important;
    border-color: var(--disabled-border) !important;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .checkbox::after {
    color: #cecece;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .label-name-box {
    color: var(--disabled-color);
    font-weight: 400;
}

.el-select-dropdown__item {
    display: flex;
    align-items: center;
}

/* 自定义列 */
.setColumn .tipsTitle {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--secondary-text);
    margin-bottom: 15px;
}

.setColumn .tipsTitle img {
    width: 18px;
    margin-right: 5px;
    vertical-align: middle;
    margin-top: 1.8px;
}

.columnDetail .fixedColumn,
.columnDetail .noFixedColumn {
    padding: 0px 25px 5px;
}

.columnDetail>div>p {
    font-size: 15px;
    font-weight: bold;
    margin: 10px 0;
}

.noFixedColumn {
    border-bottom: 1px solid var(--border-color);
}

.noFixedColumn>div>div>span>div,
.fixedColumn>div>div {
    margin: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.noFixedColumn .el-checkbox__label,
.fixedColumn .el-checkbox__label {
    padding-left: 10px;
}

.noFixedColumn .el-checkbox__label span,
.fixedColumn .el-checkbox__label span {
    color: var(--disabled-color);
}

.noFixedColumn .el-checkbox__input.is-checked+.el-checkbox__label,
.fixedColumn .el-checkbox__input.is-checked+.el-checkbox__label {
    color: var(--text-color) !important;
}

.noFixedColumn img {
    width: 16px !important;
    height: 16px !important;
}

/* ============= 下拉多选添加复选框 结束 =============== */
/* 系统公告 技术通告 */
.layoutContainer.addNoticeContent,
.layoutContainer.addBulleinContent {
    overflow-y: auto;
    padding-right: 10% !important;
}

.tox-pop--top {
    display: none;
    visibility: hidden;
    opacity: 0;
}

.addNoticeContent .el-select,
.addBulleinContent .el-input-number.is-controls-right,
.addBulleinContent .el-select {
    width: 100%
}

/* 国家分配 */
.nationalInfo .el-table__fixed {
    border-right: 1px solid var(--table-border);
    border-left: none;
}

/* 分页 */
.assignCountryArea .pagination-container {
    position: relative;
    bottom: 2px;
    right: 0px;
    border-bottom: 1px solid var(--table-border);
    box-sizing: border-box;
    padding: 8px 5px;
}

.assignCountryArea .el-dialog .el-form-item {
    margin-bottom: 10px;
    margin-top: 0px
}

.assignCountryArea .el-dialog .el-pagination .el-pagination__jump .el-input {
    width: 50px !important;
}

/*右侧 */
.assignCountryArea .nationalSelect {
    border-left: 1px solid var(--table-border);
}

.nationalSelect .authorityTitle,
.nationalSelect .nationalList {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nationalSelect .authorityTitle span {
    margin: 0 5px;

}

.nationalSelect .authorityTitle span b {
    color: var(--theme-color);
}

.nationalSelect .authorityTitle .warrantAction {
    color: var(--subColor);
    cursor: pointer;
    font-size: 12px;
    font-weight: normal;
}

.nationalSelect .authorityTitle .clearAction {
    color: #d91c1c;
    cursor: pointer;
    font-size: 14px;
    font-weight: normal;
}

.nationalSelect .nationalList span {
    display: inline-block;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.nationalSelect .el-table thead th {
    background-color: transparent !important;
}
.nationalSelect .el-table td.el-table__cell {
    border-bottom: 1px solid transparent;
}
.nationalSelect i {
    cursor: pointer;
    display: none;
    font-size: 14px;
    font-weight: bold;
}

/* 点击实物图放大 */
.el-image-viewer__mask {
    opacity: 0.7;
}

.el-image-viewer__wrapper {
    z-index: 3000 !important;
}

.el-image-viewer__close {
    top: 30px !important;
    right: 5% !important;
}

.el-image-viewer__canvas {
    width: 85% !important;
    overflow: hidden;
    margin: auto;
    height: calc(100% - 95px) !important;
    margin-top: 20px !important;
    color: #fff;
}

.el-image-viewer__actions {
    bottom: 20px !important;
}

.el-image-viewer__btn {
    opacity: 1 !important;
}

.el-image-viewer__next,
.el-image-viewer__prev {
    top: auto !important;
    bottom: 20px !important;
    transform: translateY(0%) !important;
}

.el-image-viewer__next {
    right: 33% !important;
}

.el-image-viewer__prev {
    left: 33% !important;
}

[class*=" el-icon-"],
[class^=el-icon-] {
    cursor: pointer;
}

/* 右键菜单样式 */
.contextmenuArea,
.contextmenuContent {
    min-width: 100px;
    position: absolute;
    display: none;
    background: #fff;
    border-radius: var(--border-radius);
    margin: 2px 0;
    box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
    z-index: 3000;
}

.contextmenuContent>div,
.contextmenuArea>div {
    padding: 5px 15px;
    margin: 3px 0;
    cursor: pointer;
    font-size: 14px;

}

.contextmenuContent>div i,
.contextmenuArea>div i {
    color: var(--font-textColor);
    filter: grayscale(100%) brightness(0);
    margin-right: 1px;
}

.contextmenuContent>div svg,
.contextmenuArea>div svg {
    width: 15px;
    height: 15px;
    margin-right: 0px;
    color: var(--font-textColor);
}

.contextmenuContent>div:hover,
.contextmenuArea>div:hover {
    background: var(--hover-color);
    color: var(--theme-color);
    filter: grayscale(0%) brightness(1);
}

.contextmenuContent>div:hover i,
.contextmenuArea>div:hover i {
    color: var(--theme-color);
    filter: grayscale(0%) brightness(1);
}

.greenTheme .contextmenuContent>div:hover i,
.greenTheme .contextmenuArea>div:hover i {
    filter: brightness(0.1) invert(23.2%) sepia(1) saturate(3000%) hue-rotate(136deg) brightness(1) contrast(78%);
}

.contextmenuContent>.deleteMark:hover,
.contextmenuArea>.deleteMark:hover {
    color: #F56C6C
}

.contextmenuContent>.deleteMark:hover i,
.contextmenuArea>.deleteMark:hover i {
    color: #F56C6C;
    filter: grayscale(0%);
    filter: invert(100%) sepia() opacity(0.8) saturate(8000%) hue-rotate(0deg)
}

@media screen and (max-width: 1600px) and (min-width: 1400px) {

    /* 输入框 */
    .el-select-group .el-select-dropdown__item {
        font-size: 14px;
        padding: 0 16px 0 30px;
    }

    /* 点击实物图放大 */
    .el-image-viewer__close {
        width: 35px !important;
        height: 35px !important;
        font-size: 18px !important;
    }

    .el-image-viewer__actions {
        height: 40px !important;
        padding: 0 18px !important;
    }

    .el-image-viewer__actions__inner {
        font-size: 22px !important;
    }

    .el-image-viewer__next,
    .el-image-viewer__prev {
        width: 40px !important;
        height: 40px !important;
        font-size: 20px !important;
    }

    .el-image-viewer__next {
        right: 32% !important;
    }

    .el-image-viewer__prev {
        left: 32% !important;
    }
}

@media screen and (max-width: 1440px) and (min-width: 1366px) {

    /* el-table */
    .el-table,
    .el-table thead {
        font-size: 14px;
    }

    .el-table .cell {
        /* height: 30px; */
        line-height: 30px;
    }
    .el-table .cell .rowEditShow,
    .el-table .cell .el-input__inner {
        height: 34px !important;
    }
    .el-table .cell,
    .el-table--border .el-table__cell:first-child .cell,
    .el-table th.el-table__cell>.cell {
        padding-left: 7px;
    }

    .el-table .cell,
    .el-table th.el-table__cell>.cell {
        padding-right: 7px;
    }

    .el-table .el-table__cell {
        padding: 4px 0;
    }

    /* 输入框 */
    /* .el-form-item {
        margin-bottom: 22px !important;
    } */

    .el-form-item__content,
    .el-input__inner {
        font-size: 14px !important;
        line-height: 34px !important;
    }

    .el-input__inner {
        height: 34px !important;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner,
    .secondFloat .el-input__inner {
        width: 210px;
    }

    .el-textarea__inner {
        font-size: 14px !important;
    }

    .el-input__icon {
        line-height: 34px !important;
    }

    /* label */
    .el-form-item__label {
        line-height: 34px;
    }

    /* select */
    .el-select-dropdown__item {
        font-size: 14px !important;
        padding: 6px 8px !important;
    }

    /* 排序框 */
    .el-input-number {
        line-height: 34px;
    }

    .el-input-number.is-controls-right .el-input-number__increase {
        top: 2px;
    }

    .el-input-number.is-controls-right .el-input-number__decrease {
        bottom: 2px;
    }

    /*按钮 */
    .el-button {
        padding: 0 22px;
        height: 34px;
    }

    /* el-dialog弹框 */
    .el-dialog__headerbtn .el-dialog__close,
    .el-message-box__headerbtn .el-message-box__close {
        font-size: 17px;
    }
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {

    /* 表格 */
    .el-table,
    .el-table thead {
        font-size: 13px;
    }

    .el-table .cell {
        /* height: 28px; */
        line-height: 28px;
    }
    .el-table .cell .rowEditShow,
    .el-table .cell .el-input__inner {
        height: 32px !important;
    }
    .el-table .cell,
    .el-table--border .el-table__cell:first-child .cell,
    .el-table th.el-table__cell>.cell {
        padding-left: 6px;
    }

    .el-table .cell,
    .el-table th.el-table__cell>.cell {
        padding-right: 6px;
    }

    .el-table .el-table__cell {
        padding: 4px 0;
    }

    /* 输入框 */
    /* .el-form-item {
        margin-bottom: 20px !important;
    } */

    .el-form-item__content,
    .el-input__inner {
        font-size: 13px !important;
        line-height: 32px !important;
    }

    .el-textarea__inner {
        font-size: 13px !important;
    }

    .el-input__inner {
        height: 32px !important;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner,
    .secondFloat .el-input__inner {
        width: 200px;
    }

    .el-input__icon {
        font-size: 13px !important;
        line-height: 32px !important;
    }

    /* label */
    .el-form-item__label {
        font-size: 13px;
        line-height: 32px;
        padding: 0 8px 0 0;
    }

    /* select */
    .el-select-dropdown__item {
        font-size: 13px !important;
        padding: 5px 8px !important;
    }

    .el-select-group .el-select-dropdown__item {
        padding-left: 25px !important;
    }

    .el-select-group__title {
        line-height: 24px !important;
    }

    /* 排序框 */
    .el-input-number.is-controls-right {
        line-height: 32px;
    }

    .el-input-number.is-controls-right .el-input-number__decrease {
        bottom: 2px;
    }

    .el-input-number.is-controls-right .el-input-number__increase {
        top: 2px;
    }

    /*按钮 */
    .el-button {
        font-size: 13px;
        padding: 0 20px;
        height: 32px;
    }

    /*左右布局 列表信息 */
    .infoDetail .leftData>div .topButton .infoDetail .leftData>div .taskCenter,
    .infoDetail .fromRight .rightTitle,
    .infoDetail .fromRight .formTitle {
        font-size: 14px;
        height: 36px;
        line-height: 36px;
    }

    .infoDetail .leftData>div .topButton {
        min-height: 36px;
        height: auto;
    }

    .fromRight .el-form {
        /* height: calc(100% - 36px); */
    }

    /* el-tree */
    .el-tree,
    .el-tree-node__expand-icon {
        font-size: 13px;
    }

    /* el-dialog弹框 */
    .el-dialog__header,
    .el-message-box__header {
        padding: 12px 20px;
    }

    .el-dialog__title {
        font-size: 16px;
    }

    .el-dialog__headerbtn .el-dialog__close,
    .el-message-box__headerbtn .el-message-box__close {
        font-size: 16px;
    }

    /* 点击实物图放大 */
    .el-image-viewer__close {
        width: 32px !important;
        height: 32px !important;
        font-size: 16px !important;
    }

    .el-image-viewer__actions {
        width: 240px !important;
        height: 35px !important;
        padding: 0 16px !important;
    }

    .el-image-viewer__actions__inner {
        font-size: 19px !important;
    }

    .el-image-viewer__next,
    .el-image-viewer__prev {
        width: 35px !important;
        height: 35px !important;
        font-size: 16px !important;
    }

    .el-image-viewer__next {
        right: 32% !important;
    }

    .el-image-viewer__prev {
        left: 32% !important;
    }
}

@media screen and (max-width: 1280px) and (min-width: 1024px) {

    /* el-table */
    .el-table,
    .el-table thead {
        font-size: 12px;
    }

    .el-table .cell {
        /* height: 26px; */
        line-height: 26px;
    }
    .el-table .cell .rowEditShow,
    .el-table .cell .el-input__inner {
        height: 30px !important;
    }
    .el-table .cell,
    .el-table--border .el-table__cell:first-child .cell,
    .el-table th.el-table__cell>.cell {
        padding-left: 5px;
    }

    .el-table .cell,
    .el-table th.el-table__cell>.cell {
        padding-right: 5px;
    }

    .el-table .el-table__cell {
        padding: 3px 0;
    }

    /* 输入框 */
    /* .el-form-item {
        margin-bottom: 20px !important;
    } */

    .el-form-item__content,
    .el-input__inner {
        font-size: 12px !important;
        line-height: 30px !important;
    }

    .el-textarea__inner {
        font-size: 12px !important;
    }

    .el-input__inner {
        height: 30px !important;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner,
    .secondFloat .el-input__inner {
        width: 190px;
    }

    .el-input__icon {
        line-height: 30px !important;
        font-size: 12px !important;
    }

    /* label */
    .el-form-item__label {
        font-size: 12px;
        line-height: 30px;
        padding: 0 8px 0 0;
    }

    /* select */
    .el-select-dropdown__item {
        font-size: 12px !important;
        padding: 5px 8px !important;
    }

    .el-select-group .el-select-dropdown__item {
        padding-left: 22px !important;
    }

    .el-select-group__title {
        line-height: 22px !important;
    }

    /* 下拉框 */
    .el-dropdown-menu__item {
        padding: 8px 10px !important;
        font-size: 12px !important;
    }

    /* 排序框 */
    .el-input-number {
        line-height: 30px;
    }

    .el-input-number.is-controls-right .el-input-number__increase {
        top: 1px;
    }

    .el-input-number.is-controls-right .el-input-number__decrease {
        bottom: 1px;
    }

    /*按钮 */
    .el-button {
        font-size: 12px;
        padding: 0 18px;
        height: 30px;
    }

    /*左右布局 列表信息 */
    .infoDetail .leftData>div .topButton,
    .infoDetail .leftData>div .taskCenter,
    .infoDetail .fromRight .rightTitle,
    .infoDetail .fromRight .formTitle {
        font-size: 14px;
        height: 34px;
        line-height: 34px;
    }

    .infoDetail .leftData>div .topButton {
        min-height: 34px;
        height: auto;
    }

    .fromRight .el-form {
        /* height: calc(100% - 34px); */
    }

    /* el-tree */
    .el-tree,
    .el-tree-node__expand-icon {
        font-size: 12px;
    }

    /* el-dialog弹框 */
    .el-dialog__header,
    .el-message-box__header {
        padding: 10px 20px;
    }

    .el-dialog__headerbtn .el-dialog__close,
    .el-message-box__headerbtn .el-message-box__close {
        font-size: 15px;
    }

    /* 点击实物图放大 */
    .el-image-viewer__close {
        width: 30px !important;
        height: 30px !important;
        font-size: 16px !important;
    }

    .el-image-viewer__actions {
        width: 230px !important;
        height: 32px !important;
        padding: 0 15px !important;
    }

    .el-image-viewer__actions__inner {
        font-size: 18px !important;
    }

    .el-image-viewer__next,
    .el-image-viewer__prev {
        width: 32px !important;
        height: 32px !important;
        font-size: 16px !important;
    }

    .el-image-viewer__next {
        right: 30% !important;
    }

    .el-image-viewer__prev {
        left: 30% !important;
    }
}

@media screen and (max-width: 1024px) {

    /* el-table */
    .el-table,
    .el-table thead {
        font-size: 12px;
    }

    .el-table .cell {
        /* height: 24px; */
        line-height: 24px;
    }
    .el-table .cell .rowEditShow,
    .el-table .cell .el-input__inner {
        height: 30px !important;
    }
    .el-table .cell,
    .el-table--border .el-table__cell:first-child .cell,
    .el-table th.el-table__cell>.cell {
        padding-left: 5px;
    }

    .el-table .cell,
    .el-table th.el-table__cell>.cell {
        padding-right: 5px;
    }

    .el-table .el-table__cell {
        padding: 3px 0;
    }

    /* 输入框 */
    /* .el-form-item {
        margin-bottom: 20px !important;
    } */

    .el-form-item__content,
    .el-input__inner {
        font-size: 12px !important;
        line-height: 30px !important;
    }

    .el-textarea__inner {
        font-size: 12px !important;
    }

    .el-input__inner {
        height: 30px !important;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner,
    .secondFloat .el-input__inner {
        width: 190px;
    }

    .el-input__icon {
        line-height: 30px !important;
        font-size: 12px !important;
    }

    /* label */
    .el-form-item__label {
        font-size: 12px;
        line-height: 30px;
        padding: 0 8px 0 0;
    }

    /* select */
    .el-select-dropdown__item {
        font-size: 12px !important;
        padding: 5px 8px !important;
    }

    .el-select-group .el-select-dropdown__item {
        padding-left: 22px !important;
    }

    .el-select-group__title {
        line-height: 22px !important;
    }

    /* 下拉框 */
    .el-dropdown-menu__item {
        padding: 8px 10px !important;
        font-size: 12px !important;
    }

    /* 排序框 */
    .el-input-number {
        line-height: 30px;
    }

    .el-input-number.is-controls-right .el-input-number__increase {
        top: 1px;
    }

    .el-input-number.is-controls-right .el-input-number__decrease {
        bottom: 1px;
    }

    /*按钮 */
    .el-button {
        font-size: 12px;
        padding: 0 18px;
        height: 30px;
    }

    /*左右布局 列表信息 */
    .infoDetail .leftData>div .topButton,
    .infoDetail .leftData>div .taskCenter,
    .infoDetail .fromRight .rightTitle,
    .infoDetail .fromRight .formTitle {
        font-size: 14px;
        height: 32px;
        line-height: 32px;
    }

    .infoDetail .leftData>div .topButton {
        min-height: 32px;
        height: auto;
    }

    .fromRight .el-form {
        /* height: calc(100% - 32px); */
    }

    /* el-tree */
    .el-tree,
    .el-tree-node__expand-icon {
        font-size: 12px;
    }

    /* el-dialog弹框 */
    .el-dialog__header,
    .el-message-box__header {
        padding: 10px 20px;
    }

    .el-dialog__headerbtn .el-dialog__close,
    .el-message-box__headerbtn .el-message-box__close {
        font-size: 15px;
    }

    .el-dialog__headerbtn .el-dialog__close,
    .el-message-box__headerbtn .el-message-box__close {
        font-size: 15px;
    }

    /* 点击实物图放大 */
    .el-image-viewer__close {
        width: 30px !important;
        height: 30px !important;
        font-size: 16px !important;
    }

    .el-image-viewer__actions {
        width: 230px !important;
        height: 32px !important;
        padding: 0 15px !important;
    }

    .el-image-viewer__actions__inner {
        font-size: 18px !important;
    }

    .el-image-viewer__next,
    .el-image-viewer__prev {
        width: 32px !important;
        height: 32px !important;
        font-size: 16px !important;
    }

    .el-image-viewer__next {
        right: 28% !important;
    }

    .el-image-viewer__prev {
        left: 28% !important;
    }
}

.el-upload-list__item-name [class^=el-icon] {
    margin-right: 0px;
}

.el-icon-document::before {
    content: "";
    display: inline-block;
    vertical-align: middle;
    margin-top: -3px;
}

.el-icon-document.jpg::before,
.el-icon-document-jpg::before {
    background: url("../image/fileUploadIoc/smallIoc/jpg.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

.el-icon-document.png::before,
.el-icon-document-png::before {
    background: url("../image/fileUploadIoc/smallIoc/png.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

.el-icon-document.mp3::before,
.el-icon-document-mp3::before {
    background: url("../image/fileUploadIoc/smallIoc/mp3.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

.el-icon-document.mp4::before,
.el-icon-document-mp4::before {
    background: url("../image/fileUploadIoc/smallIoc/mp4.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

.el-icon-document.xls::before,
.el-icon-document.xlsx::before,
.el-icon-document-xls::before,
.el-icon-document-xlsx::before {
    background: url("../image/fileUploadIoc/smallIoc/xls.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

.el-icon-document.doc::before,
.el-icon-document.docx::before,
.el-icon-document-docx::before,
.el-icon-document-doc::before {
    background: url("../image/fileUploadIoc/smallIoc/doc.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

.el-icon-document.zip::before,
.el-icon-document-zip::before {
    background: url("../image/fileUploadIoc/smallIoc/zip.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

.el-icon-document.pdf::before,
.el-icon-document-pdf::before {
    background: url("../image/fileUploadIoc/smallIoc/pdf.png") no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    margin-right: 7px;
}

/* 节点图标 */
.elTreeStyle .el-tree-node__content {
    height: 30px !important;
    line-height: 30px !important;
}

.elTreeStyle .el-tree .el-checkbox__inner {
    width: 15px;
    height: 15px;
    margin-top: -1px;
    margin-right: 3px;
}

.elTreeStyle .el-tree .el-checkbox__inner::after {
    border: 2px solid #fff;
    border-left: 0;
    border-top: 0;
    height: 6px !important;
    left: 4px !important;
    width: 3px !important;
}

/* 有子节点 未展开 */
/* 父节点 无子节点 */
.elTreeStyle .is-leaf.el-tree-node__expand-icon.el-icon-caret-right::before,
.elTableSpecialStyle .el-table__placeholder::before {
    content: "";
    background: transparent;
}

.elTreeStyle .noChildNode .is-leaf.el-tree-node__expand-icon.el-icon-caret-right::after,
.elTreeStyle .el-tree-node__expand-icon.el-icon-caret-right::before,
.elTreeStyle .el-tree-node__expand-icon.el-icon-caret-right::after,
.elTableSpecialStyle .el-table__expand-icon .el-icon-arrow-right::before,
.elTableSpecialStyle .el-table__expand-icon .el-icon-arrow-right::after,
.elTableSpecialStyle .directoryIcon::after {
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    background: url("../image/icon/expandIcon.png") no-repeat center;
    background-size: 100% 100%;
    margin-right: 2px;
    margin-top: -3px;
    vertical-align: middle;
}

.elTreeStyle .noChildNode .is-leaf.el-tree-node__expand-icon.el-icon-caret-right::after,
.elTreeStyle .noChildIcon::after,
.elTreeStyle .el-tree-node__expand-icon.el-icon-caret-right::after,
.elTableSpecialStyle .directoryIcon::after,
.elTableSpecialStyle .el-table__expand-icon .el-icon-arrow-right::after {
    width: 15px;
    height: 15px;
    background: url("../image/icon/treeFold.png") no-repeat center;
    background-size: 100% 100%;
    margin-right: 0px;
    margin-top: -4px
}

.elTableSpecialStyle .directoryIcon::after {
    margin-left: 16px;
    margin-right: 2px;
}

.elTableSpecialStyle .el-table__expand-icon--expanded {
    transform: none !important;
}

/* 有子节点 也展开 */
.elTreeStyle .expanded.el-tree-node__expand-icon.el-icon-caret-right::before,
.elTreeStyle .expanded.el-tree-node__expand-icon.el-icon-caret-right::after,
.elTableSpecialStyle .el-table__expand-icon.el-table__expand-icon--expanded .el-icon-arrow-right::before,
.elTableSpecialStyle .el-table__expand-icon.el-table__expand-icon--expanded .el-icon-arrow-right::after {
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    background: url("../image/icon/collapseIcon.png") no-repeat center;
    background-size: 100% 100%;
    margin-right: 2px;
    vertical-align: middle;
    margin-top: -5px;
}

.elTreeStyle .expanded.el-tree-node__expand-icon.el-icon-caret-right::after,
.elTableSpecialStyle .el-table__expand-icon.el-table__expand-icon--expanded .el-icon-arrow-right::after,
.elTableSpecialStyle .el-table__expand-icon.el-table__expand-icon--expanded .el-icon-arrow-right::after {
    width: 15px;
    height: 15px;
    background: url("../image/icon/treeOpen.png") no-repeat center;
    background-size: 100% 100%;
    margin-right: 0px;
    margin-top: -6px
}

.elTableSpecialStyle .el-table__placeholder {
    width: 0;
    margin-left: 0px;
}

/* 子节点 */
.elTreeStyle .el-tree-node__expand-icon.expanded {
    transform: rotate(0);
}

.elTreeStyle .is-leaf.el-tree-node__expand-icon.el-icon-caret-right::after,
.elTableSpecialStyle .chapterIcon::before {
    content: "";
    display: inline-block;
    width: 15px;
    height: 15px;
    background: url("../image/icon/chapterIcon.png") no-repeat center;
    background-size: 100% 100%;
    vertical-align: middle;
    margin-top: -4px;
    margin-right: 3px;
}

.elTableSpecialStyle .synopsisText {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.elTableSpecialStyle .el-table .el-table__cell {
    padding: 0;
}

.elTableSpecialStyle .el-table .cell {
    height: 30px;
    line-height: 30px;
}

.elTableSpecialStyle .el-table td.el-table__cell {
    border: none;
}

.elTableSpecialStyle .el-table .cell .el-table__expand-icon {
    width: 30px;
    height: 30px !important;
    line-height: 30px !important;
    vertical-align: middle;
    margin-right: 2px !important;
}

.elTableSpecialStyle .el-table--border::after,
.elTableSpecialStyle .el-table--group::after,
.elTableSpecialStyle .el-table::before {
    background: transparent;
}

.elTableSpecialStyle .el-table--border,
.elTableSpecialStyle .el-table--group {
    border: none;
}

.elTableSpecialStyle .el-table .el-table__body .el-table__row.current-row,
.elTableSpecialStyle .el-table__body tr.current-row:hover>td.el-table__cell,
.elTableSpecialStyle .el-table .el-table__body .el-table__row.current-row td {
    background: none !important;
}
