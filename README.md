# 润达ERP后台管理系统

## 项目概述

这是一个基于Vue.js和Element UI的企业资源规划(ERP)后台管理系统，提供完整的业务流程管理功能。

## 功能模块

### 生产管理模块 (MPS)

#### 生产任务单管理
- **列表页面**: `src/views/mps/taskOrder/list.vue`
- **功能特性**:
  - 支持多条件查询（单据日期、客户名称、销售订单号、状态等）
  - 表格排序功能（支持以下字段排序）:
    - 任务单号 (taskNo)
    - 单据日期 (taskDate)
    - 商品编码 (productCode)
    - 订购数量 (orderQty)
    - 计划生产数 (plannedProdQty)
    - 计划开工时间 (plannedStartTime)
    - 计划完工时间 (plannedEndTime)
    - 状态 (status)
  - 批量操作（审核、作废、删除等）
  - 导出功能（支持基础导出和包含物料明细的导出）
  - 物料管理（登记、追踪）

#### 生产领料单管理
- **列表页面**: `src/views/mps/issue/list.vue`
- **功能特性**:
  - 支持多条件查询（单据编号、领料时间、领料仓库、物料编码等）
  - 表格排序功能（支持以下字段排序）:
    - 领料单号 (issueNo)
    - 领料日期 (issueTime)
    - 源单据号 (sourceNo)
    - 状态 (status)
    - 审核时间 (auditTime)
    - 制单时间 (createdTime)
  - 批量操作（审核、作废、删除等）
  - 导出功能
  - 退料功能

#### 生产退料单管理
- **列表页面**: `src/views/mps/return/list.vue`
- **功能特性**:
  - 支持多条件查询（单据编号、退料时间、退料仓库、物料编码等）
  - 表格排序功能（支持以下字段排序）:
    - 退料单号 (returnNo)
    - 退料日期 (returnTime)
    - 源单据号 (sourceNo)
    - 状态 (status)
    - 审核时间 (auditTime)
    - 制单时间 (createdTime)
  - 批量操作（审核、作废、删除等）
  - 导出功能

## 排序功能实现

### 技术实现
- 使用Element UI的`el-table`组件的`sortable="custom"`属性
- 监听`@sort-change`事件处理排序变化
- 通过API参数`sortField`和`asc`传递排序信息到后端

### 排序流程
```mermaid
graph TD
    A[用户点击表头排序] --> B[触发sort-change事件]
    B --> C[handleSortChange方法处理]
    C --> D{是否有排序方向?}
    D -->|是| E[设置sortField和asc参数]
    D -->|否| F[清除排序参数]
    E --> G[调用handleSearch方法]
    F --> G
    G --> H[构建包含排序参数的请求]
    H --> I[调用API接口]
    I --> J[更新表格数据]
```

### API参数说明
```javascript
{
  query: {
    // 查询条件...
  },
  current: 1,        // 当前页码
  size: 10,          // 每页大小
  sortField: "taskNo", // 排序字段
  asc: true          // 排序方向 (true: 升序, false: 降序)
}
```

## 审核驳回功能实现

### 通用组件设计
创建了 `AuditRejectDialog` 通用组件，用于处理各种单据的审核驳回功能。

#### 组件特性
- **高度可配置**：支持自定义标题、单据标签、文件标识等
- **完整的文件上传功能**：支持拖拽上传、文件预览、删除等
- **表单验证**：驳回原因为必填项
- **加载状态**：提交时显示加载状态
- **事件回调**：支持成功、关闭等事件回调

#### 组件使用示例
```vue
<AuditRejectDialog
  :visible.sync="rejectDialogVisible"
  :document-id="rejectDocumentId"
  :document-no="rejectDocumentNo"
  document-label="任务单号"
  title="审核驳回"
  file-flag="taskOrder"
  :reject-api="handleRejectTaskOrder"
  @success="handleSearch"
  @close="handleSearch"
/>
```

#### 组件参数说明
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| visible | Boolean | 是 | false | 是否显示对话框 |
| documentId | String/Number | 是 | '' | 单据ID |
| documentNo | String | 是 | '' | 单据号 |
| documentLabel | String | 否 | '单据号' | 单据号标签 |
| title | String | 否 | '审核驳回' | 对话框标题 |
| fileFlag | String | 否 | 'document' | 文件标识 |
| rejectApi | Function | 是 | - | 驳回API方法 |

#### 组件事件
| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| success | 驳回成功时触发 | - |
| close | 对话框关闭时触发 | - |

### 重构效果
- **代码量减少**：每个页面减少了约200行重复代码
- **维护性提升**：统一的驳回逻辑，修改一处即可影响所有模块
- **一致性保证**：所有模块的驳回功能体验完全一致
- **扩展性增强**：新增模块只需引入组件即可

## 开发规范

### 代码规范
- 使用中文注释
- 方法名使用英文命名
- 遵循SOLID原则
- 使用多行注释格式

### 文件结构
```
src/
├── views/           # 页面组件
│   ├── mps/        # 生产管理模块
│   │   ├── taskOrder/  # 生产任务单
│   │   └── issue/      # 生产领料单
├── components/      # 公共组件
├── api/            # API接口
└── assets/         # 静态资源
```

## 更新日志

### 2024-12-19
- ✅ 完成生产管理模块的排序功能实现
  - 生产任务单列表：支持任务单号、单据日期、商品编码、订购数量、计划生产数、计划开工时间、计划完工时间、状态字段排序
  - 生产领料单列表：支持领料单号、领料日期、源单据号、状态、审核时间、制单时间字段排序
  - 生产退料单列表：支持退料单号、退料日期、源单据号、状态、审核时间、制单时间字段排序，并将表格列宽属性从width改为min-width
- ✅ 完成生产管理模块的审核驳回功能实现
  - 生产任务单列表：支持单个任务单驳回，包含驳回原因和附件上传
  - 生产领料单列表：支持单个领料单驳回，包含驳回原因和附件上传
  - 生产退料单列表：支持单个退料单驳回，包含驳回原因和附件上传
  - 修改API参数：将批量驳回改为单个驳回，支持附件列表参数
- ✅ 完成审核驳回功能的组件抽象化
  - 创建通用组件 `AuditRejectDialog`：支持自定义标题、单据标签、文件标识等配置
  - 重构生产管理模块：将重复的驳回逻辑抽象到通用组件中
  - 代码优化：每个页面减少约200行重复代码，提高维护性和一致性
  - 向后兼容：采购模块保持原有实现，不影响现有功能

### 2024-01-XX
- 新增生产任务单表格排序功能
- 新增生产领料单表格排序功能
- 新增生产退料单表格排序功能
- 优化查询参数传递机制
- 完善重置功能，支持清除排序状态
- 统一表格列宽设置，使用min-width替代width
