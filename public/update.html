<!doctype html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title></title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body, html, .container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .container > div {
            width: 70%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -60%);
            font-size: 22px;
            line-height: 1.6;
            margin: auto;
            text-align: center;
        }

        .container div .top {
            width: 160px;
            margin: auto
        }

        .container div .top-text {
            color: red;
            margin: 12px 0;
            font-size: 25px;
        }

        .container div .text {
            margin: 12px 0;
        }

        .container div .textTips {
            margin: 12px 0 25px;
        }

        .container div a {
            display: inline-block;
            text-decoration: none;
            width: 120px;
            margin: 0 20px;
        }

        .container div div img {
            width: 100%;
        }

        @media screen and (max-width: 1600px) and (min-width: 1440px) {
            .container > div {
                font-size: 20px;
            }

            .container div .top {
                width: 130px;
            }

            .container div .top-text {
                margin: 15px 0;
                font-size: 22px;
            }

            .container div .text {
                margin: 15px 0;
            }

            .container div a {
                width: 90px;
            }
        }

        @media screen and (max-width: 1440px) and (min-width: 1366px) {
            .container > div {
                font-size: 20px;
            }

            .container div .top {
                width: 130px;
            }

            .container div .top-text {
                margin: 10px 0;
                font-size: 22px;
            }

            .container div .text {
                margin: 10px 0;
            }

            .container div .textTips {
                margin: 10px 0 20px;
            }

            .container div a {
                width: 90px;
            }
        }

        @media screen and (max-width: 1366px) and (min-width: 1280px) {
            .container > div {
                font-size: 16px;
            }

            .container div .top {
                width: 110px;
            }

            .container div .top-text {
                margin: 8px 0;
                font-size: 18px;
            }

            .container div .text {
                margin: 8px 0;
            }

            .container div .textTips {
                margin: 8px 0 20px;
            }

            .container div a {
                width: 80px;
            }
        }

        @media screen and (max-width: 1280px) and (min-width: 1024px) {
            .container > div {
                font-size: 16px;
            }

            .container div .top {
                width: 110px;
            }

            .container div .top-text {
                margin: 8px 0;
                font-size: 18px;
            }

            .container div .text {
                margin: 8px 0;
            }

            .container div .textTips {
                margin: 8px 0 20px;
            }

            .container div a {
                width: 80px;
            }
        }

        @media screen and (max-width: 1024px) {
            .container > div {
                font-size: 16px;
            }

            .container div .top {
                width: 110px;
            }

            .container div .top-text {
                margin: 8px 0;
                font-size: 18px;
            }

            .container div .text {
                margin: 8px 0;
            }

            .container div .textTips {
                margin: 8px 0 20px;
            }

            .container div a {
                width: 80px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div>
        <img src="static/imgs/update/oops.png" alt="error" class="top">
        <div class="top-text">对不起，当前使用的浏览器不兼容，请切换至推荐浏览器</div>
        <div class="text">
            <font color="red">*提示：</font>
            推荐使用以下浏览器访问本系统： Chrome、Firefox、Edge， 其他浏览器可能会出现错误。
        </div>
        <div class="textTips">建议您下载以下浏览器的最新版本以获得更好的体验</div>
        <div>
            <a href="https://www.google.cn/chrome/">
                <img src="static/imgs/update/chrome.png" alt="">
            </a>
            <!-- <a href="https://browser.qq.com/"><img class="icon" src="static/imgs/update/QQ.png" alt=""></a>
            <a href="https://browser.360.cn/"><img class="icon" src="static/imgs/update/360.png" alt=""></a> -->
            <a href="https://www.firefox.com.cn/">
                <img src="static/imgs/update/firefox.png" alt=""/>
            </a>
            <a href="https://www.microsoft.com/zh-cn/edge">
                <img src="static/imgs/update/edge.png" alt=""/>
            </a>
        </div>
    </div>
</div>
</body>
</html>
