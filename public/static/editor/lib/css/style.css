:root {
    --border-color: #ddd;
    --border-radius: 5px;
    --color: #000;
}

[v-cloak] {
    display: none !important;
}

body, html, .container, .simflexContent {
    width: calc(100% + 0.2px);
    height: 100%;
    position: relative;
    overflow: hidden;
}

#app {

    padding: 0;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.simflexContent {
    display: flex;
}

.simflexContent .simflex-editor
.simflex-editor > .context-menu,
.tabTitle,
.attributeArea .title,
.attributeArea .nodeName,
.attributeContent table {
    font-size: 16px;
}

input, select, textarea {
    font-size: 15px;
}

/* 编辑区域 */
.simflexContent .simflex-editor {
    flex: 1;
    position: relative;
    height: 100%;
    line-height: 1.5;
    background-color: white;
    color: var(--color);
    border-right: 1px solid var(--border-color);
    text-align: left;
    overflow: hidden;
}

.simflex-editor * {
    box-sizing: border-box;
}

/* 全屏 */
/* nav tab */
.simflex-editor > .toolbar {
    position: relative;
    background-color: inherit;
}

.simflex-editor > .toolbar {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 文字选择效果 */
::selection {
    background: #d8dbe1;
}

::-moz-selection {
    background: #d8dbe1;
}

::-webkit-selection {
    background: #d8dbe1;
}

/* .simflex-editor > .content tag::selection{
  background: #d8dbe1;
}

.simflex-editor > .content tag table tr th::selection,
.simflex-editor > .content tag table tr td::selection,
.simflex-editor > .content tag b::selection,
.simflex-editor > .content tag i::selection,
.simflex-editor > .content tag u::selection,
.simflex-editor > .content tag sup::selection,
.simflex-editor > .content tag sub::selection,
.simflex-editor > .content tag span::selection,
.simflex-editor > .content tag img::selection,
.simflex-editor > .content tag br::selection,
.simflex-editor > .content tag font::selection{
  background: #d8dbe1;
}
.simflex-editor > .content tag table tr th::-moz-selection,
.simflex-editor > .content tag table tr td::-moz-selection,
.simflex-editor > .content tag b::-moz-selection,
.simflex-editor > .content tag i::-moz-selection,
.simflex-editor > .content tag u::-moz-selection,
.simflex-editor > .content tag sup::-moz-selection,
.simflex-editor > .content tag sub::-moz-selection,
.simflex-editor > .content tag span::-moz-selection,
.simflex-editor > .content tag img::-moz-selection,
.simflex-editor > .content tag br::-moz-selection,
.simflex-editor > .content tag font::-moz-selection{
  background: #d8dbe1;
}
.simflex-editor > .content tag table tr th::-webkit-selection,
.simflex-editor > .content tag table tr td::-webkit-selection,
.simflex-editor > .content tag b::-webkit-selection,
.simflex-editor > .content tag i::-webkit-selection,
.simflex-editor > .content tag u::-webkit-selection,
.simflex-editor > .content tag sup::-webkit-selection,
.simflex-editor > .content tag sub::-webkit-selection,
.simflex-editor > .content tag span::-webkit-selection,
.simflex-editor > .content tag img::-webkit-selection,
.simflex-editor > .content tag br::-webkit-selection,
.simflex-editor > .content tag font::-webkit-selection{
  background: #d8dbe1;
} */
/* .simflex-editor > .content tag table tr th::-webkit-selection,
.simflex-editor > .content tag table tr td::-webkit-selection,
.simflex-editor > .content tag table tr th::selection,
.simflex-editor > .content tag table tr td::selection{
  background: transparent;
} */
/* .simflex-editor > .content tag table tr td tag::selection{
  background: red;
} */

/*菜单中的输入框大小*/
.input_num {
    width: 50px;
}

.simflex-editor > .toolbar > ul {
    list-style: none;
    padding: 6px 4px;
    margin: 0;
    border-bottom: 1px solid var(--border-color);
}

.attributeArea ul,
.attributeArea ol {
    margin-left: 20px;
}

.simflex-editor > .toolbar > ul > li {
    display: inline-block;
    cursor: pointer;
    text-align: center;
    margin: 0 8px;
}

.simflex-editor > .toolbar > ul > li .icon {
    display: inline-block;
    vertical-align: middle;
    width: 38px;
    height: 38px;
    background-size: 20px;
}

.iconClick {
    background-color: #eeeeee !important;
    vertical-align: middle !important;
    border-radius: 4px !important;
}

.simflex-editor > .toolbar > ul > li .icon:hover {
    background-color: #eeeeee;
    vertical-align: middle;
    border-radius: 4px;
}

/* .simflex-editor > .toolbar > ul > li .icon[type='disabled'] {
  opacity: 0.5;
}
.simflex-editor > .toolbar > ul > li .icon[type='disabled']:hover {
  background-color: transparent;
  pointer-events: none;
  cursor: not-allowed !important;
} */
.simflex-editor > .toolbar > ul > li .icon[data-type='disabled'] {
    opacity: 0.5;
}

.simflex-editor > .toolbar > ul > li .icon[data-type='disabled']:hover {
    background-color: transparent;
    pointer-events: none;
    cursor: not-allowed !important;
}

/* nav图片 */
/* 撤销 */
.simflex-editor > .toolbar .pervIcon {
    background: url("../image/revokeIcon.png") no-repeat center;
}

/* 恢复 */
.simflex-editor > .toolbar .nextIcon {
    background: url("../image/redoIocn.png") no-repeat center;
}

/* 字体颜色 */
.simflex-editor > .toolbar .colorIcon {
    background: url("../image/textColor.png") no-repeat center;
}

/* 加粗 */
.simflex-editor > .toolbar .boldIcon {
    background: url("../image/fontBold.png") no-repeat center;
}

/* 斜体 */
.simflex-editor > .toolbar .italicIcon {
    background: url("../image/fontItalics.png") no-repeat center;
}

/* 下划线 */
.simflex-editor > .toolbar .underlineIcon {
    background: url("../image/underline.png") no-repeat center;
}

/* 上标 */
.simflex-editor > .toolbar .supIcon {
    background: url("../image/insertSup.png") no-repeat center;
}

/* 下标 */
.simflex-editor > .toolbar .subIcon {
    background: url("../image/insertSub.png") no-repeat center;
}

/* 常用字符 */
.simflex-editor > .toolbar .charIcon {
    background: url("../image/charIcon.png") no-repeat center;
}

/* 无序列表 */
.simflex-editor > .toolbar .ulListIcon {
    background: url("../image/ulListIocn.png") no-repeat center;
}

/* 有序列表 */
.simflex-editor > .toolbar .olListIcon {
    background: url("../image/olListIcon.png") no-repeat center;
}

/* 对齐方式 */
.simflex-editor > .toolbar .alignIcon {
    background: url("../image/alignIcon.png") no-repeat center;
}

/* 表格 */
.simflex-editor > .toolbar .tableIcon {
    background: url("../image/insertTable.png") no-repeat center;
}

/* 图片 */
.simflex-editor > .toolbar .imageIcon {
    background: url("../image/addImg.png") no-repeat center;
}

/* 链接 */
.simflex-editor > .toolbar .insertLinkIcon {
    background: url("../image/InsertLink.png") no-repeat center;
}

/* 断开链接 */
.simflex-editor > .toolbar .unlinkIcon {
    background: url("../image/unlinkIcon.png") no-repeat center;
}

/* dita */
.simflex-editor > .toolbar .ditaIcon {
    background: url("../image/ditaIcon.png") no-repeat center;
}

/* html */
.simflex-editor > .toolbar .htmlIcon {
    background: url("../image/htmlIcon.png") no-repeat center;
}

/* 搜索 */
.simflex-editor > .toolbar .searchIcon {
    background: url("../image/searchIcon.png") no-repeat center;
}

.simflex-editor > .toolbar > ul .completeStatus {
    position: absolute;
    right: 0;
    bottom: 5px;
}

.simflex-editor > .toolbar > ul .completeStatus span {
    display: inline-block;
    vertical-align: middle;
    width: 38px;
    height: 38px;
    line-height: 38px;
    display: none;
}

/* 已完成 未完成*/
.simflex-editor > .toolbar .completeStatus span img {
    max-width: 100%;
}

.simflex-editor > .toolbar > ul > .T_nextIcon::after,
.simflex-editor > .toolbar > ul > .T_charIcon::after,
.simflex-editor > .toolbar > ul > .T_alignIcon::after,
.simflex-editor > .toolbar > ul > .T_imageIcon::after,
.simflex-editor > .toolbar > ul > .T_unlinkIcon::after,
.simflex-editor > .toolbar > ul > .T_ditaIcon::after {
    content: "";
    display: inline-block;
    width: 2px;
    height: 25px;
    margin: 0 0px 0 16px;
    color: #ddd;
    background: #ddd;
    vertical-align: middle;
}

.searchHandle {
    position: absolute;
    right: 15px;
    top: 110%;

}

/* bar下拉内容 */
.simflex-editor > .toolbar > .dashboard {
    width: 345px;
    position: absolute;
    top: 99%;
    left: 0;
    right: 0;
}

.simflex-editor > .toolbar > .dashboard input[type='text'],
.simflex-editor > .toolbar > .dashboard input[type='number'],
.simflex-editor > .toolbar > .dashboard select {
    padding: 6px 12px;
    color: inherit;
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

select {
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: url("../image/arrowBottom.png") no-repeat 96% center transparent;
}

.simflex-editor > .toolbar > .dashboard input[type='text']:hover,
.simflex-editor > .toolbar > .dashboard input[type='number']:hover,
.simflex-editor > .toolbar > .dashboard select:hover {
    border-color: color(var(--border-color) blackness(30%));
}

.simflex-editor > .toolbar > .dashboard input[type='text'][disabled],
.simflex-editor > .toolbar > .dashboard input[type='text'][readonly],
.simflex-editor > .toolbar > .dashboard input[type='number'][disabled],
.simflex-editor > .toolbar > .dashboard input[type='text'][readonly],
.simflex-editor > .toolbar > .dashboard select[disabled],
.simflex-editor > .toolbar > .dashboard select[readonly] {
    background-color: #eee;
    opacity: 1;
}

.simflex-editor > .toolbar > .dashboard input[type='text'][disabled],
.simflex-editor > .toolbar > .dashboard input[type='number'][disabled],
.simflex-editor > .toolbar > .dashboard select[disabled] {
    cursor: not-allowed;
}

.simflex-editor > .toolbar > .dashboard button,
.styleContent button {
    color: inherit;
    background-color: inherit;
    padding: 6px 12px;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    margin-right: 4px;
}

.styleContent button {
    padding: 8px 6%;
    margin: 0 15px;
}

.simflex-editor > .toolbar > .dashboard button:hover {
    border-color: color(var(--border-color) blackness(30%));
}

.simflex-editor > .toolbar > .dashboard button[disabled] {
    cursor: not-allowed;
    opacity: .68;
}

.simflex-editor > .toolbar > .dashboard button:last-child {
    margin-right: 0;
}

.simflex-editor > .toolbar > .dashboard input,
.simflex-editor > .toolbar > .dashboard button,
.simflex-editor > .toolbar > .dashboard select {
    line-height: normal;
}

.simflex-editor > .toolbar > .dashboard label {
    font-weight: bolder;
}

/* 编辑内容区域 */
.simflex-editor > .content {
    overflow: auto;
    padding: 10px;
}

.simflex-editor > .content:focus {
    outline: 0;
}

.simflex-editor ul {
    list-style: disc;
    margin-left: 20px;
}

.simflex-editor ol {
    list-style: decimal;
    margin-left: 20px;
}

.simflex-editor a {
    text-decoration: underline;
}

/* tag标签 */
.simflex-editor > .content tag,
.tag {
    font-family: Verdana, Microsoft YaHei, SimSun, Arial, sans-serif;
    display: block;
    /* padding: 0px 0px 0px 15px; */
    margin: 0px 0px 0px 15px;
    word-break: break-word;
}

.simflex-editor > .content table tag {
    /* padding: 0px 0px 0px 5px; */
    margin: 0px 0px 0px 10px;
}

.simflex-editor > .content tag::before, .tag::before,
.simflex-editor > .content tag::after, .tag::after {
    height: 15px;
    line-height: 15px;
    margin: 0px 2px;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: normal;
    text-decoration: none;
    font-style: normal;
    font-family: "Bitstream Vera Sans", "Helvetica", "Verdana", sans-serif;
    white-space: nowrap;
    background-size: auto 100%;
    vertical-align: middle;
    margin-top: -2px;
}

.simflex-editor > .content tag::before, .tag::before {
    display: inline;
    content: attr(tagname) attr(tagtype) attr(tagtypeid);
    background-repeat: no-repeat;
    background-image: url(../image/tag.png);
    background-position: right;
    padding: 2px 11px 2px 7px;
}

.simflex-editor > .content tag::after, .tag::after {
    content: attr(tagname);
    background-repeat: no-repeat;
    background-image: url(../image/tag.png);
    background-position: left;
    padding: 2px 7px 2px 11px;
}

.simflex-editor > .content tag[tagname="instruction"]::before, .tag::before,
.simflex-editor > .content tag[tagname="instruction"]::after, .tag::after {
    background-image: url(../image/tagTwo.png);
}

.simflex-editor > .content tag img, img {
    max-width: 65%;
}

.simflex-editor > .content table, table {
    width: 100%;
    margin: 2px 0;
    table-layout: fixed;
}

table th, table td {
    outline: none;
}

.simflex-editor > .content table thead,
table thead {
    background-color: #e9e9e9;
}

.simflex-editor > .content table th,
table th {
    color: #333;
    font-weight: bold;
}

.simflex-editor > .content table th,
.simflex-editor > .content table td,
table th,
table td {
    border: 1px solid #555;
    padding: 6px 3px;
}

/* 底部 元素路径*/
.simflex-editor > .status-bar,
.attributeArea .status-bar {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: inherit;
    border: 1px solid var(--border-color);;
    border-left: none;
    border-right: none;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    line-height: 1.5;
}

.attributeArea .status-bar {
    width: calc(100% - 20px);
}

.attributeArea .status-bar {
    border-bottom: 1px solid transparent;
}

.simflex-editor > .status-bar div:first-child {
    flex: 1;
}

.simflex-editor > .status-bar div .pathText a {
    text-decoration: underline;
    color: #3030ff;
    cursor: pointer;
}

/* menu图片 */
.simflex-editor > .context-menu {
    font-size: 16px;
    position: absolute;
    top: 105%;
    left: 105%;
    z-index: 1001;
}

.simflex-editor > .context-menu .menu-item ul,
.threeMenu-item ul {
    border-radius: 5px;
    margin: 0;
    padding: 5px 0;
    list-style: none;
    background-color: #fff;
    box-shadow: 0 0 14px 1px rgba(102, 102, 102, 0.34);
}

.simflex-editor > .context-menu .menu-item li {
    margin: 0;
    padding: 10px 15px 10px 20px;
    list-style: none;
    cursor: pointer;
    line-height: normal;
    position: relative;
}

.simflex-editor > .context-menu .menu-item li span {
    white-space: nowrap;
    padding-right: 10px;
}

.simflex-editor > .context-menu .menu-item li .twoMenu-item,
.simflex-editor > .context-menu .menu-item li .twoMenu-item .threeMenu-item {
    position: absolute;
    left: calc(100% - 5px);
    top: -5px;
    z-index: 10;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] {
    padding: 0;
    border-bottom: 1px solid #dadce0;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item {
    position: relative;
    left: auto;
    top: 0;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item > ul {
    background: none
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul li[type="disabled"],
.simflex-editor > .context-menu .menu-item li[state='disabled'] {
    color: #a7a2a2;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul li[type="disabled"] span::before,
.simflex-editor > .context-menu .menu-item li[state='disabled'] span::before {
    opacity: 0.4;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul li[type="disabled"],
.simflex-editor > .context-menu .menu-item li[state='disabled'] {
    background: transparent;
    cursor: not-allowed !important;
    pointer-events: none;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul {
    box-shadow: none;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul li {
    padding: 10px 15px 10px 20px;
    min-width: 170px;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick']:last-child {
    border-bottom: none;
}

.simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul .threeMenu-item ul {
    box-shadow: 0 0 14px 1px rgba(102, 102, 102, 0.34);
}

.simflex-editor > .context-menu .menu-item ul li p {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.simflex-editor > .context-menu .menu-item ul li p img {
    max-width: none;
    margin-left: 25px;
}

.simflex-editor > .context-menu .menu-item .ul,
.simflex-editor > .context-menu .menu-item .ol {
    margin: 0;
    padding: 0;
}

/* 菜单 */
.simflex-editor > .context-menu .menu-item ul li p span::before,
.simflex-editor > .context-menu .menu-item ul li[type='rightClick'] .twoMenu-item span::before {
    display: inline-block;
    content: "";
    width: 18px;
    height: 18px;
    margin-top: -3px;
    margin-right: 10px;
    vertical-align: middle;
}

.simflex-editor > .context-menu .menu-item .twoMenu-item li {
    /* padding: 7px 10px */
}

.simflex-editor > .context-menu .menu-item .twoMenu-item ul li p span::before {
    width: 0;
    height: 0;
    display: none;
}

/* enter菜单 */
/* global */
/* 标题 */
.simflex-editor > .context-menu .menu-item .title::before {
    background: url("../image/title.png") no-repeat;
    background-size: 100%;
}

/* 摘要 */
.simflex-editor > .context-menu .menu-item .abstract::before {
    background: url("../image/abstract.png") no-repeat;
    background-size: 100%;
}

/* 主体 */
.simflex-editor > .context-menu .menu-item .body::before {
    background: url("../image/body.png") no-repeat;
    background-size: 100%;
}

/* aboustract */
/* 简要 */
.simflex-editor > .context-menu .menu-item .shortdesc::before {
    background: url("../image/shortdescIcon.png") no-repeat;
    background-size: 100%;
}

/* 索引名称 */
.simflex-editor > .context-menu .menu-item .indexterm::before {
    background: url("../image/indexName.png") no-repeat;
    background-size: 100%;
}

/* indexterm */
/* 关键字 */
.simflex-editor > .context-menu .menu-item .keyword::before {
    background: url("../image/keywordIcon.png") no-repeat;
    background-size: 100%;
}

/* 排序字段 */
.simflex-editor > .context-menu .menu-item .index-sort-as::before {
    background: url("../image/sortField.png") no-repeat;
    background-size: 100%;
}

/* body */
/* 章节 */
.simflex-editor > .context-menu .menu-item .section::before {
    background: url("../image/chapter.png") no-repeat;
    background-size: 100%;
}

/* section div*/
/* 段落 */
.simflex-editor > .context-menu .menu-item .p::before {
    background: url("../image/paragraphIocn.png") no-repeat;
    background-size: 100%;
}

.simflex-editor > .context-menu .menu-item .a::before {
    background: url("../image/InsertLink.png") no-repeat;
    background-size: 100%;
}

/* 注释 */
.simflex-editor > .context-menu .menu-item .note::before {
    background: url("../image/notesIocn.png") no-repeat;
    background-size: 100%;
}

/* 无序列表 */
.simflex-editor > .context-menu .menu-item .ul::before {
    background: url("../image/ulList.png") no-repeat;
    background-size: 100%;
}

/* 有序列表 */
.simflex-editor > .context-menu .menu-item .ol::before {
    background: url("../image/olList.png") no-repeat;
    background-size: 100%;
}

/* 嵌套图片 */
.simflex-editor > .context-menu .menu-item .fig::before {
    background: url("../image/imgIcon.png") no-repeat;
    background-size: 100%;
}

/* 普通图片 */
.simflex-editor > .context-menu .menu-item .image::before {
    background: url("../image/imgIcon.png") no-repeat;
    background-size: 100%;
}

/* 表格 */
.simflex-editor > .context-menu .menu-item .table::before {
    background: url("../image/tableIcon.png") no-repeat;
    background-size: 100%;
}

/* 步骤组 */
.simflex-editor > .context-menu .menu-item .procsteps::before {
    background: url("../image/stepGroup.png") no-repeat;
    background-size: 100%;
}

/* 区域 */
.simflex-editor > .context-menu .menu-item .div::before {
    background: url("../image/divIcon.png") no-repeat;
    background-size: 100%;
}

/* 分隔符 */
.simflex-editor > .context-menu .menu-item .instruction::before {
    background: url("../image/instructionIcon.png") no-repeat;
    background-size: 100%;
}

/* 上标 */
.simflex-editor > .context-menu .menu-item .sup::before {
    background: url("../image/supIcon.png") no-repeat;
    background-size: 100%;
}

/* 下标 */
.simflex-editor > .context-menu .menu-item .sub::before {
    background: url("../image/subIcon.png") no-repeat;
    background-size: 100%;
}

/* procstep */
/* 步骤 */
.simflex-editor > .context-menu .menu-item .procstep::before {
    background: url("../image/stepIcon.png") no-repeat;
    background-size: 100%;
}

/* 子步骤组 */
.simflex-editor > .context-menu .menu-item .procsubsteps::before {
    background: url("../image/substepGroup.png") no-repeat;
    background-size: 100%;
}

/* 子步骤 */
.simflex-editor > .context-menu .menu-item .procsubstep::before {
    background: url("../image/substep.png") no-repeat;
    background-size: 100%;
}

/* 是否 */
.simflex-editor > .context-menu .menu-item .whether::before {
    background: url("../image/whetherIcon.png") no-repeat;
    background-size: 100%;
}

/* list */
/* 列表项 */
.simflex-editor > .context-menu .menu-item .li::before {
    background: url("../image/listItems.png") no-repeat;
    background-size: 100%;
}

/* menuEditGroup */
/* 编辑项 */
.simflex-editor > .context-menu .menu-item .edit::before {
    background: url("../image/editItem.png") no-repeat;
    background-size: 100%;
}

/* 下方插入换行符 */
.simflex-editor > .context-menu .menu-item .topbr::before {
    background: url("../image/upInsertBr.png") no-repeat;
    background-size: 100%;
}

/* 下方插入换行符 */
.simflex-editor > .context-menu .menu-item .dowbr::before {
    background: url("../image/downInsertBr.png") no-repeat;
    background-size: 100%;
}

/* 右键菜单图标 */
/* 属性 */
.simflex-editor > .context-menu .menu-item .attr::before {
    background: url("../image/RightProperties.png") no-repeat;
    background-size: 100%;
}

/* 预览 */
.simflex-editor > .context-menu .menu-item .preview::before {
    background: url("../image/previewIcon.png") no-repeat;
    background-size: 100%;
}

/* 剪切 */
.simflex-editor > .context-menu .menu-item .cut:before {
    background: url("../image/shearIcon.png") no-repeat;
    background-size: 100%;
}

/* 复制 */
.simflex-editor > .context-menu .menu-item .copy::before {
    background: url("../image/copy.png") no-repeat;
    background-size: 100%;
}

/* 粘贴 */
.simflex-editor > .context-menu .menu-item .paste::before {
    background: url("../image/paste.png") no-repeat;
    background-size: 100%;
}

/* 删除 */
.simflex-editor > .context-menu .menu-item .delete::before {
    background: url("../image/detailIcon.png") no-repeat;
    background-size: 100%;
}

/* 合并单元格 */
.simflex-editor > .context-menu .menu-item .merge::before {
    background: url("../image/mergeCell.png") no-repeat;
    background-size: 100%;
}

/* 取消单元格的合并 */
.simflex-editor > .context-menu .menu-item .split::before {
    background: url("../image/splitCell.png") no-repeat;
    background-size: 100%;
}

/* 在上方插入行 */
.simflex-editor > .context-menu .menu-item .toprow::before {
    background: url("../image/upInsertRow.png") no-repeat;
    background-size: 100%;
}

/* 在下方插入行 */
.simflex-editor > .context-menu .menu-item .belowrow::before {
    background: url("../image/downInsertRow.png") no-repeat;
    background-size: 100%;
}

/* 在左侧插入列 */
.simflex-editor > .context-menu .menu-item .leftcol::before {
    background: url("../image/LInsertColumn.png") no-repeat;
    background-size: 100%;
}

/* 在右侧插入列 */
.simflex-editor > .context-menu .menu-item .rightcol::before {
    background: url("../image/rInsertColumn.png") no-repeat;
    background-size: 100%;
}

/* 删除整行 */
.simflex-editor > .context-menu .menu-item .delrow::before {
    background: url("../image/deleteRow.png") no-repeat;
    background-size: 100%;
}

/* 删除整列 */
.simflex-editor > .context-menu .menu-item .delcol::before {
    background: url("../image/deleteColumn.png") no-repeat;
    background-size: 100%;
}

/* 修改边框 */
.simflex-editor > .context-menu .menu-item .boundaryGroup::before {
    background: url("../image/borderIcon.png") no-repeat;
    background-size: 100%;
}

/* 默认 */
.simflex-editor > .context-menu .menu-item .threeMenu-item span::before {
    background: url("../image/borderCustom.png") no-repeat;
    background-size: 100%;
}

/* 全部 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .all::before {
    background: url("../image/borderAll.png") no-repeat;
    background-size: 100%;
}

/* 全无 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .without::before {
    background: url("../image/bordeNone.png") no-repeat;
    background-size: 100%;
}

/* 上框线 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .cell-border-top-style::before {
    background: url("../image/borderTop.png") no-repeat;
    background-size: 100%;
}

/* 下框线 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .cell-border-bottom-style::before {
    background: url("../image/borderBottom.png") no-repeat;
    background-size: 100%;
}

/* 左框线 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .cell-border-left-style::before {
    background: url("../image/borderLeft.png") no-repeat;
    background-size: 100%;
}

/* 右框线 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .cell-border-right-style::before {
    background: url("../image/borderRight.png") no-repeat;
    background-size: 100%;
}

/* 创建模板 */
.simflex-editor > .context-menu .menu-item .macro::before {
    background: url("../image/macro.png") no-repeat;
    background-size: 100%;
}

/* 拆分表格 */
.simflex-editor > .context-menu .menu-item .splitCellsGroup::before {
    background: url("../image/split_table.png") no-repeat;
    background-size: 100%;
}

/* 按行拆分 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .splitRow::before {
    background: url("../image/split_row.png") no-repeat;
    background-size: 100%;
}

/* 按列拆分 */
.simflex-editor > .context-menu .menu-item .threeMenu-item .splitCol::before {
    background: url("../image/split_col.png") no-repeat;
    background-size: 100%;
}

/* 属性区域 */
.attributeArea {
    height: 100%;
    width: 25%;
    margin-left: 10px;
    box-sizing: border-box;
    display: none;
    border-left: 1px solid var(--border-color);
}

.attributeArea .openArea,
.attributeArea .foldArea,
.attributeArea .previewArea,
.attributeArea .foldPreview {
    height: 100%;
}

.attributeArea .openArea .openTitle,
.attributeArea .foldArea .title,
.attributeArea .nodeName {
    color: #0064c1;
    font-size: 16px;
    font-weight: bold;
    padding: 2% 15px;
    box-sizing: border-box;
}

.attributeArea .nodeName {
    padding: 1% 15px;
}

.attributeArea .openArea .openTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid #0064c1;
}

.attributeArea .openArea .previewDetail {
    position: relative;
    width: 100%;
}

.attributeArea .openArea .previewDetail .previewContent {
    overflow-y: auto;
    overflow-x: hidden;
    word-break: break-all;
    padding: 5px;
    line-height: 1.7;
}

.attributeArea .foldArea .title {
    text-align: center;
    border-left: 2px solid #0064c1;
    height: 100%;
    padding: 15px 0;
    cursor: pointer;
}

.attributeArea .foldArea {
    display: none;
}

.attributeArea .openTitle .openIcon,
.attributeArea .title .foldIcon {
    width: 16px;
    height: 15px;
    cursor: pointer;
}

.attributeArea .title .foldIcon {
    padding-bottom: 2px
}

.attributeContent table {
    width: 100%;
    border: none;
    font-size: 16px;
    margin: 0;
}

.attributeContent table tbody {
    width: 100%;
    display: block;
    overflow-y: auto;
    overflow-x: hidden;
}

.attributeContent table thead tr,
.attributeContent table tbody tr {
    width: 100%;
    display: table;
    table-layout: fixed;
}

.attributeContent table tr th {
    padding: 2% 4%;
    border-top: 1px solid var(--border-color);;
    border-bottom: 1px solid var(--border-color);;
    text-align: left;
    border-right: none;
    border-left: none;
}

.attributeContent table tr th:first-child {
    border-right: 1px solid var(--border-color);
}

.attributeContent table tr td {
    border: none;
    padding: 2% 3%;
    text-align: left;
}

.attributeContent table tr td:last-child {
    padding: 2% 1.5%;
}

.attributeContent table tr td input,
.attributeContent table tr td select {
    width: 100%;
    border: 1px solid #0064c1;
    box-sizing: border-box;
    outline: none !important;
    padding: 3px 5px;
    border-radius: 3px;
}

.attributeContent table tbody tr {
    border-bottom: 1px solid #eaeaea;
    cursor: pointer;
}

.attributeContent table tbody tr:nth-child(odd) {
    background: #fff;
}

.attributeContent table tbody tr:nth-child(even) {
    background: #fafafa;
}

.attributeContent table .clickColor {
    background: #ecf3ff !important;
}

/* nav添加图片 */
#maskArea {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    position: absolute;
    top: 0;
    z-index: 1005;
    display: none;
}

.simflexContent .imageHandle,
.simflexContent .imageHandle.charArea {
    width: 65%;
    height: 88%;
    position: absolute;
    top: 0;
    background: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 4px;
    z-index: 1006;
    overflow: hidden;
}

.simflexContent .imageHandle.charArea {
    width: 33%;
    height: 72%;
}

#maskArea .imageHandle {
    width: 58%;
    height: 80%;
    position: absolute;
    top: 0;
    background: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 4px
}

#maskArea .imageHandle .dashboard,
.simflexContent .imageHandle .dashboard {
    height: 100%;
}

.imageContent .title {
    font-size: 18px;
    display: flex;
    font-weight: bold;
    align-items: center;
    justify-content: space-between;
    background: #f5f5f5;
    padding: 12px 3%;
}

.imageContent .title img {
    width: 12px;
    cursor: pointer;
}

.uploadArea {
    padding: 1.5% 3%;
}

.tabTitle {
    display: flex;
    font-size: 16px;
    color: #333;
}

.tabTitle div {
    border: 1px solid var(--border-color);
    border-radius: 4px 4px 0 0;
    background: linear-gradient(#ffffff, #f0f0f0);
    padding: 1% 2%;
    box-sizing: border-box;
    cursor: pointer;
    border-right: none
}

.imageHandle.charArea .tabTitle div {
    padding: 1.6% 2%;
}

.tabTitle div:last-child {
    border-right: 1px solid var(--border-color);
}

.tabTitle .active {
    background: #fff !important;
    border-bottom: 1px solid #fff !important;
    box-sizing: border-box;
    box-shadow: 0 0.1px 0 #fff;
}

.uploadDetail {
    border-radius: 0 4px 4px 4px;
    border: 1px solid var(--border-color);
    margin-top: -1px;
    box-sizing: border-box;
}

.imageHandle.charArea .uploadDetail {
    border: none;
}

/* 本地图片 */
.localUpload {
    border: 1px dashed var(--border-color);;
    border-radius: 4px;
    margin: 15px;
    height: calc(100% - 30px);
}

.localUpload > div button {
    font-size: 22px;
    background: #0064c1;
    color: #fff;
    border-radius: 4px;
    border: none;
    padding: 1% 2%;
    margin-top: 2%;
}

.localArea, .serveArea {
    height: 100%;
}

.butOption button,
.bottomArea button {
    cursor: pointer;
    white-space: nowrap;
    margin: 0 2%;
    background: #fff;
    outline: none !important;
    border-radius: 4px;
    border: 1px solid var(--border-color);;
    padding: 9px 18px;
    color: #868686;
    box-sizing: border-box;
    line-height: 1;
}

.butOption {
    padding-bottom: 5px
}

.butOption button:first-child,
.bottomArea button:first-child {
    border: 1px solid #0064c1;
    color: #333;
}

.localUpload {
    text-align: center;
}

.localUpload > div {
    position: relative;
    top: 50%;
    transform: translate(0, -50%);
}

.localUpload > div img {
    width: 12%;
}

/* 服务器图片 */
.serveArea .topSelect,
.serveArea .optionArea,
.serveArea .butOption {
    display: flex;
    flex-wrap: wrap;
}

.serveArea .butOption {
    margin-bottom: 5px
}

.serveArea .topSelect {
    justify-content: space-between;
    padding: 15px 15px 15px;
    flex-wrap: wrap
}

.optionArea > div {
    margin-right: 25px;
    white-space: nowrap;
    padding-bottom: 5px;
}

.serveArea .topSelect select,
.serveArea .topSelect input {
    width: 240px;
    padding: 7px 6px;
    outline: none !important;
    border: 1px solid var(--border-color);;
    border-radius: 4px;
}

.centerArea {
    padding: 0 2%;
    margin-top: -15px;
}

.detailImg {
    width: 102%;
    display: flex;
    flex-wrap: wrap;
    margin-left: -2%;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 2%;
    padding-top: 15px;
    align-content: flex-start;
}

.detailImg > div {
    width: 20%;
    padding-left: 2%;
    margin-bottom: 3%;
    box-sizing: border-box;
    text-align: center;
}

.detailImg > div div {
    border-radius: 4px;
    border: 1px solid var(--border-color);;
    box-sizing: border-box;
    height: 165px;
    line-height: 165px;
    text-align: center;
    overflow: hidden;
}

.detailImg > div div:hover {
    cursor: pointer;
    border: 1px solid #0064c1;
    box-shadow: 0 0 8px 1px rgba(0, 100, 193, 0.34);
    transform: translateY(-5px) !important;
}

.detailImg .clickStatus {
    border: 1px solid #0064c1;
    box-shadow: 0 0 10px 1px rgba(0, 100, 193, 0.34);
    transform: translateY(-5px) !important;
}

.detailImg > div div img {
    width: 100%;
    max-width: 90%;
    vertical-align: middle;
    margin-top: -5px;
}

.detailImg > div > p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 3px;
    font-size: 14px;
}

/* 分页 */
.page_bar {
    font-size: 12px;
    padding-bottom: 5px;
}

.page_bar button {
    background: transparent;
    border: none;
    outline: none;
    cursor: pointer;
    vertical-align: middle;
}

.page_bar button img {
    width: 13px;
    padding: 1px;
    cursor: pointer;
}

.page_bar button:disabled img {
    opacity: 0.5;
    cursor: not-allowed;
}

.page_bar .pervBut {
    margin: 0 1.5% 0 1%;
}

.page_bar .nextBut {
    margin: 0 1% 0 1.5%;
}

.page_bar .page {
    display: inline-block;
    margin: 0 6px;
    width: 22px;
    height: 22px;
    text-align: center;
    line-height: 22px;
    padding: 2px;
    border-radius: 50%;
    background: transparent;
    color: #333;
    cursor: pointer;
}

.pageActive {
    background: #0064c1 !important;
    color: #fff !important;
}

/* 按钮 */
.butOption button {
    margin-left: 15px;
}

.bottomArea {
    text-align: right;
    padding-bottom: 14px;
}

/* 选中 */
.bag {
    background-color: #d8dbe1 !important;
}

/* 2023-10-21 菜单项的悬浮 */
.menu-select-item {
    background-color: #f2f2f2 !important;
}

/* 历史版本比较差异的背景色 */
.contrastBag {
    background-color: #26C3EE !important;
}

/* dita html */
.detailArea {
    overflow: auto;
    border: 1px solid var(--border-color);;
    line-height: 1.7;
    position: relative;
}

.detailArea .contentDetail {
    display: flex;
}

.line {
    position: fixed;
    width: 1px;
    background-color: #ddd;
    margin-left: 45px;
}

.detailArea .contentDetail .lineNum {
    width: 40px;
    padding-right: 5px;
    margin-right: 5px;
    text-align: right;
    color: #777777;
}

.detailArea .contentDetail div:last-child {
    width: calc(100% - 80px);
    word-break: break-all;
}

/* 表格弹框 */
.tableHandle, .bulletFrameHandel {
    width: 100%;
    height: 100%;
    background-color: transparent;
    display: none;
    position: absolute;
    top: 0;
    z-index: 1006;
}

.tableStyle {
    position: absolute;
    background-color: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 4px;
    overflow: hidden;
    color: #000;
    box-shadow: 0 0 14px 1px rgb(102 102 102 / 34%);
    overflow: hidden;
    width: 45%;
    padding-bottom: 2%;
}

.tableHandle .tableIframe, .bulletFrameHandel .bulletFrameArea {
    position: absolute;
    background-color: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 4px;
    overflow: hidden;
    color: #000;
    box-shadow: 0 0 14px 1px rgb(102 102 102 / 34%);
    width: 330px;
    overflow: hidden;
}

.bulletFrameHandel .bulletFrameArea {
    width: 400px;
}

.tableHandle .tableIframe .title,
.tableStyle .styleContent .title,
.bulletFrameHandel .bulletFrameArea .title {
    font-size: 16px;
    font-weight: bold;
    padding: 12px 15px;
    background-color: #f5f5f5;
    margin-bottom: 4%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bulletFrameHandel .bulletFrameArea .bulletDetail {
    padding: 10px 15px;
}

.tableStyle .styleContent .title {
    padding: 15px 3%;
    font-size: 18px;
    margin-bottom: 2%;
}

.tableHandle .tableIframe .title img,
.tableStyle .styleContent .title img,
.bulletFrameHandel .bulletFrameArea .title img {
    width: 13px;
    cursor: pointer;
}

.tableHandle .tableIframe .tableSize {
    padding: 2% 6%;
}

.tableHandle .tableIframe .tableColum input,
.tableHandle .tableIframe .tableRow input {
    border: 1px solid #ccc !important;
    padding: 5px 6px !important;
}

.tableHandle .tableIframe .tableRow,
.tableHandle .tableIframe .tableColum {
    margin: 5% 0 5% 6%;
}

.bulletDetail > div:first-child {
    /* margin */
    padding-bottom: 10px
}

.bulletFrameHandel .bulletBut {
    text-align: right;
}

.bulletFrameHandel .bulletBut button {
    font-size: 15px;
    line-height: 1;
    outline: 0 !important;
    background: #fff !important;
    cursor: pointer;
    border-radius: 3px;
    border: 1px solid #1890ff !important;
    box-sizing: border-box;
    color: #1890ff !important;
    padding: 8px 15px;
    margin: 10px 0;
}

/* 表格样式 */
.styleDetail {
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;
}

.styleDetail > div {
    width: 25%;
}

.styleDetail > div > div {
    margin: 0 20px 20px
}

.styleDetail > div > div table tr th,
.styleDetail > div > div table tr td {
    padding: 15px 0;
    border: 1px solid #555;
}

.tableStyle .styleType {
    cursor: pointer;
}

/* 样式一 */
.tableStyle .styleType.editor-table-one thead,
.simflex-editor > .content tag[style-set="editor-table-one"] table thead {
    background-color: #985287;
}

.tableStyle .styleType.editor-table-one tbody tr:nth-child(even),
.content tag[style-set="editor-table-one"] table tbody tr:nth-child(even) {
    background-color: #f8f3f8;
}

.tableStyle .styleType.editor-table-one tr th,
.tableStyle .styleType.editor-table-one tr td,
.simflex-editor > .content tag[style-set="editor-table-one"] table tr th,
.simflex-editor > .content tag[style-set="editor-table-one"] table tr td {
    border: 1px solid #dfbfda;
}

/* 样式二 */
.tableStyle .styleType.editor-table-two thead,
.simflex-editor > .content tag[style-set="editor-table-two"] table thead {
    background-color: #0062b1;
}

.tableStyle .styleType.editor-table-two tbody tr:nth-child(even),
.content tag[style-set="editor-table-two"] table tbody tr:nth-child(even) {
    background-color: #e8f2fb;
}

.tableStyle .styleType.editor-table-two tr th,
.tableStyle .styleType.editor-table-two tr td,
.simflex-editor > .content tag[style-set="editor-table-two"] table tr th,
.simflex-editor > .content tag[style-set="editor-table-two"] table tr td {
    border: 1px solid #73bbe8;
}

/* 样式三 */
.tableStyle .styleType.editor-table-three thead,
.simflex-editor > .content tag[style-set="editor-table-three"] table thead {
    background-color: #fbd284;
}

.tableStyle .styleType.editor-table-three tbody tr:nth-child(even),
.content tag[style-set="editor-table-three"] table tbody tr:nth-child(even) {
    background-color: #fef6e8;
}

.tableStyle .styleType.editor-table-three tr th,
.tableStyle .styleType.editor-table-three tr td,
.simflex-editor > .content tag[style-set="editor-table-three"] table tr th,
.simflex-editor > .content tag[style-set="editor-table-three"] table tr td {
    border: 1px solid #fde9c6;
}

/* 样式四 */
.tableStyle .styleType.editor-table-four thead,
.simflex-editor > .content tag[style-set="editor-table-four"] table thead {
    background-color: #a6d1ad;
}

.tableStyle .styleType.editor-table-four tbody tr:nth-child(even),
.content tag[style-set="editor-table-four"] table tbody tr:nth-child(even) {
    background-color: #eff6f0;
}

.tableStyle .styleType.editor-table-four tr th,
.tableStyle .styleType.editor-table-four tr td,
.simflex-editor > .content tag[style-set="editor-table-four"] table tr th,
.simflex-editor > .content tag[style-set="editor-table-four"] table tr td {
    border: 1px solid #d5ead7;
}

/* 按钮 */
.styleName {
    margin: 8px 0;
    text-align: center
}

.styleName input {
    display: none
}

.styleName label::before {
    content: "";
    width: 18px;
    height: 18px;
    border: 2px solid #555;
    border-radius: 20px;
    display: inline-block;
    box-sizing: border-box;
    vertical-align: middle;
    margin-top: -1.8px;
    margin-right: 6px;
}

.styleName input:checked + label::before {
    border: none;
    background: url('../image/checkedIcon.png') no-repeat;
    background-size: 100% 100%;
}

.attrColor {
    display: flex;
    border: 1px solid #0064c1;
    box-sizing: border-box;
    border-radius: 3px;
    background: #fff;
    padding: 3px 5px;
}

.attributeArea .attrColor input {
    flex: 1;
    border: none;
    padding: 0 2px;
}

.attributeArea .attrColor .bgColor,
.attributeArea .attrColor .textBgColor {
    width: 15px;
    height: 15px;
    vertical-align: bottom;
    margin-top: 2px;
    margin-right: 5px;
    box-shadow: 0 0 2px 0 rgba(76, 76, 76, 0.5);
}

.colorHandle {
    position: absolute;
    right: 10px;
}

.colorHandle .colorContainer {
    width: 95%;
    padding: 4% 3%;
}

.colorHandle .colorContainer .tipsArea div {
    background: none;
    box-shadow: none;
}

.colorHandle .colorContainer .tipsArea div img {
    width: 100%;
    height: 100%;
    max-width: none;
}

/* 滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

/*正常情况下滑块的样式*/
::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 6px;
}

/*正常时候的主干部分*/
::-webkit-scrollbar-track {
    border-radius: 6px;
    background-color: transparent;
}

.cm-invalidchar {
    display: none;
}

@counter-style circled-decimal {
    system: fixed;
    symbols: ① ② ③ ④ ⑤ ⑥ ⑦ ⑧ ⑨ ⑩ ⑪ ⑫ ⑬ ⑭ ⑮ ⑯ ⑰ ⑱ ⑲ ⑳;
    suffix: " ";
}

@media screen and (max-width: 1680px) and (min-width: 1600px) {
    .simflexContent .imageHandle.charArea {
        width: 35%;
        height: 70%;
    }
}

@media screen and (max-width: 1600px) and (min-width: 1440px) {
    /* 编辑区域 */
    .simflexContent .simflex-editor,
    .simflex-editor > .context-menu,
    .tabTitle,
    .attributeArea .title,
    .attributeArea .nodeName,
    .attributeContent table {
        font-size: 15px;
    }

    input, select, textarea {
        font-size: 14px;
    }

    .simflex-editor > .toolbar > ul > li .icon {
        width: 30px;
        height: 30px;
        background-size: 18px;
    }

    .simflex-editor > .toolbar > ul .completeStatus span {
        width: 30px;
        height: 30px;
        line-height: 30px;
    }

    /* tag标签 */
    .simflex-editor > .content tag::before, .tag::before,
    .simflex-editor > .content tag::after, .tag::after {
        height: 14px;
        line-height: 14px;
        font-size: 13px;
    }

    /* nav添加图片 */
    .imageContent .title {
        font-size: 18px;
        padding: 12px 3%;
    }

    .imageHandle.charArea .imageContent .title {
        font-size: 17px;
        padding: 11px 3%;
    }

    .imageContent .title img {
        width: 11px;
    }

    .localUpload > div img {
        width: 11%;
    }

    .serveArea .topSelect select,
    .serveArea .topSelect input {
        width: 190px;
    }

    .butOption button, .bottomArea button {
        font-size: 15px;
        padding: 8px 18px;
    }

    .butOption button {
        margin-left: 12px;
    }

    .simflexContent .imageHandle.charArea {
        width: 34%;
        height: 58%;
    }

    /* 底部按钮 */
    .localUpload > div button {
        font-size: 17px;
    }

    /*menu*/
    .simflex-editor > .context-menu .menu-item li,
    .simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul li {
        padding: 8px 10px 8px 15px
    }

    .simflex-editor > .context-menu .menu-item ul li p span::before,
    .simflex-editor > .context-menu .menu-item ul li[type='rightClick'] .twoMenu-item span::before {
        display: inline-block;
        content: "";
        width: 17px;
        height: 17px;
        margin-right: 9px;
    }

    /* 属性 */
    .attributeArea .openTitle .openIcon,
    .attributeArea .title .foldIcon {
        width: 15px;
        height: 14px;
    }

    /* 表格弹框 */
    .tableHandle .tableIframe .title,
    .bulletFrameHandel .bulletFrameArea .title {
        font-size: 16px;
        padding: 11px 15px;
    }

    .bulletFrameHandel .bulletFrameArea .bulletDetail {
        padding: 10px 15px;
    }

    .tableHandle .tableIframe .title img,
    .bulletFrameHandel .bulletFrameArea .title img {
        width: 12px;
    }

    .styleDetail > div > div table tr th,
    .styleDetail > div > div table tr td {
        padding: 13px 0;
    }

    .tableStyle {
        width: 46%;
    }

    .styleContent button {
        padding: 7px 5.5%;
    }

    .tableStyle .styleContent .title {
        padding: 13px 3%;
        font-size: 16px
    }

    .tableStyle .styleContent .title img {
        width: 12px;
    }

    /* 颜色选择 */
    .colorHandle {
        right: 1%
    }

    .colorHandle .color-card,
    .colorHandle .tipsArea div {
        width: 16px;
        height: 16px;
    }

    .colorHandle .colorContainer {
        width: 98%;
        padding: 3% 2.5%;
    }

    /* 表格样式 */
    .styleName label::before {
        width: 17px;
        height: 17px;
    }

    .serveArea .topSelect {
        padding: 1.5% 2% 1.8%;
    }

    .detailImg > div div {
        height: 145px;
        line-height: 145px;
    }

    .page_bar .page {
        width: 20px;
        height: 20px;
        line-height: 20px;
    }

    .detailImg > div > p {
        font-size: 13px
    }
}

@media screen and (max-width: 1440px) and (min-width: 1400px) {
    .serveArea .topSelect {
        padding: 2% 2% 2%;
    }

    .serveArea .topSelect select, .serveArea .topSelect input {
        padding: 6px 5px;
    }

    .detailImg > div div {
        height: 135px;
        line-height: 135px;
    }

    .page_bar .page {
        width: 20px;
        height: 20px;
        line-height: 20px;
    }

    .simflexContent .imageHandle.charArea {
        width: 38%;
        height: 60%;
    }
}

@media screen and (max-width: 1400px) and (min-width: 1366px) {
    /* tag标签 */
    .simflex-editor > .content tag::before, .tag::before,
    .simflex-editor > .content tag::after, .tag::after {
        height: 14px;
        line-height: 14px;
        font-size: 13px;
    }

    /* nav添加图片 */
    .imageContent .title {
        font-size: 16px;
        padding: 13px 3%;
    }

    .imageContent .title img {
        width: 10px;
    }

    .tabTitle {
        font-size: 15px;
    }

    .localUpload > div img {
        width: 11%;
    }

    /* 弹框 */
    .simflexContent .imageHandle {
        width: 68%;
        height: 88%;
    }

    /* .simflexContent .imageHandle.charArea {
      width: 44%;
      height: 58%;
    } */
    /* 底部按钮 */
    .localUpload > div button {
        font-size: 15px;
    }

    .butOption button,
    .bottomArea button {
        font-size: 15px;
    }

    .serveArea .topSelect select,
    .serveArea .topSelect input {
        width: 200px;
        margin-left: 6px;
        padding: 7px 5px;
    }

    .butOption button {
        margin: 0 10px;
    }

    /* 表格弹框 */
    .styleDetail > div > div table tr th,
    .styleDetail > div > div table tr td {
        padding: 13.7px 0;
    }

    .tableStyle {
        width: 56%;
    }

    /* 颜色选择 */
    .colorHandle {
        right: 1%
    }

    .colorHandle .colorContainer {
        width: 100%;
        padding: 4% 2.5%;
    }

    .colorHandle .tipsArea div,
    .colorHandle .color-card {
        width: 15px;
        height: 15px;
    }

    .detailImg > div div {
        height: 160px;
        line-height: 160px;
    }

    .detailImg > div > p {
        font-size: 13px;
    }
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {
    /* 编辑器 */
    .simflexContent .simflex-editor,
    .simflex-editor > .context-menu,
    .tabTitle,
    .attributeArea .title,
    .attributeArea .nodeName,
    .attributeContent table,
    .simflex-editor > .status-bar,
    .attributeArea .status-bar,
    .styleName {
        font-size: 15px;
    }

    input, select, textarea {
        font-size: 14px;
    }

    /* tag标签 */
    .simflex-editor > .content tag::before, .tag::before,
    .simflex-editor > .content tag::after, .tag::after {
        height: 13px;
        line-height: 13px;
        font-size: 12px;
    }

    /* nav图片 */
    .simflex-editor > .toolbar > ul > li .icon {
        width: 30px;
        height: 30px;
        background-size: 16px;
    }

    .simflex-editor > .toolbar > ul .completeStatus span {
        width: 30px;
        height: 30px;
        line-height: 30px;
    }

    .simflex-editor > .toolbar > ul .completeStatus {
        position: absolute;
        right: 0;
        bottom: 3px;
    }

    .simflex-editor > .toolbar > ul {
        padding: 4px 3px;
    }

    .simflex-editor > .toolbar > ul > li {
        margin: 0 7px;
    }

    .simflex-editor > .toolbar > ul > .T_nextIcon::after,
    .simflex-editor > .toolbar > ul > .T_charIcon::after,
    .simflex-editor > .toolbar > ul > .T_alignIcon::after,
    .simflex-editor > .toolbar > ul > .T_imageIcon::after,
    .simflex-editor > .toolbar > ul > .T_unlinkIcon::after,
    .simflex-editor > .toolbar > ul > .T_ditaIcon::after {
        height: 22px;
        margin: 0 0px 0 14px;
    }

    /* menu图片 */
    .simflex-editor > .context-menu {
        font-size: 14px;
    }

    .simflex-editor > .context-menu .menu-item ul {
        padding: 4px 0;
    }

    .simflex-editor > .context-menu .menu-item li,
    .simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul li {
        padding: 7px 10px 7px 15px;
    }

    .simflex-editor > .context-menu .menu-item ul li p span::before,
    .simflex-editor > .context-menu .menu-item ul li[type='rightClick'] .twoMenu-item span::before {
        display: inline-block;
        content: "";
        width: 16px;
        height: 16px;
        margin-right: 8px;
    }

    .simflex-editor > .context-menu .menu-item ul li p img {
        width: 7px;
    }

    /* nav添加图片 */
    .imageContent .title {
        font-size: 15px;
        padding: 10px 3%;
    }

    .imageHandle.charArea .imageContent .title {
        padding: 8px 3%
    }

    .imageContent .title img {
        width: 10px;
    }

    .tabTitle div {
        padding: 1% 1.8%;
    }

    .localUpload > div img {
        width: 11%;
    }

    /* 弹框 */
    .page_bar {
        font-size: 12px;
        margin: 5px 0 5px
    }

    .simflexContent .imageHandle {
        width: 68%;
        height: 90%;
    }

    .simflexContent .imageHandle.charArea {
        width: 38%;
        height: 75%;
    }

    .serveArea .topSelect {
        padding: 1.5% 2% 1.5%;
    }

    .serveArea .topSelect select,
    .serveArea .topSelect input {
        width: 180px;
        margin-left: 6px;
        padding: 5px;
        font-size: 12px;
    }

    select {
        background-size: 11px auto;
    }

    .page_bar button img {
        width: 11px;
    }

    .page_bar .page {
        margin: 0 8px;
        width: 16px;
        height: 16px;
        line-height: 16px;
    }

    /* 底部按钮 */
    .localUpload > div button {
        font-size: 15px;
    }

    .butOption button,
    .bottomArea button {
        font-size: 13px;
        padding: 7px 16px;
    }

    .butOption button {
        margin: 0 10px;
    }

    .topSelect {
        font-size: 15px;
    }

    /* 属性 */
    .attributeArea .openTitle .openIcon, .attributeArea .title .foldIcon {
        width: 14px;
        height: 13px;
    }

    .attributeArea .openArea .openTitle,
    .attributeArea .foldArea .title,
    .attributeArea .nodeName {
        font-size: 15px;
    }

    /* 表格弹框 */
    .styleContent button {
        padding: 5.5px 5%;
        font-size: 15px
    }

    .tableHandle .tableIframe {
        width: 300px;
    }

    .bulletFrameHandel .bulletFrameArea {
        width: 310px;
    }

    .tableHandle .tableIframe .title,
    .bulletFrameHandel .bulletFrameArea .title {
        font-size: 15px;
        padding: 9px 12px;
    }

    .bulletFrameHandel .bulletFrameArea .bulletDetail {
        font-size: 14px;
        padding: 6px 12px;
    }

    .bulletFrameHandel .bulletBut button {
        font-size: 13px;
        padding: 6px 11px;
        margin: 5px 0 10px
    }

    .tableHandle .tableIframe .title img,
    .bulletFrameHandel .bulletFrameArea .title img {
        width: 10px;
    }

    .tableHandle .tableIframe .tableSize {
        font-size: 15px;
    }

    .tableHandle .tableIframe .tableColum input, .tableHandle .tableIframe .tableRow input {
        padding: 3px 4px !important;
    }

    .styleDetail > div > div table tr th,
    .styleDetail > div > div table tr td {
        padding: 12px 0;
    }

    .tableStyle {
        width: 48%;
    }

    .styleDetail > div > div {
        margin: 0 15px 20px;
    }

    .tableStyle .styleContent .title {
        padding: 10px 3%;
        font-size: 15px
    }

    .tableStyle .styleContent .title img {
        width: 10px;
    }

    .styleName label::before {
        content: "";
        width: 16px;
        height: 16px;
        margin-top: -3px;
    }

    /* 颜色选择 */
    .colorHandle {
        right: 1%
    }

    .colorHandle .colorContainer {
        width: 100%;
        padding: 4% 2.5%;
    }

    .colorHandle .tipsArea div,
    .colorHandle .color-card {
        width: 15px;
        height: 15px;
    }

    .detailImg > div {
        margin-bottom: 2%
    }

    .detailImg > div div {
        height: 120px;
        line-height: 120px;
    }

    .detailImg > div > p {
        font-size: 12px
    }
}

@media screen and (max-width: 1280px) and (min-width: 1024px) {
    /* nav图片 */
    .simflexContent .simflex-editor,
    .simflex-editor > .context-menu,
    .tabTitle,
    .attributeArea .title,
    .attributeArea .nodeName,
    .attributeContent table,
    .styleName {
        font-size: 14px;
    }

    input, select, textarea {
        font-size: 13px;
    }

    /* tag标签 */
    .simflex-editor > .content tag::before, .tag::before,
    .simflex-editor > .content tag::after, .tag::after {
        height: 13px;
        line-height: 13px;
        font-size: 12px;
    }

    /* nav添加图片 */
    .simflex-editor > .toolbar > ul > li {
        margin: 0 6px;
    }

    .simflex-editor > .toolbar > ul > li .icon {
        width: 28px;
        height: 28px;
        background-size: 16px;
    }

    .simflex-editor > .toolbar > ul .completeStatus span {
        width: 28px;
        height: 28px;
        line-height: 28px;
    }

    .simflex-editor > .toolbar > ul > .T_nextIcon::after,
    .simflex-editor > .toolbar > ul > .T_charIcon::after,
    .simflex-editor > .toolbar > ul > .T_alignIcon::after,
    .simflex-editor > .toolbar > ul > .T_imageIcon::after,
    .simflex-editor > .toolbar > ul > .T_unlinkIcon::after,
    .simflex-editor > .toolbar > ul > .T_ditaIcon::after {
        height: 22px;
        margin: 0 0px 0 12px;
    }

    .imageContent .title {
        font-size: 14px;
        padding: 8px 3%;
    }

    .imageContent .title img {
        width: 9px;
    }

    .tabTitle {
        font-size: 14px;
    }

    .localUpload > div img {
        width: 11%;
    }

    .topSelect {
        font-size: 13px;
    }

    .simflex-editor > .context-menu .menu-item ul li p span::before,
    .simflex-editor > .context-menu .menu-item ul li[type='rightClick'] .twoMenu-item span::before {
        display: inline-block;
        content: "";
        width: 15px;
        height: 15px;
        margin-right: 6px;
    }

    .simflex-editor > .context-menu .menu-item ul li p img {
        width: 7px;
    }

    /* 元素路径 */
    .simflex-editor > .status-bar,
    .attributeArea .status-bar {
        font-size: 14px;
    }

    /* 弹框 */
    .simflexContent .imageHandle.charArea {
        width: 40%;
        height: 75%;
    }

    .page_bar .page {
        margin: 0 5px;
        width: 17px;
        height: 17px;
        line-height: 17px;
    }

    /* 底部按钮 */
    .localUpload > div button {
        font-size: 14px;
    }

    .butOption button,
    .bottomArea button {
        font-size: 13px;
        padding: 7px 14px;
    }

    .butOption button {
        margin: 0 10px;
    }

    .simflexContent .imageHandle {
        width: 70%;
    }

    .serveArea .topSelect select,
    .serveArea .topSelect input {
        width: 190px;
        margin-left: 8px;
        padding: 5px 4px;
        font-size: 12px;
    }

    select {
        background-size: 10px auto;
    }

    /* 表格弹框 */
    .tableHandle .tableIframe {
        width: 300px;
    }

    .bulletFrameHandel .bulletFrameArea {
        width: 310px;
    }

    .tableHandle .tableIframe .title,
    .bulletFrameHandel .bulletFrameArea .title {
        font-size: 15px;
        padding: 8px 12px;
    }

    .bulletFrameHandel .bulletFrameArea .bulletDetail {
        font-size: 14px;
        padding: 6px 12px;
    }

    .bulletFrameHandel .bulletBut button {
        font-size: 13px;
        padding: 6px 11px;
        margin: 5px 0 10px
    }

    .tableHandle .tableIframe .title img,
    .bulletFrameHandel .bulletFrameArea .title img {
        width: 10px;
    }

    .tableHandle .tableIframe .tableColum input, .tableHandle .tableIframe .tableRow input {
        padding: 3px 4px !important;
    }

    .styleDetail > div > div table tr th,
    .styleDetail > div > div table tr td {
        padding: 11px 0;
    }

    .styleDetail > div > div {
        margin: 0 12px 20px;
    }

    .tableStyle {
        width: 46%;
    }

    .styleName label::before {
        content: "";
        width: 15px;
        height: 15px;
        margin-top: -1px;
    }

    .tableStyle .styleContent .title {
        padding: 10px 3%;
        font-size: 15px
    }

    .tableStyle .styleContent .title img {
        width: 9px;
    }

    .tableHandle .tableIframe .tableSize {
        font-size: 14px;
    }

    .styleContent button {
        padding: 5px 5%;
        margin: 0 10px;
    }

    /* 属性 */
    .attributeArea .openTitle .openIcon, .attributeArea .title .foldIcon {
        width: 13px;
        height: 12px;
    }

    .attributeArea .openArea .openTitle,
    .attributeArea .foldArea .title,
    .attributeArea .nodeName {
        font-size: 14px;
    }

    /* 颜色选择 */
    .colorHandle {
        right: 1.2%
    }

    .colorHandle .colorContainer {
        width: 100%;
        padding: 4% 3%;
    }

    .colorHandle .tipsArea div,
    .colorHandle .color-card {
        width: 14px;
        height: 14px;
    }

    .serveArea .topSelect {
        padding: 1% 2% 1.3%;
    }

    .detailImg > div div {
        height: 115px;
        line-height: 115px;
    }

    .detailImg > div > p {
        font-size: 12px
    }

    .uploadArea {
        padding: 1.5% 3%
    }
}

@media screen and (max-width: 1024px) {
    /* 编辑 */
    .simflexContent .simflex-editor,
    .simflex-editor > .context-menu,
    .tabTitle,
    .attributeArea .title,
    .attributeArea .nodeName,
    .attributeContent table,
    input::placeholder,
    .styleName {
        font-size: 14px;
    }

    /* tag标签 */
    .simflex-editor > .content tag::before, .tag::before,
    .simflex-editor > .content tag::after, .tag::after {
        height: 13px;
        line-height: 13px;
        font-size: 12px;
    }

    /* 属性 */
    .attributeArea .openTitle .openIcon, .attributeArea .title .foldIcon {
        width: 13px;
        height: 12px;
    }

    .attributeArea .openArea .openTitle,
    .attributeArea .foldArea .title,
    .attributeArea .nodeName {
        font-size: 14px;
    }

    input, select, textarea,
    .simflex-editor > .status-bar,
    .attributeArea .status-bar {
        font-size: 13px;
    }

    /* nav图片 */
    .simflex-editor > .toolbar > ul > li {
        margin: 0 5px;
    }

    .simflex-editor > .toolbar > ul > li .icon {
        width: 26px;
        height: 26px;
        background-size: 15px;
    }

    .simflex-editor > .toolbar > ul .completeStatus span {
        width: 26px;
        height: 26px;
        line-height: 26px;
    }

    .simflex-editor > .toolbar > ul > .T_nextIcon::after,
    .simflex-editor > .toolbar > ul > .T_charIcon::after,
    .simflex-editor > .toolbar > ul > .T_alignIcon::after,
    .simflex-editor > .toolbar > ul > .T_imageIcon::after,
    .simflex-editor > .toolbar > ul > .T_unlinkIcon::after,
    .simflex-editor > .toolbar > ul > .T_ditaIcon::after {
        height: 20px;
        margin: 0 0px 0 10px;
    }

    .imageContent .title {
        font-size: 15px;
        padding: 8px 3%;
    }

    .imageContent .title img {
        width: 9px;
    }

    /* 弹框 */
    .simflexContent .imageHandle {
        width: 75%;
        height: 96%;
    }

    .simflexContent .imageHandle.charArea {
        width: 50%;
        height: 73%;
    }

    .serveArea .topSelect select,
    .serveArea .topSelect input {
        width: 180px;
        margin-left: 5px;
        padding: 5px;
    }

    .butOption button,
    .bottomArea button {
        font-size: 12px;
        padding: 7px 14px;
    }

    .butOption button {
        margin: 0 5px;
    }

    .detailImg > div div {
        height: 135px;
        line-height: 135px;
    }

    .detailImg > div > p {
        font-size: 12px
    }

    /* 分页 */
    .page_bar {
        font-size: 11px;
    }

    .page_bar button img {
        width: 11px;
    }

    .page_bar .page {
        margin: 0 5px;
        width: 16px;
        height: 16px;
        line-height: 16px;
    }

    /* menu图片 */
    .simflex-editor > .context-menu .menu-item ul {
        padding: 4px 0;
    }

    .simflex-editor > .context-menu .menu-item li,
    .simflex-editor > .context-menu .menu-item li[type='rightClick'] .twoMenu-item ul li {
        padding: 7px 8px 7px 12px;
    }

    .simflex-editor > .context-menu .menu-item ul li p span::before,
    .simflex-editor > .context-menu .menu-item ul li[type='rightClick'] .twoMenu-item span::before {
        display: inline-block;
        content: "";
        width: 15px;
        height: 15px;
        margin-right: 8px;
    }

    .topSelect {
        font-size: 14px;
    }

    .simflex-editor > .context-menu .menu-item ul li p img {
        width: 7px;
    }

    /* 表格弹框 */
    .tableHandle .tableIframe {
        width: 300px;
    }

    .bulletFrameHandel .bulletFrameArea {
        width: 310px;
    }

    .styleContent button {
        padding: 5px 5%;
        margin: 0 10px;
        font-size: 14px
    }

    .tableHandle .tableIframe .title,
    .bulletFrameHandel .bulletFrameArea .title {
        font-size: 15px;
        padding: 8px 12px;
    }

    .bulletFrameHandel .bulletFrameArea .bulletDetail {
        font-size: 13px;
        padding: 6px 12px;
    }

    .bulletFrameHandel .bulletBut button {
        font-size: 14px;
        padding: 6px 11px;
        margin: 5px 0 10px
    }

    .tableHandle .tableIframe .title img,
    .bulletFrameHandel .bulletFrameArea .title img {
        width: 10px;
    }

    .tableHandle .tableIframe .tableSize {
        font-size: 14px;
    }

    .tableHandle .tableIframe .tableColum input, .tableHandle .tableIframe .tableRow input {
        padding: 3px 4px !important;
    }

    .styleDetail > div > div table tr th,
    .styleDetail > div > div table tr td {
        padding: 11px 0;
    }

    .styleDetail > div > div {
        margin: 0 10.4px 20px;
    }

    .tableStyle {
        width: 55%;
    }

    .styleName label::before {
        content: "";
        width: 15px;
        height: 15px;
        margin-top: -1px;
    }

    .tableStyle .styleContent .title {
        padding: 10px 3%;
        font-size: 15px
    }

    .tableStyle .styleContent .title img {
        width: 10px;
    }

    /* 颜色选择 */
    .colorHandle {
        right: 3.6%
    }

    .colorHandle .colorContainer {
        width: 120%;
        padding: 3% 2%;
    }

    .colorHandle .tipsArea div,
    .colorHandle .color-card {
        width: 14px;
        height: 14px;
    }
}

@media screen and (max-width: 1024px) and (min-width: 900px) {
    .simflexContent .imageHandle.charArea {
        width: 53%;
        height: 88%;
    }
}

@media screen and (max-width: 900px) and (min-width: 700px) {
    .simflexContent .imageHandle.charArea {
        width: 58%;
        height: 88%;
    }
}

@media screen and (max-width: 700px) and (min-width: 500px) {
    .simflexContent .imageHandle.charArea {
        width: 70%;
        height: 88%;
    }
}
