<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>vue html5 editor demo</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport"/>
    <link rel="stylesheet" href="./css/reset.css">
    <script src="../dist/simflex-editor.js"></script>
    <script src="./js/vue.js"></script>
    <script src="./js/axios-0.18.0.js"></script>
    <link href="./css/style.css" rel="stylesheet">
    <link rel="stylesheet" href="./css/font-awesome.min.css">
</head>
<body ondragstart="return false;">
<div id="app">
    <div class="container" v-cloak>
        <simflex-editor :content="content" :height="300" :show-module-name="showModuleName"
                        @change="updateData" ref="editor"></simflex-editor>
    </div>
    <div id="maskArea"></div>
</div>

<script>
    Vue.use(SimflexEditor, {
        showModuleName: true,
        image: {
            sizeLimit: 512 * 1024,
            compress: true,
            width: 500,
            height: 500,
            quality: 80
        }
    })
    new Vue({
        el: "#app",
        data: {
            content: "<br>",
            showModuleName: false,
        },
        methods: {
            updateData: function (data) {
                this.content = data
            },
            fullScreen: function () {
                this.$refs.editor.enableFullScreen()
            },
            focus: function () {
                this.$refs.editor.focus()
            },
            reset: function () {
                var newContent = prompt('please input some html code: ')
                if (newContent) {
                    this.content = newContent
                }
            },
            // 获取内容
            getContent() {
                return this.$refs.editor.getContent()
            },
            // 设置内容
            setContent(text, bool) {
                let div = document.createElement('div');
                div.innerHTML = text;
                let styles = div.getElementsByTagName('style');
                for (let i = styles.length - 1; i >= 0; i--) {
                    const el = styles[i];
                    document.head.appendChild(el);
                }
                if (bool) {
                    let nodes = div.children
                    for (let i = 0; i < nodes.length; i++) {
                        const el = nodes[i];
                        if (el.getAttribute('tagname') === 'title') {
                            el.setAttribute("contenteditable", "false")
                        }
                    }
                }
                this.content = div.innerHTML ? div.innerHTML : '<br>'
            },
            findReplace(findVal, replaceVal) {
                this.$refs.editor.searchDashboard = null
                window.lightVal = undefined
                setTimeout(() => {
                    if (findVal !== '' && findVal !== undefined) {
                        if (this.$refs.editor.searchDashboard === null) {
                            this.$refs.editor.searchDashboard = 'dashboard-search'
                        }
                        const inputVal = []
                        inputVal.push(findVal, replaceVal)
                        window.findReplaceVal = inputVal
                        return inputVal
                    }
                }, 10)
            },
            findLight(findVal) {
                this.$refs.editor.searchDashboard = null
                window.findReplaceVal = undefined
                setTimeout(() => {
                    if (findVal !== '' && findVal !== undefined) {
                        if (this.$refs.editor.searchDashboard === null) {
                            this.$refs.editor.searchDashboard = 'dashboard-search'
                        }
                        window.lightVal = findVal
                    } else {
                        if (this.$refs.editor.searchDashboard !== null) {
                            this.$refs.editor.searchDashboard = null
                            const target = document.getElementsByClassName('content')[0]
                            target.innerHTML = target.innerHTML.replace(/<span class="highLight" .*?>(.+?)<\/span>/g, '$1')
                            window.lightVal = findVal
                        } else {
                            window.lightVal = undefined
                        }
                    }
                    return findVal
                }, 10)
            },
            // 设置属性
            setWindow(path, params) {
                window.url = {
                    url: path,
                    param: params
                }
            },
            // 禁用编辑器
            disabledEditor() {
                this.$refs.editor.disabledEditor()
            },
            // 启用编辑器
            removeEditor() {
                this.$refs.editor.removeEditor()
            },
            // 编辑title
            editTitle(str) {
                this.$refs.editor.editTitle(str)
            },
            getContentHasChange() {
                // 返回保存标志
                return this.$refs.editor.getContentHasChange()
            },
            onSaveContent() {
                // 桌面端保存过后，重新指定指针位置
                this.$refs.editor.onSaveContent()
            },
            assignContent(str) {
                this.$refs.editor.assignContent(str)
            },
            // 隐藏工具栏
            noneToolbar() {
                this.$refs.editor.noneToolbar()
            },
            // 显示工具栏
            displayToolbar() {
                this.$refs.editor.displayToolbar()
            },
            compare(node) {
                setTimeout(() => {
                    this.$refs.editor.compare(node)
                })
            },
            // 跳转链接
            jumpInnerLinkByClientCall(id) {
                this.$refs.editor.jumpInnerLinkByClientCall(id)
            },
            // 插入模板
            insertTemplateStr(text) {
                this.$refs.editor.insertTemplateStr(text)
            },
            // 判断是否有焦点
            cursorIsInBrowser() {
                return this.$refs.editor.cursorIsInBrowser()
            },
            // 修改状态
            updateFinishStatus(b) {
                this.$refs.editor.updateFinishStatus(b)
            },
            // 刷新图片
            imgageRefresh() {
                this.$refs.editor.imgageRefresh()
            }
        },
        mounted() {
            _this = this
            // 使用方法：
            // 1. 引入：<iframe id="editFrame" marginwidth=0 marginheight=0 width="100%" height="100%" src="./index.html" frameborder="no"></iframe>
            // 2. 获取子窗体的window对象: let childWindow = document.getElementById("editFrame").contentWindow;
            // 3. 子窗体完全加载完毕：
            // childWindow.onload = function(){
            //      设置内容： childWindow.setContent(str)
            //      获取内容： childWindow.getContent()
            // }

            // 将方法注册给window对象
            // 获取
            window.jsCallback = function (id, str) {
                // jsConnector.callback(id,str)
                console.log("id: " + id, "str: " + str);
            }
            // 获取
            window.getContent = function () {
                return _this.getContent();
            }
            // 设置
            window.setContent = function (text, bool) {
                _this.setContent(text, bool);
            }
            // 查找替换
            window.findReplace = function (findVal, replaceVal) {
                if (findVal !== '' && findVal !== undefined) {
                    _this.findReplace(findVal, replaceVal)
                }
            }
            // 查找高亮
            window.findLight = function (findVal) {
                if (window.lightVal !== findVal) {
                    _this.findLight(findVal)
                }
            }
            // 设置path
            window.paramUrl = function (path, params) {
                _this.setWindow(path, params)
            }

            window.setToken = function (token) {

                window.axios.defaults.headers.common['Authorization'] = token;

            }

            window.disabledEditor = function () {
                _this.disabledEditor();
            }
            window.removeEditor = function () {
                _this.removeEditor();
            }
            window.editTitle = function (str) {
                _this.editTitle(str);
            }

            window.getContentHasChange = function () {
                // 返回保存标志
                return _this.getContentHasChange()
            }
            window.onSaveContent = function () {
                // 桌面端保存过后，重新指定指针位置
                _this.onSaveContent()
            }
            window.assignContent = function (str) {
                // 设置内容
                _this.assignContent(str)
            }

            window.noneToolbar = function () {
                // 隐藏工具栏
                _this.noneToolbar()
            }

            window.displayToolbar = function () {
                // 显示工具栏
                _this.displayToolbar()
            }

            window.compare = function (str) {
                _this.compare(str)
            }

            // 跳转链接
            window.jumpInnerLinkByClientCall = function (id) {
                _this.jumpInnerLinkByClientCall(id)
            }

            // 插入模板
            window.insertTemplateStr = function (text) {
                _this.insertTemplateStr(text)
            }

            // 判断存不存在焦点
            window.cursorIsInBrowser = function () {
                return _this.cursorIsInBrowser()
            }

            window.updateFinishStatus = function (b) {
                _this.updateFinishStatus(b)
            }

            // 刷新图片
            window.imgageRefresh = function () {
                _this.imgageRefresh()
            }

        },
    })
</script>
</body>
</html>
